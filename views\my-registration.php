<?php
if (!function_exists('wc_get_orders')) {
  echo '<div class="alert alert-warning">WooCommerce is not active.</div>';
  return;
}

// Enqueue the my-registration CSS
wp_enqueue_style('my-registration-css', LCI2025_URL . 'assets/css/my-registration.css', array(), LCI2025_VERSION);

$user_id = get_current_user_id();
$current_user = wp_get_current_user();

$display_name = $current_user->display_name;
$email = $current_user->user_email;
$phone = get_user_meta($user_id, 'billing_phone', true);

// Try to get registration data from main event table first
$registration_data = null;
if (class_exists('LCI_Main_Event') && method_exists('LCI_Main_Event', 'get_user_registration_data')) {
  $registration_data = LCI_Main_Event::get_user_registration_data($user_id);

  // Log for debugging
  if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('LCI Dashboard - Registration data from main event table for user ' . $user_id . ': ' . ($registration_data ? json_encode($registration_data) : 'Not found'));
  }
}

// Get ALL orders for this user
$all_orders = wc_get_orders([
  'customer_id' => $user_id,
  'orderby'     => 'date',
  'order'       => 'DESC',
  'limit'       => -1, // Get all orders
]);

// Log for debugging
if (defined('WP_DEBUG') && WP_DEBUG) {
  error_log('LCI Dashboard - Found ' . count($all_orders) . ' orders for user ' . $user_id);
}

// For backward compatibility, keep the $order variable for the most recent order
$order = !empty($all_orders) ? $all_orders[0] : null;
$order_id = $order ? $order->get_id() : 'N/A';

// If we have registration data, get the specific order referenced in it
if ($registration_data) {
  // Use data from main event table
  $main_event_order_id = $registration_data['order_id'];

  // Get the actual order for other meta data
  $main_event_order = wc_get_order($main_event_order_id);

  // If the order exists, use it as the primary order
  if ($main_event_order) {
    $order = $main_event_order;
    $order_id = $main_event_order_id;
  }
}

$order_meta = $order ? $order->get_meta_data() : [];

function get_order_meta_value($order, $key, $default = '�') {
  return $order ? ($order->get_meta($key, true) ?: $default) : $default;
}

$attendee_id = get_order_meta_value($order, '_alg_wc_full_custom_order_number', $order_id);
$your_association = get_order_meta_value($order, 'your_association');
$assoc_map = [
  'RT' => 'rt_association',
  'LC' => 'lc_association',
  'TANGENT' => 'tangent_association',
  'AGORA' => 'agora_association',
  'C41' => 'c41_club',
];
$assoc_key = isset($assoc_map[$your_association]) ? $assoc_map[$your_association] : '';
$association_raw = $assoc_key ? get_order_meta_value($order, $assoc_key) : '�';
$association_clean = str_replace('_', ' ', $association_raw);

$club_no = get_order_meta_value($order, 'club_no');
$club_position = get_order_meta_value($order, 'position');
$diet = get_order_meta_value($order, 'specific_diet');

// Get allergies from various meta keys
$allergies_from_allergies_key = get_order_meta_value($order, 'allergies');
$allergies_from_other_key = get_order_meta_value($order, 'other');
$allergies_from_alergies_key = get_order_meta_value($order, 'alergies'); // Note the spelling
$allergies_from_underscore_key = get_order_meta_value($order, '_allergies');

// Check for allergies in order items
$item_allergies = [];
if ($order) {
  foreach ($order->get_items() as $item_id => $item) {
    // Get allergies from item meta
    $item_allergies_meta = wc_get_order_item_meta($item_id, 'allergies', true);
    if (!empty($item_allergies_meta) && $item_allergies_meta !== '�') {
      $item_allergies[] = $item_allergies_meta;
    }

    // Get allergies from 'other' meta
    $item_other_meta = wc_get_order_item_meta($item_id, 'other', true);
    if (!empty($item_other_meta) && $item_other_meta !== '�') {
      $item_allergies[] = $item_other_meta;
    }
  }
}

// Combine allergies from all sources
$all_allergies = [];
foreach ([$allergies_from_allergies_key, $allergies_from_other_key, $allergies_from_alergies_key, $allergies_from_underscore_key] as $allergy_source) {
  if (!empty($allergy_source) && $allergy_source !== '�') {
    $all_allergies[] = $allergy_source;
  }
}

// Add item allergies
foreach ($item_allergies as $item_allergy) {
  $all_allergies[] = $item_allergy;
}

// Join all allergies
$allergies = implode(', ', $all_allergies);

// Process allergies for display
$formatted_allergies = [];
if (!empty($allergies)) {
  // If it's a comma-separated list, split it
  if (strpos($allergies, ',') !== false) {
    $allergy_parts = explode(',', $allergies);
    foreach ($allergy_parts as $part) {
      $part = trim($part);
      if (!empty($part) && !in_array($part, $formatted_allergies)) {
        $formatted_allergies[] = $part;
      }
    }
  } else {
    $allergies = trim($allergies);
    if (!empty($allergies)) {
      $formatted_allergies[] = $allergies;
    }
  }
}

$room_share = get_order_meta_value($order, 'room_share_options');
$share_with = $room_share === 'yes' ? get_order_meta_value($order, 'share_room_with') : '�';

$registration_type = 'N/A';
$registration_icon = 'fa-question-circle';
$registration_types = []; // Array to hold all registration types found

// Use registration data from main event table if available
if ($registration_data) {
  $registration_type = $registration_data['event_package'];
  $payment_status = ucfirst($registration_data['payment_status']);

  // Log for debugging
  if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('LCI Dashboard - Using registration data from main event table: Type=' . $registration_type . ', Status=' . $payment_status);
  }

  // Set appropriate icon based on registration type
  if (strpos($registration_type, 'Councilor') !== false) {
    $registration_icon = 'fa-user-tie';
  } elseif (strpos($registration_type, 'Full Event') !== false) {
    $registration_icon = 'fa-certificate';
  } elseif (strpos($registration_type, 'Partners') !== false) {
    $registration_icon = 'fa-handshake';
  }
}
// Fallback to checking ALL WooCommerce orders if no registration data
elseif (!empty($all_orders)) {
  // Check all orders for registration types
  foreach ($all_orders as $current_order) {
    // Log the order ID and status for debugging
    if (defined('WP_DEBUG') && WP_DEBUG) {
      error_log('LCI Dashboard - Checking order #' . $current_order->get_id() . ' (Status: ' . $current_order->get_status() . ')');
    }

    // Dump all items in the order for debugging
    if (defined('WP_DEBUG') && WP_DEBUG) {
      $items_debug = [];
      foreach ($current_order->get_items() as $debug_item) {
        $items_debug[] = [
          'id' => $debug_item->get_product_id(),
          'name' => $debug_item->get_name()
        ];
      }
      error_log('LCI Dashboard - Order items: ' . json_encode($items_debug));
    }

    foreach ($current_order->get_items() as $item) {
      $product_id = $item->get_product_id();
      $product_name = $item->get_name();

      // Get the product object to check if it's a variation
      $product = $item->get_product();
      $parent_id = null;
      $product_categories = [];

      if ($product) {
        if ($product->is_type('variation')) {
          $parent_id = $product->get_parent_id();
          // Get categories from parent product
          $product_categories = wp_get_post_terms($parent_id, 'product_cat', ['fields' => 'ids']);
        } else {
          // Get categories directly
          $product_categories = wp_get_post_terms($product_id, 'product_cat', ['fields' => 'ids']);
        }
      }

      // Log for debugging
      if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Dashboard - Checking product ID ' . $product_id . ' (' . $product_name . ') for registration type');
        if ($parent_id) {
          error_log('LCI Dashboard - This is a variation. Parent ID: ' . $parent_id);
        }
        if (!empty($product_categories)) {
          error_log('LCI Dashboard - Product categories: ' . implode(', ', $product_categories));
        }
      }

      // Check for exact product IDs (both direct and parent IDs)
      if ($product_id == 41 || $parent_id == 41) {
        $registration_types[] = [
          'type' => 'Full Event Package',
          'icon' => 'fa-certificate',
          'priority' => 2,
          'order_id' => $current_order->get_id(),
          'status' => $current_order->get_status()
        ];

        // Log for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - Found Full Event Package (ID: ' . ($parent_id == 41 ? 'parent ' . $parent_id : $product_id) . ')');
        }
      } elseif ($product_id == 40 || $parent_id == 40) {
        $registration_types[] = [
          'type' => 'Councilor Package',
          'icon' => 'fa-user-tie',
          'priority' => 1,
          'order_id' => $current_order->get_id(),
          'status' => $current_order->get_status()
        ];

        // Log for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - Found Councilor Package (ID: ' . ($parent_id == 40 ? 'parent ' . $parent_id : $product_id) . ')');
        }
      } elseif ($product_id == 42 || $parent_id == 42) {
        $registration_types[] = [
          'type' => 'Partners Package',
          'icon' => 'fa-handshake',
          'priority' => 3,
          'order_id' => $current_order->get_id(),
          'status' => $current_order->get_status()
        ];

        // Log for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - Found Partners Package (ID: ' . ($parent_id == 42 ? 'parent ' . $parent_id : $product_id) . ')');
        }
      }

      // Check by product category
      elseif (!empty($product_categories)) {
        // Category ID 18 = Main Event
        if (in_array(18, $product_categories)) {
          $registration_types[] = [
            'type' => 'Full Event Package',
            'icon' => 'fa-certificate',
            'priority' => 2,
            'order_id' => $current_order->get_id(),
            'status' => $current_order->get_status()
          ];

          // Log for debugging
          if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Dashboard - Found Full Event Package by category (18)');
          }
        }
        // Category ID 20 = Councilors
        elseif (in_array(20, $product_categories)) {
          $registration_types[] = [
            'type' => 'Councilor Package',
            'icon' => 'fa-user-tie',
            'priority' => 1,
            'order_id' => $current_order->get_id(),
            'status' => $current_order->get_status()
          ];

          // Log for debugging
          if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Dashboard - Found Councilor Package by category (20)');
          }
        }
      }
      // Also check product name as a fallback
      elseif (stripos($product_name, 'full event') !== false) {
        $registration_types[] = [
          'type' => 'Full Event Package',
          'icon' => 'fa-certificate',
          'priority' => 2,
          'order_id' => $current_order->get_id(),
          'status' => $current_order->get_status()
        ];

        // Log for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - Found Full Event Package by name');
        }
      } elseif (stripos($product_name, 'councilor') !== false) {
        $registration_types[] = [
          'type' => 'Councilor Package',
          'icon' => 'fa-user-tie',
          'priority' => 1,
          'order_id' => $current_order->get_id(),
          'status' => $current_order->get_status()
        ];

        // Log for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - Found Councilor Package by name');
        }
      } elseif (stripos($product_name, 'partner') !== false) {
        $registration_types[] = [
          'type' => 'Partners Package',
          'icon' => 'fa-handshake',
          'priority' => 3,
          'order_id' => $current_order->get_id(),
          'status' => $current_order->get_status()
        ];

        // Log for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - Found Partners Package by name');
        }
      }
    }
  }

  // Log for debugging
  if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('LCI Dashboard - Found ' . count($registration_types) . ' registration types across all orders');
  }

  // If we found any registration types, use the highest priority one
  if (!empty($registration_types)) {
    // Remove duplicate registration types (same type, different orders)
    $unique_types = [];
    foreach ($registration_types as $reg_type) {
      $type_key = $reg_type['type'];
      if (!isset($unique_types[$type_key]) || $reg_type['priority'] < $unique_types[$type_key]['priority']) {
        $unique_types[$type_key] = $reg_type;
      }
    }
    $registration_types = array_values($unique_types);

    // Sort by priority (lower number = higher priority)
    usort($registration_types, function($a, $b) {
      return $a['priority'] - $b['priority'];
    });

    // Use the highest priority registration type
    $highest_priority = $registration_types[0];
    $registration_type = $highest_priority['type'];
    $registration_icon = $highest_priority['icon'];
    $payment_status = ucfirst($highest_priority['status']);

    // Log for debugging
    if (defined('WP_DEBUG') && WP_DEBUG) {
      error_log('LCI Dashboard - Using highest priority registration type: ' . $registration_type);
      error_log('LCI Dashboard - Found ' . count($registration_types) . ' unique registration types after deduplication');
    }
  } else {
    // No registration types found in any order
    // Check if we have any orders at all
    if (!empty($all_orders)) {
      // We have orders but couldn't determine the registration type
      // Let's check if any of the orders have a status of 'completed' or 'processing'
      $has_valid_order = false;
      foreach ($all_orders as $check_order) {
        $status = $check_order->get_status();
        if (in_array($status, ['completed', 'processing'])) {
          $has_valid_order = true;
          $payment_status = ucfirst($status);
          break;
        }
      }

      if ($has_valid_order) {
        // We have a valid order but couldn't determine the type
        // Let's default to "Full Event Package" as a fallback
        $registration_type = 'Full Event Package';
        $registration_icon = 'fa-certificate';

        // Log for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - No registration types found but has valid order. Using default: ' . $registration_type);
        }
      } else {
        // We have orders but none are valid
        $payment_status = $order ? ucfirst($order->get_status()) : 'Unpaid';

        // Log for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - No valid orders found');
        }
      }
    } else {
      // No orders at all
      $payment_status = 'Unpaid';

      // Log for debugging
      if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Dashboard - No orders found at all');
      }
    }
  }
} else {
  $payment_status = 'Unpaid';

  // Log for debugging
  if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('LCI Dashboard - No orders found, using defaults: Type=' . $registration_type . ', Status=' . $payment_status);
  }
}
$badge_status = get_post_meta($order_id, 'badge_pickup_status', true) ?: 'Pending';
$pub_party_number = 'Pending';
$gala_dinner_table = 'Pending';
$accommodation_items = [];
$tour_items = [];
$no_accommodation_selected = true; // Default to true, will set to false if we find accommodation

// Check ALL orders for accommodation and tour items
if (!empty($all_orders)) {
  // Log for debugging
  if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('LCI Dashboard - Checking all orders for accommodation and tour items');
  }

  // Track all products for fallback detection
  $all_products = [];
  $hotel_keywords = ['hotel', 'palace', 'accommodation', 'stay', 'room', 'night', 'aro', 'kronwell', 'radisson', 'gott', 'armatti', 'pullman'];

  foreach ($all_orders as $current_order) {
    // Log order details
    if (defined('WP_DEBUG') && WP_DEBUG) {
      error_log('LCI Dashboard - Checking order #' . $current_order->get_id() . ' (Status: ' . $current_order->get_status() . ')');
    }

    foreach ($current_order->get_items() as $item) {
      $product = $item->get_product();
      if ($product) {
        $product_id = $item->get_product_id();
        $variation_id = $item->get_variation_id();
        $parent_id = $product->is_type('variation') ? $product->get_parent_id() : null;
        $product_name = $product->get_name();

        // Store all products for fallback detection
        $all_products[] = [
          'id' => $product_id,
          'variation_id' => $variation_id,
          'parent_id' => $parent_id,
          'name' => $product_name
        ];

        // Log product info for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - Found product: ' . $product_name . ' (ID: ' . $product_id . ', Variation: ' . $variation_id . ', Parent: ' . $parent_id . ')');
        }

        // Skip "No Accommodation" products but still mark them as seen
        if (in_array($product_id, [1679, 137])) {
          if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Dashboard - Skipping "No Accommodation" product: ' . $product_name);
          }
          continue;
        }

        // Get both direct categories and parent categories
        $term_ids = [];
        if ($parent_id) {
          $parent_term_ids = wp_get_post_terms($parent_id, 'product_cat', ['fields' => 'ids']);
          $term_ids = array_merge($term_ids, $parent_term_ids);
        }
        $direct_term_ids = wp_get_post_terms($product_id, 'product_cat', ['fields' => 'ids']);
        $term_ids = array_merge($term_ids, $direct_term_ids);
        $term_ids = array_unique($term_ids);

        // Log category IDs for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
          error_log('LCI Dashboard - Product categories: ' . implode(', ', $term_ids));
        }

        // Check for accommodation products by category
        $is_accommodation = false;
        if (!empty($term_ids)) {
          // Check for accommodation products
          if (in_array(18, $term_ids)) {
            $accommodation_items[] = $product_name . ' (3 nights)';
            $no_accommodation_selected = false; // Found accommodation
            $is_accommodation = true;

            // Log for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
              error_log('LCI Dashboard - Found Main Event accommodation by category: ' . $product_name);
            }
          } elseif (in_array(20, $term_ids)) {
            $accommodation_items[] = $product_name . ' (4 nights)';
            $no_accommodation_selected = false; // Found accommodation
            $is_accommodation = true;

            // Log for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
              error_log('LCI Dashboard - Found Councilor accommodation by category: ' . $product_name);
            }
          }

          // Check for tour products
          if (in_array(21, $term_ids)) {
            $tour_items[] = $product_name;

            // Log for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
              error_log('LCI Dashboard - Found tour: ' . $product_name);
            }
          }
        }

        // If not identified by category, try to identify by name
        if (!$is_accommodation) {
          $product_name_lower = strtolower($product_name);
          foreach ($hotel_keywords as $keyword) {
            if (strpos($product_name_lower, strtolower($keyword)) !== false) {
              // This looks like an accommodation product
              $accommodation_items[] = $product_name . ' (nights TBD)';
              $no_accommodation_selected = false; // Found accommodation

              // Log for debugging
              if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('LCI Dashboard - Found accommodation by keyword "' . $keyword . '": ' . $product_name);
              }
              break;
            }
          }
        }
      }
    }
  }

  // Fallback: If no accommodation found but we have products, check if any look like accommodation
  if ($no_accommodation_selected && !empty($all_products)) {
    if (defined('WP_DEBUG') && WP_DEBUG) {
      error_log('LCI Dashboard - No accommodation found by category, trying fallback detection');
    }

    foreach ($all_products as $product) {
      $product_name_lower = strtolower($product['name']);

      // Check if product name contains hotel-related keywords
      foreach ($hotel_keywords as $keyword) {
        if (strpos($product_name_lower, strtolower($keyword)) !== false) {
          // This looks like an accommodation product
          $accommodation_items[] = $product['name'] . ' (nights TBD)';
          $no_accommodation_selected = false; // Found accommodation

          // Log for debugging
          if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Dashboard - Fallback: Found accommodation by keyword "' . $keyword . '": ' . $product['name']);
          }
          break;
        }
      }
    }
  }

  // Remove duplicate items
  $accommodation_items = array_unique($accommodation_items);
  $tour_items = array_unique($tour_items);

  // Log final counts
  if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('LCI Dashboard - Found ' . count($accommodation_items) . ' unique accommodation items and ' . count($tour_items) . ' unique tour items');
    if (empty($accommodation_items)) {
      error_log('LCI Dashboard - WARNING: No accommodation items found for user ' . $user_id . ' despite having ' . count($all_orders) . ' orders');
    }
  }
}
?>

<div class="card shadow p-4 rounded-4 border-0">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-start mb-4">
    <div class="d-flex align-items-center">
      <?php
      $avatar_id = get_user_meta($user_id, 'wp_user_avatar', true);
      $avatar_url = $avatar_id ? wp_get_attachment_url($avatar_id) : get_avatar_url($user_id);
      ?>
      <img src="<?php echo esc_url($avatar_url); ?>" class="rounded-circle me-3" width="64" height="64" alt="User Avatar">
      <div>
        <h4 class="mb-0"><?php echo esc_html($display_name); ?></h4>
        <small class="text-muted">Attendee #<?php echo esc_html($attendee_id); ?></small>
      </div>
    </div>

  </div>

  <!-- Personal Information -->
  <h6 class="mb-3 text-secondary lci-title-dashboard">Personal Information</h6>
  <div class="row row-cols-1 row-cols-md-2 g-3 profile-fields-container">
    <div class="col"><div class="lci-info-line"><i class="fas fa-envelope text-primary me-2"></i><strong class="me-1">Email:</strong><span><?php echo esc_html($email); ?></span></div></div>
    <div class="col"><div class="lci-info-line"><i class="fas fa-phone text-primary me-2"></i><strong class="me-1">Phone:</strong><span><?php echo esc_html($phone ?: '�'); ?></span></div></div>
    <div class="col"><div class="lci-info-line"><i class="fas fa-users text-primary me-2"></i><strong class="me-1">Association:</strong><span><?php echo esc_html($association_clean); ?></span></div></div>
    <div class="col"><div class="lci-info-line"><i class="fas fa-id-badge text-primary me-2"></i><strong class="me-1">Club No / Name:</strong><span><?php echo esc_html($club_no); ?></span></div></div>
    <div class="col"><div class="lci-info-line"><i class="fas fa-user-tie text-primary me-2"></i><strong class="me-1">Position in Club:</strong><span><?php echo esc_html($club_position ?: '�'); ?></span></div></div>
    <?php if ($room_share === 'yes'): ?>
      <div class="col"><div class="lci-info-line"><i class="fas fa-bed text-primary me-2"></i><strong class="me-1">Share Room With:</strong><span><?php echo esc_html($share_with); ?></span></div></div>
    <?php endif; ?>
    <div class="col"><div class="lci-info-line"><i class="fas fa-utensils text-primary me-2"></i><strong class="me-1">Diet:</strong><span><?php echo esc_html($diet); ?></span></div></div>

    <!-- Allergies section moved to a separate full-width div below -->
  </div>

  <!-- Food Allergies Section - 100% width with mobile-first responsive design -->
  <div class="allergies-container" style="width: 100%; margin-top: 15px; margin-bottom: 15px;">
    <div class="allergies-wrapper" style="position: relative;">
      <!-- Ribbon - Hidden on very small screens -->
      <div class="allergies-ribbon d-none d-sm-block" style="position: absolute; top: 0; right: 0; background-color: #ff6b6b; color: white; padding: 5px 10px; border-radius: 0 0 0 10px; font-size: 11px; font-weight: 600; z-index: 2;">
        <i class="fas fa-exclamation-triangle me-1"></i> Important Health Information
      </div>

      <!-- Mobile Ribbon - Only visible on very small screens -->
      <div class="allergies-ribbon-mobile d-sm-none" style="background-color: #ff6b6b; color: white; padding: 5px 10px; border-radius: 5px 5px 0 0; font-size: 11px; font-weight: 600; text-align: center; margin-bottom: -1px;">
        <i class="fas fa-exclamation-triangle me-1"></i> Important Health Information
      </div>

      <!-- Main Content -->
      <div class="allergies-content" style="background: rgba(255, 107, 107, 0.1); border-left: 4px solid #ff6b6b; padding: 15px; border-radius: 10px; margin-top: 20px;">
        <!-- Title - Centered on mobile, left-aligned on larger screens -->
        <h6 class="allergies-title text-center text-sm-start" style="color: #ff6b6b; margin-bottom: 15px; font-weight: 600; font-size: 14px;">
          <i class="fas fa-allergies me-2"></i>Food Allergies or Intolerances
        </h6>

        <?php if (!empty($formatted_allergies)): ?>
        <!-- Allergies Badges - Centered on mobile -->
        <div class="allergies-badges d-flex flex-wrap justify-content-center justify-content-sm-start" style="width: 100%;">
          <?php foreach ($formatted_allergies as $allergy): ?>
            <div class="allergy-badge badge bg-light text-dark me-2 mb-2" style="padding: 6px 10px; border-radius: 20px; font-size: 12px; font-weight: 500; border: 1px solid rgba(0,0,0,0.1); max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: normal; word-wrap: break-word; display: inline-flex; align-items: center;">
              <i class="fas fa-allergies me-1" style="color: #ff6b6b; flex-shrink: 0;"></i>
              <span style="overflow: hidden; text-overflow: ellipsis;"><?php echo esc_html($allergy); ?></span>
            </div>
          <?php endforeach; ?>
        </div>
        <?php else: ?>
        <!-- No Allergies Message -->
        <div class="no-allergies-message alert alert-light text-center text-sm-start" style="background-color: rgba(255, 255, 255, 0.7); border: 1px solid rgba(0, 0, 0, 0.1); border-radius: 10px; padding: 10px; margin-bottom: 0;">
          <i class="fas fa-info-circle text-primary me-2"></i> No allergies or food intolerances found in your registration data.
        </div>
        <?php endif; ?>

        <!-- Footer Note - Smaller on mobile -->
        <p class="allergies-note text-center text-sm-start" style="font-size: 12px; color: #777; margin-top: 15px; margin-bottom: 0;">
          <i class="fas fa-info-circle me-1"></i> If this information is incorrect or needs to be updated, please contact support.
        </p>
      </div>
    </div>
  </div>

  <!-- Mobile-specific styles -->
  <style>
    @media (max-width: 576px) {
      .allergies-container {
        margin-top: 10px;
        margin-bottom: 10px;
      }
      .allergies-content {
        padding: 12px;
        border-radius: 8px;
        margin-top: 0;
      }
      .allergies-title {
        font-size: 13px;
        margin-bottom: 10px;
      }
      .allergy-badge {
        font-size: 11px !important;
        padding: 6px 10px !important;
      }
      .allergies-note {
        font-size: 11px !important;
      }
    }
  </style>

  <div class="text-center">
    <a href="?tab=profile" class="btn btn-ux2025 btn-ux2025-primary w-100 no-text-decoration bottom-rounded-only" style="color: #fff;">
      <i class="fas fa-user-edit small-icon" style="color: #fff;"></i> Edit Profile
    </a>
  </div>
</div>
<div class="card shadow p-4 rounded-4 border-0">
  <!-- Event Details -->
  <h6 class="mt-5 mb-4 text-secondary lci-title-dashboard">Event Details</h6>
  <div class="row row-cols-1 row-cols-md-2 g-3 event-details-container">
    <div class="col"><div class="lci-info-line"><i class="fas <?php echo $registration_icon; ?> text-primary me-2"></i><strong class="me-1">Registration Type:</strong><span><?php echo esc_html($registration_type); ?></span></div></div>
    <div class="col"><div class="lci-info-line"><i class="fas fa-credit-card text-primary me-2"></i><strong class="me-1">Payment Status:</strong><span class="badge bg-<?php echo in_array($payment_status, ['Completed', 'Processing']) ? 'success' : 'secondary'; ?>"><?php echo esc_html($payment_status); ?></span></div></div>
    <div class="col"><div class="lci-info-line"><i class="fas fa-id-card text-primary me-2"></i><strong class="me-1">Badge Pickup:</strong><span class="badge bg-<?php echo $badge_status === 'Picked Up' ? 'success' : 'secondary'; ?>"><?php echo esc_html($badge_status); ?></span></div></div>
      <div class="col">
  <div class="lci-info-line">
    <i class="fas fa-beer text-primary me-2"></i>
    <strong class="me-1">Pub Party Number:</strong>
    <span class="badge bg-<?php echo $pub_party_number === 'Picked Up' ? 'success' : 'secondary'; ?>"><?php echo esc_html($pub_party_number); ?></span>
  </div>
</div>

<div class="col">
  <div class="lci-info-line">
    <i class="fas fa-utensils text-primary me-2"></i>
    <strong class="me-1">Gala Dinner Table No:</strong>
   <span class="badge bg-<?php echo $gala_dinner_table === 'Picked Up' ? 'success' : 'secondary'; ?>"><?php echo esc_html($gala_dinner_table); ?></span>
  </div>
</div>

  </div>

</div>
<!-- Accommodation Section with UX2025 Design -->
<div class="registration-card mt-4">
    <div class="registration-card-header">
        <h5><i class="fas fa-bed"></i> Your Accommodation</h5>
    </div>
    <div class="registration-card-body">
        <div class="accommodation-container">
            <?php if (isset($no_accommodation_selected) && $no_accommodation_selected): ?>
                <div class="accommodation-empty">
                    <i class="fas fa-bed"></i>
                    <p>You haven't selected any accommodation yet.</p>
                </div>
            <?php endif; ?>

            <?php if (!empty($accommodation_items)): ?>
                <?php foreach ($accommodation_items as $index => $accommodation): ?>
                    <?php
                        // Extract hotel name and nights
                        $parts = explode(' (', $accommodation);
                        $hotel_name = $parts[0];
                        $nights = isset($parts[1]) ? str_replace(')', '', $parts[1]) : '3 nights';

                        // Check for special accommodation types
                        $item_product_id = null;
                        $is_self_arranged = false;
                        $is_pullman = false;

                        // Check if this is "I will arrange my accommodation" option
                        if (strpos($hotel_name, 'I will arrange my') !== false ||
                            strpos($hotel_name, 'arrange my own') !== false ||
                            strpos($hotel_name, 'No Hotel') !== false) {

                            $is_self_arranged = true;

                            // Log for debugging
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log('LCI Dashboard - Identified self-arranged accommodation: ' . $hotel_name);
                            }

                            // For self-arranged accommodation, set the nights to empty
                            $nights = '';
                        }
                        // Check if this is the Pullman Bucharest hotel
                        else if (strpos($hotel_name, 'Pullman') !== false ||
                                 strpos(strtolower($hotel_name), 'pullman') !== false) {
                            $item_product_id = 2151; // Pullman Bucharest product ID
                            $is_pullman = true;

                            // Log for debugging
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log('LCI Dashboard - Identified Pullman Bucharest with product ID 2151');
                            }

                            // For Pullman Bucharest, set the nights to 2 nights
                            $nights = '2 nights';

                            // FORCE the correct dates for Pullman Bucharest
                            $check_in = 'Aug 16, 2025';
                            $check_out = 'Aug 18, 2025';
                            $real_dates_found = true; // Mark as found so we don't try to look up dates

                            // Log for debugging
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log('LCI Dashboard - FORCED Pullman dates: ' . $check_in . ' to ' . $check_out);
                            }
                        }

                        // Try to get check-in and check-out dates from product meta
                        $product_checkin = '';
                        $product_checkout = '';

                        if ($item_product_id) {
                            $product_checkin = get_post_meta($item_product_id, '_checkin_date', true);
                            $product_checkout = get_post_meta($item_product_id, '_checkout_date', true);

                            // Log for debugging
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log('LCI Dashboard - Product meta for ID ' . $item_product_id . ': checkin=' . $product_checkin . ', checkout=' . $product_checkout);
                            }
                        }

                        // Try to get real check-in/check-out dates from the accommodation table
                        $real_dates_found = false;
                        $check_in = '';
                        $check_out = '';

                        // Check if we can access the accommodation table
                        global $wpdb;
                        $accommodation_table = $wpdb->prefix . 'lci2025_accommodation_unified';
                        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$accommodation_table'") === $accommodation_table;

                        if ($table_exists) {
                            // Try to find this hotel in the accommodation table for this user
                            $accommodation_data = $wpdb->get_row($wpdb->prepare(
                                "SELECT checkin_date, checkout_date FROM $accommodation_table
                                WHERE user_id = %d AND hotel_name = %s
                                ORDER BY id DESC LIMIT 1",
                                $user_id, $hotel_name
                            ));

                            if ($accommodation_data && !empty($accommodation_data->checkin_date) && !empty($accommodation_data->checkout_date)) {
                                $check_in = date('M d, Y', strtotime($accommodation_data->checkin_date));
                                $check_out = date('M d, Y', strtotime($accommodation_data->checkout_date));
                                $real_dates_found = true;

                                // Log for debugging
                                if (defined('WP_DEBUG') && WP_DEBUG) {
                                    error_log('LCI Dashboard - Found real dates for ' . $hotel_name . ': ' . $check_in . ' to ' . $check_out);
                                }
                            }
                        }

                        // If we couldn't find real dates, try product meta or use the actual event dates
                        // Skip date determination for self-arranged accommodation and Pullman (which has forced dates)
                        if (!$real_dates_found && !$is_self_arranged && !$is_pullman) {
                            // First check if we have dates from product meta
                            if (!empty($product_checkin) && !empty($product_checkout)) {
                                // Format the dates from product meta
                                $check_in = date('M d, Y', strtotime($product_checkin));
                                $check_out = date('M d, Y', strtotime($product_checkout));

                                // Log for debugging
                                if (defined('WP_DEBUG') && WP_DEBUG) {
                                    error_log('LCI Dashboard - Using product meta dates for ' . $hotel_name . ': ' . $check_in . ' to ' . $check_out);
                                }
                            }
                            // Check if this is the Pullman Bucharest hotel (product ID 2151)
                            else if (strpos($hotel_name, 'Pullman Bucharest') !== false ||
                                (isset($product_id) && $product_id == 2151) ||
                                (isset($item_product_id) && $item_product_id == 2151)) {
                                // Hardcoded special dates for Pullman Bucharest
                                $check_in = 'Aug 16, 2025';
                                $check_out = 'Aug 18, 2025';

                                // Log for debugging
                                if (defined('WP_DEBUG') && WP_DEBUG) {
                                    error_log('LCI Dashboard - Using hardcoded Pullman dates: ' . $check_in . ' to ' . $check_out);
                                }

                                // Log for debugging
                                if (defined('WP_DEBUG') && WP_DEBUG) {
                                    error_log('LCI Dashboard - Using special dates for Pullman Bucharest: ' . $check_in . ' to ' . $check_out);
                                }
                            }
                            // Use the actual event dates for 2025 for other hotels
                            else if (strpos($nights, '3 nights') !== false) {
                                // Main Event dates (3 nights)
                                $check_in = 'Aug 21, 2025';
                                $check_out = 'Aug 24, 2025';
                            } elseif (strpos($nights, '4 nights') !== false) {
                                // Councilor dates (4 nights)
                                $check_in = 'Aug 20, 2025';
                                $check_out = 'Aug 24, 2025';
                            } else {
                                // Default dates for other packages
                                $check_in = 'Aug 21, 2025';
                                $check_out = 'Aug 24, 2025';
                            }

                            // Log for debugging
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log('LCI Dashboard - Final dates for ' . $hotel_name . ': ' . $check_in . ' to ' . $check_out);
                            }
                        }
                    ?>
                    <div class="accommodation-item">
                        <div class="accommodation-item-icon">
                            <i class="fas fa-hotel"></i>
                        </div>
                        <div class="accommodation-item-details">
                            <div class="accommodation-item-name"><?php echo esc_html($hotel_name); ?></div>
                            <div class="accommodation-item-info">
                                <?php if (!$is_self_arranged): ?>
                                    <i class="fas fa-calendar-alt"></i> <?php echo esc_html($check_in); ?> - <?php echo esc_html($check_out); ?>
                                <?php endif; ?>
                                <?php if (!empty($nights)): ?>
                                    <span class="accommodation-item-badge"><?php echo esc_html($nights); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="accommodation-item-actions">
                            <a href="?tab=accommodation" class="btn btn-ux2025 btn-ux2025-outline btn-ux2025-sm">
                                <i class="fas fa-edit"></i> Modify
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>

                <div class="text-center mt-4">
                    <a href="?tab=accommodation" class="btn btn-ux2025 btn-ux2025-primary w-100" style="color: #fff;">
                        <i class="fas fa-plus-circle" style="color: #fff;"></i> Add Extra Nights
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php if (isset($no_accommodation_selected) && $no_accommodation_selected): ?>
    <div class="text-center">
        <a href="?tab=accommodation" class="btn btn-ux2025 btn-ux2025-primary w-100 no-text-decoration bottom-rounded-only" style="color: #fff;">
            <i class="fas fa-plus-circle small-icon" style="color: #fff;"></i> Add Your Accommodation
        </a>
    </div>
    <?php endif; ?>
</div>
<!-- Tours Section with UX2025 Design - Matching tours-experience-content design -->
<div class="registration-card">
    <div class="registration-card-header">
        <h5><i class="fas fa-route"></i> Your Tour Experiences</h5>
    </div>
    <div class="registration-card-body">
        <div class="tour-container">
            <?php if (!empty($tour_items)): ?>
                <?php
                // Default image for tours without images
                $default_tour_image = 'https://images.unsplash.com/photo-1516483638261-f4dbaf036963?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60';

                // Default order status if not available
                $default_order_status = 'completed';
                ?>

                <div class="tours-experiences-list">
                    <?php foreach ($tour_items as $index => $tour): ?>
                        <?php
                        // Get product ID from the tour name
                        $product_id = null;
                        $product = null;
                        $order_status = $default_order_status;

                        // Try to find the product by name
                        $args = array(
                            'post_type'      => 'product',
                            'posts_per_page' => 1,
                            'post_status'    => 'publish',
                            'title'          => $tour
                        );

                        $products = get_posts($args);
                        if (!empty($products)) {
                            $product_id = $products[0]->ID;
                            $product = wc_get_product($product_id);
                        }

                        // If we found the product, get its metadata
                        if ($product) {
                            // Get product image
                            $image = wp_get_attachment_url($product->get_image_id());
                            if (empty($image)) {
                                $image = $default_tour_image;
                            }

                            // Get tour type and dates from meta data
                            $tour_type = get_post_meta($product_id, '_tour_type', true);
                            $starting_day = get_post_meta($product_id, '_starting_day', true);
                            $ending_day = get_post_meta($product_id, '_ending_day', true);
                            $product_description = $product->get_description();
                            $short_description = $product->get_short_description();

                            // Set default values if empty
                            $tour_type = empty($tour_type) ? 'tour' : $tour_type;
                        } else {
                            // Fallback if product not found
                            $image = $default_tour_image;
                            $tour_type = 'tour';
                            $starting_day = '';
                            $ending_day = '';
                            $product_description = '';
                            $short_description = '';
                        }

                        // Set ribbon text and class based on tour type
                        $ribbon_text = ($tour_type == 'pretour') ? 'Main Pretour' : 'Tour';
                        $ribbon_class = ($tour_type == 'pretour') ? 'pretour' : '';

                        // Format dates nicely - exactly matching the tours page format
                        $formatted_start_date = !empty($starting_day) ? date('jS \of F Y', strtotime($starting_day)) : '';
                        $formatted_end_date = !empty($ending_day) ? date('jS \of F Y', strtotime($ending_day)) : '';
                        ?>
                        <div class="tours-experience-card" style="--card-index: <?php echo $index; ?>">
                            <!-- Tour Type Ribbon -->
                            <div class="tour-type-ribbon <?php echo esc_attr($ribbon_class); ?>">
                                <i class="fas <?php echo ($tour_type == 'pretour') ? 'fa-flag' : 'fa-map-marker-alt'; ?> me-1"></i>
                                <?php echo esc_html($ribbon_text); ?>
                            </div>

                            <!-- Main Content -->
                            <div class="tours-experience-content">
                                <div class="tours-experience-image">
                                    <?php if (!empty($image)): ?>
                                    <img src="<?php echo esc_url($image); ?>" alt="<?php echo esc_attr($tour); ?>">
                                    <?php else: ?>
                                    <div class="tours-experience-no-image">
                                        <i class="fas fa-mountain"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="tours-experience-details">
                                    <div class="tours-experience-header">
                                        <h4 class="tours-experience-title"><?php echo esc_html($tour); ?></h4>
                                    </div>

                                    <div class="tour-short-description">
                                        <?php if (!empty($product_description)): ?>
                                            <?php echo wp_kses_post($product_description); ?>
                                        <?php elseif (!empty($short_description)): ?>
                                            <?php echo wp_kses_post($short_description); ?>
                                        <?php else: ?>
                                            <p>Experience the beauty and culture of Brașov and its surroundings with this unforgettable tour. Perfect for all ages and interests.</p>
                                        <?php endif; ?>
                                    </div>

                                    <div class="tour-info-row">
                                        <div class="tour-dates">
                                            <i class="far fa-calendar-alt"></i>
                                            <?php if ($tour_type == 'pretour' && !empty($ending_day)): ?>
                                                <span>From <?php echo esc_html($formatted_start_date); ?> to <?php echo esc_html($formatted_end_date); ?></span>
                                            <?php else: ?>
                                                <span>Date: <?php echo esc_html($formatted_start_date); ?></span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="tours-experience-status tours-experience-status-<?php echo esc_attr($order_status); ?>">
                                            <i class="fas <?php echo ($order_status === 'completed') ? 'fa-money-bill-wave' : 'fa-check-circle'; ?> me-1"></i>
                                            <?php echo ($order_status === 'completed') ? 'Paid' : ucfirst(esc_html($order_status)); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="text-center mt-4">
                    <a href="?tab=tours" class="btn btn-ux2025 btn-ux2025-primary w-100" style="color: #fff;">
                        <i class="fas fa-plus-circle" style="color: #fff;"></i> Explore More Tours
                    </a>
                </div>
            <?php else: ?>
                <div class="tour-empty">
                    <i class="fas fa-route"></i>
                    <p>You haven't booked any tours yet.</p>
                    <p class="text-muted">Explore the beautiful surroundings of Brașov with our curated tour experiences.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php if (empty($tour_items)): ?>
    <div class="text-center">
        <a href="?tab=tours" class="btn btn-ux2025 btn-ux2025-primary w-100 no-text-decoration bottom-rounded-only" style="color: #fff;">
            <i class="fas fa-compass small-icon" style="color: #fff;"></i> Discover Tours
        </a>
    </div>
    <?php endif; ?>
</div>


