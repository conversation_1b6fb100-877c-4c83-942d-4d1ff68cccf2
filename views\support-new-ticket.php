<?php
/**
 * Support New Ticket view for LCI 2025 Dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_email = $current_user->user_email;

// Define ticket categories
$ticket_categories = [
    'registration' => 'Registration Issues',
    'payment' => 'Payment & Invoices',
    'accommodation' => 'Accommodation',
    'tours' => 'Tours & Activities',
    'visa' => 'Visa & Travel',
    'other' => 'Other Questions'
];
?>

<script>
// Immediately hide all elements with x-show attribute
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('[x-show]').forEach(function(el) {
        el.style.display = 'none';
    });
});
</script>

<div class="support-container" x-data="supportTickets">
    <!-- Header Section - 2025 UX/UI Style -->
    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 40px 20px; position: relative; overflow: hidden; border-radius: 0 0 30px 30px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2); margin-bottom: 40px;">
        <!-- Animated background elements -->
        <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%); border-radius: 50%; animation: pulse 8s infinite alternate;"></div>
        <div class="position-absolute" style="bottom: -30px; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%); border-radius: 50%; animation: float 12s infinite alternate;"></div>
        <div class="position-absolute" style="top: 20%; right: 20%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%); border-radius: 50%; animation: float 7s infinite alternate-reverse;"></div>

        <!-- Glassmorphism overlay -->
        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>

        <div class="container position-relative" style="z-index: 2;">
            <div class="d-flex align-items-center justify-content-center flex-column text-center">
                <!-- Modern 3D icon with shadow and glow -->
                <div style="background: rgba(255, 255, 255, 0.2); width: 80px; height: 80px; border-radius: 24px; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); transform: perspective(500px) rotateX(10deg); border: 1px solid rgba(255, 255, 255, 0.3);">
                    <div style="position: relative;">
                        <i class="fas fa-plus-circle" style="color: white; font-size: 36px; filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));"></i>
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.8), transparent 70%); filter: blur(8px); opacity: 0.5; animation: glow 3s infinite alternate;"></div>
                    </div>
                </div>

                <!-- Typography with modern styling -->
                <h1 style="color: white; font-size: 32px; font-weight: 800; margin: 0; letter-spacing: -0.5px; text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">Create New Support Ticket</h1>
                <p style="color: rgba(255, 255, 255, 0.9); font-size: 18px; margin: 0; margin-top: 8px; max-width: 600px; font-weight: 300; letter-spacing: 0.5px;">Submit a new support request for any questions or issues</p>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div x-show="formSuccess" x-transition class="container mb-4">
        <div style="background: rgba(76, 175, 80, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px; border: 1px solid rgba(76, 175, 80, 0.2); display: flex; align-items: center;">
            <div style="background: rgba(76, 175, 80, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                <i class="fas fa-check-circle" style="color: #4caf50; font-size: 20px;"></i>
            </div>
            <div style="flex: 1;">
                <p style="color: #2c3e50; margin: 0;" x-text="successMessage"></p>
            </div>
            <button @click="formSuccess = false" style="background: none; border: none; color: #4caf50; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div x-show="formError" x-transition class="container mb-4">
        <div style="background: rgba(244, 67, 54, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px; border: 1px solid rgba(244, 67, 54, 0.2); display: flex; align-items: center;">
            <div style="background: rgba(244, 67, 54, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                <i class="fas fa-exclamation-circle" style="color: #f44336; font-size: 20px;"></i>
            </div>
            <div style="flex: 1;">
                <p style="color: #2c3e50; margin: 0;" x-text="errorMessage"></p>
            </div>
            <button @click="formError = false" style="background: none; border: none; color: #f44336; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <!-- Back to tickets button -->
                <div class="mb-4">
                    <a href="<?php echo add_query_arg('tab', 'support'); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i> Back to My Tickets
                    </a>
                </div>

                <!-- Create New Ticket - 2025 UX/UI Style -->
                <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden;">
                    <!-- Decorative elements -->
                    <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                    <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                    <!-- Section header with 3D effect -->
                    <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
                        <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                            <i class="fas fa-plus-circle" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                        </div>
                        <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Create New Support Ticket</h3>
                    </div>

                    <form id="support-ticket-form" class="position-relative" style="z-index: 1;" method="post">
                        <input type="hidden" name="nonce" value="<?php echo wp_create_nonce('lci_support_nonce'); ?>" />

                        <!-- Subject field with floating label -->
                        <div class="position-relative mb-4">
                            <label for="ticket_subject" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Subject*</label>
                            <input type="text" id="ticket_subject" name="ticket_subject" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                        </div>

                        <!-- Category field with floating label -->
                        <div class="position-relative mb-4">
                            <label for="ticket_category" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Category*</label>
                            <select id="ticket_category" name="ticket_category" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 15l-4.243-4.243 1.415-1.414L12 12.172l2.828-2.829 1.415 1.414z" fill="rgba(54,177,220,1)"/></svg>'); background-repeat: no-repeat; background-position: right 16px center;">
                                <option value="" selected disabled>Select a category</option>
                                <?php foreach ($ticket_categories as $value => $label) : ?>
                                    <option value="<?php echo esc_attr($value); ?>"><?php echo esc_html($label); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Message field with floating label -->
                        <div class="position-relative mb-4">
                            <label for="ticket_message" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Message*</label>
                            <textarea id="ticket_message" name="ticket_message" rows="5" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none; resize: vertical;"></textarea>
                            <div style="margin-top: 8px; font-size: 14px; color: #64748b;">Please provide as much detail as possible to help us assist you better.</div>
                        </div>

                        <!-- Email field with floating label -->
                        <div class="position-relative mb-4">
                            <label for="ticket_email" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Email for Responses*</label>
                            <input type="email" id="ticket_email" name="ticket_email" value="<?php echo esc_attr($user_email); ?>" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                            <div style="margin-top: 8px; font-size: 14px; color: #64748b;">We'll send updates about your ticket to this email address.</div>
                        </div>

                        <!-- Attachment field with floating label -->
                        <div class="position-relative mb-5">
                            <label for="ticket_attachment" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Attachment (optional)</label>
                            <input type="file" id="ticket_attachment" name="ticket_attachment" style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease;">
                            <div style="margin-top: 8px; font-size: 14px; color: #64748b;">You can attach a file to provide more information (max 5MB).</div>
                        </div>

                        <!-- Submit Button - 3D effect -->
                        <div class="text-center">
                            <button type="button" @click="submitNewTicket" :disabled="isSubmitting" class="position-relative" style="background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; font-weight: 700; text-transform: uppercase; letter-spacing: 1px; padding: 16px 32px; border-radius: 16px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.3), inset 0 2px 0 rgba(255, 255, 255, 0.3); border: none; font-size: 16px; cursor: pointer; transform-style: preserve-3d; perspective: 1000px; transition: all 0.3s ease; overflow: hidden; min-width: 250px;">
                                <!-- Button shadow/base -->
                                <div class="position-absolute" style="top: 5px; left: 0; right: 0; height: 100%; background: linear-gradient(to bottom, #2980b9, #2573a7); border-radius: 16px; transform: translateZ(-5px); filter: blur(2px); opacity: 0.7; z-index: -1;"></div>

                                <!-- Button content -->
                                <div class="d-flex align-items-center justify-content-center">
                                    <template x-if="isSubmitting">
                                        <div class="spinner-border spinner-border-sm text-white me-2" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </template>
                                    <template x-if="!isSubmitting">
                                        <i class="fas fa-paper-plane me-2"></i>
                                    </template>
                                    <span x-text="isSubmitting ? 'SUBMITTING...' : 'SUBMIT TICKET'"></span>
                                </div>

                                <!-- Button hover effect -->
                                <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1)); opacity: 0; transition: opacity 0.3s ease; z-index: 1;" onmouseover="this.style.opacity='1';" onmouseout="this.style.opacity='0';"></div>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add animations -->
<style>
    @keyframes pulse {
        0% { transform: scale(1); opacity: 0.5; }
        100% { transform: scale(1.2); opacity: 0.2; }
    }
    @keyframes float {
        0% { transform: translateY(0) translateX(0); }
        50% { transform: translateY(-20px) translateX(10px); }
        100% { transform: translateY(10px) translateX(-10px); }
    }
    @keyframes glow {
        0% { opacity: 0.3; }
        100% { opacity: 0.7; }
    }
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    [x-cloak] {
        display: none !important;
    }
</style>

<script>
// Define the Alpine.js component
document.addEventListener('alpine:init', function() {
    // Make sure lciSupport is available
    if (typeof window.lciSupport === 'undefined') {
        console.error('lciSupport is not defined');
        window.lciSupport = {
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('lci_support_nonce'); ?>',
            messages: {
                ticketSubmitted: '<?php echo esc_js(__('Your ticket has been submitted successfully.', 'lci-2025-dashboard')); ?>',
                ticketError: '<?php echo esc_js(__('There was an error submitting your ticket. Please try again.', 'lci-2025-dashboard')); ?>',
                replySubmitted: '<?php echo esc_js(__('Your reply has been submitted successfully.', 'lci-2025-dashboard')); ?>',
                replyError: '<?php echo esc_js(__('There was an error submitting your reply. Please try again.', 'lci-2025-dashboard')); ?>'
            }
        };
    }

    Alpine.data('supportTickets', function() {
        return {
            isSubmitting: false,
            formSuccess: false,
            formError: false,
            errorMessage: '',
            successMessage: '',

            submitNewTicket() {
                const form = document.getElementById('support-ticket-form');
                if (!form) return;

                // Form validation
                const subject = document.getElementById('ticket_subject').value;
                const category = document.getElementById('ticket_category').value;
                const message = document.getElementById('ticket_message').value;

                if (!subject || !category || !message) {
                    this.showError('Please fill in all required fields.');
                    return;
                }

                // File size validation
                const fileInput = document.getElementById('ticket_attachment');
                if (fileInput && fileInput.files.length > 0) {
                    const fileSize = fileInput.files[0].size; // in bytes
                    const maxSize = 5 * 1024 * 1024; // 5MB

                    if (fileSize > maxSize) {
                        this.showError('File size exceeds the maximum limit of 5MB.');
                        return;
                    }
                }

                this.isSubmitting = true;

                const formData = new FormData(form);
                formData.append('action', 'lci_submit_ticket');
                formData.append('nonce', window.lciSupport.nonce);

                fetch(window.lciSupport.ajaxUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.showSuccess(window.lciSupport.messages.ticketSubmitted || 'Ticket submitted successfully');
                        form.reset();

                        // Redirect to support page after 2 seconds
                        setTimeout(() => {
                            window.location.href = '<?php echo add_query_arg('tab', 'support'); ?>';
                        }, 2000);
                    } else {
                        this.showError(data.data.message || window.lciSupport.messages.ticketError || 'Error submitting ticket');
                    }
                    this.isSubmitting = false;
                })
                .catch(error => {
                    this.showError(window.lciSupport.messages.ticketError || 'Error submitting ticket. Please try again.');
                    this.isSubmitting = false;
                });
            },

            showSuccess(message) {
                this.successMessage = message;
                this.formSuccess = true;
                this.formError = false;

                setTimeout(() => {
                    this.formSuccess = false;
                }, 5000);
            },

            showError(message) {
                this.errorMessage = message;
                this.formError = true;
                this.formSuccess = false;

                setTimeout(() => {
                    this.formError = false;
                }, 5000);
            }
        };
    });
});
</script>
