document.addEventListener("DOMContentLoaded", () => {
  const loginForm = document.getElementById("lci-login-form");
  const forgotForm = document.getElementById("lci-forgot-form");
  const magicForm = document.getElementById("lci-magic-form");
  const changeForm = document.getElementById("lci-change-password-form");

  // Set default form visibility
  const setDefaultFormVisibility = function() {
    const urlParams = new URLSearchParams(window.location.search);
    const key = urlParams.get('key');
    const login = urlParams.get('login');
    const action = urlParams.get('action');
    const showResetForm = urlParams.get('show-reset-form');

    // Check if this is a password reset URL
    const isResetUrl = key && login && (action === 'reset_password' || showResetForm === 'true');

    // If not a reset URL, make sure login form is visible
    if (!isResetUrl) {
      console.log('Setting default form visibility: showing login form');

      if (loginForm) {
        loginForm.classList.remove('d-none');
        loginForm.style.display = 'block';
        loginForm.style.opacity = '1';
      }

      if (changeForm) {
        changeForm.classList.add('d-none');
        changeForm.style.display = 'none';
      }
    }
  };

  // Run immediately and after a short delay
  setDefaultFormVisibility();
  setTimeout(setDefaultFormVisibility, 100);

  const loginNonce = document.getElementById("lci_nonce")?.value;
  const forgotNonce = document.getElementById("lci_forgot_nonce")?.value;
  const magicNonce = document.getElementById("lci_magic_nonce")?.value;

  // Debug nonce values
  console.log('Auth nonce values:', {
    loginNonce,
    'lci_ajax.nonce': lci_ajax?.nonce || 'not defined',
    'form field nonce': document.getElementById("lci_nonce")?.value || 'not found'
  });

  // Password toggle functionality
  document.querySelectorAll('.password-toggle').forEach(toggle => {
    toggle.addEventListener('click', function() {
      const input = this.previousElementSibling;
      const icon = this.querySelector('i');

      // Toggle password visibility
      if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }

      // Add ripple effect
      const ripple = document.createElement('span');
      ripple.classList.add('password-toggle-ripple');
      this.appendChild(ripple);

      // Remove ripple after animation
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });

  // Form switching functionality
  const showLoginForm = () => {
    loginForm.classList.remove('d-none');
    forgotForm.classList.add('d-none');
    magicForm.classList.add('d-none');
    changeForm.classList.add('d-none');

    // Add animation classes
    setTimeout(() => {
      loginForm.style.opacity = '1';
      loginForm.style.transform = 'translateY(0)';
    }, 10);
  };

  const showForgotForm = () => {
    loginForm.classList.add('d-none');
    forgotForm.classList.remove('d-none');
    magicForm.classList.add('d-none');
    changeForm.classList.add('d-none');

    // Add animation classes
    setTimeout(() => {
      forgotForm.style.opacity = '1';
      forgotForm.style.transform = 'translateY(0)';
    }, 10);
  };

  const showMagicForm = () => {
    loginForm.classList.add('d-none');
    forgotForm.classList.add('d-none');
    magicForm.classList.remove('d-none');
    changeForm.classList.add('d-none');

    // Add animation classes
    setTimeout(() => {
      magicForm.style.opacity = '1';
      magicForm.style.transform = 'translateY(0)';
    }, 10);
  };

  const showChangeForm = () => {
    console.log('Showing change password form');
    loginForm.classList.add('d-none');
    forgotForm.classList.add('d-none');
    magicForm.classList.add('d-none');
    changeForm.classList.remove('d-none');

    // Force display properties to ensure visibility
    changeForm.style.display = 'block';

    // Add animation classes
    setTimeout(() => {
      changeForm.style.opacity = '1';
      changeForm.style.transform = 'translateY(0)';
      console.log('Change form opacity set to 1');
    }, 10);
  };

  // Add event listeners for form switching
  document.getElementById('lci-show-forgot')?.addEventListener('click', (e) => {
    e.preventDefault();
    showForgotForm();
  });

  document.getElementById('lci-show-magic')?.addEventListener('click', (e) => {
    e.preventDefault();
    showMagicForm();
  });

  document.getElementById('lci-show-login')?.addEventListener('click', (e) => {
    e.preventDefault();
    showLoginForm();
  });

  document.getElementById('lci-show-login-2')?.addEventListener('click', (e) => {
    e.preventDefault();
    showLoginForm();
  });

  document.getElementById('lci-show-login-3')?.addEventListener('click', (e) => {
    e.preventDefault();
    showLoginForm();
  });

  // Check URL parameters for password reset or magic link errors
  const urlParams = new URLSearchParams(window.location.search);
  const key = urlParams.get('key');
  const login = urlParams.get('login');
  const action = urlParams.get('action');
  const showResetForm = urlParams.get('show-reset-form');
  const loginError = urlParams.get('login_error');
  const loginSuccess = urlParams.get('login') === 'success';

  console.log('Auth.js - URL params:', {
    key,
    login,
    action,
    showResetForm,
    fullUrl: window.location.href,
    search: window.location.search
  });

  // Debug function to check form state
  const debugFormState = () => {
    const keyField = document.getElementById("lci-reset-key");
    const loginField = document.getElementById("lci-reset-login");
    const emailField = document.getElementById("lci-change-email");

    console.log("=== FORM STATE DEBUG ===");
    console.log("Key field exists:", !!keyField);
    console.log("Key value:", keyField ? keyField.value : "N/A");
    console.log("Login field exists:", !!loginField);
    console.log("Login value:", loginField ? loginField.value : "N/A");
    console.log("Email field exists:", !!emailField);
    console.log("Email value:", emailField ? emailField.value : "N/A");
    console.log("URL params:", window.location.search);

    // Try to parse the key from URL directly
    const urlParams = new URLSearchParams(window.location.search);
    console.log("Key from URL:", urlParams.get("key"));
    console.log("Login from URL:", urlParams.get("login"));
    console.log("=== END DEBUG ===");
  };

  // SIMPLIFIED RESET URL DETECTION
  // Check if this is a password reset URL - we need both key and login parameters
  const hasKeyParam = !!key;
  const hasLoginParam = !!login;
  const hasResetAction = action === 'reset_password';

  // For the lost-password page, we have a special case
  const isLostPasswordPage = window.location.href.includes('lost-password');

  // A URL is a reset URL if:
  // 1. It has key, login, and reset_password action, OR
  // 2. It's the lost-password page with show-reset-form=true
  const isResetUrl = (hasKeyParam && hasLoginParam && hasResetAction) ||
                    (isLostPasswordPage && showResetForm === 'true');

  console.log('URL check:', {
    isLostPasswordPage,
    isResetUrl,
    hasKeyParam,
    hasLoginParam,
    hasResetAction,
    key,
    login,
    action,
    showResetForm
  });

  // Set a flag to indicate if this is a reset URL
  window.isPasswordResetUrl = isResetUrl;

  // Function to show messages directly in the form
  const showFormMessage = (container, message, type) => {
    if (container) {
      container.className = "alert alert-" + type + " text-center mt-3";
      container.innerHTML = message;
      container.classList.remove("d-none");
    }
  };

  // Check localStorage for saved reset parameters
  const savedKey = localStorage.getItem('lci_reset_key');
  const savedLogin = localStorage.getItem('lci_reset_login');

  // If we have saved parameters, use them
  if (savedKey && savedLogin) {
    console.log('Found saved reset parameters in localStorage');
    key = savedKey;
    login = savedLogin;
    isResetUrl = true;
  }

  if (isResetUrl) {
    console.log('Password reset URL detected, showing change password form');

    // Show the change password form
    if (changeForm && loginForm) {
      // Hide all other forms
      loginForm.classList.add('d-none');
      if (forgotForm) forgotForm.classList.add('d-none');
      if (magicForm) magicForm.classList.add('d-none');

      // Show change password form
      changeForm.classList.remove('d-none');
      changeForm.style.display = 'block';
      changeForm.style.opacity = '1';
      changeForm.style.transform = 'translateY(0)';

      // SIMPLIFIED FIELD HANDLING
      // Get the login and key from URL parameters (most reliable source)
      const urlParams = new URLSearchParams(window.location.search);
      const keyFromUrl = urlParams.get('key');
      const loginFromUrl = urlParams.get('login');

      console.log('URL parameters:', {
        keyFromUrl,
        loginFromUrl
      });

      // Set the email field
      const emailField = document.getElementById('lci-change-email');
      if (emailField && loginFromUrl) {
        emailField.value = loginFromUrl;
        console.log('Email field set to:', loginFromUrl);
      }

      // Create or update the login field
      let loginField = document.getElementById('lci-reset-login');
      if (!loginField) {
        loginField = document.createElement('input');
        loginField.type = 'hidden';
        loginField.id = 'lci-reset-login';
        loginField.name = 'login';
        changeForm.appendChild(loginField);
      }

      // Set the login value from URL
      if (loginFromUrl) {
        loginField.value = loginFromUrl;
        console.log('Login field set to:', loginFromUrl);
      }

      // Create or update the key field
      let keyField = document.getElementById('lci-reset-key');
      if (keyField) {
        // Remove existing field to avoid duplicates
        keyField.remove();
      }

      // Create a new key field
      keyField = document.createElement('input');
      keyField.type = 'hidden';
      keyField.id = 'lci-reset-key';
      keyField.name = 'key';
      changeForm.appendChild(keyField);

      // Set the key value from URL
      if (keyFromUrl) {
        keyField.value = keyFromUrl;
        console.log('Key field set to:', keyFromUrl);
      } else if (isLostPasswordPage && loginFromUrl) {
        // On lost-password page with login but no key, generate a key
        console.log('On lost-password page with login but no key - generating key');

        // Make an AJAX request to get a key
        const keyXhr = new XMLHttpRequest();
        keyXhr.open('POST', lci_ajax.ajax_url, true);
        keyXhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        keyXhr.onload = function() {
          if (keyXhr.status >= 200 && keyXhr.status < 300) {
            try {
              const response = JSON.parse(keyXhr.responseText);
              if (response.success && response.data && response.data.key) {
                keyField.value = response.data.key;
                console.log('Generated key via AJAX:', response.data.key);
              }
            } catch (e) {
              console.error('Error parsing key response:', e);
            }
          }
        };

        keyXhr.send(new URLSearchParams({
          action: 'lci_generate_reset_key',
          login: loginFromUrl
        }).toString());
      }

      // Call debug function to check state after setting fields
      debugFormState();
    }
  } else {
    console.log('Not a password reset URL, showing login form');
    // Make sure login form is visible by default
    if (loginForm) {
      loginForm.classList.remove('d-none');
    }
  }

  // Add a message to inform the user when in password reset mode
  if (isResetUrl) {
    // Check if the message already exists
    if (!document.querySelector('#lci-change-password-form .alert-info')) {
      const messageEl = document.createElement('div');
      messageEl.className = 'alert alert-info text-center mb-4';
      messageEl.innerHTML = 'Please create a new password for your account.';

      const heading = document.querySelector('#lci-change-password-form h2');
      if (heading && heading.parentNode) {
        heading.parentNode.insertBefore(messageEl, heading.nextSibling);
      }
    }
  }

  // Handle magic link errors
  if (loginError) {
    let errorMessage = '';

    switch (loginError) {
      case 'invalid_user':
        errorMessage = 'Invalid user. Please try again.';
        break;
      case 'invalid_token':
        errorMessage = 'Invalid or expired login link. Please request a new one.';
        break;
      case 'expired_token':
        errorMessage = 'Your login link has expired. Please request a new one.';
        break;
      default:
        errorMessage = 'An error occurred. Please try again.';
    }

    showMessage('Login Error', errorMessage, 'error');
  }

  // Handle successful magic link login
  if (loginSuccess) {
    showMessage('Login Successful', 'You have been logged in successfully.', 'success');
  }

  // Handle forgot password form submission
  forgotForm?.addEventListener('submit', (e) => {
    e.preventDefault();

    const email = document.getElementById('lci-forgot-email').value;

    if (!email) {
      showMessage('Error', 'Please enter your email address.', 'error');
      return;
    }

    const data = new FormData();
    data.append('action', 'lci_ajax_forgot_password');
    data.append('security', forgotNonce);
    data.append('email', email);

    fetch(lci_ajax.ajax_url, {
      method: 'POST',
      credentials: 'same-origin',
      body: data
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showMessage('Success', data.data.message, 'success');
      } else {
        showMessage('Error', data.data.message, 'error');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      showMessage('Error', 'An unexpected error occurred. Please try again.', 'error');
    });
  });

  // Handle magic link form submission
  magicForm?.addEventListener('submit', (e) => {
    e.preventDefault();

    const email = document.getElementById('lci-magic-email').value;

    if (!email) {
      showMessage('Error', 'Please enter your email address.', 'error');
      return;
    }

    const data = new FormData();
    data.append('action', 'lci_ajax_magic_link');
    data.append('security', magicNonce);
    data.append('email', email);

    fetch(lci_ajax.ajax_url, {
      method: 'POST',
      credentials: 'same-origin',
      body: data
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showMessage('Success', data.data.message, 'success');
      } else {
        showMessage('Error', data.data.message, 'error');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      showMessage('Error', 'An unexpected error occurred. Please try again.', 'error');
    });
  });

  // Handle change password form submission - using the enhanced version below (lines 356-416)

  const showMessage = (title, text, type) => {
    // Use our custom Alpine.js modal instead of SweetAlert
    showLciModal(
      title,
      text,
      type,
      type !== 'success', // showConfirmButton
      'OK',
      false, // showCancelButton
      'Cancel',
      type === 'success', // autoClose
      type === 'success' ? 3000 : 0 // autoCloseDelay
    );
  };

  // Functions are now defined above

  // View switch logic is already handled by the event listeners defined above (lines 94-117)

  // This section is now handled by the new code above

  // 🔐 Login
  loginForm?.addEventListener("submit", async (e) => {
    e.preventDefault();

    // Get form elements
    const userLogin = document.getElementById('lci-email');
    const userPass = document.getElementById('lci-password');
    const rememberMe = document.getElementById('rememberme');

    // Only proceed if all required elements exist
    if (!loginForm || !userLogin || !userPass) {
        console.error('Required login form elements not found', {
            loginForm: !!loginForm,
            userLogin: !!userLogin,
            userPass: !!userPass
        });
        return;
    }

    const email = userLogin.value;
    const password = userPass.value;
    const remember = rememberMe ? rememberMe.checked : false;

    // Get the nonce from the form field
    // We need to use the logintoLCIAGM_nonce from the form field, not the lci-dashboard-add-to-cart-nonce from lci_ajax
    const nonce = loginNonce || document.getElementById("lci_nonce")?.value;

    console.log('Using nonce for login:', nonce);

    // Show loading modal
    showLciModal(
        'Logging in...',
        'Please wait while we authenticate your credentials.',
        'info',
        false, // showConfirmButton
        'OK',
        false, // showCancelButton
        'Cancel',
        false, // autoClose
        0 // autoCloseDelay
    );

    fetch(lci_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            'action': 'logintoLCIAGM',
            'email': email,
            'password': password,
            'remember': remember,
            'security': nonce
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success modal
            showLciModal(
                'Login Successful',
                'You have been successfully logged in. Redirecting you to the dashboard...',
                'success',
                false, // showConfirmButton
                'OK',
                false, // showCancelButton
                'Cancel',
                true, // autoClose
                2000 // autoCloseDelay
            );

            // Redirect after a short delay
            setTimeout(() => {
                window.location.href = '/lci-dashboard';
            }, 2000);
        } else {
            // Format the error message
            let errorMessage = data.data.message;
            if (errorMessage.includes('Lost your password?')) {
                errorMessage = errorMessage.replace(
                    /<strong>Error:<\/strong> The password you entered for the email address <strong>(.*?)<\/strong> is incorrect\. <a href="(.*?)">Lost your password\?<\/a>/,
                    `<div class="text-center">
                        <p class="mb-3">The password you entered for <span class="font-semibold">$1</span> is incorrect.</p>
                        <a href="$2" class="text-blue-600 hover:text-blue-800 underline">Forgot your password?</a>
                    </div>`
                );
            }

            // Show error modal
            showLciModal(
                'Login Failed',
                errorMessage,
                'error',
                true, // showConfirmButton
                'Try Again',
                false, // showCancelButton
                'Cancel',
                false, // autoClose
                0 // autoCloseDelay
            );
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // Show error modal
        showLciModal(
            'Error',
            'An unexpected error occurred. Please try again.',
            'error',
            true, // showConfirmButton
            'Try Again',
            false, // showCancelButton
            'Cancel',
            false, // autoClose
            0 // autoCloseDelay
        );
    });
  });

  // 🔁 Forgot password event listener is already defined above (lines 171-203)

  // 🔁 Change password
  changeForm?.addEventListener("submit", function (e) {
    // Debug form state before submission
    // console.log("Form submission started");
    // debugFormState();
    e.preventDefault();
    const emailField = document.getElementById("lci-change-email");
    const email = emailField ? emailField.value : '';
    const newPass = document.getElementById("lci-new-password").value;
    const confirmPass = document.getElementById("lci-confirm-password").value;

    // SIMPLIFIED LOGIN AND KEY HANDLING
    // Get values directly from the form fields which were populated from URL parameters
    const resetLoginField = document.getElementById('lci-reset-login');
    const resetKeyField = document.getElementById('lci-reset-key');

    // Get values from fields
    const loginFromField = resetLoginField ? resetLoginField.value : '';
    const keyFromField = resetKeyField ? resetKeyField.value : '';

    // Fallback to URL parameters if fields are empty
    const urlParams = new URLSearchParams(window.location.search);
    const loginFromUrl = urlParams.get('login');
    const keyFromUrl = urlParams.get('key');

    // Use the best available values
    let login = loginFromField || loginFromUrl || email;

    // Check if we're on the lost-password page
    const isOnLostPasswordPage = window.location.href.includes('lost-password');

    // Log the sources for debugging
    // console.log('Login and key sources:', {
    //   loginFromField,
    //   loginFromUrl,
    //   email,
    //   keyFromField,
    //   keyFromUrl,
    //   finalLogin: login
    // });

    // Only prompt if we have no login from any source
    if (!login && isOnLostPasswordPage) {
      // Prompt the user for their username or email
      login = prompt("Please enter your username or email address to reset your password:");

      // If the user provided a login, update the fields
      if (login) {
        // console.log('User provided login:', login);

        // Update the email field
        if (emailField) {
          emailField.value = login;
        }

        // Update the hidden login field
        if (resetLoginField) {
          resetLoginField.value = login;
        }
      }
    }

    // console.log('Form submission - login sources:', {
    //   'hidden field': loginFromField,
    //   'URL parameter': loginFromUrl,
    //   'email field': email,
    //   'final value': login
    // });

    // DIRECT KEY RETRIEVAL - Simplified and robust approach
    // First try to get the key from the URL parameters (most reliable source)
    let key = keyFromField || keyFromUrl;
    // console.log("Key value:", key);

    // If we still don't have a key, try to generate one asynchronously
    if (!key && login) {
      // console.log('No key found, generating one asynchronously');

      // Create an asynchronous request to get a key
      const xhr = new XMLHttpRequest();
      xhr.open('POST', lci_ajax.ajax_url, true); // true makes it asynchronous
      xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
      xhr.onload = function() {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.success && response.data && response.data.key) {
              key = response.data.key;
              // console.log('Generated key asynchronously:', key);
              // Update the key field
              if (resetKeyField) {
                resetKeyField.value = key;
              }
            } else {
              console.error('Failed to generate key:', response);
            }
          } catch (e) {
            console.error('Error parsing response:', e);
          }
        }
      };
      xhr.send(new URLSearchParams({
        action: "lci_generate_reset_key",
        login: login
      }).toString());
    }

    // Always create/update the key field with the current key value
    if (resetKeyField) {
      resetKeyField.remove(); // Remove existing field to avoid duplicates
    }

    // Create a new key field
    const newKeyField = document.createElement('input');
    newKeyField.type = 'hidden';
    newKeyField.id = 'lci-reset-key';
    newKeyField.name = 'key';
    newKeyField.value = key || ''; // Set to empty string if key is null/undefined
    changeForm.appendChild(newKeyField);

    // console.log('Final key value for submission:', key);

    // console.log('Submitting password change with key:', key, 'and login:', login);

    // Get the message container
    const messageContainer = document.getElementById("lci-change-password-message");

    // Validate password length
    if (newPass.length < 8) {
      showFormMessage(messageContainer, "<strong>Error:</strong> Password must be at least 8 characters long.", "danger");
      showMessage("Error", "Password must be at least 8 characters long.", "error");
      return;
    }

    // Validate password match
    if (newPass !== confirmPass) {
      showFormMessage(messageContainer, "<strong>Error:</strong> Passwords do not match.", "danger");
      showMessage("Error", "Passwords do not match.", "error");
      return;
    }

    // Make sure we have a login value
    if (!login) {
      showFormMessage(messageContainer, "<strong>Error:</strong> Missing username or email. Please try again.", "danger");
      showMessage("Error", "Missing username or email. Please try again.", "error");
      return;
    }

    // We already have isOnLostPasswordPage defined above

    // If we don't have a key but we're on the lost-password page, we'll generate one
    if (!key && isOnLostPasswordPage) {
      // console.log('No key but on lost-password page - will generate one');

      // Show a loading message
      showFormMessage(messageContainer, "<strong>Processing:</strong> Generating reset key...", "info");

      // We'll generate a key asynchronously before proceeding
      const keyXhr = new XMLHttpRequest();
      keyXhr.open('POST', lci_ajax.ajax_url, true); // true makes it asynchronous
      keyXhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
      keyXhr.onload = function() {
        if (keyXhr.status === 200) {
          try {
            const response = JSON.parse(keyXhr.responseText);
            if (response.success && response.data && response.data.key) {
              key = response.data.key;
              // console.log('Generated key asynchronously:', key);

              // Update the key field
              newKeyField.value = key;
            } else {
              showFormMessage(messageContainer, "<strong>Error:</strong> Failed to generate reset key. Please try again or request a new password reset link.", "danger");
              showMessage("Error", "Failed to generate reset key. Please try again or request a new password reset link.", "error");
              return;
            }
          } catch (e) {
            console.error('Error parsing response:', e);
            showFormMessage(messageContainer, "<strong>Error:</strong> Failed to generate reset key. Please try again or request a new password reset link.", "danger");
            showMessage("Error", "Failed to generate reset key. Please try again or request a new password reset link.", "error");
            return;
          }
        } else {
          showFormMessage(messageContainer, "<strong>Error:</strong> Failed to generate reset key. Please try again or request a new password reset link.", "danger");
          showMessage("Error", "Failed to generate reset key. Please try again or request a new password reset link.", "error");
          return;
        }
      };
      keyXhr.send(new URLSearchParams({
        action: 'lci_generate_reset_key',
        login: login
      }).toString());
    } else if (!key) {
      // If we're not on the lost-password page and don't have a key, show an error
      showFormMessage(messageContainer, "<strong>Error:</strong> Missing reset key. Please try again or request a new password reset link.", "danger");
      showMessage("Error", "Missing reset key. Please try again or request a new password reset link.", "error");
      return;
    }

    // console.log('Sending password reset request with login:', login, 'and key:', key);

    // Final check of form state before submission
    // debugFormState();

    // Create the form data manually to ensure all fields are included
    const formData = new FormData();
    formData.append('action', 'lci_ajax_change_password');
    formData.append('login', login);
    formData.append('email', email); // Include both for backward compatibility
    formData.append('password', newPass);
    formData.append('key', key);

    // Log the exact data being sent
    // console.log('Sending form data:', {
    //   action: 'lci_ajax_change_password',
    //   login: login,
    //   email: email,
    //   password: '(redacted)',
    //   key: key
    // });

    // Disable the submit button and show loading state
    const submitBtn = changeForm.querySelector("button[type='submit']");
    const originalBtnText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';

    // Log the request for debugging
    // console.log('Sending password reset request to:', lci_ajax.ajax_url);

    // IMPORTANT: Use XMLHttpRequest instead of fetch to ensure the request completes
    // even if the page is about to unload
    const xhr = new XMLHttpRequest();
    xhr.open('POST', lci_ajax.ajax_url, true); // true for async

    // Set up event handlers
    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const data = JSON.parse(xhr.responseText);
          // console.log('Response data:', data);

          if (data.success) {
            // console.log("Password change successful:", data);

            // Clear localStorage values
            localStorage.removeItem('lci_reset_key');
            localStorage.removeItem('lci_reset_login');
            // console.log('Cleared reset parameters from localStorage');

            // Show success message in the form first
            if (messageContainer) {
              messageContainer.className = "alert alert-success text-center mt-3";
              messageContainer.innerHTML = "<strong>Success:</strong> Your password has been successfully changed. You can now log in with your new password.";
              messageContainer.scrollIntoView({ behavior: 'smooth' });
            }

            // Show popup message
            showMessage("Success", "Your password has been successfully changed. You can now log in with your new password.", "success");

            // Reset the form
            changeForm.reset();

            // Update button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Password Changed Successfully';
            submitBtn.className = 'btn w-100 py-2 fw-bold text-white mb-3 btn-success';
          } else {
            // Show error message
            showMessage("Error", data.data?.message || "Could not reset password.", "error");

            // Restore the button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;

            // Show a message in the form as well
            if (messageContainer) {
              messageContainer.className = "alert alert-danger text-center mt-3";
              messageContainer.innerHTML = "<strong>Error:</strong> " + (data.data?.message || "Could not reset password. Please try again.");
            }
          }
        } catch (e) {
          console.error('Error parsing response:', e);
          showMessage("Error", "An unexpected error occurred. Please try again.", "error");
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalBtnText;
        }
      } else {
        console.error('Request failed with status:', xhr.status);
        showMessage("Error", "An unexpected error occurred. Please try again.", "error");
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalBtnText;
      }
    };

    xhr.onerror = function() {
      console.error('Request failed');
      showMessage("Error", "An unexpected error occurred. Please try again.", "error");
      submitBtn.disabled = false;
      submitBtn.innerHTML = originalBtnText;
    };

    // Send the request
    xhr.send(formData);
  });
});
