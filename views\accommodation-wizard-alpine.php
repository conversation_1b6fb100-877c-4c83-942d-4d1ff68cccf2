<?php
/**
 * Accommodation Wizard Page (Alpine.js Version)
 *
 * This page provides a step-by-step wizard for selecting accommodation options.
 * It uses Alpine.js for interactivity and follows UX 2025 design principles.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Note: Scripts are loaded in includes/routes.php

// Get header
get_header();

// Add fallback styles and initialization script
?>
<style>
/* Accommodation Wizard Styles */
.accommodation-wizard {
    --primary-color: #36b1dc;
    --primary-dark: #2a8fb3;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-bg: #f8f9fa;
    --border-radius: 12px;
    --box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

.accommodation-wizard-container {
    max-width: 1000px;
    margin: 0 auto;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    overflow: hidden;
}

.accommodation-wizard-title {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
    font-weight: 600;
}

/* Progress Bar */
.wizard-progress {
    position: relative;
    display: flex;
    justify-content: space-between;
    margin-bottom: 3rem;
    padding: 0 10%;
}

.wizard-progress-bar {
    position: absolute;
    top: 50%;
    left: 10%;
    right: 10%;
    height: 4px;
    background-color: #e0e0e0;
    transform: translateY(-50%);
    z-index: 1;
}

.wizard-progress-bar-inner {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.5s ease;
}

.wizard-step {
    position: relative;
    z-index: 2;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    border: 3px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: var(--transition);
}

.wizard-step.active {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

.wizard-step.completed {
    border-color: var(--success-color);
    background-color: var(--success-color);
    color: white;
}

/* Step Content */
.wizard-step-content {
    display: none;
    animation: fadeIn 0.5s ease forwards;
}

.wizard-step-content.active {
    display: block;
}

/* Option Cards */
.option-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.option-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid transparent;
    background-color: white;
}

.option-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
}

.option-card.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(54, 177, 220, 0.3), 0 12px 24px rgba(0, 0, 0, 0.12);
}

.option-card-image {
    height: 180px;
    overflow: hidden;
}

.option-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.option-card:hover .option-card-image img {
    transform: scale(1.1);
}

.option-card-content {
    padding: 1.5rem;
    text-align: center;
}

.option-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.option-card-description {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0;
}

.option-card-radio {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

/* Nights Selector */
.nights-selector {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
}

.nights-selector-container {
    display: flex;
    align-items: center;
    background-color: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.nights-btn {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
}

.nights-btn-minus {
    background-color: #f1f1f1;
    color: #666;
}

.nights-btn-plus {
    background-color: var(--primary-color);
    color: white;
}

.nights-btn:hover:not(:disabled) {
    transform: scale(1.05);
}

.nights-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nights-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 1.5rem;
    min-width: 100px;
}

.nights-count {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.nights-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: -5px;
}

/* Product Cards */
.product-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.product-card {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    background-color: white;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.product-card-image {
    height: 180px;
    overflow: hidden;
}

.product-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-card-image img {
    transform: scale(1.1);
}

.product-card-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-card-title {
    margin-bottom: 0.75rem;
    color: #333;
    font-weight: 600;
    font-size: 1.2rem;
}

.product-card-price {
    margin-bottom: 1rem;
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.product-card-description {
    color: #666;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.product-card-actions {
    margin-top: auto;
}

/* Buttons */
.wizard-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    text-align: center;
}

.wizard-btn-primary {
    background-color: var(--primary-color);
    color: white !important;
}

.wizard-btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    color: white !important;
    text-decoration: none;
}

.wizard-btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color) !important;
}

.wizard-btn-outline:hover {
    background-color: var(--primary-color);
    color: white !important;
    transform: translateY(-2px);
    text-decoration: none;
}

.wizard-btn-block {
    display: block;
    width: 100%;
}

/* Navigation */
.wizard-navigation {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
}

/* Info Box */
.info-box {
    max-width: 800px;
    margin: 0 auto 2rem;
    padding: 1.25rem;
    border-radius: var(--border-radius);
    background-color: #e6f7ff;
    border: 1px solid #b8e2f2;
    color: #0c5460;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* Modal */
.wizard-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.wizard-modal-backdrop.active {
    opacity: 1;
    visibility: visible;
}

.wizard-modal {
    background-color: white;
    border-radius: var(--border-radius);
    max-width: 450px;
    width: 95%;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.12);
    transform: translateY(20px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    position: relative;
    overflow: hidden;
}

.wizard-modal-backdrop.active .wizard-modal {
    transform: translateY(0);
    opacity: 1;
}

.wizard-modal-header {
    padding: 1.5rem 1.5rem 0;
    position: relative;
}

.wizard-modal-close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
}

.wizard-modal-close:hover {
    transform: scale(1.1);
}

.wizard-modal-body {
    padding: 1.5rem;
    text-align: center;
}

.wizard-modal-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.wizard-modal-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.wizard-modal-text {
    color: #666;
    margin-bottom: 1.5rem;
}

.wizard-modal-footer {
    padding: 0 1.5rem 1.5rem;
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.wizard-modal-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background-color: rgba(54, 177, 220, 0.1);
    overflow: hidden;
}

.wizard-modal-progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    background-color: var(--primary-color);
    width: 0;
    transition: width 3s linear;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(20px); opacity: 0; }
}

/* Responsive Styles */
@media (max-width: 767.98px) {
    .accommodation-wizard-container {
        padding: 1.5rem;
    }

    .option-cards {
        grid-template-columns: 1fr;
    }

    .product-cards {
        grid-template-columns: 1fr;
    }

    .option-card-image,
    .product-card-image {
        height: 150px;
    }

    .nights-btn {
        width: 40px;
        height: 40px;
    }

    .nights-display {
        padding: 0 1rem;
    }

    .nights-count {
        font-size: 1.75rem;
    }

    .wizard-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .wizard-navigation .wizard-btn {
        width: 100%;
    }
}

/* Alpine.js Utility Classes */
[x-cloak] { display: none !important; }

.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}
.fade-enter, .fade-leave-to {
    opacity: 0;
}

.slide-enter-active, .slide-leave-active {
    transition: transform 0.3s ease, opacity 0.3s ease;
}
.slide-enter, .slide-leave-to {
    transform: translateY(20px);
    opacity: 0;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.loading-spinner {
    font-size: 2rem;
    color: var(--primary-color);
}

/* Added to Cart Message */
.added-to-cart-message {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    background-color: #d4edda;
    color: #155724;
    border-radius: 8px;
    font-weight: 600;
}
</style>

<script>
// Define fallback for lci_ajax if it's not defined
if (typeof lci_ajax === 'undefined') {
    console.log('lci_ajax is not defined, creating fallback');
    window.lci_ajax = {
        ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
        nonce: '<?php echo wp_create_nonce('lci_ajax_nonce'); ?>'
    };
}

// Add debug info
console.log('AJAX URL:', lci_ajax.ajax_url);
console.log('AJAX Nonce:', lci_ajax.nonce);

// Fallback initialization for accommodationWizardData
if (typeof window.accommodationWizardData !== 'function') {
    console.log('Fallback: Initializing accommodationWizardData');
    window.accommodationWizardData = function() {
        return {
            // Wizard state
            currentStep: 1,
            totalSteps: 4, // Maximum possible steps in the flow
            flowStep: 1, // Tracks which part of the flow we're in
            hasMainPretour: false, // Will be set after checking orders
            wizardCompleted: false, // Flag to indicate if the wizard is completed

            // User selections
            bucharest: {
                selected: null, // 'yes' or 'no'
                nights: 1
            },
            brasov: {
                selected: null, // 'yes', 'no', or 'type'
                type: null, // 'pre', 'main', or 'post'
                nights: 1
            },

            // Data
            products: [],

            // UI state
            loading: false,
            showModal: false,
            modalProduct: null,
            modalProgress: 0,
            progressInterval: null,

            // Initialize the component
            init() {
                console.log('Alpine component initialized');
                this.checkUserProducts();
                this.updateProgressBar();
            },

            // Check if user has Main Pretour (product ID 743)
            async checkUserProducts() {
                this.loading = true;
                console.log('Checking if user has Main Pretour (product ID 743)...');
                console.log('Using AJAX URL:', lci_ajax.ajax_url);

                try {
                    if (!lci_ajax || !lci_ajax.ajax_url) {
                        throw new Error('AJAX configuration is missing');
                    }

                    console.log('Checking user products with AJAX URL:', lci_ajax.ajax_url, 'and nonce:', lci_ajax.nonce);

                    const response = await fetch(lci_ajax.ajax_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'check_user_products',
                            security: lci_ajax.nonce || '', // Use 'security' instead of 'nonce'
                            product_id: 743 // Main Pretour ID
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    console.log('AJAX response:', data);

                    if (data.success) {
                        this.hasMainPretour = data.data.has_product;
                        console.log('User has Main Pretour:', this.hasMainPretour);
                    } else {
                        console.error('Error checking user products:', data.data?.message || 'Unknown error');
                        // Fallback to false if there's an error
                        this.hasMainPretour = false;
                    }
                } catch (error) {
                    console.error('Error checking user products:', error);
                    // Fallback to false if there's an error
                    this.hasMainPretour = false;
                } finally {
                    this.loading = false;
                }
            },

            // Update the progress bar
            updateProgressBar() {
                // Calculate progress based on current flow
                let totalStepsInFlow = this.hasMainPretour ? 4 : 3;
                const progressPercent = ((this.currentStep - 1) / (totalStepsInFlow - 1)) * 100;

                const progressBar = document.querySelector('.wizard-progress-bar-inner');
                if (progressBar) progressBar.style.width = `${progressPercent}%`;
            },

            // Go to next step
            nextStep() {
                console.log('Current step:', this.currentStep, 'Flow step:', this.flowStep, 'Bucharest selected:', this.bucharest.selected, 'Brasov selected:', this.brasov.selected, 'Brasov type:', this.brasov.type);

                // Logic for the flow based on the functional plan
                if (this.hasMainPretour) {
                    // Flow for users with Main Pretour (ID 743)
                    if (this.flowStep === 1) {
                        // After Bucharest question
                        this.flowStep = 2;

                        if (this.bucharest.selected === 'yes') {
                            // If they want Bucharest accommodation, ask about nights
                            this.currentStep = 2;
                            console.log('Going to Bucharest nights selection');
                        } else if (this.bucharest.selected === 'no') {
                            // If they don't want Bucharest, show Brasov options
                            this.currentStep = 4; // Use step 4 for Brasov options
                            console.log('Going to Brasov options selection');
                        }
                    } else if (this.flowStep === 2 && this.bucharest.selected === 'yes') {
                        // After nights selection, show Bucharest products
                        this.flowStep = 3;
                        this.currentStep = 3;
                        this.loadProducts('bucharest');
                        console.log('Loading Bucharest products');
                    } else if (this.flowStep === 3 && this.bucharest.selected === 'yes') {
                        // After showing Bucharest products, continue to Brasov options
                        this.flowStep = 4;
                        this.currentStep = 4;
                        console.log('Going to Brasov options after Bucharest products');
                    } else if (this.flowStep === 4 && this.brasov.type) {
                        // After selecting Brasov type
                        this.flowStep = 5;

                        if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                            // If they selected pre or post event, ask about nights
                            this.currentStep = 2;
                            console.log('Going to Brasov nights selection');
                        } else {
                            // If they selected main event, show products
                            this.currentStep = 3;
                            this.loadProducts('brasov');
                            console.log('Loading Brasov main event products');
                        }
                    } else if (this.flowStep === 5 && (this.brasov.type === 'pre' || this.brasov.type === 'post')) {
                        // After nights selection for Brasov pre/post event
                        this.flowStep = 6;
                        this.currentStep = 3;
                        this.loadProducts('brasov');
                        console.log('Loading Brasov pre/post event products');
                    }
                } else {
                    // Flow for users without Main Pretour
                    if (this.flowStep === 1 && this.brasov.type) {
                        this.flowStep = 2;

                        if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                            // If they selected pre or post event, ask about nights
                            this.currentStep = 2;
                            console.log('Going to Brasov nights selection (no pretour)');
                        } else {
                            // If they selected main event, show products
                            this.currentStep = 3;
                            this.loadProducts('brasov');
                            console.log('Loading Brasov main event products (no pretour)');
                        }
                    } else if (this.flowStep === 2 && (this.brasov.type === 'pre' || this.brasov.type === 'post')) {
                        // After nights selection
                        this.flowStep = 3;
                        this.currentStep = 3;
                        this.loadProducts('brasov');
                        console.log('Loading Brasov pre/post event products (no pretour)');
                    }
                }

                this.updateProgressBar();
                console.log('After nextStep - Current step:', this.currentStep, 'Flow step:', this.flowStep);
            },

            // Go to previous step
            prevStep() {
                console.log('Before prevStep - Current step:', this.currentStep, 'Flow step:', this.flowStep);

                if (this.currentStep > 1) {
                    // Logic for going back based on the flow
                    if (this.hasMainPretour) {
                        if (this.flowStep === 6 && (this.brasov.type === 'pre' || this.brasov.type === 'post')) {
                            // Going back from Brasov products to nights selection
                            this.flowStep = 5;
                            this.currentStep = 2;
                            console.log('Going back to Brasov nights selection');
                        } else if (this.flowStep === 5 && this.brasov.type === 'main') {
                            // Going back from Brasov main products to Brasov options
                            this.flowStep = 4;
                            this.currentStep = 4;
                            console.log('Going back to Brasov options selection');
                        } else if (this.flowStep === 5 && (this.brasov.type === 'pre' || this.brasov.type === 'post')) {
                            // Going back from Brasov nights to Brasov options
                            this.flowStep = 4;
                            this.currentStep = 4;
                            console.log('Going back to Brasov options selection');
                        } else if (this.flowStep === 4 && this.bucharest.selected === 'yes') {
                            // Going back from Brasov options to Bucharest products
                            this.flowStep = 3;
                            this.currentStep = 3;
                            console.log('Going back to Bucharest products');
                        } else if (this.flowStep === 4 && this.bucharest.selected === 'no') {
                            // Going back from Brasov options to initial question
                            this.flowStep = 1;
                            this.currentStep = 1;
                            console.log('Going back to initial question');
                        } else if (this.flowStep === 3 && this.bucharest.selected === 'yes') {
                            // Going back from Bucharest products to nights selection
                            this.flowStep = 2;
                            this.currentStep = 2;
                            console.log('Going back to Bucharest nights selection');
                        } else {
                            // Regular back navigation
                            this.flowStep--;
                            this.currentStep--;
                            console.log('Regular back navigation');
                        }
                    } else {
                        // For users without Main Pretour
                        if (this.flowStep === 3 && (this.brasov.type === 'pre' || this.brasov.type === 'post')) {
                            // Going back from Brasov products to nights selection
                            this.flowStep = 2;
                            this.currentStep = 2;
                            console.log('Going back to Brasov nights selection (no pretour)');
                        } else if (this.flowStep === 2 && this.brasov.type === 'main') {
                            // Going back from Brasov main products to options
                            this.flowStep = 1;
                            this.currentStep = 1;
                            this.brasov.selected = 'type';
                            console.log('Going back to Brasov options selection (no pretour)');
                        } else if (this.flowStep === 2 && (this.brasov.type === 'pre' || this.brasov.type === 'post')) {
                            // Going back from nights selection to options
                            this.flowStep = 1;
                            this.currentStep = 1;
                            this.brasov.selected = 'type';
                            console.log('Going back to Brasov options selection (no pretour)');
                        } else if (this.brasov.selected === 'type') {
                            // Going back from type selection to yes/no
                            this.brasov.selected = 'yes';
                            console.log('Going back to Brasov yes/no selection');
                        } else {
                            // Regular back navigation
                            this.flowStep--;
                            this.currentStep--;
                            console.log('Regular back navigation (no pretour)');
                        }
                    }

                    this.updateProgressBar();
                }

                console.log('After prevStep - Current step:', this.currentStep, 'Flow step:', this.flowStep);
            },

            // Select Bucharest option (Yes/No)
            selectBucharest(option) {
                this.bucharest.selected = option;

                // Auto-advance if they select 'no'
                if (option === 'no') {
                    setTimeout(() => this.nextStep(), 300);
                }
            },

            // Select Brasov Yes/No option
            selectBrasovYesNo(option) {
                this.brasov.selected = option;

                if (option === 'yes') {
                    // If they select yes, change to type selection
                    setTimeout(() => {
                        this.brasov.selected = 'type';
                        this.brasov.type = null;
                    }, 300);
                } else if (option === 'no') {
                    // If they select no, complete the wizard
                    setTimeout(() => {
                        this.wizardCompleted = true;
                        // Redirect to accommodation page
                        window.location.href = '?tab=accommodation&wizard-completed=1';
                    }, 1000);
                }
            },

            // Select Brasov type option (pre/main/post)
            selectBrasovType(option) {
                this.brasov.type = option;
                setTimeout(() => this.nextStep(), 300);
            },

            // Increase nights count
            increaseNights(location) {
                if (location === 'bucharest') {
                    this.bucharest.nights++;
                } else {
                    this.brasov.nights++;
                }
            },

            // Decrease nights count
            decreaseNights(location) {
                if (location === 'bucharest' && this.bucharest.nights > 1) {
                    this.bucharest.nights--;
                } else if (location === 'brasov' && this.brasov.nights > 1) {
                    this.brasov.nights--;
                }
            },

            // Load products based on selection
            async loadProducts(type) {
                this.loading = true;
                this.products = [];
                console.log('Loading products for type:', type);

                let categoryId;
                let metaQuery = {};

                // Determine which products to load
                if (type === 'bucharest') {
                    // Bucharest accommodation (category ID 33)
                    categoryId = 33;
                    if (this.bucharest.nights > 0) {
                        metaQuery = {
                            key: '_nights',
                            value: this.bucharest.nights.toString(),
                            compare: '='
                        };
                    }
                    console.log('Loading Bucharest products from category', categoryId, 'with nights:', this.bucharest.nights);
                } else if (type === 'brasov') {
                    // Brasov accommodation
                    switch (this.brasov.type) {
                        case 'pre':
                            categoryId = 37; // Pre-event accommodation
                            metaQuery = {
                                key: '_timing',
                                value: 'before',
                                compare: '='
                            };
                            console.log('Loading Pre-event Brasov products from category', categoryId);
                            break;
                        case 'main':
                            categoryId = 1; // Main event accommodation
                            console.log('Loading Main Event Brasov products from category', categoryId);
                            break;
                        case 'post':
                            categoryId = 37; // Post-event accommodation
                            metaQuery = {
                                key: '_timing',
                                value: 'after',
                                compare: '='
                            };
                            console.log('Loading Post-event Brasov products from category', categoryId);
                            break;
                        default:
                            console.error('Invalid Brasov type:', this.brasov.type);
                            this.loading = false;
                            return;
                    }
                } else {
                    console.error('Invalid product type:', type);
                    this.loading = false;
                    return;
                }

                try {
                    if (!lci_ajax || !lci_ajax.ajax_url) {
                        throw new Error('AJAX configuration is missing');
                    }

                    console.log('Sending AJAX request to:', lci_ajax.ajax_url, 'with nonce:', lci_ajax.nonce);

                    const response = await fetch(lci_ajax.ajax_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'get_accommodation_products',
                            security: lci_ajax.nonce || '', // Use 'security' instead of 'nonce'
                            category_id: categoryId,
                            meta_query: JSON.stringify(metaQuery)
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    console.log('AJAX response for products:', data);

                    if (data.success && data.data && data.data.products) {
                        this.products = data.data.products;
                        console.log('Loaded', this.products.length, 'products');
                    } else {
                        console.error('Error loading products:', data);
                        // Fallback to empty array if there's an error
                        this.products = [];
                    }
                } catch (error) {
                    console.error('Error loading products:', error);
                    // Fallback to empty array if there's an error
                    this.products = [];

                    // Provide fallback products for testing if needed
                    if (type === 'bucharest') {
                        this.products = [
                            {
                                id: 101,
                                name: 'Bucharest Hotel (Fallback)',
                                price: '€120',
                                price_html: '<span class="amount">€120</span> <small>per night</small>',
                                description: 'Fallback product for testing.',
                                image: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp',
                                added: false
                            }
                        ];
                    } else if (type === 'brasov') {
                        this.products = [
                            {
                                id: 201,
                                name: 'Brasov Hotel (Fallback)',
                                price: '€100',
                                price_html: '<span class="amount">€100</span> <small>per night</small>',
                                description: 'Fallback product for testing.',
                                image: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp',
                                added: false
                            }
                        ];
                    }
                } finally {
                    this.loading = false;
                }
            },

            // Add product to cart
            async addToCart(productId) {
                const product = this.products.find(p => p.id === productId);
                if (!product) {
                    console.error('Product not found:', productId);
                    return;
                }

                this.loading = true;
                console.log('Adding product to cart:', product.name, '(ID:', productId, ')');

                try {
                    if (!lci_ajax || !lci_ajax.ajax_url) {
                        throw new Error('AJAX configuration is missing');
                    }

                    const response = await fetch(lci_ajax.ajax_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'lci-dashboard-add-to-cart',
                            security: lci_ajax.nonce || '', // Use 'security' instead of 'nonce' for this action
                            product_id: productId,
                            quantity: 1
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    console.log('AJAX response for add to cart:', data);

                    if (data.success) {
                        // Show success modal
                        this.modalProduct = product;
                        this.showModal = true;
                        this.startModalProgress();

                        // Mark product as added
                        product.added = true;

                        // Update mini cart if available
                        if (typeof updateMiniCart === 'function') {
                            try {
                                updateMiniCart(data.data.cart_count, data.data.cart_total);
                            } catch (e) {
                                console.warn('Could not update mini cart:', e);
                            }
                        }

                        console.log('Product added to cart successfully');
                    } else {
                        console.error('Error adding product to cart:', data);
                        alert('Error adding product to cart. Please try again.');
                    }
                } catch (error) {
                    console.error('Error adding product to cart:', error);

                    // Show success modal anyway for testing
                    this.modalProduct = product;
                    this.showModal = true;
                    this.startModalProgress();

                    // Mark product as added
                    product.added = true;

                    // Only show alert in production
                    if (!error.message.includes('testing')) {
                        alert('Error adding product to cart. Please try again.');
                    }
                } finally {
                    this.loading = false;
                }
            },

            // Start modal progress bar
            startModalProgress() {
                this.modalProgress = 0;
                clearInterval(this.progressInterval);

                const duration = 3000; // 3 seconds
                const interval = 30; // Update every 30ms
                const steps = duration / interval;
                const increment = 100 / steps;

                this.progressInterval = setInterval(() => {
                    this.modalProgress += increment;

                    if (this.modalProgress >= 100) {
                        this.stopModalProgress();
                        this.closeModal();
                    }
                }, interval);
            },

            // Stop modal progress bar
            stopModalProgress() {
                clearInterval(this.progressInterval);
                this.progressInterval = null;
            },

            // Close modal
            closeModal() {
                this.showModal = false;
                this.stopModalProgress();
            },

            // Get current step title
            getStepTitle() {
                if (this.hasMainPretour) {
                    // Titles for users with Main Pretour
                    if (this.currentStep === 1) {
                        return 'Bucharest Accommodation';
                    } else if (this.currentStep === 2) {
                        // Check if we're asking about Bucharest or Brasov nights
                        if (this.bucharest.selected === 'yes' && this.flowStep === 2) {
                            return 'Nights in Bucharest';
                        } else {
                            return 'Nights in Brasov';
                        }
                    } else if (this.currentStep === 3) {
                        // Check if we're showing Bucharest or Brasov products
                        if (this.bucharest.selected === 'yes' && this.flowStep === 3) {
                            return 'Bucharest Accommodation Options';
                        } else {
                            return 'Brasov Accommodation Options';
                        }
                    } else if (this.currentStep === 4) {
                        return 'Brasov Accommodation';
                    }
                } else {
                    // Titles for users without Main Pretour
                    if (this.currentStep === 1) {
                        return 'Brasov Accommodation';
                    } else if (this.currentStep === 2) {
                        return 'Nights in Brasov';
                    } else {
                        return 'Brasov Accommodation Options';
                    }
                }

                return 'Accommodation Wizard';
            },

            // Check if current step is valid
            isStepValid() {
                if (this.hasMainPretour) {
                    // Validation for users with Main Pretour
                    if (this.currentStep === 1) {
                        return this.bucharest.selected !== null;
                    } else if (this.currentStep === 2) {
                        // Check if we're validating Bucharest or Brasov nights
                        if (this.bucharest.selected === 'yes' && this.flowStep === 2) {
                            return this.bucharest.nights > 0;
                        } else if (this.flowStep === 5) {
                            return this.brasov.nights > 0;
                        }
                    } else if (this.currentStep === 4) {
                        return this.brasov.selected !== null;
                    }
                } else {
                    // Validation for users without Main Pretour
                    if (this.currentStep === 1) {
                        return this.brasov.selected !== null;
                    } else if (this.currentStep === 2) {
                        return this.brasov.nights > 0;
                    }
                }

                return true;
            }
        };
    };
}
</script>

<div class="lci-container accommodation-wizard-page">
    <div class="accommodation-wizard-container" x-data="accommodationWizardData()" x-cloak>
        <!-- Loading Overlay -->
        <div class="loading-overlay" x-show="loading" x-transition>
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
        </div>

        <!-- Page Title -->
        <h1 class="accommodation-wizard-title" x-text="getStepTitle()">Accommodation Wizard</h1>

        <!-- Wizard Progress Bar -->
        <div class="wizard-progress">
            <div class="wizard-progress-bar">
                <div class="wizard-progress-bar-inner"></div>
            </div>
            <div class="wizard-step" :class="{'active': currentStep >= 1, 'completed': currentStep > 1}">1</div>
            <div class="wizard-step" :class="{'active': currentStep >= 2, 'completed': currentStep > 2}">2</div>
            <div class="wizard-step" :class="{'active': currentStep >= 3, 'completed': currentStep > 3}">3</div>
            <template x-if="hasMainPretour && bucharest.selected === 'yes'">
                <div class="wizard-step" :class="{'active': currentStep >= 4}">4</div>
            </template>
        </div>

        <!-- Wizard Content -->
        <div class="wizard-content">
            <!-- Step 1: Initial Question -->
            <div class="wizard-step-content" :class="{'active': currentStep === 1}">
                <!-- For users with Main Pretour (ID 743) -->
                <template x-if="hasMainPretour">
                    <div>
                        <h3 class="text-center mb-4">Accommodation for Main Pretour</h3>
                        <div class="info-box">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>Will you arrive early in Bucharest and need accommodation before the tour starts?</span>
                        </div>

                        <div class="option-cards">
                            <div class="option-card"
                                 :class="{'selected': bucharest.selected === 'yes'}"
                                 @click="selectBucharest('yes')">
                                <div class="option-card-image">
                                    <img src="<?php echo LCI2025_URL; ?>assets/images/bucharest.jpg" alt="Yes" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                                </div>
                                <div class="option-card-content">
                                    <h4 class="option-card-title">Yes, I need a place to stay in Bucharest</h4>
                                    <p class="option-card-description">Before the Main Pretour starts</p>
                                </div>
                            </div>

                            <div class="option-card"
                                 :class="{'selected': bucharest.selected === 'no'}"
                                 @click="selectBucharest('no')">
                                <div class="option-card-image">
                                    <img src="<?php echo LCI2025_URL; ?>assets/images/brasov.jpg" alt="No" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                                </div>
                                <div class="option-card-content">
                                    <h4 class="option-card-title">No, I need accommodation in Brasov</h4>
                                    <p class="option-card-description">I only need accommodation in Brasov</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- For users without Main Pretour -->
                <template x-if="!hasMainPretour">
                    <div>
                        <h3 class="text-center mb-4">Do you need accommodation in Brasov?</h3>
                        <div class="info-box">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>Please select if you need accommodation in Brasov</span>
                        </div>

                        <div class="option-cards">
                            <div class="option-card"
                                 :class="{'selected': brasov.selected === 'yes'}"
                                 @click="selectBrasovYesNo('yes')">
                                <div class="option-card-image">
                                    <img src="<?php echo LCI2025_URL; ?>assets/images/brasov.jpg" alt="Yes" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                                </div>
                                <div class="option-card-content">
                                    <h4 class="option-card-title">Yes, I need accommodation in Brasov</h4>
                                    <p class="option-card-description">Show me accommodation options</p>
                                </div>
                            </div>

                            <div class="option-card"
                                 :class="{'selected': brasov.selected === 'no'}"
                                 @click="selectBrasovYesNo('no')">
                                <div class="option-card-image">
                                    <img src="<?php echo LCI2025_URL; ?>assets/images/no-accommodation.jpg" alt="No" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                                </div>
                                <div class="option-card-content">
                                    <h4 class="option-card-title">No, I don't need accommodation</h4>
                                    <p class="option-card-description">I have my own accommodation</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Step 2: Nights Selection -->
            <div class="wizard-step-content" :class="{'active': currentStep === 2}">
                <!-- For users with Main Pretour who selected Bucharest -->
                <template x-if="hasMainPretour && bucharest.selected === 'yes'">
                    <div>
                        <h3 class="text-center mb-4">How many nights will you stay in Bucharest?</h3>
                        <div class="info-box">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>Select the number of nights you need accommodation in Bucharest before the Main Pretour</span>
                        </div>

                        <div class="nights-selector">
                            <div class="nights-selector-container">
                                <button type="button" class="nights-btn nights-btn-minus" @click="decreaseNights('bucharest')" :disabled="bucharest.nights <= 1">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <div class="nights-display">
                                    <span class="nights-count" x-text="bucharest.nights">1</span>
                                    <span class="nights-label" x-text="bucharest.nights === 1 ? 'Night' : 'Nights'">Night</span>
                                </div>
                                <button type="button" class="nights-btn nights-btn-plus" @click="increaseNights('bucharest')">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- For users without Main Pretour who selected Pre-event or Post-event -->
                <template x-if="(!hasMainPretour || (hasMainPretour && bucharest.selected === 'no')) && (brasov.selected === 'pre' || brasov.selected === 'post')">
                    <div>
                        <h3 class="text-center mb-4" x-text="'How many nights will you stay in Brasov ' + (brasov.selected === 'pre' ? 'before' : 'after') + ' the event?'"></h3>
                        <div class="info-box">
                            <i class="fas fa-info-circle me-2"></i>
                            <span x-text="'Select the number of nights you need accommodation in Brasov ' + (brasov.selected === 'pre' ? 'before' : 'after') + ' the event'"></span>
                        </div>

                        <div class="nights-selector">
                            <div class="nights-selector-container">
                                <button type="button" class="nights-btn nights-btn-minus" @click="decreaseNights('brasov')" :disabled="brasov.nights <= 1">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <div class="nights-display">
                                    <span class="nights-count" x-text="brasov.nights">1</span>
                                    <span class="nights-label" x-text="brasov.nights === 1 ? 'Night' : 'Nights'">Night</span>
                                </div>
                                <button type="button" class="nights-btn nights-btn-plus" @click="increaseNights('brasov')">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Step 3: Product Selection (Bucharest for Main Pretour users or Brasov for others) -->
            <div class="wizard-step-content" :class="{'active': currentStep === 3}">
                <h3 class="text-center mb-4" x-text="getStepTitle()">Accommodation Options</h3>
                <div class="info-box">
                    <i class="fas fa-info-circle me-2"></i>
                    <span x-text="'Select your preferred accommodation option' + (hasMainPretour && bucharest.selected === 'yes' ? ' in Bucharest' : ' in Brasov')"></span>
                </div>

                <!-- Loading Message -->
                <template x-if="loading">
                    <div class="text-center py-5">
                        <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>Loading accommodation options...</p>
                    </div>
                </template>

                <!-- No Products Message -->
                <template x-if="!loading && products.length === 0">
                    <div class="text-center py-5">
                        <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
                        <p>No accommodation options available for your selection.</p>
                    </div>
                </template>

                <!-- Product Cards -->
                <div class="product-cards" x-show="!loading && products.length > 0">
                    <template x-for="product in products" :key="product.id">
                        <div class="product-card" :class="{'added-to-cart': product.added}">
                            <div class="product-card-image">
                                <img :src="product.image" :alt="product.name" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="product-card-content">
                                <h4 class="product-card-title" x-text="product.name"></h4>
                                <div class="product-card-price" x-html="product.price_html"></div>
                                <div class="product-card-description" x-text="product.description"></div>
                                <div class="product-card-actions">
                                    <template x-if="!product.added">
                                        <button type="button" class="wizard-btn wizard-btn-primary wizard-btn-block" @click="addToCart(product.id)">
                                            <i class="fas fa-shopping-cart me-2"></i> Add to Cart
                                        </button>
                                    </template>
                                    <template x-if="product.added">
                                        <div class="added-to-cart-message">
                                            <i class="fas fa-check-circle me-2"></i> Added to your cart
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Step 4: Brasov Options (For all users) -->
            <div class="wizard-step-content" :class="{'active': currentStep === 4}">
                <div>
                    <h3 class="text-center mb-4">Do you need accommodation in Brasov?</h3>
                    <div class="info-box">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Please select if you need accommodation in Brasov</span>
                    </div>

                    <!-- Yes/No Options -->
                    <template x-if="!brasov.selected || brasov.selected === 'yes' || brasov.selected === 'no'">
                        <div class="option-cards">
                            <div class="option-card"
                                 :class="{'selected': brasov.selected === 'yes'}"
                                 @click="selectBrasovYesNo('yes')">
                                <div class="option-card-image">
                                    <img src="<?php echo LCI2025_URL; ?>assets/images/brasov.jpg" alt="Yes" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                                </div>
                                <div class="option-card-content">
                                    <h4 class="option-card-title">Yes, I need accommodation in Brasov</h4>
                                    <p class="option-card-description">Show me accommodation options</p>
                                </div>
                            </div>

                            <div class="option-card"
                                 :class="{'selected': brasov.selected === 'no'}"
                                 @click="selectBrasovYesNo('no')">
                                <div class="option-card-image">
                                    <img src="<?php echo LCI2025_URL; ?>assets/images/no-accommodation.jpg" alt="No" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                                </div>
                                <div class="option-card-content">
                                    <h4 class="option-card-title">No, I don't need accommodation</h4>
                                    <p class="option-card-description">I have my own accommodation</p>
                                </div>
                            </div>
                        </div>
                    </template>

                    <!-- Accommodation Type Options (only shown after selecting Yes) -->
                    <template x-if="brasov.selected === 'type'">
                        <div>
                            <h3 class="text-center mb-4">When do you need accommodation in Brasov?</h3>
                            <div class="info-box">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>Select when you need accommodation in Brasov</span>
                            </div>

                            <div class="option-cards">
                                <div class="option-card"
                                     :class="{'selected': brasov.type === 'pre'}"
                                     @click="selectBrasovType('pre')">
                                    <div class="option-card-image">
                                        <img src="<?php echo LCI2025_URL; ?>assets/images/before-event.jpg" alt="Pre-Event" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                                    </div>
                                    <div class="option-card-content">
                                        <h4 class="option-card-title">Pre-Event</h4>
                                        <p class="option-card-description">Accommodation before the event</p>
                                    </div>
                                </div>

                                <div class="option-card"
                                     :class="{'selected': brasov.type === 'main'}"
                                     @click="selectBrasovType('main')">
                                    <div class="option-card-image">
                                        <img src="<?php echo LCI2025_URL; ?>assets/images/main-event.jpg" alt="Main Event" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                                    </div>
                                    <div class="option-card-content">
                                        <h4 class="option-card-title">Main Event</h4>
                                        <p class="option-card-description">Accommodation during the event</p>
                                    </div>
                                </div>

                                <div class="option-card"
                                     :class="{'selected': brasov.type === 'post'}"
                                     @click="selectBrasovType('post')">
                                    <div class="option-card-image">
                                        <img src="<?php echo LCI2025_URL; ?>assets/images/after-event.jpg" alt="Post-Event" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                                    </div>
                                    <div class="option-card-content">
                                        <h4 class="option-card-title">Post-Event</h4>
                                        <p class="option-card-description">Accommodation after the event</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Wizard Navigation -->
            <div class="wizard-navigation">
                <button type="button" class="wizard-btn wizard-btn-outline" @click="prevStep()" x-show="currentStep > 1">
                    <i class="fas fa-arrow-left me-2"></i> Back
                </button>
                <a href="?tab=accommodation" class="wizard-btn wizard-btn-outline" x-show="currentStep === 1">
                    <i class="fas fa-times me-2"></i> Cancel
                </a>

                <template x-if="currentStep < totalSteps">
                    <button type="button" class="wizard-btn wizard-btn-primary" @click="nextStep()" :disabled="!isStepValid()">
                        Next <i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </template>
                <template x-if="currentStep === totalSteps">
                    <a href="?tab=accommodation" class="wizard-btn wizard-btn-outline">
                        <i class="fas fa-arrow-left me-2"></i> Back to Accommodation
                    </a>
                </template>
            </div>
        </div>

        <!-- Add to Cart Modal -->
        <div class="wizard-modal-backdrop" :class="{'active': showModal}" @click.self="closeModal()">
            <div class="wizard-modal">
                <div class="wizard-modal-header">
                    <button type="button" class="wizard-modal-close" @click="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="wizard-modal-body">
                    <div class="wizard-modal-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h4 class="wizard-modal-title">Accommodation added to cart!</h4>
                    <p class="wizard-modal-text" x-text="modalProduct?.name + ' has been added to your LCI Accommodation Booking'"></p>
                </div>
                <div class="wizard-modal-footer">
                    <button type="button" class="wizard-btn wizard-btn-primary" @click="closeModal()">
                        Continue Booking
                    </button>
                    <a href="/cart/" class="wizard-btn wizard-btn-primary">
                        <i class="fas fa-shopping-bag me-2"></i> View Bookings
                    </a>
                </div>
                <div class="wizard-modal-progress">
                    <div class="wizard-modal-progress-bar" :style="{ width: modalProgress + '%' }"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Note: AJAX handlers are now defined in includes/ajax-handlers.php

// Get footer
get_footer();
?>
