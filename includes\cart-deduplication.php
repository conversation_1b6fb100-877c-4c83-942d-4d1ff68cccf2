<?php
/**
 * Cart Deduplication Functions
 *
 * Prevents duplicate items in the WooCommerce cart
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Deduplicate cart items
 * This function removes duplicate items from the WooCommerce cart
 */
function lci_deduplicate_cart_items() {
    // Only proceed if WooCommerce is active and cart is loaded
    if (!function_exists('WC') || !WC()->cart) {
        return;
    }

    // Get the cart
    $cart = WC()->cart;

    // If cart is empty, nothing to do
    if ($cart->is_empty()) {
        return;
    }

    // EMERGENCY FIX: Group items by product ID only
    // Store the unique products
    $unique_products = [];
    $processed_product_ids = [];

    // Find unique products by product ID only
    foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
        $product_id = $cart_item['product_id'];
        $variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;
        $variation_attributes = isset($cart_item['variation']) ? $cart_item['variation'] : [];
        $quantity = $cart_item['quantity'];

        // Only add if we haven't processed this product ID yet
        if (!in_array($product_id, $processed_product_ids)) {
            $processed_product_ids[] = $product_id;
            $unique_products[] = [
                'product_id' => $product_id,
                'variation_id' => $variation_id,
                'variation_attributes' => $variation_attributes,
                'quantity' => $quantity
            ];
        }
    }

    // Log how many items were removed
    error_log('EMERGENCY DEDUPLICATION: Keeping ' . count($unique_products) . ' unique products out of ' . count($cart->get_cart()) . ' total items');

    // Only rebuild if we found duplicates
    if (count($unique_products) < count($cart->get_cart())) {
        // Clear the cart
        $cart->empty_cart();

        // Add the unique products back to the cart
        foreach ($unique_products as $product) {
            $cart->add_to_cart(
                $product['product_id'],
                $product['quantity'],
                $product['variation_id'],
                $product['variation_attributes']
            );
        }

        // Log for debugging
        error_log('LCI Cart: Rebuilt cart with ' . count($unique_products) . ' unique items (removed ' .
            (count($cart->get_cart()) - count($unique_products)) . ' duplicates)');
    }
}

/**
 * Hook into WooCommerce to deduplicate cart items
 */
function lci_init_cart_deduplication() {
    // EMERGENCY FIX: Completely rebuild the cart when it's loaded from session
    add_action('woocommerce_cart_loaded_from_session', 'lci_emergency_rebuild_cart_on_load', 5);

    // Deduplicate cart items when cart is loaded (as a backup)
    add_action('woocommerce_cart_loaded_from_session', 'lci_deduplicate_cart_items', 20);

    // Deduplicate cart items before calculating totals
    add_action('woocommerce_before_calculate_totals', 'lci_deduplicate_cart_items', 20);

    // Deduplicate cart items after adding to cart
    add_action('woocommerce_add_to_cart', 'lci_deduplicate_cart_items', 20);

    // Deduplicate cart items when updating cart
    add_action('woocommerce_cart_updated', 'lci_deduplicate_cart_items', 20);

    // Deduplicate cart items when checking out
    add_action('woocommerce_checkout_update_order_review', 'lci_deduplicate_cart_items', 20);
}

/**
 * Emergency fix to completely rebuild the cart when it's loaded from session
 * This prevents the entire cart from being duplicated
 */
function lci_emergency_rebuild_cart_on_load() {
    // Only proceed if WooCommerce is active and cart is loaded
    if (!function_exists('WC') || !WC()->cart) {
        return;
    }

    // Get the cart
    $cart = WC()->cart;

    // If cart is empty, nothing to do
    if ($cart->is_empty()) {
        return;
    }

    // Store unique products by product ID
    $unique_products = [];
    $processed_product_ids = [];

    // Find unique products by product ID
    foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
        $product_id = $cart_item['product_id'];

        // Skip if we've already processed this product ID
        if (in_array($product_id, $processed_product_ids)) {
            continue;
        }

        // Add to processed product IDs
        $processed_product_ids[] = $product_id;

        // Get variation details
        $variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;
        $variation_attributes = isset($cart_item['variation']) ? $cart_item['variation'] : [];
        $quantity = $cart_item['quantity'];

        // Add to unique products
        $unique_products[] = [
            'product_id' => $product_id,
            'variation_id' => $variation_id,
            'variation_attributes' => $variation_attributes,
            'quantity' => $quantity
        ];
    }

    // Only rebuild if we found duplicates
    if (count($unique_products) < count($cart->get_cart())) {
        // Log what we're doing
        error_log('EMERGENCY: Rebuilding cart on load. Found ' . count($cart->get_cart()) . ' items, keeping ' . count($unique_products) . ' unique products.');

        // Remember the cart contents
        $cart_contents = $unique_products;

        // Clear the cart
        $cart->empty_cart();

        // Add the unique products back to the cart
        foreach ($cart_contents as $item) {
            $cart->add_to_cart(
                $item['product_id'],
                $item['quantity'],
                $item['variation_id'],
                $item['variation_attributes']
            );
        }
    }
}

// Initialize cart deduplication
lci_init_cart_deduplication();

/**
 * Prevent adding duplicate items to cart
 */
function lci_prevent_duplicate_add_to_cart($passed, $product_id, $quantity, $variation_id = 0) {
    // Only proceed if WooCommerce is active and cart is loaded
    if (!function_exists('WC') || !WC()->cart) {
        return $passed;
    }

    // Get the cart
    $cart = WC()->cart;

    // If cart is empty, allow adding
    if ($cart->is_empty()) {
        return $passed;
    }

    // Check if product is already in cart
    foreach ($cart->get_cart() as $cart_item) {
        if ($cart_item['product_id'] == $product_id) {
            // Product is already in cart, prevent adding again
            error_log('LCI Cart: Prevented adding duplicate product to cart: ' . $product_id);

            // Return false to prevent adding to cart
            return false;
        }
    }

    // Product is not in cart, allow adding
    return $passed;
}

// Hook into WooCommerce to prevent adding duplicate items
add_filter('woocommerce_add_to_cart_validation', 'lci_prevent_duplicate_add_to_cart', 20, 4);

/**
 * EMERGENCY FIX: Prevent the entire cart from being duplicated
 * This hooks into the WooCommerce session loading process
 */
function lci_prevent_cart_duplication() {
    // Only run this on the regalia shop page
    if (!isset($_GET['tab']) || $_GET['tab'] !== 'regalia-shop') {
        return;
    }

    // Add a filter to intercept the cart data before it's loaded
    add_filter('woocommerce_get_cart_from_session', 'lci_filter_cart_from_session', 1, 1);
}
add_action('init', 'lci_prevent_cart_duplication');

/**
 * Filter the cart data from session to prevent duplication
 */
function lci_filter_cart_from_session($cart_contents) {
    // If cart is empty, nothing to do
    if (empty($cart_contents)) {
        return $cart_contents;
    }

    // Log the original cart contents
    error_log('EMERGENCY: Filtering cart from session. Original cart has ' . count($cart_contents) . ' items.');

    // Store unique products by product ID
    $unique_products = [];
    $processed_product_ids = [];

    // Find unique products by product ID
    foreach ($cart_contents as $cart_item_key => $cart_item) {
        $product_id = $cart_item['product_id'];

        // Skip if we've already processed this product ID
        if (in_array($product_id, $processed_product_ids)) {
            continue;
        }

        // Add to processed product IDs
        $processed_product_ids[] = $product_id;

        // Keep this item
        $unique_products[$cart_item_key] = $cart_item;
    }

    // Log the filtered cart contents
    error_log('EMERGENCY: Filtered cart from session. New cart has ' . count($unique_products) . ' items.');

    return $unique_products;
}
