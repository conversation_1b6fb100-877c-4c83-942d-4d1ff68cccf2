.nav-link.active {
  background-color: #36b1dc !important;
  color: white !important;
  font-weight: 600;
    text-decoration: none !important;
}
.nav-link {
      text-decoration: none !important;
}
.btn-outline-primary {
    background-color: #36b1dc !important;
    color: #fff;
    width: 100%;
    text-transform: uppercase;
    font-weight: 700;
    border: 0px;
    padding: 10px 0 10px 0;
}
.btn-outline-primary:hover {
    background-color: #36b1dc !important;
    color: #fff;
}

/* Global button styles for 2025 UX */
.btn {
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* UX2025 Button Styles */
.btn-ux2025 {
    background-color: #36b1dc !important;
    color: #fff !important;
    border: none;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.btn-ux2025:hover {
    background-color: #2d93b7 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-ux2025:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-ux2025-primary {
    background-color: #36b1dc !important;
    color: #fff !important;
}

.btn-ux2025-outline {
    background-color: transparent !important;
    color: #36b1dc !important;
    border: 1px solid #36b1dc;
}

.btn-ux2025-outline:hover {
    background-color: rgba(54, 177, 220, 0.1) !important;
}

/* Mobile Menu UX2025 Styling */
.mobile-menu-toggle:hover {
  background-color: #2d93b7 !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

.mobile-menu-toggle:active {
  transform: translateY(0);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
}

/* Add animation to the mobile menu */
#lciSidebar.collapsing {
  transition: height 0.35s ease;
}

#lciSidebar.collapse.show {
  animation: fadeInMenu 0.4s ease forwards;
}

@keyframes fadeInMenu {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhance mobile menu items */
@media (max-width: 767.98px) {
  .lci-menu .nav-item {
    margin-bottom: 8px;
  }

  .lci-menu .nav-link {
    border-radius: 8px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
  }

  .lci-menu .nav-link:hover:not(.active) {
    background-color: rgba(54, 177, 220, 0.1);
    border-left-color: #36b1dc;
  }

  .lci-menu .nav-link.active {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateX(5px);
  }

  .lci-menu .nav-link i {
    font-size: 18px;
    width: 24px;
    text-align: center;
  }
}

/* Background color for UX2025 */
.bg-ux2025-primary {
    background-color: #36b1dc !important;
}
.text-primary {
    --bs-text-opacity: 1;
    color: #36b1dc !important;
}
.nav-link:hover {
  background-color: #e0f5fd;
}

.card {
  border-radius: 1.25rem !important;
  margin-top: 1rem !important;
}
.lci-title-dashboard {
    text-transform: uppercase;
}
@media (max-width: 767px) {
  .card .nav-link {
    font-size: 1rem;
    padding: 0.75rem;
  }
}
.lci-menu {
    list-style: none !important;
}
.column_column ul, .column_helper ul, .column_post_content ul, .column_product_short_description ul, .column_visual ul, .elementor-widget-text-editor ul, .icon_box ul, .mfn-acc ul, .mfn-toggle .toggle-content ul, .post-excerpt ul, .the_content_wrapper ul, .ui-tabs-panel ul {
    list-style: none !important;
    margin: 0 0 15px 30px;
}
.nav {
  list-style: none;
  padding-left: 0;
}
.card h4 {
  font-weight: 700;
    text-transform: uppercase;
}

.bg-light {
  background-color: #f8f9fa !important;
}
.mb-4 {
    margin-bottom: 15px !important;
}
.mt-5 {
    margin-top: 15px !important;
}
.badge {
  font-size: 0.85rem;
  padding: 0.4em 0.6em;
  border-radius: 0.5rem;
}
#lciSidebar .nav-item::marker {
    display: none;
}
.lci-info-modern {
  --gap: 1.25rem;
}

.lci-info-line {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f9f9f9;
  border-radius: 0.75rem;
  transition: all 0.2s ease-in-out;
  font-size: 0.95rem;
}

.lci-info-line:hover {
  background-color: #eef8fd;
  transform: scale(1.01);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.03);
}

.lci-info-line i {
  font-size: 1rem;
  width: 22px;
}
input:focus {
  box-shadow: none !important;
}

.input-group-text {
  border-right: 0;
}

.input-group .form-control {
  border-left: 0;
}

.btn:hover {
  opacity: 0.95;
  transition: all 0.2s ease-in-out;
}
.auth-view {
  transition: all 0.3s ease-in-out;
}
.auth-view.d-none {
  opacity: 0;
  height: 0;
  overflow: hidden;
}
.avatar-glow {
  box-shadow: 0 0 12px 2px #36b1dc;
  transition: box-shadow 0.3s ease-in-out;
}

/* Avatar styling */
.avatar-container {
  cursor: pointer;
  overflow: visible;
  border-radius: 50%;
  position: relative;
  width: 90px;
  height: 90px;
  margin: 0 auto 15px;
}

.avatar-image-wrapper {
  position: relative;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #36b1dc;
  box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3);
  z-index: 2;
  transition: all 0.3s ease;
}

.avatar-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* UX2025 Avatar Styling */
.ux2025-avatar {
  position: relative;
}

.avatar-decoration-circle {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #36b1dc;
  border-radius: 50%;
  top: -5px;
  right: 5px;
  z-index: 1;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.avatar-decoration-dot {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #36b1dc;
  border-radius: 50%;
  bottom: 15px;
  left: -5px;
  z-index: 1;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.avatar-container:hover .avatar-image-wrapper {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(54, 177, 220, 0.4);
}

.avatar-container:hover img {
  transform: scale(1.05);
}

.avatar-container:hover .avatar-decoration-circle {
  transform: scale(1.2) translateX(3px);
  opacity: 1;
}

.avatar-container:hover .avatar-decoration-dot {
  transform: scale(1.2) translateX(-3px);
  opacity: 1;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1 !important;
}

.avatar-overlay {
  background-color: rgba(54, 177, 220, 0.7);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
  z-index: 3;
}

.avatar-link {
  display: inline-block;
  text-decoration: none;
}

.avatar-link:focus {
  outline: none;
}

.avatar-link:focus .avatar-container {
  box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.5);
}

/* Upload animation */
@keyframes upload-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.avatar-uploading {
  animation: upload-pulse 1.5s infinite;
}

/* Profile Styles */
#profileTabs .nav-link {
  color: #555;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  margin-right: 0.5rem;
  transition: all 0.2s ease;
}

#profileTabs .nav-link:hover {
  background-color: #f8f9fa;
}

#profileTabs .nav-link.active {
  color: #fff;
  background-color: #36b1dc;
  font-weight: 600;
}

.password-toggle {
  cursor: pointer;
}

.password-toggle:hover {
  color: #36b1dc;
}

.password-strength .progress {
  border-radius: 0.25rem;
  margin-top: 0.25rem;
}

.form-control:focus, .form-control:active {
  border-color: #36b1dc;
}

.input-group-text {
  min-width: 40px;
  justify-content: center;
}