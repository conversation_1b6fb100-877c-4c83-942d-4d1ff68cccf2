/* Profile page styles */

/* Choices.js custom styling */
.choices-outer {
    width: 100%;
    background-color: transparent;
    border: none;
}

.choices-inner {
    background-color: transparent;
    border: none;
    min-height: 38px;
    padding: 0;
}

.choices__list--single {
    padding: 0;
}

.choices__list--dropdown {
    border: 1px solid #e0e0e0;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.choices__list--dropdown .choices__item--selectable {
    padding: 8px 12px;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted {
    background-color: var(--primary-color, #36b1dc);
    color: white;
}

.choices__list--dropdown .choices__item--selectable.is-selected {
    background-color: #f0f0f0;
}

.choices__item--selectable {
    font-size: 14px;
}

.choices[data-type*='select-one'] .choices__inner {
    padding-bottom: 0;
}

.choices[data-type*='select-one']:after {
    border-color: #999 transparent transparent;
    right: 12px;
    top: 50%;
    margin-top: -2.5px;
}

.choices[data-type*='select-one'].is-open:after {
    border-color: transparent transparent #999;
    margin-top: -7.5px;
}

.choices[data-type*='select-one'] .choices__button {
    right: 25px;
    opacity: 0.5;
}

/* Fix for input group */
.input-group .choices {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
}

/* Fix for dropdown positioning */
.choices__list--dropdown {
    z-index: 1000;
}

/* Alpine.js dropdown styling */
.hover-bg-light:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.z-index-dropdown {
    z-index: 1000;
}

.cursor-pointer {
    cursor: pointer;
}

.relative {
    position: relative;
}

/* Dropdown scrollable area */
.dropdown-scrollable {
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
}

.dropdown-scrollable::-webkit-scrollbar {
    width: 6px;
}

.dropdown-scrollable::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.dropdown-scrollable::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px;
}

.dropdown-scrollable::-webkit-scrollbar-thumb:hover {
    background: #999;
}
