<?php

add_action('wp_ajax_nopriv_lci_login', 'lci_handle_login');

function lci_handle_login() {
    $input = json_decode(file_get_contents('php://input'), true);
    $creds = [
        'user_login'    => sanitize_email($input['email']),
        'user_password' => sanitize_text_field($input['password']),
        'remember'      => true,
    ];

    $user = wp_signon($creds, false);

    if (is_wp_error($user)) {
        wp_send_json_error(['message' => 'Invalid credentials.']);
    }

    wp_set_current_user($user->ID);
    wp_set_auth_cookie($user->ID, true);

    wp_send_json_success(['message' => 'Login successful.']);
}
