<?php
/**
 * Regalia Mini Cart Template
 * Clean implementation that shows each product only once
 */
?>
<script>
    // Ensure lci_ajax_object is defined
    if (typeof lci_ajax_object === 'undefined') {
        window.lci_ajax_object = {
            ajax_url: '<?php echo esc_url(admin_url('admin-ajax.php')); ?>',
            nonce: '<?php echo wp_create_nonce('lci_mini_cart_nonce'); ?>'
        };
    }
</script>

<div x-data="{
    open: false,
    items: [],
    loading: false,
    total: '<?php echo esc_attr(WC()->cart->get_cart_total()); ?>',
    count: <?php echo count(lci_get_unique_cart_items(WC()->cart->get_cart())); ?>,
    isInitialized: false,

    init() {
        // Set initialization flag
        this.isInitialized = false;

        // Fetch items on page load
        this.fetchItems();

        // Set initialization flag to true after a short delay
        setTimeout(() => {
            this.isInitialized = true;
        }, 500);

        // Store a reference to this component globally
        if (!window.lciComponents) {
            window.lciComponents = {};
        }
        window.lciComponents.miniCart = this;

        // Create a global function to open the cart
        window.openMiniCart = () => {
            this.openCart();
        };

        // Listen for open-mini-cart event
        window.addEventListener('open-mini-cart', () => {
            this.openCart();
        });

        // Listen for cart updated events
        window.addEventListener('lci:cartUpdated', (event) => {
            this.fetchItems();
        });

        // Also listen for WooCommerce events
        document.body.addEventListener('added_to_cart', () => {
            this.fetchItems();
        });

        document.body.addEventListener('removed_from_cart', () => {
            this.fetchItems();
        });

        document.body.addEventListener('updated_cart_totals', () => {
            this.fetchItems();
        });
    },

    openCart() {
        this.open = true;
        this.fetchItems();

        // Add modal-open class to body
        document.body.classList.add('modal-open');

        // Prevent scrolling
        document.body.style.overflow = 'hidden';
    },

    closeCart() {
        this.open = false;

        // Remove modal-open class from body
        document.body.classList.remove('modal-open');

        // Restore scrolling
        document.body.style.removeProperty('overflow');
    },

    fetchItems() {
        // Prevent duplicate fetches
        if (this.loading) {
            return;
        }

        this.loading = true;

        // Fetch cart items
        fetch(lci_ajax_object.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=lci_get_mini_cart_items_ajax&nonce=' + lci_ajax_object.nonce
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update cart data
                this.items = data.data.items || [];
                this.total = data.data.total || '0.00 €';
                this.count = data.data.count || 0;

                // Update fundraising message
                const fundraisingMessage = document.getElementById('lci-fundraising-message');
                if (fundraisingMessage) {
                    // Check if there are category 22 products and fundraising amount
                    if (data.data.has_category_22 && data.data.fundraising_amount > 0) {
                        // Show the message
                        fundraisingMessage.style.display = '';

                        // Update the message content if provided
                        if (data.data.fundraising_message) {
                            fundraisingMessage.querySelector('.fundraising-message-text').innerHTML =
                                data.data.fundraising_message;
                        }
                    } else {
                        // Hide the message
                        fundraisingMessage.style.display = 'none';
                    }
                }
            } else {
                this.items = [];
            }

            this.loading = false;
        })
        .catch(error => {
            // Console error removed
            this.items = [];
            this.loading = false;
        });
    },

    removeItem(key) {
        // Find the item to remove
        const itemToRemove = this.items.find(item => item.key === key);
        if (!itemToRemove) return;

        // Remove the item from the array
        const itemIndex = this.items.findIndex(item => item.key === key);
        if (itemIndex !== -1) {
            this.items.splice(itemIndex, 1);
        }

        // Update count
        this.count = this.items.length;

        // If all items are removed, set total to 0
        if (this.items.length === 0) {
            this.total = '0.00 €';

            // Hide fundraising message
            const fundraisingMessage = document.getElementById('lci-fundraising-message');
            if (fundraisingMessage) {
                fundraisingMessage.style.display = 'none';
            }
        }

        // Make the AJAX call to remove the item from the server
        fetch(lci_ajax_object.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=lci_remove_cart_item&nonce=' + lci_ajax_object.nonce + '&cart_item_key=' + key
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the total with the accurate value from the server
                this.total = data.cart_total || '0.00 €';

                // Fetch the latest cart data
                this.fetchItems();

                // Trigger events
                document.dispatchEvent(new CustomEvent('lci:cart-item-removed', {
                    detail: { key: key }
                }));

                jQuery(document.body).trigger('removed_from_cart');
                jQuery(document.body).trigger('updated_cart_totals');
            } else {
                this.fetchItems();
            }
        })
        .catch(error => {
            this.fetchItems();
        });
    }
}" x-cloak id="mini-cart-container" class="lci-mini-cart-container">
    <!-- Mini Cart Button -->
    <button
        id="mini-cart-button"
        @click="openCart"
        class="btn btn-primary d-flex align-items-center"
        style="color: #fff !important;"
    >
        <i class="fas fa-shopping-bag me-2" style="color: #fff !important;"></i>
        <span class="mini-cart-button-text" x-html="total"></span>
        <template x-if="count > 0">
            <span class="badge rounded-pill ms-2" style="background-color: #fab33a;" x-text="count"></span>
        </template>
    </button>

    <!-- Mini Cart Modal -->
    <div
        x-show="open"
        x-transition:enter="fade-in"
        x-transition:leave="fade-out"
        class="lci-modal-backdrop"
        @click.self="closeCart"
        style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center; overflow-y: auto; padding: 20px 0;"
    >
        <div
            class="lci-modal lci-mini-cart-modal"
            x-transition:enter="slide-in"
            x-transition:leave="slide-out"
            style="background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.12); max-width: 700px; width: 95%;"
        >
            <div class="lci-modal-header" style="background-color: white; box-shadow: 0 4px 6px rgba(0,0,0,0.05); position: relative;">
                <h2 class="lci-modal-title" style="color: #36b1dc; font-weight: 600; margin: 0 auto; padding: 1rem 0;">
                    <i class="fas fa-shopping-bag me-2"></i> LCI AGM 2025
                </h2>
                <button @click="closeCart" class="lci-modal-close" style="position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; font-size: 1.25rem; color: #6c757d; transition: all 0.2s ease;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="lci-modal-body p-0">
                <div class="mini-cart-items-container">
                    <!-- Loading state -->
                    <template x-if="loading">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading your items...</p>
                        </div>
                    </template>

                    <!-- Empty cart -->
                    <template x-if="!loading && items.length === 0">
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-bag text-muted" style="font-size: 3rem;"></i>
                            <p class="mt-3">Your goodies bag is empty</p>
                        </div>
                    </template>

                    <!-- Cart items -->
                    <template x-if="!loading && items.length > 0">
                        <div class="cart-items-list" style="max-height: 300px; overflow-y: auto;">
                            <template x-for="(item, index) in items" :key="item.key">
                                <div class="mini-cart-item d-flex align-items-center p-3 border-bottom">
                                    <!-- Product Image -->
                                    <div class="mini-cart-item-image me-2">
                                        <img :src="item.image" :alt="item.name" class="img-fluid rounded" style="width: 50px; height: 50px; object-fit: contain;">
                                    </div>

                                    <!-- Quantity Badge -->
                                    <div class="mini-cart-item-quantity-container me-2">
                                        <span class="mini-cart-item-quantity badge bg-light text-dark">
                                            <span x-text="item.quantity"></span>
                                            <span> ×</span>
                                        </span>
                                    </div>

                                    <!-- Product Details -->
                                    <div class="mini-cart-item-details flex-grow-1 d-flex align-items-center justify-content-between">
                                        <div class="mini-cart-item-name-container">
                                            <span class="mini-cart-item-name" x-text="item.name"></span>
                                        </div>
                                        <div class="mini-cart-item-price-container">
                                            <span class="mini-cart-item-price text-primary fw-bold" x-html="item.price"></span>
                                        </div>
                                    </div>

                                    <!-- Remove Button -->
                                    <div class="mini-cart-item-remove ms-2">
                                        <a href="#" class="remove-cart-item" @click.prevent="removeItem(item.key)" style="color: #36b1dc; display: inline-flex; align-items: center; justify-content: center; width: 24px; height: 24px; border-radius: 50%; transition: all 0.2s ease;">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Fundraising Message -->
            <?php
            // Only display the fundraising message if there are category 22 products in the cart
            lci_display_fundraising_message();
            ?>

            <div class="lci-modal-footer">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div class="mini-cart-total-container">
                        Total: <span class="mini-cart-modal-total" x-html="total"></span>
                    </div>
                    <div>
                        <button @click="closeCart" class="lci-btn lci-btn-secondary me-2">Continue Shopping</button>
                        <a href="<?php echo esc_url(wc_get_checkout_url()); ?>" class="lci-btn lci-btn-primary">
                            <i class="fas fa-credit-card me-1"></i> Checkout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
