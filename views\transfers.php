<?php
/**
 * Transfers view for LCI 2025 Dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Ensure user is logged in
if (!$user_id) {
    // Redirect to login page if not logged in
    wp_redirect(wp_login_url(add_query_arg(array(), $_SERVER['REQUEST_URI'])));
    exit;
}
?>

<div class="transfers-container">
    <!-- Header Section - 2025 UX/UI Style -->
    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 40px 20px; position: relative; overflow: hidden; border-radius: 0 0 30px 30px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2); margin-bottom: 40px;">
        <!-- Animated background elements -->
        <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%); border-radius: 50%; animation: pulse 8s infinite alternate;"></div>
        <div class="position-absolute" style="bottom: -30px; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%); border-radius: 50%; animation: float 12s infinite alternate;"></div>
        <div class="position-absolute" style="top: 20%; right: 20%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%); border-radius: 50%; animation: float 7s infinite alternate-reverse;"></div>

        <!-- Glassmorphism overlay -->
        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>

        <div class="container position-relative" style="z-index: 2;">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <!-- Modern 3D icon with shadow and glow -->
                    <div style="background: rgba(255, 255, 255, 0.2); width: 60px; height: 60px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); transform: perspective(500px) rotateX(10deg); border: 1px solid rgba(255, 255, 255, 0.3);">
                        <div style="position: relative;">
                            <i class="fas fa-shuttle-van" style="color: white; font-size: 28px; filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.8), transparent 70%); filter: blur(8px); opacity: 0.5; animation: glow 3s infinite alternate;"></div>
                        </div>
                    </div>

                    <!-- Typography with modern styling -->
                    <div>
                        <h1 style="color: white; font-size: 28px; font-weight: 800; margin: 0; letter-spacing: -0.5px; text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">Airport & City Transfers</h1>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; margin: 0; margin-top: 4px; font-weight: 300; letter-spacing: 0.5px;">Book your transportation to and from Brasov</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Wizard Section - 2025 UX/UI Style -->
    <div class="transfers-wizard-container">
        <!-- Decorative elements -->
        <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
        <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

        <!-- Pass PHP variables to JavaScript -->
        <script>
            // Pass the has_main_pretour variable to JavaScript
            var hasMainPretour = <?php echo isset($has_main_pretour) && $has_main_pretour ? 'true' : 'false'; ?>;

            // Create a dedicated AJAX object for transfers to avoid conflicts
            var lci_transfers_ajax = {
                ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
                nonce: '<?php echo wp_create_nonce('lci_transfers_nonce'); ?>'
            };

            // Ensure transferWizardData function is available
            if (typeof window.transferWizardData !== 'function') {
                console.log('Loading transferWizardData function directly');
                window.transferWizardData = function() {
                    return {
                        // Current step (1-6)
                        currentStep: 1,

                        // Step 1: Airport Selection
                        selectedAirport: '',

                        // Check if user has Main Pretour
                        hasMainPretour: typeof hasMainPretour !== 'undefined' ? hasMainPretour : false,

                        // Step 2: Transfer Type
                        transferType: '',
                        isRoundTrip: false,
                        isArrivalOnly: false,
                        isDepartureOnly: false,

                        // Step 3: Arrival Details
                        arrivalDate: '',
                        arrivalTime: '',
                        arrivalFlight: '',

                        // Step 4: Departure Details (for round trips)
                        departureDate: '',
                        departureTime: '',
                        departureFlight: '',

                        // Step 5: Additional Details
                        specialRequests: '',

                        // Step 6: Cart Summary
                        products: [],
                        loading: false,
                        errors: {}, // Initialize as an empty object, not null

                        // Initialize
                        init() {
                            console.log('Initializing transfer wizard with fresh state');

                            // Always start from step 1 on page load/refresh
                            this.currentStep = 1;

                            // Clear any saved state from localStorage
                            localStorage.removeItem('transferWizardState');

                            // Ensure all properties are properly initialized
                            if (typeof this.errors === 'undefined' || this.errors === null) {
                                console.log('Initializing errors object');
                                this.errors = {};
                            }

                            if (typeof this.products === 'undefined' || this.products === null) {
                                console.log('Initializing products array');
                                this.products = [];
                            }

                            // Reset all flags
                            this.isRoundTrip = false;
                            this.isArrivalOnly = false;
                            this.isDepartureOnly = false;

                            // Clear all form fields
                            this.selectedAirport = '';
                            this.transferType = '';
                            this.arrivalDate = '';
                            this.arrivalTime = '';
                            this.arrivalFlight = '';
                            this.departureDate = '';
                            this.departureTime = '';
                            this.departureFlight = '';
                            this.specialRequests = '';

                            console.log('Wizard initialized to step 1 with all fields cleared');

                            // Force clear all flatpickr instances first
                            const destroyAllPickers = () => {
                                const elements = ['arrival-date', 'departure-date', 'arrival-time', 'departure-time'];
                                elements.forEach(id => {
                                    const element = document.getElementById(id);
                                    if (element && element._flatpickr) {
                                        element._flatpickr.destroy();
                                        console.log(`Destroyed existing flatpickr instance for ${id}`);
                                    }
                                });
                            };

                            destroyAllPickers();

                            // Clear input fields directly
                            const clearInputFields = () => {
                                const fields = [
                                    'arrival-date', 'departure-date',
                                    'arrival-time', 'departure-time',
                                    'arrival-flight', 'departure-flight',
                                    'special-requests'
                                ];

                                fields.forEach(id => {
                                    const element = document.getElementById(id);
                                    if (element) {
                                        element.value = '';
                                        // Also clear any data attributes that might store values
                                        if (element.dataset) {
                                            delete element.dataset.date;
                                            delete element.dataset.time;
                                        }
                                        console.log(`Cleared input field: ${id}`);
                                    }
                                });
                            };

                            // Clear input fields immediately
                            clearInputFields();

                            // Initialize date pickers after a short delay to ensure DOM is ready
                            setTimeout(() => {
                                this.initDatePickers();

                                // Clear input fields again after date pickers are initialized
                                // This ensures flatpickr doesn't override our cleared values
                                clearInputFields();

                                // Double-check that model values are cleared
                                this.arrivalDate = '';
                                this.arrivalTime = '';
                                this.departureDate = '';
                                this.departureTime = '';

                                console.log('All date/time fields have been reset');
                            }, 100);

                            // Add page unload event to clear state
                            window.addEventListener('beforeunload', () => {
                                localStorage.removeItem('transferWizardState');
                            });

                            console.log('Transfer wizard initialized with clean state');
                        },



                        // Initialize Flatpickr date and time pickers
                        initDatePickers() {
                            console.log('Initializing date pickers');
                            if (typeof flatpickr === 'function') {
                                // First, destroy any existing flatpickr instances to ensure clean initialization
                                const destroyExistingPickers = () => {
                                    const elements = ['arrival-date', 'departure-date', 'arrival-time', 'departure-time'];
                                    elements.forEach(id => {
                                        const element = document.getElementById(id);
                                        if (element && element._flatpickr) {
                                            element._flatpickr.destroy();
                                            console.log(`Destroyed existing flatpickr instance for ${id}`);
                                        }
                                    });
                                };

                                destroyExistingPickers();

                                // Base date picker configuration
                                const baseDateConfig = {
                                    dateFormat: "Y-m-d",
                                    disableMobile: false,
                                    static: true,
                                    // Don't set a default date
                                    defaultDate: undefined
                                };

                                // Arrival date picker configuration (August 12-20)
                                const arrivalDateConfig = {
                                    ...baseDateConfig,
                                    minDate: "2025-08-12",
                                    maxDate: "2025-08-20",
                                    onChange: (_, dateStr) => {
                                        this.arrivalDate = dateStr;
                                        console.log('Arrival date set to:', dateStr);
                                    }
                                };

                                // Departure date picker configuration (August 24-27)
                                const departureDateConfig = {
                                    ...baseDateConfig,
                                    minDate: "2025-08-24",
                                    maxDate: "2025-08-27",
                                    onChange: (_, dateStr) => {
                                        this.departureDate = dateStr;
                                        console.log('Departure date set to:', dateStr);
                                    }
                                };

                                // Time picker configuration
                                const timeConfig = {
                                    enableTime: true,
                                    noCalendar: true,
                                    dateFormat: "H:i",
                                    time_24hr: true,
                                    minuteIncrement: 15,
                                    // Don't set a default time
                                    defaultDate: undefined,
                                    onChange: (_, timeStr, instance) => {
                                        if (instance.element.id === 'arrival-time') {
                                            this.arrivalTime = timeStr;
                                            console.log('Arrival time set to:', timeStr);
                                        } else if (instance.element.id === 'departure-time') {
                                            this.departureTime = timeStr;
                                            console.log('Departure time set to:', timeStr);
                                        }
                                    }
                                };

                                // Initialize date pickers with explicit clearing
                                if (document.getElementById('arrival-date')) {
                                    console.log('Initializing arrival date picker with range: Aug 12-20');
                                    const arrivalPicker = flatpickr('#arrival-date', {
                                        ...arrivalDateConfig,
                                        onReady: (selectedDates, dateStr, instance) => {
                                            // Force clear on initialization
                                            instance.clear();
                                            this.arrivalDate = '';
                                            console.log('Arrival date picker ready and cleared');
                                        }
                                    });
                                    // Clear the input value
                                    document.getElementById('arrival-date').value = '';
                                }

                                if (document.getElementById('departure-date')) {
                                    console.log('Initializing departure date picker with range: Aug 24-27');
                                    const departurePicker = flatpickr('#departure-date', {
                                        ...departureDateConfig,
                                        onReady: (selectedDates, dateStr, instance) => {
                                            // Force clear on initialization
                                            instance.clear();
                                            this.departureDate = '';
                                            console.log('Departure date picker ready and cleared');
                                        }
                                    });
                                    // Clear the input value
                                    document.getElementById('departure-date').value = '';
                                }

                                // Initialize time pickers
                                if (document.getElementById('arrival-time')) {
                                    console.log('Initializing arrival time picker');
                                    const arrivalTimePicker = flatpickr('#arrival-time', {
                                        ...timeConfig,
                                        onReady: (selectedDates, dateStr, instance) => {
                                            // Force clear on initialization
                                            instance.clear();
                                            this.arrivalTime = '';
                                            console.log('Arrival time picker ready and cleared');
                                        }
                                    });
                                    // Clear the input value
                                    document.getElementById('arrival-time').value = '';
                                }

                                if (document.getElementById('departure-time')) {
                                    console.log('Initializing departure time picker');
                                    const departureTimePicker = flatpickr('#departure-time', {
                                        ...timeConfig,
                                        onReady: (selectedDates, dateStr, instance) => {
                                            // Force clear on initialization
                                            instance.clear();
                                            this.departureTime = '';
                                            console.log('Departure time picker ready and cleared');
                                        }
                                    });
                                    // Clear the input value
                                    document.getElementById('departure-time').value = '';
                                }
                            } else {
                                console.error('Flatpickr not available');
                            }
                        },

                        // Navigation functions
                        nextStep() {
                            console.log('Next step clicked. Current step:', this.currentStep);

                            // Basic validation for each step
                            let isValid = true;

                            if (this.currentStep === 1 && !this.selectedAirport) {
                                console.log('Step 1 validation failed: No airport selected');
                                this.errors.selectedAirport = 'Please select your departure airport';
                                isValid = false;
                            }

                            if (this.currentStep === 2 && !this.transferType) {
                                console.log('Step 2 validation failed: No transfer type selected');
                                this.errors.transferType = 'Please select your transfer type';
                                isValid = false;
                            }

                            if (this.currentStep === 3) {
                                // Clear previous errors
                                delete this.errors.arrivalDate;
                                delete this.errors.arrivalTime;
                                delete this.errors.arrivalFlight;

                                // Log current values for debugging
                                console.log('STEP 3 VALIDATION:');
                                console.log('arrivalDate:', this.arrivalDate);
                                console.log('arrivalTime:', this.arrivalTime);
                                console.log('arrivalFlight:', this.arrivalFlight);
                                console.log('isDepartureOnly:', this.isDepartureOnly);
                                console.log('isArrivalOnly:', this.isArrivalOnly);
                                console.log('isRoundTrip:', this.isRoundTrip);

                                // Always validate arrival details in step 3
                                if (!this.arrivalDate) {
                                    console.log('Step 3 validation failed: No arrival date');
                                    this.errors.arrivalDate = 'Please select an arrival date';
                                    isValid = false;
                                }

                                if (!this.arrivalTime) {
                                    console.log('Step 3 validation failed: No arrival time');
                                    this.errors.arrivalTime = 'Please select an arrival time';
                                    isValid = false;
                                }

                                // Flight number is optional, so no validation needed

                                console.log('Step 3 validation result:', isValid);
                            }

                            // Add validation for step 4 (departure details for round trips)
                            if (this.currentStep === 4) {
                                // Clear previous errors
                                delete this.errors.departureDate;
                                delete this.errors.departureTime;
                                delete this.errors.departureFlight;

                                // Log current values for debugging
                                console.log('departureDate:', this.departureDate);
                                console.log('departureTime:', this.departureTime);
                                console.log('departureFlight:', this.departureFlight);

                                if (!this.departureDate) {
                                    console.log('Step 4 validation failed: No departure date');
                                    this.errors.departureDate = 'Please select a departure date';
                                    isValid = false;
                                }

                                if (!this.departureTime) {
                                    console.log('Step 4 validation failed: No departure time');
                                    this.errors.departureTime = 'Please select a departure time';
                                    isValid = false;
                                }

                                // Flight number is optional, so no validation needed

                                console.log('Step 4 validation result:', isValid);
                            }

                            if (isValid) {
                                console.log('Validation passed, moving to next step');

                                // Log the current state of the flags for debugging
                                if (this.currentStep === 2) {
                                    console.log('Current transfer type flags:');
                                    console.log('isRoundTrip:', this.isRoundTrip);
                                    console.log('isArrivalOnly:', this.isArrivalOnly);
                                    console.log('isDepartureOnly:', this.isDepartureOnly);
                                }

                                // Always proceed to the next step in sequence
                                // No more skipping steps - we'll follow the wizard flow step by step
                                console.log('BEFORE STEP CHANGE: Current step =', this.currentStep);
                                const oldStep = this.currentStep;
                                this.currentStep++;
                                console.log('AFTER STEP CHANGE: Old step =', oldStep, 'New step =', this.currentStep);

                                // Force a redraw by triggering a small timeout
                                setTimeout(() => {
                                    console.log('AFTER TIMEOUT: Current step =', this.currentStep);
                                }, 10);

                                console.log('New current step:', this.currentStep);
                            } else {
                                console.log('Validation failed, staying on current step');
                            }
                        },

                        prevStep() {
                            console.log('Previous step clicked. Current step:', this.currentStep);

                            // Always go back one step at a time - no skipping
                            this.currentStep--;

                            console.log('Moving to previous step in sequence:', this.currentStep);
                        },

                        // Add to cart function
                        addToCart() {
                            console.log('Add to cart clicked');

                            // Validate product selection
                            const selectedProducts = this.products.filter(p => p.selected);

                            if (!selectedProducts.length) {
                                console.log('No products selected');
                                this.errors.products = 'Please select at least one transfer option';
                                return;
                            }

                            console.log('Selected products:', selectedProducts);
                            alert('This is a placeholder for the addToCart function. In a real implementation, this would add the selected products to the cart.');
                        },

                        // Debug function to help diagnose issues
                        debugState() {
                            const state = {
                                currentStep: this.currentStep,
                                transferType: this.transferType,
                                isRoundTrip: this.isRoundTrip,
                                isDepartureOnly: this.isDepartureOnly,
                                isArrivalOnly: this.isArrivalOnly,
                                arrivalDate: this.arrivalDate,
                                arrivalTime: this.arrivalTime,
                                departureDate: this.departureDate,
                                departureTime: this.departureTime
                            };

                            console.log('CURRENT STATE:', state);
                            alert('Current state logged to console. Check browser developer tools.');
                        },

                        // Reset wizard function
                        resetWizard() {
                            console.log('Resetting wizard...');

                            // Show confirmation dialog
                            if (!confirm('Are you sure you want to reset the wizard? All your selections and entered information will be cleared.')) {
                                console.log('Reset cancelled by user');
                                return;
                            }

                            // Clear all fields and selections
                            this.currentStep = 1;
                            this.selectedAirport = '';
                            this.transferType = '';
                            this.isRoundTrip = false;
                            this.isArrivalOnly = false;
                            this.isDepartureOnly = false;
                            this.arrivalDate = '';
                            this.arrivalTime = '';
                            this.arrivalFlight = '';
                            this.departureDate = '';
                            this.departureTime = '';
                            this.departureFlight = '';
                            this.specialRequests = '';
                            this.products = [];
                            this.errors = {};

                            // Clear localStorage
                            localStorage.removeItem('transferWizardState');

                            // Reinitialize date pickers
                            setTimeout(() => {
                                this.initDatePickers();
                            }, 100);

                            // Show success message
                            alert('Wizard has been reset successfully!');

                            console.log('Wizard reset complete');

                            // Scroll to top
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                        }
                    };
                };
            }
        </script>

        <!-- Flatpickr for date/time picker -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

        <style>
            /* Custom styles for Flatpickr */
            .flatpickr-calendar {
                background: #fff;
                border-radius: 15px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(54, 177, 220, 0.2);
                font-family: inherit;
            }

            .flatpickr-day.selected,
            .flatpickr-day.startRange,
            .flatpickr-day.endRange,
            .flatpickr-day.selected.inRange,
            .flatpickr-day.startRange.inRange,
            .flatpickr-day.endRange.inRange,
            .flatpickr-day.selected:focus,
            .flatpickr-day.startRange:focus,
            .flatpickr-day.endRange:focus,
            .flatpickr-day.selected:hover,
            .flatpickr-day.startRange:hover,
            .flatpickr-day.endRange:hover,
            .flatpickr-day.selected.prevMonthDay,
            .flatpickr-day.startRange.prevMonthDay,
            .flatpickr-day.endRange.prevMonthDay,
            .flatpickr-day.selected.nextMonthDay,
            .flatpickr-day.startRange.nextMonthDay,
            .flatpickr-day.endRange.nextMonthDay {
                background: #36b1dc;
                border-color: #36b1dc;
            }

            .flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),
            .flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),
            .flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
                box-shadow: -10px 0 0 #36b1dc;
            }

            .flatpickr-time input:hover,
            .flatpickr-time .flatpickr-am-pm:hover,
            .flatpickr-time input:focus,
            .flatpickr-time .flatpickr-am-pm:focus {
                background: #f3f9fd;
            }

            .flatpickr-months {
                padding-top: 10px;
            }

            .flatpickr-months .flatpickr-month {
                height: 40px;
            }

            .flatpickr-current-month {
                padding-top: 5px;
            }

            .flatpickr-monthDropdown-months {
                border-radius: 5px;
                padding: 5px;
            }

            .numInputWrapper span {
                border-radius: 3px;
            }

            .flatpickr-day {
                border-radius: 5px;
            }

            .flatpickr-day.today {
                border-color: #36b1dc;
            }

            .flatpickr-day.today:hover,
            .flatpickr-day.today:focus {
                background: #f3f9fd;
                border-color: #36b1dc;
            }
        </style>

        <!-- Alpine.js Initialization -->
        <script>
            // Check if transferWizardData is available
            if (typeof window.transferWizardData === 'function') {
                console.log('transferWizardData function is available');
            } else {
                console.error('transferWizardData function is still not defined');
            }
        </script>

        <!-- Header with title, reset button, and mini cart -->
        <div class="d-flex justify-content-between align-items-center mb-4 position-relative" style="z-index: 1;">
            <div class="d-flex align-items-center">
                <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                    <i class="fas fa-shuttle-van" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                </div>
                <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Book Your Transfer</h3>
            </div>

            <div class="d-flex align-items-center">
                <!-- Mini Cart Button -->
                <div class="mini-cart-wrapper">
                    <?php echo lci_get_mini_cart_html(); ?>
                </div>
            </div>
        </div>

        <!-- Transfer Wizard -->
        <div id="transfers-wizard"
             x-data="transferWizardData()"
             x-init="$data.init ? $data.init() : console.error('init method not found')"
             @reset-wizard.window="$data.resetWizard ? $data.resetWizard() : console.error('resetWizard method not found')"
             @load.window="$data.currentStep = 1">
            <!-- Modern 2025 Progress Bar -->
            <div class="transfers-wizard-progress-2025">
                <!-- Progress Indicator -->
                <div class="transfers-progress-track">
                    <div class="transfers-progress-fill" :style="'width: ' + ((currentStep - 1) / 5 * 100) + '%'"></div>
                </div>

                <!-- Step Indicators -->
                <div class="transfers-steps-container">
                    <div class="transfers-step"
                         :class="{ 'active': currentStep >= 1, 'completed': currentStep > 1 }"
                         @click="currentStep > 1 ? currentStep = 1 : null">
                        <div class="transfers-step-icon">
                            <i class="fas fa-plane" x-show="currentStep > 1"></i>
                            <span x-show="currentStep <= 1">1</span>
                        </div>
                        <div class="transfers-step-label">Airport</div>
                    </div>

                    <div class="transfers-step"
                         :class="{ 'active': currentStep >= 2, 'completed': currentStep > 2 }"
                         @click="currentStep > 2 ? currentStep = 2 : null">
                        <div class="transfers-step-icon">
                            <i class="fas fa-shuttle-van" x-show="currentStep > 2"></i>
                            <span x-show="currentStep <= 2">2</span>
                        </div>
                        <div class="transfers-step-label">Transfer</div>
                    </div>

                    <div class="transfers-step"
                         :class="{ 'active': currentStep >= 3, 'completed': currentStep > 3 }"
                         @click="currentStep > 3 ? currentStep = 3 : null">
                        <div class="transfers-step-icon">
                            <i class="fas fa-plane-arrival" x-show="currentStep > 3"></i>
                            <span x-show="currentStep <= 3">3</span>
                        </div>
                        <div class="transfers-step-label">Arrival</div>
                    </div>

                    <div class="transfers-step"
                         :class="{ 'active': currentStep >= 4, 'completed': currentStep > 4 }"
                         @click="currentStep > 4 ? currentStep = 4 : null">
                        <div class="transfers-step-icon">
                            <i class="fas fa-plane-departure" x-show="currentStep > 4"></i>
                            <span x-show="currentStep <= 4">4</span>
                        </div>
                        <div class="transfers-step-label">Departure</div>
                    </div>

                    <div class="transfers-step"
                         :class="{ 'active': currentStep >= 5, 'completed': currentStep > 5 }"
                         @click="currentStep > 5 ? currentStep = 5 : null">
                        <div class="transfers-step-icon">
                            <i class="fas fa-clipboard-list" x-show="currentStep > 5"></i>
                            <span x-show="currentStep <= 5">5</span>
                        </div>
                        <div class="transfers-step-label">Details</div>
                    </div>

                    <div class="transfers-step"
                         :class="{ 'active': currentStep >= 6 }">
                        <div class="transfers-step-icon">
                            <span>6</span>
                        </div>
                        <div class="transfers-step-label">Book</div>
                    </div>
                </div>
            </div>

            <!-- Wizard Content -->
            <div class="transfers-wizard-content">
                <!-- Step 1: Airport Selection -->
                <div class="transfers-wizard-step-content" :class="{ 'active': currentStep === 1 }">
                    <div class="section-title-2025 mb-4">
                        <div class="section-title-icon">
                            <i class="fas fa-plane-departure"></i>
                        </div>
                        <div class="section-title-content">
                            <h4>Select Your Departure Airport</h4>
                            <p class="section-subtitle">Choose where your journey begins</p>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div class="alert alert-danger" x-show="errors.selectedAirport" x-text="errors.selectedAirport"></div>

                    <div class="transfers-type-grid">
                        <!-- Option 1: Bucharest Otopeni Airport -->
                        <div class="transfers-type-card"
                             :class="{ 'selected': $data.selectedAirport === 'bucharest' }"
                             @click="$data.selectedAirport = 'bucharest'">
                            <div class="transfers-type-icon">
                                <i class="fas fa-plane"></i>
                            </div>
                            <div class="transfers-type-content">
                                <div class="transfers-type-title">Bucharest Otopeni Airport</div>
                                <div class="transfers-type-description">International flights arrive at Bucharest Henri Coandă International Airport (OTP).</div>
                            </div>
                        </div>

                        <!-- Option 2: Brasov Airport -->
                        <div class="transfers-type-card"
                             :class="{ 'selected': $data.selectedAirport === 'brasov' }"
                             @click="$data.selectedAirport = 'brasov'">
                            <div class="transfers-type-icon">
                                <i class="fas fa-plane"></i>
                            </div>
                            <div class="transfers-type-content">
                                <div class="transfers-type-title">Brasov Airport</div>
                                <div class="transfers-type-description">Brasov International Airport (BRV) serves domestic and some international flights.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="transfers-wizard-buttons">
                        <div></div> <!-- Empty div for spacing -->
                        <button type="button" class="transfers-btn transfers-btn-primary" @click="$data.nextStep()">
                            <i class="fas fa-arrow-right text-white"></i> <span class="text-white">Continue</span>
                        </button>
                    </div>
                </div>

                <!-- Step 2: Transfer Type -->
                <div class="transfers-wizard-step-content" :class="{ 'active': currentStep === 2 }">
                    <div class="section-title-2025 mb-4">
                        <div class="section-title-icon">
                            <i class="fas fa-shuttle-van"></i>
                        </div>
                        <div class="section-title-content">
                            <h4>Select Your Transfer Type</h4>
                            <p class="section-subtitle">Choose how you'd like to travel</p>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div class="alert alert-danger" x-show="errors.transferType" x-text="errors.transferType"></div>

                    <div class="transfers-type-grid">
                        <!-- Bucharest Airport Options -->
                        <template x-if="$data.selectedAirport === 'bucharest'">
                            <div class="row">
                                <!-- Option 1: Bucharest to Main Pretour Hotel (Only shown if user has Main Pretour) -->
                                <div class="col-md-12 mb-4" x-show="hasMainPretour">
                                    <div class="transfers-type-card"
                                         :class="{ 'selected': $data.transferType === 'bucharest_to_pretour_hotel' }"
                                         @click="$data.transferType = 'bucharest_to_pretour_hotel'; $data.isRoundTrip = false; $data.isDepartureOnly = false; $data.isArrivalOnly = true;">
                                        <div class="transfers-type-icon">
                                            <i class="fas fa-hotel"></i>
                                        </div>
                                        <div class="transfers-type-content">
                                            <div class="transfers-type-title">Bucharest Airport to Main Pretour Hotel</div>
                                            <div class="transfers-type-description">One-way transfer from Bucharest Otopeni Airport directly to your Main Pretour hotel.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Option 2: Bucharest to Brasov Main Event Hotel (One-way) -->
                                <div class="col-md-12 mb-4">
                                    <div class="transfers-type-card"
                                         :class="{ 'selected': $data.transferType === 'bucharest_to_brasov' }"
                                         @click="$data.transferType = 'bucharest_to_brasov'; $data.isRoundTrip = false; $data.isDepartureOnly = false; $data.isArrivalOnly = true;">
                                        <div class="transfers-type-icon">
                                            <i class="fas fa-plane-arrival"></i>
                                        </div>
                                        <div class="transfers-type-content">
                                            <div class="transfers-type-title">Bucharest Airport to Brasov (One-way)</div>
                                            <div class="transfers-type-description">One-way transfer from Bucharest Otopeni Airport to your Main Event hotel in Brasov.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Option 3: Brasov to Bucharest Airport (One-way) -->
                                <div class="col-md-12 mb-4">
                                    <div class="transfers-type-card"
                                         :class="{ 'selected': $data.transferType === 'brasov_to_bucharest' }"
                                         @click="$data.transferType = 'brasov_to_bucharest'; $data.isRoundTrip = false; $data.isDepartureOnly = true; $data.isArrivalOnly = false;">
                                        <div class="transfers-type-icon">
                                            <i class="fas fa-plane-departure"></i>
                                        </div>
                                        <div class="transfers-type-content">
                                            <div class="transfers-type-title">Brasov to Bucharest Airport (One-way)</div>
                                            <div class="transfers-type-description">One-way transfer from your Main Event hotel in Brasov to Bucharest Otopeni Airport.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Option 4: Bucharest to Brasov (Round Trip) -->
                                <div class="col-md-12 mb-4">
                                    <div class="transfers-type-card"
                                         :class="{ 'selected': $data.transferType === 'bucharest_to_brasov_round' }"
                                         @click="$data.transferType = 'bucharest_to_brasov_round'; $data.isRoundTrip = true">
                                        <div class="transfers-type-icon">
                                            <i class="fas fa-exchange-alt"></i>
                                        </div>
                                        <div class="transfers-type-content">
                                            <div class="transfers-type-title">Bucharest Airport ↔ Brasov (Round Trip)</div>
                                            <div class="transfers-type-description">Round-trip transfer between Bucharest Otopeni Airport and your Main Event hotel in Brasov.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Note for users with Main Pretour -->
                                <div class="col-md-12" x-show="hasMainPretour">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Note:</strong> Since you have the Main Pretour package, you can choose either transfer option above.
                                    </div>
                                </div>
                            </div>
                        </template>

                        <!-- Brasov Airport Options -->
                        <template x-if="$data.selectedAirport === 'brasov'">
                            <div class="row">
                                <!-- Option 3: Brasov Airport to Brasov Main Event Hotel -->
                                <div class="col-md-12">
                                    <div class="transfers-type-card"
                                         :class="{ 'selected': $data.transferType === 'brasov_airport_to_venue' }"
                                         @click="$data.transferType = 'brasov_airport_to_venue'; $data.isRoundTrip = false; $data.isDepartureOnly = false; $data.isArrivalOnly = true;">
                                        <div class="transfers-type-icon">
                                            <i class="fas fa-plane-arrival"></i>
                                        </div>
                                        <div class="transfers-type-content">
                                            <div class="transfers-type-title">Brasov Airport to Brasov Main Event Hotel</div>
                                            <div class="transfers-type-description">One-way transfer from Brasov Airport to your Main Event hotel in Brasov.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>

                        <!-- No Airport Selected Message -->
                        <template x-if="!$data.selectedAirport">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Please go back and select your departure airport first.
                            </div>
                        </template>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="transfers-wizard-buttons">
                        <div>
                            <button type="button" class="transfers-btn transfers-btn-secondary me-2" @click="$data.prevStep()">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                        </div>
                        <button type="button" class="transfers-btn transfers-btn-primary" @click="$data.nextStep()">
                            <i class="fas fa-arrow-right text-white"></i> <span class="text-white">Continue</span>
                        </button>
                    </div>
                </div>

                <!-- Step 3: Arrival/Departure Details (based on transfer type) -->
                <div class="transfers-wizard-step-content" :class="{ 'active': currentStep === 3 }">


                    <!-- Arrival Details Section - Always Visible in Step 3 -->
                    <div>
                        <div class="section-title-2025 mb-4">
                            <div class="section-title-icon">
                                <i class="fas fa-plane-arrival"></i>
                            </div>
                            <div class="section-title-content">
                                <h4>Arrival Details</h4>
                                <p class="section-subtitle">Tell us when you'll be arriving</p>
                            </div>
                        </div>

                        <!-- Error Messages -->
                        <div class="alert alert-danger" x-show="errors.arrivalDate || errors.arrivalTime || errors.arrivalFlight">
                            <div x-show="errors.arrivalDate" x-text="errors.arrivalDate"></div>
                            <div x-show="errors.arrivalTime" x-text="errors.arrivalTime"></div>
                            <div x-show="errors.arrivalFlight" x-text="errors.arrivalFlight"></div>
                        </div>

                        <div class="row">
                            <!-- Arrival Date -->
                            <div class="col-md-6 mb-4">
                                <div class="transfers-form-group">
                                    <label class="transfers-form-label" for="arrival-date">
                                        <i class="fas fa-calendar-alt me-2 text-primary"></i>Arrival Date
                                    </label>
                                    <div class="input-group date-picker-container">
                                        <span class="input-group-text">
                                            <i class="fas fa-calendar-alt"></i>
                                        </span>
                                        <input type="text" id="arrival-date"
                                            class="transfers-form-control date-picker-input"
                                            :class="{ 'is-invalid': errors.arrivalDate }"
                                            x-model="arrivalDate"
                                            placeholder="Select arrival date">
                                    </div>
                                    <div class="transfers-invalid-feedback" x-show="errors.arrivalDate" x-text="errors.arrivalDate"></div>
                                    <small class="text-muted">Select a date between August 12-20, 2025</small>
                                </div>
                            </div>

                            <!-- Arrival Time -->
                            <div class="col-md-6 mb-4">
                                <div class="transfers-form-group">
                                    <label class="transfers-form-label" for="arrival-time">
                                        <i class="fas fa-clock me-2 text-primary"></i>Arrival Time
                                    </label>
                                    <div class="input-group time-picker-container">
                                        <span class="input-group-text">
                                            <i class="fas fa-clock"></i>
                                        </span>
                                        <input type="text" id="arrival-time"
                                            class="transfers-form-control time-picker-input"
                                            :class="{ 'is-invalid': errors.arrivalTime }"
                                            x-model="arrivalTime"
                                            placeholder="Select arrival time">
                                    </div>
                                    <div class="transfers-invalid-feedback" x-show="errors.arrivalTime" x-text="errors.arrivalTime"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Flight Number (always visible) -->
                        <div class="transfers-form-group mb-4">
                            <label class="transfers-form-label" for="arrival-flight">
                                <i class="fas fa-plane me-2 text-primary"></i>Flight Number (Optional)
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-plane"></i>
                                </span>
                                <input type="text" id="arrival-flight"
                                    class="transfers-form-control"
                                    :class="{ 'is-invalid': errors.arrivalFlight }"
                                    x-model="arrivalFlight"
                                    placeholder="Optional: e.g., RO123">
                            </div>
                            <div class="transfers-invalid-feedback" x-show="errors.arrivalFlight" x-text="errors.arrivalFlight"></div>
                            <small class="text-muted">Optional: Enter your flight number to help us track your arrival</small>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="transfers-wizard-buttons">
                        <div>
                            <button type="button" class="transfers-btn transfers-btn-secondary me-2" @click="$data.prevStep()">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button type="button" class="transfers-btn transfers-btn-outline me-2" @click="$data.resetWizard()">
                                <i class="fas fa-redo-alt"></i> Reset
                            </button>
                        </div>
                        <button type="button" class="transfers-btn transfers-btn-primary" @click="$data.nextStep()">
                            <i class="fas fa-arrow-right text-white"></i> <span class="text-white">Continue</span>
                        </button>
                    </div>
                </div>

                <!-- Step 4: Departure Details (for round trips) -->
                <div class="transfers-wizard-step-content" :class="{ 'active': currentStep === 4 }">
                    <div class="section-title-2025 mb-4">
                        <div class="section-title-icon">
                            <i class="fas fa-plane-departure"></i>
                        </div>
                        <div class="section-title-content">
                            <h4>Departure Details</h4>
                            <p class="section-subtitle">Tell us when you'll be departing</p>
                        </div>
                    </div>

                    <!-- Error Messages -->
                    <div class="alert alert-danger" x-show="errors.departureDate || errors.departureTime || errors.departureFlight">
                        <div x-show="errors.departureDate" x-text="errors.departureDate"></div>
                        <div x-show="errors.departureTime" x-text="errors.departureTime"></div>
                        <div x-show="errors.departureFlight" x-text="errors.departureFlight"></div>
                    </div>

                    <div class="row">
                        <!-- Departure Date -->
                        <div class="col-md-6 mb-4">
                            <div class="transfers-form-group">
                                <label class="transfers-form-label" for="departure-date">
                                    <i class="fas fa-calendar-alt me-2 text-primary"></i>Departure Date
                                </label>
                                <div class="input-group date-picker-container">
                                    <span class="input-group-text">
                                        <i class="fas fa-calendar-alt"></i>
                                    </span>
                                    <input type="text" id="departure-date"
                                        class="transfers-form-control date-picker-input"
                                        :class="{ 'is-invalid': errors.departureDate }"
                                        x-model="departureDate"
                                        placeholder="Select departure date">
                                </div>
                                <div class="transfers-invalid-feedback" x-show="errors.departureDate" x-text="errors.departureDate"></div>
                                <small class="text-muted">Select a date between August 24-27, 2025</small>
                            </div>
                        </div>

                        <!-- Departure Time -->
                        <div class="col-md-6 mb-4">
                            <div class="transfers-form-group">
                                <label class="transfers-form-label" for="departure-time">
                                    <i class="fas fa-clock me-2 text-primary"></i>Departure Time
                                </label>
                                <div class="input-group time-picker-container">
                                    <span class="input-group-text">
                                        <i class="fas fa-clock"></i>
                                    </span>
                                    <input type="text" id="departure-time"
                                        class="transfers-form-control time-picker-input"
                                        :class="{ 'is-invalid': errors.departureTime }"
                                        x-model="departureTime"
                                        placeholder="Select departure time">
                                </div>
                                <div class="transfers-invalid-feedback" x-show="errors.departureTime" x-text="errors.departureTime"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Flight Number (always visible) -->
                    <div class="transfers-form-group mb-4">
                        <label class="transfers-form-label" for="departure-flight">
                            <i class="fas fa-plane me-2 text-primary"></i>Flight Number (Optional)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-plane"></i>
                            </span>
                            <input type="text" id="departure-flight"
                                class="transfers-form-control"
                                :class="{ 'is-invalid': errors.departureFlight }"
                                x-model="departureFlight"
                                placeholder="Optional: e.g., RO123">
                        </div>
                        <div class="transfers-invalid-feedback" x-show="errors.departureFlight" x-text="errors.departureFlight"></div>
                        <small class="text-muted">Optional: Enter your flight number to help us track your departure</small>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="transfers-wizard-buttons">
                        <div>
                            <button type="button" class="transfers-btn transfers-btn-secondary me-2" @click="$data.prevStep()">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button type="button" class="transfers-btn transfers-btn-outline" @click="$data.resetWizard()">
                                <i class="fas fa-redo-alt"></i> Reset
                            </button>
                        </div>
                        <button type="button" class="transfers-btn transfers-btn-primary" @click="$data.nextStep()">
                            <i class="fas fa-arrow-right text-white"></i> <span class="text-white">Continue</span>
                        </button>
                    </div>
                </div>

                <!-- Step 5: Additional Details -->
                <div class="transfers-wizard-step-content" :class="{ 'active': currentStep === 5 }">
                    <div class="section-title-2025 mb-4">
                        <div class="section-title-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="section-title-content">
                            <h4>Additional Details</h4>
                            <p class="section-subtitle">Any special requirements for your journey</p>
                        </div>
                    </div>

                    <!-- No error messages needed since we removed passenger count -->

                    <!-- Special Requests -->
                    <div class="transfers-form-group mb-4">
                        <label class="transfers-form-label" for="special-requests">
                            <i class="fas fa-comment-alt me-2 text-primary"></i>Special Requests (Optional)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text align-items-start" style="padding-top: 10px;">
                                <i class="fas fa-comment-alt"></i>
                            </span>
                            <textarea id="special-requests"
                                class="transfers-form-control"
                                x-model="specialRequests" rows="3"
                                placeholder="Any special requirements or additional information"></textarea>
                        </div>
                        <small class="text-muted">Please let us know if you have any special requirements for your transfer</small>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="transfers-wizard-buttons">
                        <div>
                            <button type="button" class="transfers-btn transfers-btn-secondary me-2" @click="$data.prevStep()">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button type="button" class="transfers-btn transfers-btn-outline" @click="$data.resetWizard()">
                                <i class="fas fa-redo-alt"></i> Reset
                            </button>
                        </div>
                        <button type="button" class="transfers-btn transfers-btn-primary" @click="$data.nextStep()">
                            <i class="fas fa-arrow-right text-white"></i> <span class="text-white">Continue</span>
                        </button>
                    </div>
                </div>

                <!-- Step 6: Review & Book -->
                <div class="transfers-wizard-step-content" :class="{ 'active': currentStep === 6 }">
                    <div class="section-title-2025 mb-4">
                        <div class="section-title-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="section-title-content">
                            <h4>Review & Book Your Transfer</h4>
                            <p class="section-subtitle">Confirm your selections and complete your booking</p>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div x-show="loading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Loading available transfer options...</p>
                    </div>

                    <!-- Error Message -->
                    <div class="alert alert-danger" x-show="errors.general" x-text="errors.general"></div>
                    <div class="alert alert-danger" x-show="errors.products" x-text="errors.products"></div>

                    <!-- Transfer Summary -->
                    <div x-show="!loading" class="card mb-4">
                        <div class="card-header text-white" style="background-color: #369fcd;">
                            <h5 class="mb-0"><i class="fas fa-shuttle-van me-2 text-white"></i>Transfer Details</h5>
                        </div>
                        <div class="card-body">
                            <!-- Transfer Type & Airport Info -->
                            <div class="transfer-summary-box mb-4">
                                <div class="transfer-summary-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <div class="transfer-summary-content">
                                    <h6 class="transfer-summary-title">Route Information</h6>
                                    <div class="transfer-summary-details">
                                        <p class="mb-2"><strong>Departure Airport:</strong>
                                            <span x-show="$data.selectedAirport === 'bucharest'">Bucharest Otopeni Airport</span>
                                            <span x-show="$data.selectedAirport === 'brasov'">Brasov Airport</span>
                                        </p>
                                        <p class="mb-0"><strong>Transfer Type:</strong>
                                            <span x-show="$data.transferType === 'bucharest_to_brasov'">Bucharest Airport to Brasov (One-way)</span>
                                            <span x-show="$data.transferType === 'brasov_to_bucharest'">Brasov to Bucharest Airport (One-way)</span>
                                            <span x-show="$data.transferType === 'bucharest_to_brasov_round'">Bucharest Airport ↔ Brasov (Round Trip)</span>
                                            <span x-show="$data.transferType === 'bucharest_to_pretour_hotel'">Bucharest Airport to Main Pretour Hotel</span>
                                            <span x-show="$data.transferType === 'brasov_airport_to_venue'">Brasov Airport to Brasov Main Event Hotel</span>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Arrival Details Card -->
                            <template x-if="!$data.isDepartureOnly">
                                <div class="transfer-detail-card arrival-card mb-4">
                                    <div class="transfer-detail-header">
                                        <div class="transfer-detail-icon">
                                            <i class="fas fa-plane-arrival"></i>
                                        </div>
                                        <h6 class="transfer-detail-title">Arrival Details</h6>
                                    </div>
                                    <div class="transfer-detail-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="transfer-detail-item">
                                                    <div class="detail-icon"><i class="fas fa-calendar-alt"></i></div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Date</span>
                                                        <span class="detail-value" x-text="$data.arrivalDate || 'Not specified'"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="transfer-detail-item">
                                                    <div class="detail-icon"><i class="fas fa-clock"></i></div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Time</span>
                                                        <span class="detail-value" x-text="$data.arrivalTime || 'Not specified'"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <template x-if="$data.arrivalFlight">
                                            <div class="transfer-detail-item mt-3">
                                                <div class="detail-icon"><i class="fas fa-plane"></i></div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Flight Number</span>
                                                    <span class="detail-value" x-text="$data.arrivalFlight"></span>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>

                            <!-- Departure Details Card -->
                            <template x-if="$data.isDepartureOnly || $data.isRoundTrip">
                                <div class="transfer-detail-card departure-card mb-4">
                                    <div class="transfer-detail-header">
                                        <div class="transfer-detail-icon">
                                            <i class="fas fa-plane-departure"></i>
                                        </div>
                                        <h6 class="transfer-detail-title">Departure Details</h6>
                                    </div>
                                    <div class="transfer-detail-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="transfer-detail-item">
                                                    <div class="detail-icon"><i class="fas fa-calendar-alt"></i></div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Date</span>
                                                        <span class="detail-value" x-text="$data.departureDate || 'Not specified'"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="transfer-detail-item">
                                                    <div class="detail-icon"><i class="fas fa-clock"></i></div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Time</span>
                                                        <span class="detail-value" x-text="$data.departureTime || 'Not specified'"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <template x-if="$data.departureFlight">
                                            <div class="transfer-detail-item mt-3">
                                                <div class="detail-icon"><i class="fas fa-plane"></i></div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Flight Number</span>
                                                    <span class="detail-value" x-text="$data.departureFlight"></span>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>

                            <!-- Special Requests -->
                            <template x-if="$data.specialRequests">
                                <div class="transfer-detail-card special-requests-card">
                                    <div class="transfer-detail-header">
                                        <div class="transfer-detail-icon">
                                            <i class="fas fa-comment-alt"></i>
                                        </div>
                                        <h6 class="transfer-detail-title">Special Requests</h6>
                                    </div>
                                    <div class="transfer-detail-body">
                                        <p class="mb-0" x-text="$data.specialRequests"></p>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- Available Transfer Products -->
                    <div x-show="!loading && products.length > 0" class="mb-4">
                        <h5 class="mb-3">Available Transfer Options</h5>
                        <p class="text-muted mb-3">Please select at least one transfer option:</p>

                        <div class="transfers-products-grid">
                            <template x-for="(product, index) in products" :key="product.id">
                                <div class="transfers-product-card"
                                     :class="{ 'selected': product.selected }"
                                     @click="$data.products[index].selected = !$data.products[index].selected">
                                    <div class="transfers-product-image" style="height: 180px; overflow: hidden; position: relative;">
                                        <img :src="product.image" :alt="product.name" style="width: 100%; height: 100%; object-fit: cover;">
                                        <div x-show="product.selected" class="transfers-product-badge" style="position: absolute; top: 10px; right: 10px; background: rgba(40, 167, 69, 0.9); color: white; padding: 5px 10px; border-radius: 10px; font-size: 12px; font-weight: 600; z-index: 2;">Selected</div>
                                    </div>
                                    <div class="transfers-product-content" style="padding: 20px;">
                                        <h4 class="transfers-product-title" x-text="product.name"></h4>
                                        <div class="transfers-product-description" x-html="product.description"></div>
                                        <div class="transfers-product-price" x-html="product.price_html"></div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="transfers-wizard-buttons">
                        <div>
                            <button type="button" class="transfers-btn transfers-btn-secondary me-2" @click="$data.prevStep()">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button type="button" class="transfers-btn transfers-btn-danger" @click="$data.resetWizard()">
                                <i class="fas fa-times text-white"></i> Cancel
                            </button>
                        </div>
                        <button type="button" class="transfers-btn transfers-btn-success" @click="$data.addToCart()" :disabled="loading">
                            <i class="fas fa-shopping-cart text-white"></i> Book Transfer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional CSS for improved UX/UI -->
    <style>
        /* Transfers Wizard Styles */
        /* 2025 Section Titles */
        .section-title-2025 {
            display: flex;
            align-items: center;
            margin-bottom: 2.5rem;
            position: relative;
            padding: 10px 0;
        }

        .section-title-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            background: linear-gradient(135deg, #36b1dc, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 25px;
            box-shadow: 0 8px 20px rgba(54, 177, 220, 0.2);
            position: relative;
            overflow: hidden;
            flex-shrink: 0;
        }

        .section-title-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            z-index: 1;
        }

        .section-title-icon i {
            color: white;
            font-size: 28px;
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
        }

        .section-title-content {
            flex: 1;
            padding: 5px 0;
        }

        .section-title-content h4 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1.3;
            letter-spacing: -0.5px;
            margin-bottom: 12px;
        }

        .section-subtitle {
            margin: 0;
            font-size: 18px;
            color: #6c757d;
            font-weight: 400;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .section-title-2025 {
                margin-bottom: 2rem;
                padding: 8px 0;
            }

            .section-title-icon {
                width: 60px;
                height: 60px;
                border-radius: 14px;
                margin-right: 20px;
            }

            .section-title-icon i {
                font-size: 24px;
            }

            .section-title-content {
                padding: 3px 0;
            }

            .section-title-content h4 {
                font-size: 22px;
                margin-bottom: 8px;
            }

            .section-subtitle {
                font-size: 16px;
            }
        }

        /* Modern 2025 Progress Bar - Clean & Engaging Design */
        .transfers-wizard-progress-2025 {
            margin-bottom: 40px;
            position: relative;
            padding: 0 10px;
        }

        /* Progress Track & Fill */
        .transfers-progress-track {
            position: relative;
            height: 6px;
            background-color: #f0f4f8;
            border-radius: 10px;
            margin: 0 auto 30px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .transfers-progress-fill {
            position: absolute;
            height: 100%;
            background: linear-gradient(90deg, #36b1dc, #2980b9);
            border-radius: 10px;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Steps Container */
        .transfers-steps-container {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 5;
        }

        /* Individual Step */
        .transfers-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .transfers-step:hover {
            transform: translateY(-2px);
        }

        /* Step Icon */
        .transfers-step-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .transfers-step-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            z-index: 1;
        }

        .transfers-step-icon i,
        .transfers-step-icon span {
            position: relative;
            z-index: 2;
            font-size: 18px;
        }

        /* Active Step */
        .transfers-step.active .transfers-step-icon {
            background: linear-gradient(135deg, #36b1dc, #2980b9);
            border-color: #36b1dc;
            color: white;
            box-shadow: 0 8px 16px rgba(54, 177, 220, 0.2);
            transform: scale(1.05);
        }

        /* Completed Step */
        .transfers-step.completed .transfers-step-icon {
            background: linear-gradient(135deg, #28a745, #218838);
            border-color: #28a745;
            color: white;
            box-shadow: 0 8px 16px rgba(40, 167, 69, 0.2);
        }

        /* Step Label */
        .transfers-step-label {
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            margin-top: 4px;
            text-align: center;
            transition: color 0.3s ease;
        }

        .transfers-step.active .transfers-step-label {
            color: #36b1dc;
            font-weight: 600;
        }

        .transfers-step.completed .transfers-step-label {
            color: #28a745;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .transfers-step-icon {
                width: 40px;
                height: 40px;
            }

            .transfers-step-icon i,
            .transfers-step-icon span {
                font-size: 16px;
            }

            .transfers-step-label {
                font-size: 12px;
                max-width: 70px;
            }
        }

        .transfers-wizard-step-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .transfers-wizard-step-content.active {
            display: block;
        }

        .transfers-wizard-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .transfers-btn {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            font-size: 16px;
            letter-spacing: 0.3px;
        }

        .transfers-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
            z-index: 1;
        }

        .transfers-btn-primary {
            background: linear-gradient(135deg, #36b1dc, #2980b9);
            color: white;
        }

        .transfers-btn-primary:hover {
            background: linear-gradient(135deg, #2980b9, #1e6091);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(54, 177, 220, 0.3);
        }

        .transfers-btn-primary:hover .text-white,
        .transfers-btn-primary:hover i.text-white {
            color: white !important;
        }

        .transfers-btn-primary:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(54, 177, 220, 0.3);
        }

        .transfers-btn-secondary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .transfers-btn-secondary:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.05);
        }

        .transfers-btn-secondary:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .transfers-btn-success {
            background: linear-gradient(135deg, #28a745, #218838);
            color: white;
        }

        .transfers-btn-success:hover {
            background: linear-gradient(135deg, #218838, #1a6e2d);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        }

        .transfers-btn-success:hover .text-white,
        .transfers-btn-success:hover i.text-white {
            color: white !important;
        }

        .transfers-btn-success:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .transfers-btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .transfers-btn-danger:hover {
            background: linear-gradient(135deg, #c82333, #a71d2a);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(220, 53, 69, 0.3);
        }

        .transfers-btn-danger:hover .text-white,
        .transfers-btn-danger:hover i.text-white {
            color: white !important;
        }

        .transfers-btn-danger:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
        }

        .transfers-btn-outline {
            background: transparent;
            color: #36b1dc;
            border: 2px solid #36b1dc;
            box-shadow: none;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .transfers-btn-outline:hover {
            background: #36b1dc;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(54, 177, 220, 0.15);
        }

        .transfers-btn-outline:hover i {
            color: white !important;
        }

        .transfers-btn-outline:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(54, 177, 220, 0.15);
        }

        .transfers-form-group {
            margin-bottom: 20px;
        }

        .transfers-form-label {
            display: flex;
            margin-bottom: 10px;
            font-weight: 600;
            color: #2c3e50;
            align-items: center;
            font-size: 16px;
            letter-spacing: 0.2px;
        }

        .transfers-form-label i {
            margin-right: 8px;
            color: #36b1dc;
            font-size: 18px;
        }

        .transfers-form-control {
            width: 100%;
            padding: 14px 16px;
            border: 1px solid #ced4da;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
            background-color: #fff;
        }

        .transfers-form-control:focus {
            border-color: #36b1dc;
            outline: none;
        }

        /* Simple Date and Time Picker Styles */
        .date-picker-container,
        .time-picker-container {
            border: 2px solid #dee2e6;
            border-radius: 6px;
            background-color: white;
        }

        .date-picker-input,
        .time-picker-input {
            height: 46px;
            font-size: 16px;
            background-color: white !important;
            border: none;
            padding-left: 10px;
            font-weight: 500;
            color: #333 !important;
        }

        /* Fix for flatpickr inline display */
        .flatpickr-input {
            background-color: white !important;
        }

        /* Make sure the selected date is visible */
        .flatpickr-input:not([readonly]),
        .flatpickr-input[readonly] {
            cursor: pointer;
            color: #333 !important;
            opacity: 1 !important;
            background-color: white !important;
        }

        /* Override any flatpickr styles that might hide the text */
        .flatpickr-wrapper input {
            color: #333 !important;
            opacity: 1 !important;
        }

        .date-picker-container .input-group-text,
        .time-picker-container .input-group-text {
            background-color: #f8f9fa;
            border: none;
            border-right: 1px solid #dee2e6;
            color: #36b1dc;
            font-size: 18px;
        }

        /* Simple Flatpickr Calendar Customization */
        .flatpickr-calendar {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid #dee2e6 !important;
        }

        .flatpickr-day.selected {
            background: #36b1dc !important;
            border-color: #36b1dc !important;
        }

        .flatpickr-day.today {
            border-color: #36b1dc !important;
        }
            box-shadow: 0 0 0 4px rgba(54, 177, 220, 0.15);
            transform: translateY(-1px);
        }

        .transfers-form-control:hover {
            border-color: #adb5bd;
        }

        .transfers-form-control.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.1);
        }

        .transfers-invalid-feedback {
            color: #dc3545;
            font-size: 14px;
            margin-top: 6px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .transfers-invalid-feedback::before {
            content: '⚠️';
            margin-right: 6px;
            font-size: 14px;
        }

        /* Input Group Styling */
        .input-group {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: stretch;
            width: 100%;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .input-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .input-group:focus-within {
            box-shadow: 0 4px 15px rgba(54, 177, 220, 0.15);
        }

        .input-group-text {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #36b1dc;
            text-align: center;
            white-space: nowrap;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 12px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            transition: all 0.3s ease;
        }

        .input-group:focus-within .input-group-text {
            border-color: #36b1dc;
            color: #2980b9;
        }

        .input-group .transfers-form-control {
            position: relative;
            flex: 1 1 auto;
            width: 1%;
            min-width: 0;
            margin-bottom: 0;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: 0;
            padding-left: 0;
        }

        .input-group:focus-within .transfers-form-control {
            border-color: #36b1dc;
        }

        .transfers-type-grid {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 20px;
        }

        .transfers-type-card {
            border: 2px solid #e9ecef;
            border-radius: 16px;
            padding: 24px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            display: flex;
            flex-direction: row;
            align-items: center;
            text-align: left;
            background-color: #fff;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
            width: 100%;
        }

        .transfers-type-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(90deg, transparent, transparent);
            transition: all 0.4s ease;
        }

        .transfers-type-card:hover {
            border-color: #36b1dc;
            transform: translateY(-8px);
            box-shadow: 0 16px 32px rgba(54, 177, 220, 0.15);
        }

        .transfers-type-card:hover::after {
            background: linear-gradient(90deg, #36b1dc, #2980b9);
        }

        .transfers-type-card.selected {
            border-color: #36b1dc;
            background-color: rgba(54, 177, 220, 0.05);
            box-shadow: 0 16px 32px rgba(54, 177, 220, 0.2);
        }

        .transfers-type-card.selected::after {
            background: linear-gradient(90deg, #36b1dc, #2980b9);
        }

        .transfers-type-icon {
            font-size: 28px;
            color: #36b1dc;
            margin-right: 24px;
            margin-bottom: 0;
            min-width: 70px;
            height: 70px;
            background-color: rgba(54, 177, 220, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            box-shadow: 0 6px 12px rgba(54, 177, 220, 0.1);
            position: relative;
            z-index: 1;
        }

        .transfers-type-icon::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
            z-index: 2;
        }

        .transfers-type-card.selected .transfers-type-icon {
            background-color: #36b1dc;
            color: white;
            transform: scale(1.15);
            box-shadow: 0 8px 16px rgba(54, 177, 220, 0.3);
        }

        .transfers-type-content {
            flex: 1;
        }

        .transfers-type-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 12px;
            color: #2c3e50;
            transition: all 0.3s ease;
        }

        .transfers-type-card:hover .transfers-type-title,
        .transfers-type-card.selected .transfers-type-title {
            color: #36b1dc;
        }

        .transfers-type-description {
            color: #6c757d;
            font-size: 15px;
            line-height: 1.5;
            margin: 0;
        }

        .transfers-products-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .transfers-product-card {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .transfers-product-card:hover {
            border-color: #36b1dc;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .transfers-product-card.selected {
            border-color: #28a745;
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.2);
        }

        .transfers-product-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .transfers-product-description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .transfers-product-price {
            font-weight: bold;
            color: #36b1dc;
            font-size: 18px;
        }

        /* Card styling for review section */
        .card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .card-header {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .card-body {
            padding: 20px;
        }

        /* Alert styling */
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Transfer Details Styling */
        .transfer-summary-box {
            display: flex;
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #369fcd;
        }

        .transfer-summary-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: #369fcd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .transfer-summary-icon i {
            color: white;
            font-size: 22px;
        }

        .transfer-summary-content {
            flex: 1;
        }

        .transfer-summary-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .transfer-summary-details {
            color: #495057;
        }

        /* Transfer Detail Cards */
        .transfer-detail-card {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        .arrival-card {
            border-top: 4px solid #369fcd;
        }

        .departure-card {
            border-top: 4px solid #fd7e14;
        }

        .special-requests-card {
            border-top: 4px solid #6c757d;
        }

        .transfer-detail-header {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .transfer-detail-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .arrival-card .transfer-detail-icon {
            background: #369fcd;
            color: white;
        }

        .departure-card .transfer-detail-icon {
            background: linear-gradient(135deg, #fd7e14, #e67e22);
            color: white;
        }

        .special-requests-card .transfer-detail-icon {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .transfer-detail-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0;
        }

        .transfer-detail-body {
            padding: 20px;
        }

        .transfer-detail-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .detail-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #6c757d;
            flex-shrink: 0;
        }

        .arrival-card .detail-icon {
            color: #369fcd;
        }

        .departure-card .detail-icon {
            color: #fd7e14;
        }

        .detail-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 2px;
        }

        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }
    </style>

    <!-- Travel Information Section - 2025 UX/UI Style -->
    <div class="section-container" style="background: rgba(255, 255, 255, 0.85); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.03); padding: 40px; border: 1px solid rgba(255, 255, 255, 0.9); position: relative; overflow: hidden; margin-bottom: 30px;">
        <!-- Decorative elements for 2025 design -->
        <div style="position: absolute; top: -100px; right: -100px; width: 300px; height: 300px; background: linear-gradient(45deg, rgba(54, 177, 220, 0.1), rgba(54, 177, 220, 0.05)); border-radius: 50%; z-index: 0;"></div>
        <div style="position: absolute; bottom: -80px; left: -80px; width: 200px; height: 200px; background: linear-gradient(45deg, rgba(40, 167, 69, 0.08), rgba(40, 167, 69, 0.03)); border-radius: 50%; z-index: 0;"></div>
        <!-- Decorative elements -->
        <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
        <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

        <!-- Section header with 3D effect -->
        <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
            <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                <i class="fas fa-info-circle" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
            </div>
            <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Travel Information</h3>
        </div>

        <!-- Travel Info Cards -->
        <div style="display: grid; grid-template-columns: 1fr; gap: 20px; margin-top: 20px;">
            <!-- Train Travel Card -->
            <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.8); transition: all 0.3s ease; height: 100%; animation: fadeIn 0.5s ease forwards; opacity: 0;"
                onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.1)';"
                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0, 0, 0, 0.05)';">

                <!-- Card Header with gradient background -->
                <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 20px; color: white; position: relative; overflow: hidden;">
                    <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1), transparent 70%);"></div>

                    <div style="display: flex; justify-content: space-between; align-items: center; position: relative; z-index: 1;">
                        <h4 style="margin: 0; font-weight: 600; font-size: 18px; color: white; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">Train Travel</h4>

                        <div style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-train" style="color: white; font-size: 18px;"></i>
                        </div>
                    </div>
                </div>

                <!-- Card Content -->
                <div style="padding: 20px;">
                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <p style="margin: 0; color: #2c3e50; line-height: 1.6; text-align: justify !important;">
                            The most convenient way to reach Brasov from Bucharest is by train. Trains run regularly from Bucharest North Station (Gara de Nord) to Brasov.
                        </p>

                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #36b1dc;">
                            <h5 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 16px; font-weight: 600;">Train Schedule Highlights:</h5>
                            <ul style="margin: 0; padding-left: 20px; color: #2c3e50;">
                                <li>Journey time: approximately 2.5 hours</li>
                                <li>Multiple departures daily (6:00 AM to 8:30 PM)</li>
                                <li>Ticket price: €10-15 (First class: €20-25)</li>
                                <li>Tickets can be purchased online at <a href="https://www.cfrcalatori.ro/en/" target="_blank" style="color: #36b1dc; text-decoration: none; font-weight: 500;">CFR Călători</a></li>
                            </ul>
                        </div>

                        <p style="margin: 0; color: #2c3e50; line-height: 1.6; text-align: justify !important;">
                            From Bucharest Airport (Henri Coandă), take the 780 Express bus to Bucharest North Station, then board a train to Brasov.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information Section -->
    <div class="mt-4 mb-5">
        <div style="background: rgba(54, 177, 220, 0.05); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 20px; border: 1px solid rgba(54, 177, 220, 0.1); display: flex; align-items: flex-start;">
            <div style="background: rgba(54, 177, 220, 0.1); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                <i class="fas fa-info-circle" style="color: #36b1dc; font-size: 20px;"></i>
            </div>
            <div style="flex: 1;">
                <p style="color: #2c3e50; margin: 0; font-size: 15px; line-height: 1.6; text-align: justify !important;">
                    For any questions regarding transportation options, please contact our support team through the Support tab. We're here to help you plan your journey to Brasov!
                </p>
            </div>
        </div>
    </div>
</div>



<!-- Animations and Styles -->
<style>
    /* Animations */
    @keyframes pulse {
        0% { transform: scale(1); opacity: 0.5; }
        100% { transform: scale(1.2); opacity: 0.2; }
    }
    @keyframes float {
        0% { transform: translateY(0) translateX(0); }
        50% { transform: translateY(-20px) translateX(10px); }
        100% { transform: translateY(10px) translateX(-10px); }
    }
    @keyframes glow {
        0% { opacity: 0.3; }
        100% { opacity: 0.7; }
    }
    @keyframes fadeIn {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    /* Alpine.js Cloak */
    [x-cloak] { display: none !important; }

    /* Modal Active State */
    .modal-active {
        opacity: 1 !important;
        visibility: visible !important;
    }

    .modal-active .modal-container {
        transform: scale(1) !important;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 767px) {
        /* Reduce padding on all containers */
        .container {
            padding-left: 10px !important;
            padding-right: 10px !important;
        }

        /* Make cards take full width with no padding */
        .section-container {
            padding: 0 !important;
            border-radius: 20px !important;
        }

        /* Adjust card styles */
        [style*="border-radius: 20px"] {
            border-radius: 15px !important;
        }

        /* Add padding only to content areas */
        [style*="padding: 20px; flex-grow: 1;"],
        [style*="padding: 20px;"] {
            padding: 15px !important;
        }

        /* Make info rows stack on mobile */
        [style*="display: flex; align-items: center; gap: 15px; background-color: #f8f9fa;"] {
            flex-direction: column !important;
            gap: 10px !important;
        }

        /* Remove divider on mobile */
        [style*="width: 1px; height: 30px; background-color: #dee2e6;"] {
            display: none !important;
        }

        /* Adjust spacing */
        .mb-5 {
            margin-bottom: 15px !important;
        }

        /* Make header more compact */
        h1 {
            font-size: 24px !important;
        }

        h3 {
            font-size: 18px !important;
        }

        p {
            font-size: 14px !important;
        }

        /* Center align text on mobile */
        h4, p {
            text-align: center !important;
        }

        /* Make grid single column on mobile */
        [style*="grid-template-columns: repeat(2, 1fr)"] {
            grid-template-columns: 1fr !important;
        }

        /* Hide seconds on mobile */
        #countdown-seconds, #countdown-seconds + span {
            display: none !important;
        }

        /* Make button text smaller and full width on mobile */
        [style*="display: inline-flex; align-items: center; justify-content: center; padding: 12px"] {
            font-size: 14px !important;
            padding: 10px !important;
            width: 100% !important;
        }
    }
</style>

<script>
// Define ajaxurl if it's not already defined
var ajaxurl = "<?php echo admin_url('admin-ajax.php'); ?>";

// Transfers functionality
jQuery(document).ready(function($) {
    // Handle Add to Cart button clicks
    $('.transfers-add-to-cart-btn').on('click', function() {
        const $button = $(this);
        const productId = $button.data('product-id');

        // Disable button and show loading state
        $button.prop('disabled', true);
        const originalText = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin me-2"></i> Adding...');

        // Add to cart via AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'lci-dashboard-add-to-cart',
                security: lci_ajax.nonce,
                nonce: lci_ajax.nonce, // Add nonce parameter as fallback
                product_id: productId,
                quantity: 1
            },
            success: function(response) {
                if (response.success) {
                    // Show success state
                    $button.html('<i class="fas fa-check-circle me-2"></i> Added!');
                    $button.css('background', '#28a745');

                    // Add "In Cart" badge to product card
                    const $productCard = $button.closest('.transfers-product-card');
                    const $productImage = $productCard.find('.transfers-product-image');

                    if ($productImage.find('.transfers-product-badge').length === 0) {
                        $productImage.prepend('<div class="transfers-product-badge" style="position: absolute; top: 10px; right: 10px; background: rgba(54, 177, 220, 0.9); color: white; padding: 5px 10px; border-radius: 10px; font-size: 12px; font-weight: 600; z-index: 2;">In Cart</div>');
                    }

                    // Replace button with View Cart button after a short delay
                    setTimeout(function() {
                        $button.replaceWith('<button type="button" class="transfers-view-cart-btn" style="width: 100%; padding: 10px; background: #28a745; color: white; border: none; border-radius: 10px; font-weight: 600; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 5px 15px rgba(40, 167, 69, 0.3)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'none\';" onclick="window.location.href=\'<?php echo esc_url(wc_get_cart_url()); ?>\'"><i class="fas fa-check-circle me-2"></i> View Cart</button>');
                    }, 1000);

                    // Update mini cart
                    if (typeof updateMiniCart === 'function') {
                        updateMiniCart();
                    } else {
                        // Fallback: reload the page after a delay
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    }

                    // Show success message
                    const successAlert = $('<div class="alert alert-success alert-dismissible fade show" role="alert" style="margin-top: 20px;">' +
                        '<i class="fas fa-check-circle me-2"></i> Product added to your cart!' +
                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                        '</div>');

                    $('.transfers-products-container').prepend(successAlert);

                    // Auto-dismiss after 5 seconds
                    setTimeout(function() {
                        successAlert.alert('close');
                    }, 5000);
                } else {
                    // Show error state
                    $button.html('<i class="fas fa-exclamation-circle me-2"></i> Error');
                    $button.css('background', '#dc3545');

                    // Re-enable button after a delay
                    setTimeout(function() {
                        $button.prop('disabled', false);
                        $button.html(originalText);
                        $button.css('background', 'linear-gradient(135deg, #36b1dc, #2980b9)');
                    }, 2000);

                    // Show error message
                    const errorAlert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert" style="margin-top: 20px;">' +
                        '<i class="fas fa-exclamation-circle me-2"></i> Failed to add product to cart. Please try again.' +
                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                        '</div>');

                    $('.transfers-products-container').prepend(errorAlert);

                    // Auto-dismiss after 5 seconds
                    setTimeout(function() {
                        errorAlert.alert('close');
                    }, 5000);
                }
            },
            error: function() {
                // Show error state
                $button.html('<i class="fas fa-exclamation-circle me-2"></i> Error');
                $button.css('background', '#dc3545');

                // Re-enable button after a delay
                setTimeout(function() {
                    $button.prop('disabled', false);
                    $button.html(originalText);
                    $button.css('background', 'linear-gradient(135deg, #36b1dc, #2980b9)');
                }, 2000);

                // Show error message
                const errorAlert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert" style="margin-top: 20px;">' +
                    '<i class="fas fa-exclamation-circle me-2"></i> Network error. Please check your connection and try again.' +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>');

                $('.transfers-products-container').prepend(errorAlert);

                // Auto-dismiss after 5 seconds
                setTimeout(function() {
                    errorAlert.alert('close');
                }, 5000);
            }
        });
    });

    // Update mini cart function
    function updateMiniCart() {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'lci_get_mini_cart',
                security: lci_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('.mini-cart-wrapper').html(response.data.mini_cart);
                }
            }
        });
    }

    // Initialize animations for cards
    setTimeout(function() {
        $('[style*="animation: fadeIn"]').css('opacity', '1');
    }, 100);
});
</script>
