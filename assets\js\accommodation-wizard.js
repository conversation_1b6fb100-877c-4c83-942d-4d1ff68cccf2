document.addEventListener('DOMContentLoaded', function() {
    // Handle radio button selection for options
    const optionRadios = document.querySelectorAll('.accommodation-option-radio');
    optionRadios.forEach(function(radio) {
        radio.addEventListener('change', function() {
            // Remove selected class from all options
            document.querySelectorAll('.accommodation-wizard-option').forEach(function(option) {
                option.classList.remove('selected');
            });

            // Add selected class to the parent option
            if (this.checked) {
                this.closest('.accommodation-wizard-option-label')
                    .querySelector('.accommodation-wizard-option')
                    .classList.add('selected');

                // Auto-submit the form when an option is selected
                const currentStep = typeof wizardCurrentStep !== 'undefined' ? wizardCurrentStep : 1;

                // For Main Pretour users on step 1 (Bucharest question)
                if (currentStep === 1 && this.name === 'bucharest_option') {
                    // Add a small delay to show the selection before submitting
                    setTimeout(() => {
                        this.closest('form').submit();
                    }, 300);
                }

                // For regular users on step 1 or Main Pretour users on step 2 (timing selection)
                if ((currentStep === 1 || currentStep === 2) && this.name === 'timing') {
                    // Add a small delay to show the selection before submitting
                    setTimeout(() => {
                        this.closest('form').submit();
                    }, 300);
                }
            }
        });
    });

    // Handle Add to Cart buttons
    const addToCartButtons = document.querySelectorAll('.add-to-cart-button');
    addToCartButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const url = this.getAttribute('href');

            // Show loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Adding to Cart...';
            this.disabled = true;

            // Add to cart via AJAX
            fetch(url)
                .then(response => {
                    // Refresh mini-cart if available
                    if (typeof updateMiniCart === 'function') {
                        updateMiniCart();
                    }

                    // Show success message
                    this.innerHTML = '<i class="fas fa-check me-2"></i> Added to Cart';
                    this.classList.remove('accommodation-btn-primary');
                    this.classList.add('btn-success');

                    // Reset after 2 seconds
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.classList.add('accommodation-btn-primary');
                        this.classList.remove('btn-success');
                        this.disabled = false;
                    }, 2000);
                })
                .catch(error => {
                    console.error('Error adding to cart:', error);
                    this.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> Error';

                    // Reset after 2 seconds
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 2000);
                });
        });
    });

    // Fix for any HTML structure issues
    fixHtmlStructure();
});

// Function to fix any HTML structure issues
function fixHtmlStructure() {
    // Find all card-body divs
    const cardBodies = document.querySelectorAll('.card-body');

    // Check if any card-body divs are not properly closed
    cardBodies.forEach(function(cardBody) {
        // Check if this card-body is the last child of its parent
        const parent = cardBody.parentElement;
        if (parent && cardBody === parent.lastElementChild && parent.classList.contains('card')) {
            // This is fine - card-body is the last child of a card
        } else if (parent && !parent.classList.contains('card')) {
            // This card-body might be improperly nested
            console.log('Found potentially unclosed card-body', cardBody);

            // Try to find the closest card parent
            const cardParent = cardBody.closest('.card');
            if (cardParent) {
                // Move this card-body to be the last child of the card
                cardParent.appendChild(cardBody);
            }
        }
    });

    // Find all col-md-9 divs
    const colMd9Divs = document.querySelectorAll('.col-md-9');

    // Check if any col-md-9 divs are not properly closed
    colMd9Divs.forEach(function(colDiv) {
        // Check if this col-md-9 is properly nested in a row
        const rowParent = colDiv.closest('.row');
        if (rowParent) {
            // Check if this col-md-9 is the last child of the row
            const lastChild = Array.from(rowParent.children).pop();
            if (lastChild !== colDiv) {
                console.log('Found potentially unclosed col-md-9', colDiv);

                // Make sure all content is inside the col-md-9
                const nextSiblings = [];
                let currentSibling = colDiv.nextSibling;

                while (currentSibling && currentSibling !== rowParent.lastChild) {
                    nextSiblings.push(currentSibling);
                    currentSibling = currentSibling.nextSibling;
                }

                // Move any siblings that should be inside the col-md-9
                nextSiblings.forEach(function(sibling) {
                    if (sibling.nodeType === 1 && !sibling.classList.contains('col-md-3') && !sibling.classList.contains('col-md-9')) {
                        colDiv.appendChild(sibling);
                    }
                });
            }
        }
    });
}
