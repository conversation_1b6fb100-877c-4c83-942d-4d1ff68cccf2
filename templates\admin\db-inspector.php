<div class="wrap lci-admin-wrap">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold">Database Inspector</h1>
    </div>

    <!-- Loading state -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="spinner"></div>
        <span class="loading-text">Loading data...</span>
    </div>

    <!-- Main content -->
    <div id="main-content" class="bg-white rounded-xl shadow-neumorph p-6 mb-6" style="display: none;">
        <div class="mb-6">
            <label class="form-label">Select Table</label>
            <div class="flex space-x-4">
                <select id="table-select" class="form-input flex-1">
                    <option value="">Select a table</option>
                    <!-- Tables will be populated here -->
                </select>
                <button id="refresh-btn" class="btn btn-primary" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                    </svg>
                    Refresh
                </button>
            </div>
        </div>

        <div id="search-container" class="mb-6" style="display: none;">
            <div class="flex flex-col md:flex-row md:items-end space-y-4 md:space-y-0 md:space-x-4">
                <div class="flex-1">
                    <label class="form-label">Search</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <input
                            type="text"
                            id="search-input"
                            placeholder="Search in all columns..."
                            class="form-input pl-10"
                        >
                    </div>
                </div>
                <button id="search-btn" class="btn btn-primary">
                    Search
                </button>
            </div>
        </div>

        <!-- Table Structure -->
        <div id="table-structure-container" class="mb-6" style="display: none;">
            <h2 class="text-xl font-bold mb-4">Table Structure</h2>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="text-left bg-gray-50">
                            <th class="p-3 font-medium text-gray-600">Field</th>
                            <th class="p-3 font-medium text-gray-600">Type</th>
                            <th class="p-3 font-medium text-gray-600">Null</th>
                            <th class="p-3 font-medium text-gray-600">Key</th>
                            <th class="p-3 font-medium text-gray-600">Default</th>
                            <th class="p-3 font-medium text-gray-600">Extra</th>
                        </tr>
                    </thead>
                    <tbody id="table-structure-body">
                        <!-- Table structure will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Table Data -->
        <div id="table-data-container" class="mb-6" style="display: none;">
            <h2 class="text-xl font-bold mb-4">Table Data</h2>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead id="table-data-header">
                        <!-- Table data header will be populated here -->
                    </thead>
                    <tbody id="table-data-body">
                        <!-- Table data will be populated here -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="mt-4 flex justify-between items-center" style="display: none;">
                <div class="text-sm text-gray-600" id="pagination-info">
                    <!-- Pagination info will be populated here -->
                </div>

                <div class="flex space-x-2">
                    <button
                        id="prev-page-btn"
                        class="btn btn-sm btn-secondary"
                    >
                        Previous
                    </button>

                    <button
                        id="next-page-btn"
                        class="btn btn-sm btn-secondary"
                    >
                        Next
                    </button>
                </div>
            </div>
        </div>

        <div id="no-data-message" class="p-4 bg-yellow-50 rounded-lg text-yellow-800" style="display: none;">
            No data found in this table.
        </div>
    </div>
</div>

<script>
    jQuery(document).ready(function($) {
        // Variables
        let tables = [];
        let selectedTable = '';
        let tableStructure = [];
        let tableData = [];
        let searchQuery = '';
        let currentPage = 1;
        let perPage = 20;
        let totalPages = 1;
        let totalRecords = 0;
        let isLoading = true;

        // Elements
        const loadingOverlay = $('#loading-overlay');
        const mainContent = $('#main-content');
        const tableSelect = $('#table-select');
        const refreshBtn = $('#refresh-btn');
        const searchContainer = $('#search-container');
        const searchInput = $('#search-input');
        const searchBtn = $('#search-btn');
        const tableStructureContainer = $('#table-structure-container');
        const tableStructureBody = $('#table-structure-body');
        const tableDataContainer = $('#table-data-container');
        const tableDataHeader = $('#table-data-header');
        const tableDataBody = $('#table-data-body');
        const paginationContainer = $('#pagination-container');
        const paginationInfo = $('#pagination-info');
        const prevPageBtn = $('#prev-page-btn');
        const nextPageBtn = $('#next-page-btn');
        const noDataMessage = $('#no-data-message');

        // Initialize
        fetchTables();

        // Event listeners
        tableSelect.on('change', function() {
            selectedTable = $(this).val();
            if (selectedTable) {
                refreshBtn.prop('disabled', false);
                searchContainer.show();
                fetchTableStructure();
                fetchTableData();
            } else {
                refreshBtn.prop('disabled', true);
                searchContainer.hide();
                tableStructureContainer.hide();
                tableDataContainer.hide();
                noDataMessage.hide();
            }
        });

        refreshBtn.on('click', function() {
            fetchTableStructure();
            fetchTableData();
        });

        searchInput.on('keyup', function(e) {
            if (e.key === 'Enter') {
                search();
            }
        });

        searchBtn.on('click', search);

        prevPageBtn.on('click', function() {
            if (currentPage > 1) {
                currentPage--;
                fetchTableData();
            }
        });

        nextPageBtn.on('click', function() {
            if (currentPage < totalPages) {
                currentPage++;
                fetchTableData();
            }
        });

        // Functions
        function fetchTables() {
            isLoading = true;
            updateUI();

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'lci_get_tables_list',
                    nonce: lciAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        tables = response.data.tables;
                        populateTableSelect();
                    } else {
                        showToast(response.data.message || 'Error fetching tables', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // Console error removed
                    showToast('Error fetching tables', 'error');
                },
                complete: function() {
                    isLoading = false;
                    updateUI();
                }
            });
        }

        function fetchTableStructure() {
            isLoading = true;
            updateUI();

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'lci_get_table_structure',
                    nonce: lciAdmin.nonce,
                    table: selectedTable
                },
                success: function(response) {
                    if (response.success) {
                        tableStructure = response.data.columns;
                        populateTableStructure();
                    } else {
                        showToast(response.data.message || 'Error fetching table structure', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // Console error removed
                    showToast('Error fetching table structure', 'error');
                },
                complete: function() {
                    isLoading = false;
                    updateUI();
                }
            });
        }

        function fetchTableData() {
            isLoading = true;
            updateUI();

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'lci_get_table_data',
                    nonce: lciAdmin.nonce,
                    table: selectedTable,
                    search: searchInput.val(),
                    page: currentPage,
                    per_page: perPage
                },
                success: function(response) {
                    if (response.success) {
                        tableData = response.data.data;
                        totalPages = response.data.total_pages;
                        totalRecords = response.data.total;
                        currentPage = response.data.page;
                        populateTableData();
                    } else {
                        showToast(response.data.message || 'Error fetching table data', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // Console error removed
                    showToast('Error fetching table data', 'error');
                },
                complete: function() {
                    isLoading = false;
                    updateUI();
                }
            });
        }

        function search() {
            currentPage = 1;
            fetchTableData();
        }

        function populateTableSelect() {
            tableSelect.find('option:not(:first)').remove();

            tables.forEach(function(table) {
                tableSelect.append(`<option value="${table.name}">${table.name} - ${table.description}</option>`);
            });
        }

        function populateTableStructure() {
            tableStructureBody.empty();

            tableStructure.forEach(function(column) {
                tableStructureBody.append(`
                    <tr class="border-t border-gray-200 hover:bg-gray-50">
                        <td class="p-3 font-medium">${column.Field}</td>
                        <td class="p-3">${column.Type}</td>
                        <td class="p-3">${column.Null}</td>
                        <td class="p-3">${column.Key}</td>
                        <td class="p-3">${column.Default || '-'}</td>
                        <td class="p-3">${column.Extra || '-'}</td>
                    </tr>
                `);
            });

            tableStructureContainer.show();
        }

        function populateTableData() {
            // Populate header
            tableDataHeader.empty();
            let headerRow = $('<tr class="text-left bg-gray-50"></tr>');

            tableStructure.forEach(function(column) {
                headerRow.append(`<th class="p-3 font-medium text-gray-600">${column.Field}</th>`);
            });

            tableDataHeader.append(headerRow);

            // Populate body
            tableDataBody.empty();

            if (tableData.length === 0) {
                let emptyRow = $(`<tr><td colspan="${tableStructure.length}" class="p-3 text-center text-gray-500">No data found.</td></tr>`);
                tableDataBody.append(emptyRow);
                tableDataContainer.show();
                paginationContainer.hide();
                noDataMessage.show();
                return;
            }

            tableData.forEach(function(row) {
                let dataRow = $('<tr class="border-t border-gray-200 hover:bg-gray-50"></tr>');

                tableStructure.forEach(function(column) {
                    const value = row[column.Field] !== null ? row[column.Field] : '-';
                    dataRow.append(`<td class="p-3">${value}</td>`);
                });

                tableDataBody.append(dataRow);
            });

            // Update pagination
            paginationInfo.html(`
                Showing ${(currentPage - 1) * perPage + 1}
                to ${Math.min(currentPage * perPage, totalRecords)}
                of ${totalRecords} records
            `);

            prevPageBtn.prop('disabled', currentPage === 1);
            nextPageBtn.prop('disabled', currentPage === totalPages);

            if (currentPage === 1) {
                prevPageBtn.addClass('opacity-50 cursor-not-allowed');
            } else {
                prevPageBtn.removeClass('opacity-50 cursor-not-allowed');
            }

            if (currentPage === totalPages) {
                nextPageBtn.addClass('opacity-50 cursor-not-allowed');
            } else {
                nextPageBtn.removeClass('opacity-50 cursor-not-allowed');
            }

            tableDataContainer.show();
            paginationContainer.toggle(totalPages > 1);
            noDataMessage.hide();
        }

        function updateUI() {
            if (isLoading) {
                loadingOverlay.show();
                mainContent.hide();
            } else {
                loadingOverlay.hide();
                mainContent.show();
            }
        }

        function showToast(message, type = 'info') {
            // Use the global showToast function if available
            if (typeof window.showToast === 'function') {
                window.showToast(message, type);
                return;
            }

            // Fallback implementation
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = message;

            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            // Hide toast after 5 seconds
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 5000);
        }
    });
</script>
