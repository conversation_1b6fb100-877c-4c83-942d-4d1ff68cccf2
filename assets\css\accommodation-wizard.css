/* Accommodation Wizard Styles */

/* Wizard Container */
.wizard-container {
    max-width: 900px;
    margin: 0 auto;
    font-family: 'Open Sans', 'Noto Sans', sans-serif;
}

/* Wizard Header */
.wizard-header {
    text-align: center;
    margin-bottom: 2rem;
}

.wizard-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.wizard-subtitle {
    font-size: 1rem;
    color: #666;
}

/* Wizard Progress */
.wizard-progress {
    margin-bottom: 2rem;
}

.wizard-progress-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.wizard-progress-bar-inner {
    height: 100%;
    background-color: #36b1dc;
    transition: width 0.3s ease;
}

.wizard-steps {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
}

.wizard-step {
    text-align: center;
    flex: 1;
    font-size: 0.8rem;
    color: #666;
}

.wizard-step.active {
    color: #36b1dc;
    font-weight: 600;
}

.wizard-step.completed {
    color: #28a745;
}

/* Wizard Content */
.wizard-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

/* Info Box */
.info-box {
    background-color: #e8f4f8;
    border-left: 4px solid #36b1dc;
    padding: 1rem;
    margin-bottom: 2rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
}

.info-box i {
    color: #36b1dc;
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

/* Option Cards */
.option-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.option-card {
    flex: 1;
    min-width: 250px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.option-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.option-card.selected {
    border-color: #36b1dc;
    box-shadow: 0 5px 15px rgba(54, 177, 220, 0.3);
}

.option-card-image {
    height: 160px;
    overflow: hidden;
}

.option-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.option-card:hover .option-card-image img {
    transform: scale(1.05);
}

.option-card-content {
    padding: 1.5rem;
}

.option-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.option-card-description {
    font-size: 0.9rem;
    color: #666;
}

/* Nights Selector */
.nights-selector {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
}

.nights-selector-container {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 50px;
    padding: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nights-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background-color: #fff;
    color: #36b1dc;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nights-btn:hover:not(:disabled) {
    background-color: #36b1dc;
    color: #fff;
    transform: scale(1.05);
}

.nights-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nights-display {
    margin: 0 1.5rem;
    text-align: center;
}

.nights-count {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    display: block;
}

.nights-label {
    font-size: 0.9rem;
    color: #666;
}

/* Accommodation Cards */
.product-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.accommodation-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.accommodation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.accommodation-card-title {
    text-align: center;
    font-size: 1.4rem;
    font-weight: 600;
    color: #00b2e3;
    margin: 1rem 0 0.5rem;
    padding: 0 1rem;
}

.accommodation-card-stars {
    text-align: center;
    margin-bottom: 1rem;
    color: #ffc107;
}

.accommodation-card-image {
    height: 200px;
    overflow: hidden;
}

.accommodation-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.accommodation-card-price-info {
    background-color: #00b2e3;
    color: #fff;
    text-align: center;
    padding: 0.75rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.accommodation-card-features {
    padding: 1rem;
}

.accommodation-card-feature {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.accommodation-card-feature i {
    color: #28a745;
    margin-right: 0.5rem;
}

.accommodation-card-website {
    margin-top: 1rem;
    text-align: center;
}

.accommodation-card-website a {
    color: #00b2e3;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
}

.accommodation-card-website a i {
    margin-right: 0.5rem;
}

.accommodation-card-rooms {
    padding: 0 1rem 1rem;
}

.accommodation-card-room {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.accommodation-card-room:hover {
    border-color: #00b2e3;
    background-color: #f8f9fa;
}

.accommodation-card-room input[type="radio"] {
    margin-right: 0.5rem;
}

.room-name {
    flex: 1;
    font-size: 0.9rem;
}

.room-price {
    font-weight: 600;
    color: #333;
}

.accommodation-card-room.single-option {
    cursor: default;
    justify-content: space-between;
}

.accommodation-card-btn {
    margin-top: auto;
    background-color: #00b2e3;
    color: #fff;
    border: none;
    padding: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.accommodation-card-btn:hover {
    background-color: #0099c4;
}

.accommodation-card-btn.added {
    background-color: #28a745;
}

/* Wizard Navigation */
.wizard-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
}

.wizard-navigation > div {
    display: flex;
    gap: 0.5rem;
}

.wizard-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    border: none;
}

.wizard-btn i {
    margin-right: 0.5rem;
}

.wizard-btn-secondary {
    background-color: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.wizard-btn-secondary:hover {
    background-color: #e9ecef;
}

.wizard-btn-primary {
    background-color: #36b1dc;
    color: #fff;
}

.wizard-btn-primary:hover {
    background-color: #2a9bc2;
}

.wizard-btn-danger {
    background-color: #dc3545;
    color: #fff;
}

.wizard-btn-danger:hover {
    background-color: #c82333;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .option-cards {
        flex-direction: column;
    }

    .option-card {
        width: 100%;
    }

    .product-cards {
        grid-template-columns: 1fr;
    }

    .wizard-content {
        padding: 1.5rem;
    }
}
