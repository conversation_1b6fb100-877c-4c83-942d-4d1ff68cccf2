// Define the Alpine.js data function globally
window.accommodationWizardData = function() {
        return {
            currentStep: 1,
            totalSteps: 3,
            hasMainPretour: false,
            bucharest: {
                selected: null, // 'yes' or 'no'
                nights: 1
            },
            brasov: {
                selected: null, // 'pre', 'main', 'post'
                nights: 1
            },
            products: [],
            loading: false,
            showModal: false,
            modalProduct: null,
            modalProgress: 0,
            progressInterval: null,

            init() {
                // Check if user has Main Pretour (product ID 743)
                this.checkUserProducts();

                // Set progress bar width
                this.updateProgressBar();
            },

            // Check if user has purchased specific products
            async checkUserProducts() {
                this.loading = true;

                try {
                    const response = await fetch(lci_ajax.ajax_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'check_user_products',
                            nonce: lci_ajax.nonce,
                            product_id: 743 // Main Pretour ID
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.hasMainPretour = data.data.has_product;
                    }
                } catch (error) {
                    console.error('Error checking user products:', error);
                } finally {
                    this.loading = false;
                }
            },

            // Update progress bar based on current step
            updateProgressBar() {
                const progressPercent = ((this.currentStep - 1) / (this.totalSteps - 1)) * 100;
                document.querySelector('.wizard-progress-bar-inner').style.width = `${progressPercent}%`;
            },

            // Go to next step
            nextStep() {
                if (this.currentStep < this.totalSteps) {
                    this.currentStep++;
                    this.updateProgressBar();

                    // Load products if we're at the product selection step
                    if (this.currentStep === this.totalSteps) {
                        this.loadProducts();
                    }
                }
            },

            // Go to previous step
            prevStep() {
                if (this.currentStep > 1) {
                    this.currentStep--;
                    this.updateProgressBar();
                }
            },

            // Select Bucharest option
            selectBucharest(option) {
                this.bucharest.selected = option;

                // If they don't need Bucharest accommodation, skip to Brasov options
                if (option === 'no') {
                    setTimeout(() => this.nextStep(), 300);
                }
            },

            // Select Brasov option
            selectBrasov(option) {
                this.brasov.selected = option;
                setTimeout(() => this.nextStep(), 300);
            },

            // Increase nights
            increaseNights(location) {
                if (location === 'bucharest') {
                    this.bucharest.nights++;
                } else {
                    this.brasov.nights++;
                }
            },

            // Decrease nights
            decreaseNights(location) {
                if (location === 'bucharest' && this.bucharest.nights > 1) {
                    this.bucharest.nights--;
                } else if (location === 'brasov' && this.brasov.nights > 1) {
                    this.brasov.nights--;
                }
            },

            // Load products based on selection
            async loadProducts() {
                this.loading = true;
                this.products = [];

                let categoryId;
                let metaQuery = {};

                // Determine which products to load
                if (this.hasMainPretour && this.bucharest.selected === 'yes') {
                    // Bucharest accommodation (category ID 33)
                    categoryId = 33;
                    metaQuery = {
                        key: '_nights',
                        value: this.bucharest.nights,
                        compare: '='
                    };
                } else {
                    // Brasov accommodation
                    switch (this.brasov.selected) {
                        case 'pre':
                            categoryId = 37; // Pre-event accommodation
                            metaQuery = {
                                key: '_timing',
                                value: 'before',
                                compare: '='
                            };
                            break;
                        case 'main':
                            categoryId = 1; // Main event accommodation
                            break;
                        case 'post':
                            categoryId = 37; // Post-event accommodation
                            metaQuery = {
                                key: '_timing',
                                value: 'after',
                                compare: '='
                            };
                            break;
                    }
                }

                try {
                    const response = await fetch(lci_ajax.ajax_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'get_accommodation_products',
                            nonce: lci_ajax.nonce,
                            category_id: categoryId,
                            meta_query: JSON.stringify(metaQuery)
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.products = data.data.products;
                    }
                } catch (error) {
                    console.error('Error loading products:', error);
                } finally {
                    this.loading = false;
                }
            },

            // Add product to cart
            async addToCart(productId) {
                const product = this.products.find(p => p.id === productId);
                if (!product) return;

                this.loading = true;

                try {
                    const response = await fetch(lci_ajax.ajax_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'lci-dashboard-add-to-cart',
                            nonce: lci_ajax.nonce,
                            product_id: productId,
                            quantity: 1
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Show success modal
                        this.modalProduct = product;
                        this.showModal = true;
                        this.startModalProgress();

                        // Update mini cart if available
                        if (typeof updateMiniCart === 'function') {
                            updateMiniCart(data.data.cart_count, data.data.cart_total);
                        }

                        // Mark product as added in the UI
                        product.added = true;
                    }
                } catch (error) {
                    console.error('Error adding to cart:', error);
                } finally {
                    this.loading = false;
                }
            },

            // Start modal progress bar
            startModalProgress() {
                this.modalProgress = 0;
                clearInterval(this.progressInterval);

                const duration = 3000; // 3 seconds
                const interval = 30; // Update every 30ms
                const steps = duration / interval;
                const increment = 100 / steps;

                this.progressInterval = setInterval(() => {
                    this.modalProgress += increment;

                    if (this.modalProgress >= 100) {
                        this.stopModalProgress();
                        this.closeModal();
                    }
                }, interval);
            },

            // Stop modal progress bar
            stopModalProgress() {
                clearInterval(this.progressInterval);
                this.progressInterval = null;
            },

            // Close modal
            closeModal() {
                this.showModal = false;
                this.stopModalProgress();
            },

            // Get current step title
            getStepTitle() {
                if (this.currentStep === 1) {
                    return this.hasMainPretour ? 'Bucharest Accommodation' : 'Brasov Accommodation';
                } else if (this.currentStep === 2) {
                    if (this.hasMainPretour && this.bucharest.selected === 'yes') {
                        return 'Bucharest Accommodation Nights';
                    } else {
                        return 'Brasov Accommodation Options';
                    }
                } else {
                    if (this.hasMainPretour && this.bucharest.selected === 'yes') {
                        return 'Bucharest Accommodation Options';
                    } else {
                        switch (this.brasov.selected) {
                            case 'pre': return 'Pre-Event Accommodation Options';
                            case 'main': return 'Main Event Accommodation Options';
                            case 'post': return 'Post-Event Accommodation Options';
                            default: return 'Accommodation Options';
                        }
                    }
                }
            },

            // Check if current step is valid
            isStepValid() {
                if (this.currentStep === 1) {
                    return this.hasMainPretour ? this.bucharest.selected !== null : true;
                } else if (this.currentStep === 2) {
                    if (this.hasMainPretour && this.bucharest.selected === 'yes') {
                        return this.bucharest.nights > 0;
                    } else {
                        return this.brasov.selected !== null;
                    }
                }
                return true;
            }
        };
    };

// Initialize Alpine.js when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, accommodationWizardData is:', typeof window.accommodationWizardData);

    // Make sure Alpine is loaded
    if (typeof Alpine !== 'undefined') {
        console.log('Alpine.js is loaded');
    } else {
        console.log('Alpine.js is not loaded yet, waiting...');

        // Wait for Alpine to load
        const checkAlpine = setInterval(function() {
            if (typeof Alpine !== 'undefined') {
                console.log('Alpine.js is now loaded');
                clearInterval(checkAlpine);
            }
        }, 100);
    }
});
