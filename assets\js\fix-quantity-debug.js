/**
 * Debug and fix for quantity issues in regalia shop
 */
(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Quantity debug fix loaded');

        // Fix the add to cart buttons to properly read quantity
        const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

        addToCartButtons.forEach(button => {
            // Clone and replace to remove any existing event listeners
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Add our fixed event listener
            newButton.addEventListener('click', function(e) {
                e.preventDefault();

                const productId = this.getAttribute('data-product-id');
                const originalText = this.innerHTML;
                const productCard = this.closest('.regalia-product-card');

                if (!productCard) {
                    console.error('Product card not found');
                    return;
                }

                // Get quantity from input field if available
                const quantityCard = productCard.querySelector('.regalia-quantity-card');
                let quantity = 1;

                if (quantityCard) {
                    const quantityInput = quantityCard.querySelector('.quantity-input-field');
                    if (quantityInput) {
                        quantity = parseInt(quantityInput.value) || 1;
                        console.log('Selected quantity:', quantity);
                    }
                }

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';
                this.disabled = true;

                // Use our custom AJAX endpoint
                const addToCartUrl = ajaxurl + '?action=lci-dashboard-add-to-cart';

                // Log the data we're sending
                console.log('Adding to cart:', {
                    product_id: productId,
                    quantity: quantity,
                    security: lci_nonce
                });

                fetch(addToCartUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({
                        product_id: productId,
                        quantity: quantity,
                        security: lci_nonce
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Cart response:', data);

                    // Check if data is valid
                    if (!data) {
                        throw new Error('Invalid response from server');
                    }

                    if (data.error) {
                        // Show error
                        alert(data.message || 'Error adding product to cart');
                    } else if (data.success) {
                        // Hide the quantity card if it exists
                        if (quantityCard) {
                            quantityCard.classList.remove('active');
                        }

                        // Get product image and name for confirmation modal
                        const productImage = productCard.querySelector('.regalia-product-image img');
                        const productTitle = productCard.querySelector('.regalia-product-title');

                        // Get the cart data from the response
                        const cartCount = data.cart_count || 0;
                        const cartTotal = data.cart_total || '0.00 €';

                        // Update mini cart button if it exists
                        const miniCartButton = document.getElementById('mini-cart-button');
                        if (miniCartButton) {
                            const countBadge = miniCartButton.querySelector('.badge');
                            const totalText = miniCartButton.querySelector('.mini-cart-button-text');

                            if (countBadge) {
                                countBadge.textContent = cartCount;
                                countBadge.style.display = cartCount > 0 ? '' : 'none';
                            }

                            if (totalText) {
                                totalText.innerHTML = cartTotal;
                            }
                        }

                        // Update button to show "Added to cart!"
                        this.innerHTML = '<i class="fas fa-check me-2"></i> Added to cart!';
                        this.disabled = false;

                        // Reset button state after 2 seconds
                        setTimeout(() => {
                            this.innerHTML = originalText;
                        }, 2000);

                        // Show confirmation using Alpine.js modal
                        if (typeof showAddToCartConfirmation === 'function') {
                            showAddToCartConfirmation(
                                productTitle ? productTitle.textContent : 'Product',
                                productImage ? productImage.src : ''
                            );
                        } else {
                            // Fallback: dispatch an event that the confirmation component might listen for
                            window.dispatchEvent(new CustomEvent('show-add-to-cart-confirmation', {
                                detail: {
                                    productName: productTitle ? productTitle.textContent : 'Product',
                                    productImage: productImage ? productImage.src : ''
                                }
                            }));
                        }

                        // Update mini cart by dispatching an event
                        window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
                            detail: {
                                cart_count: cartCount,
                                cart_total: cartTotal
                            }
                        }));

                        // Also trigger standard WooCommerce events for compatibility
                        jQuery(document.body).trigger('added_to_cart', [null, null, null]);
                        jQuery(document.body).trigger('updated_cart_totals');
                    }
                })
                .catch(error => {
                    console.error('Error adding to cart:', error);
                    alert('Could not add product to your order. Please try again.');

                    // Hide the quantity card if it exists
                    if (quantityCard) {
                        quantityCard.classList.remove('active');
                    }
                })
                .finally(() => {
                    // Reset button
                    this.innerHTML = originalText;
                    this.disabled = false;
                });
            });
        });

        // Also fix variable product buttons
        const addVariableToCartButtons = document.querySelectorAll('.add-variable-to-cart-btn');

        addVariableToCartButtons.forEach(button => {
            // Clone and replace to remove any existing event listeners
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Add our fixed event listener
            newButton.addEventListener('click', function(e) {
                e.preventDefault();

                const productId = this.getAttribute('data-product-id');
                const originalText = this.innerHTML;
                const productCard = this.closest('.regalia-product-card');
                const variationCard = this.closest('.regalia-variation-card');

                if (!productCard || !variationCard) {
                    console.error('Product card or variation card not found');
                    return;
                }

                // Get selected variations
                const variationSelects = variationCard.querySelectorAll('.variation-dropdown');
                const variationData = {};
                let allVariationsSelected = true;

                variationSelects.forEach(select => {
                    const attributeName = select.getAttribute('data-attribute');
                    const attributeValue = select.value;

                    if (!attributeValue) {
                        allVariationsSelected = false;
                        select.classList.add('border-red-500');
                    } else {
                        select.classList.remove('border-red-500');
                        variationData[`attribute_${attributeName}`] = attributeValue;
                    }
                });

                if (!allVariationsSelected) {
                    alert('Please select all product options before ordering.');
                    return;
                }

                // Get quantity
                const quantityInput = variationCard.querySelector('.quantity-input-field');
                const quantity = quantityInput ? parseInt(quantityInput.value) || 1 : 1;
                console.log('Selected variation quantity:', quantity);

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';
                this.disabled = true;

                const formData = new URLSearchParams({
                    product_id: productId,
                    quantity: quantity,
                    security: lci_nonce
                });

                // Add variation data
                for (const [key, value] of Object.entries(variationData)) {
                    formData.append(key, value);
                }

                // Log the data we're sending
                console.log('Adding variation to cart:', Object.fromEntries(formData));

                // Use our custom AJAX endpoint
                const addToCartUrl = ajaxurl + '?action=lci-dashboard-add-to-cart';

                fetch(addToCartUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Cart response for variation:', data);

                    // Check if data is valid
                    if (!data) {
                        throw new Error('Invalid response from server');
                    }

                    if (data.error) {
                        // Show error
                        alert(data.message || 'Error adding product to cart');
                    } else if (data.success) {
                        // Hide the variation card
                        variationCard.classList.remove('active');

                        // Get product image and name for confirmation modal
                        const productImage = productCard.querySelector('.regalia-product-image img');
                        const productTitle = productCard.querySelector('.regalia-product-title');

                        // Get the cart data from the response
                        const cartCount = data.cart_count || 0;
                        const cartTotal = data.cart_total || '0.00 €';

                        // Update mini cart button if it exists
                        const miniCartButton = document.getElementById('mini-cart-button');
                        if (miniCartButton) {
                            const countBadge = miniCartButton.querySelector('.badge');
                            const totalText = miniCartButton.querySelector('.mini-cart-button-text');

                            if (countBadge) {
                                countBadge.textContent = cartCount;
                                countBadge.style.display = cartCount > 0 ? '' : 'none';
                            }

                            if (totalText) {
                                totalText.innerHTML = cartTotal;
                            }
                        }

                        // Update button to show "Added to cart!"
                        this.innerHTML = '<i class="fas fa-check me-2"></i> Added to cart!';
                        this.disabled = false;

                        // Reset button state after 2 seconds
                        setTimeout(() => {
                            this.innerHTML = originalText;
                        }, 2000);

                        // Show confirmation using Alpine.js modal
                        if (typeof showAddToCartConfirmation === 'function') {
                            showAddToCartConfirmation(
                                productTitle ? productTitle.textContent : 'Product',
                                productImage ? productImage.src : ''
                            );
                        } else {
                            // Fallback: dispatch an event that the confirmation component might listen for
                            window.dispatchEvent(new CustomEvent('show-add-to-cart-confirmation', {
                                detail: {
                                    productName: productTitle ? productTitle.textContent : 'Product',
                                    productImage: productImage ? productImage.src : ''
                                }
                            }));
                        }

                        // Update mini cart by dispatching an event
                        window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
                            detail: {
                                cart_count: cartCount,
                                cart_total: cartTotal
                            }
                        }));

                        // Also trigger standard WooCommerce events for compatibility
                        jQuery(document.body).trigger('added_to_cart', [null, null, null]);
                        jQuery(document.body).trigger('updated_cart_totals');
                    }
                })
                .catch(error => {
                    console.error('Error adding variation to cart:', error);
                    alert('Could not add product to your order. Please try again.');
                })
                .finally(() => {
                    // Reset button
                    this.innerHTML = originalText;
                    this.disabled = false;
                });
            });
        });
    });
})();
