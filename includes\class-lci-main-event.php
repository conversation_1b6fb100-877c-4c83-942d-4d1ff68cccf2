<?php
/**
 * LCI 2025 Dashboard Main Event Handler
 *
 * Manages main event-related operations for the LCI 2025 Dashboard plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class LCI_Main_Event {
    // Product IDs for different event packages
    const COUNCILOR_PACKAGE_ID = 40;
    const MAIN_EVENT_ID = 41;
    const PARTNERS_PACKAGE_ID = 42;

    // Log file path
    const LOG_FILE = 'main-event-sync.log';

    /**
     * Initialize the main event handler
     */
    public static function init() {
        // Register activation hook for table creation
        register_activation_hook(LCI2025_PLUGIN_FILE, [__CLASS__, 'create_tables']);

        // Add AJAX handlers
        add_action('wp_ajax_lci_sync_main_event_participants', [__CLASS__, 'ajax_sync_main_event_participants']);
        add_action('wp_ajax_lci_get_main_event_participants', [__CLASS__, 'ajax_get_main_event_participants']);
        add_action('wp_ajax_lci_update_main_event_participant', [__CLASS__, 'ajax_update_main_event_participant']);
        add_action('wp_ajax_lci_delete_main_event_participant', [__CLASS__, 'ajax_delete_main_event_participant']);
        add_action('wp_ajax_lci_clear_main_event_table', [__CLASS__, 'ajax_clear_main_event_table']);
        add_action('wp_ajax_lci_recreate_main_event_table', [__CLASS__, 'ajax_recreate_main_event_table']);
        add_action('wp_ajax_lci_get_main_event_countries', [__CLASS__, 'ajax_get_main_event_countries']);
    }

    /**
     * Write to custom log file
     *
     * @param string $message Message to log
     * @param string $level Log level (info, warning, error)
     * @return void
     */
    public static function log($message, $level = 'info') {
        $log_dir = WP_CONTENT_DIR;
        $log_file = $log_dir . '/' . self::LOG_FILE;

        $timestamp = current_time('mysql');
        $formatted_message = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;

        // Append to log file
        file_put_contents($log_file, $formatted_message, FILE_APPEND);
    }

    /**
     * Create main event tables
     *
     * @param bool $force Force recreation of tables even if they exist
     */
    public static function create_tables($force = false) {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Main Event table
        $table_name = $wpdb->prefix . 'lci2025_main_event';

        self::log("Creating main event table: $table_name", "info");

        // Check if table exists before dropping
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        self::log("Table exists before drop: " . ($table_exists ? "Yes" : "No"), "info");

        // Check if we need to recreate the table
        if ($table_exists) {
            if ($force) {
                self::log("Force parameter is true, will recreate table regardless of structure", "info");
                $wpdb->query("DROP TABLE IF EXISTS $table_name");
                self::log("Table dropped due to force parameter", "info");
            } else {
                self::log("Table exists, checking structure...", "info");
                $table_structure = $wpdb->get_results("DESCRIBE $table_name");
                self::log("Current table structure: " . print_r($table_structure, true), "info");

                // Check if we need to recreate the table
                $needs_recreation = false;

                // Check for required columns
                $required_columns = ['country_code', 'event_package'];
                foreach ($required_columns as $column) {
                    $column_exists = false;
                    foreach ($table_structure as $field) {
                        if ($field->Field === $column) {
                            $column_exists = true;
                            break;
                        }
                    }

                    if (!$column_exists) {
                        self::log("Column '$column' is missing, table needs to be recreated", "info");
                        $needs_recreation = true;
                        break;
                    }
                }

                if ($needs_recreation) {
                    self::log("Dropping table to recreate with updated structure", "info");
                    $wpdb->query("DROP TABLE IF EXISTS $table_name");
                    self::log("Table dropped", "info");
                } else {
                    self::log("Table structure is up to date, no need to recreate", "info");
                    return; // Skip recreation if the table structure is already correct
                }
            }
        } else {
            self::log("Table does not exist, will create it", "info");
        }

        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            participant_id BIGINT(20) UNSIGNED NOT NULL,
            unique_reg_id VARCHAR(12) NOT NULL,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            order_id BIGINT(20) UNSIGNED NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(50),
            country VARCHAR(100),
            country_code VARCHAR(10),
            order_date DATETIME,
            payment_status VARCHAR(50) NOT NULL,
            event_package VARCHAR(100) NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY unique_reg_id (unique_reg_id),
            KEY participant_id (participant_id),
            KEY user_id (user_id),
            KEY order_id (order_id),
            KEY country_code (country_code),
            KEY event_package (event_package)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result = dbDelta($sql);

        // Log the result of the table creation
        self::log("Table creation result: " . print_r($result, true), "info");

        // Verify table was created
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        self::log("Table exists after creation: " . ($table_exists ? "Yes" : "No"), "info");

        if ($table_exists) {
            $table_structure = $wpdb->get_results("DESCRIBE $table_name");
            self::log("Created table structure: " . print_r($table_structure, true), "info");
        }
    }

    /**
     * Check if main event table exists
     *
     * @return array Table existence and data status
     */
    public static function check_main_event_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_main_event';

        // Check if table exists
        $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        $count = 0;

        if ($exists) {
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        }

        return [
            'exists' => $exists,
            'has_data' => $count > 0,
            'count' => $count
        ];
    }

    /**
     * Get main event participants
     *
     * @param array $args Query arguments
     * @return array Main event participants
     */
    public static function get_main_event_participants($args = []) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_main_event';

        self::log("Getting main event participants with args: " . print_r($args, true), "info");

        // Default arguments
        $defaults = [
            'orderby' => 'order_date',
            'order' => 'DESC',
            'limit' => -1,
            'offset' => 0,
            'search' => '',
            'event_package' => '',
            'country' => '',
            'payment_status' => ''
        ];

        $args = wp_parse_args($args, $defaults);

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

        if (!$table_exists) {
            // Create the table if it doesn't exist
            self::create_tables();

            // Check again if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if (!$table_exists) {
                // If still doesn't exist, return empty result
                return [
                    'participants' => [],
                    'total' => 0,
                    'source' => 'none'
                ];
            }
        }

        // Build query
        $query = "SELECT * FROM $table_name";
        $count_query = "SELECT COUNT(*) FROM $table_name";
        $where = [];

        // Add search condition
        if (!empty($args['search'])) {
            $search = '%' . $wpdb->esc_like($args['search']) . '%';
            $where[] = $wpdb->prepare("(first_name LIKE %s OR last_name LIKE %s OR email LIKE %s OR unique_reg_id LIKE %s)",
                $search, $search, $search, $search);
        }

        // Add event package filter with partial matching
        if (!empty($args['event_package'])) {
            $package = '%' . $wpdb->esc_like($args['event_package']) . '%';
            $where[] = $wpdb->prepare("event_package LIKE %s", $package);
            self::log("Added package filter: " . $args['event_package'], "info");
        }

        // Add country filter
        if (!empty($args['country'])) {
            $where[] = $wpdb->prepare("country = %s", $args['country']);
            self::log("Added country filter: " . $args['country'], "info");
        }

        // Add payment status filter
        if (!empty($args['payment_status'])) {
            $where[] = $wpdb->prepare("payment_status = %s", $args['payment_status']);
            self::log("Added payment status filter: " . $args['payment_status'], "info");
        }

        // Combine WHERE clauses
        if (!empty($where)) {
            $query .= " WHERE " . implode(" AND ", $where);
            $count_query .= " WHERE " . implode(" AND ", $where);
        }

        // Add ORDER BY
        if (!empty($args['orderby'])) {
            $query .= " ORDER BY {$args['orderby']} {$args['order']}";
        }

        // Add LIMIT
        if ($args['limit'] > 0) {
            $query .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['limit']);
        }

        // Get total count
        $total = $wpdb->get_var($count_query);

        // Get participants
        $participants = $wpdb->get_results($query, ARRAY_A);

        // Log the results
        self::log("Found " . count($participants) . " participants", "info");
        if (count($participants) > 0) {
            self::log("First participant: " . print_r($participants[0], true), "info");
        }

        // Log the SQL query
        self::log("SQL Query: " . $query, "info");

        // Check for SQL errors
        if ($wpdb->last_error) {
            self::log("SQL Error: " . $wpdb->last_error, "error");
        }

        // Ensure all fields are properly formatted for JSON
        foreach ($participants as &$participant) {
            // Convert numeric strings to numbers
            foreach (['id', 'participant_id', 'user_id', 'order_id'] as $field) {
                if (isset($participant[$field])) {
                    $participant[$field] = intval($participant[$field]);
                }
            }

            // Ensure date fields are properly formatted
            foreach (['order_date', 'created_at', 'updated_at'] as $field) {
                if (isset($participant[$field])) {
                    // Ensure date is in ISO format
                    $date = strtotime($participant[$field]);
                    if ($date) {
                        $participant[$field] = date('c', $date);
                    }
                }
            }
        }

        // Log the formatted participants
        if (count($participants) > 0) {
            self::log("First participant after formatting: " . print_r($participants[0], true), "info");
        }

        return [
            'participants' => $participants,
            'total' => $total,
            'source' => 'database'
        ];
    }

    /**
     * Sync main event participants from WooCommerce orders
     *
     * @param bool $debug Whether to include debug information in the result
     * @return array Result of sync operation
     */
    public static function sync_main_event_participants($debug = false) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_main_event';
        $participants_table = $wpdb->prefix . 'lci2025_participants';

        // Log table structure for debugging
        if ($debug) {
            self::log("Starting main event sync process", "info");

            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
            self::log("Table exists check: " . ($table_exists ? "Yes" : "No"), "info");

            if ($table_exists) {
                $table_structure = $wpdb->get_results("DESCRIBE $table_name");
                self::log("Main Event Table Structure: " . print_r($table_structure, true), "info");
            }
        }

        // Always ensure the table exists before syncing
        self::create_tables();

        // Get all orders with relevant product IDs
        $orders = wc_get_orders([
            'limit' => -1,
            'status' => ['processing', 'completed', 'on-hold'],
            'return' => 'ids',
        ]);

        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'total_count' => count($orders),
            'errors' => [],
            'councilor_main_count' => 0,
            'main_event_count' => 0,
            'partners_count' => 0
        ];

        // Process each order
        foreach ($orders as $order_id) {
            $order = wc_get_order($order_id);

            if (!$order) {
                $result['error_count']++;
                $result['errors'][] = "Order #$order_id not found";
                continue;
            }

            // Check for product IDs in this order
            $has_councilor = false;
            $has_main_event = false;
            $has_partners = false;

            foreach ($order->get_items() as $item) {
                $product_id = $item->get_product_id();

                if ($product_id == self::COUNCILOR_PACKAGE_ID) {
                    $has_councilor = true;
                } elseif ($product_id == self::MAIN_EVENT_ID) {
                    $has_main_event = true;
                } elseif ($product_id == self::PARTNERS_PACKAGE_ID) {
                    $has_partners = true;
                }
            }

            // Determine event package
            $event_package = '';
            if ($has_councilor && $has_main_event) {
                $event_package = 'Councilor Package + Main Event';
                $result['councilor_main_count']++;
            } elseif ($has_main_event) {
                $event_package = 'Main Event';
                $result['main_event_count']++;
            } elseif ($has_partners) {
                $event_package = 'Partners Package';
                $result['partners_count']++;
            } else {
                // Skip orders without relevant products
                continue;
            }

            // Get user ID from order
            $user_id = $order->get_user_id();

            // Get additional user information from WordPress users table
            $user_info = [];
            if ($user_id) {
                $user = get_userdata($user_id);
                if ($user) {
                    $user_info = [
                        'user_login' => $user->user_login,
                        'user_email' => $user->user_email,
                        'display_name' => $user->display_name,
                        'user_registered' => $user->user_registered
                    ];
                }
            }

            // Try to find participant in main participants table
            $participant = null;
            if ($wpdb->get_var("SHOW TABLES LIKE '$participants_table'") === $participants_table) {
                $participant = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $participants_table WHERE order_id = %d",
                    $order_id
                ));
            }

            // Get country information
            $country_code = $order->get_billing_country();
            $country_name = WC()->countries->get_countries()[$country_code] ?? $country_code;

            // Prepare data for insertion/update
            $now = current_time('mysql');
            $data = [
                'user_id' => $user_id,
                'order_id' => $order_id,
                'first_name' => $order->get_billing_first_name(),
                'last_name' => $order->get_billing_last_name(),
                'email' => $order->get_billing_email() ?: ($user_info['user_email'] ?? ''),
                'phone' => $order->get_billing_phone(),
                'country' => $country_name,
                'country_code' => $country_code,
                'order_date' => $order->get_date_created() ? $order->get_date_created()->date('Y-m-d H:i:s') : $now,
                'payment_status' => $order->get_status(),
                'event_package' => $event_package,
                'updated_at' => $now
            ];

            // If we found a participant in the main table, use their ID and unique_reg_id
            if ($participant) {
                $data['participant_id'] = $participant->id;
                $data['unique_reg_id'] = $participant->unique_reg_id;
            } else {
                // Generate a unique registration ID
                if (class_exists('LCI_Database') && method_exists('LCI_Database', 'generate_unique_reg_id')) {
                    $data['unique_reg_id'] = LCI_Database::generate_unique_reg_id();
                } else {
                    // Fallback if LCI_Database class is not available
                    $data['unique_reg_id'] = uniqid('ME', true);
                }
                $data['participant_id'] = 0;
            }

            // Make sure we have required fields
            if (empty($data['email'])) {
                $result['error_count']++;
                $error_msg = "Error processing order #$order_id: Missing email address";
                $result['errors'][] = $error_msg;
                self::log($error_msg, "error");
                continue;
            }

            if (empty($data['unique_reg_id'])) {
                $result['error_count']++;
                $error_msg = "Error processing order #$order_id: Could not generate unique registration ID";
                $result['errors'][] = $error_msg;
                self::log($error_msg, "error");
                continue;
            }

            // Log the data we're about to insert/update
            if ($debug) {
                self::log("Processing order #$order_id with data: " . print_r($data, true), "info");
            }

            try {
                // Check if this order is already in the main event table
                $existing = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $table_name WHERE order_id = %d",
                    $order_id
                ));

                if ($existing) {
                    // Update existing record
                    $data['updated_at'] = $now;

                    self::log("Updating existing record for order #$order_id with data: " . print_r($data, true), "info");

                    // Log the SQL query that will be executed
                    $wpdb->update(
                        $table_name,
                        $data,
                        ['id' => $existing->id]
                    );

                    self::log("Update SQL: " . $wpdb->last_query, "info");

                    if ($wpdb->last_error) {
                        throw new Exception($wpdb->last_error);
                    }

                    // Verify the update
                    $updated_record = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $table_name WHERE id = %d",
                        $existing->id
                    ));

                    if ($updated_record) {
                        self::log("Record updated successfully for order #$order_id", "info");
                    } else {
                        throw new Exception("Failed to verify updated record");
                    }
                } else {
                    // Insert new record
                    $data['created_at'] = $now;

                    self::log("Inserting new record for order #$order_id with data: " . print_r($data, true), "info");

                    $insert_result = $wpdb->insert($table_name, $data);

                    self::log("Insert SQL: " . $wpdb->last_query, "info");

                    if ($insert_result === false) {
                        throw new Exception($wpdb->last_error ?: "Unknown database error during insert");
                    }

                    // Get the inserted ID
                    $insert_id = $wpdb->insert_id;
                    self::log("Inserted record with ID: $insert_id", "info");

                    // Verify the insert
                    $inserted_record = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $table_name WHERE id = %d",
                        $insert_id
                    ));

                    if ($inserted_record) {
                        self::log("Record inserted successfully for order #$order_id", "info");
                    } else {
                        throw new Exception("Failed to verify inserted record");
                    }
                }

                $result['success_count']++;
            } catch (Exception $e) {
                $result['error_count']++;
                $error_message = "Error processing order #$order_id: " . $e->getMessage();
                $result['errors'][] = $error_message;
                self::log($error_message, "error");

                // Log additional details for debugging
                if ($debug) {
                    $trace = $e->getTraceAsString();
                    self::log("Exception trace for order #$order_id: " . $trace, "error");

                    // Log the SQL query if available
                    if (!empty($wpdb->last_query)) {
                        self::log("Last SQL query: " . $wpdb->last_query, "error");
                    }
                }
            }
        }

        return $result;
    }

    /**
     * AJAX handler for syncing main event participants
     */
    public static function ajax_sync_main_event_participants() {
        self::log("AJAX sync request received", "info");

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            self::log("Invalid nonce in sync request", "error");
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            self::log("Permission denied for user: " . get_current_user_id(), "error");
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        try {
            self::log("Starting sync process", "info");

            // Ensure the table exists
            self::create_tables();

            // Sync participants with debug mode enabled
            self::log("Calling sync_main_event_participants with debug mode", "info");
            $result = self::sync_main_event_participants(true);

            // Get the first few errors to display
            $error_sample = array_slice($result['errors'], 0, 5);
            $error_message = '';

            if (!empty($error_sample)) {
                $error_message = ' Sample errors: ' . implode('; ', $error_sample);
                if (count($result['errors']) > 5) {
                    $error_message .= ' (and ' . (count($result['errors']) - 5) . ' more)';
                }
            }

            $response_message = sprintf(
                'Sync completed. Processed %d orders: %d successful, %d errors. Found %d Councilor+Main, %d Main Event, %d Partners Package.%s',
                $result['total_count'],
                $result['success_count'],
                $result['error_count'],
                $result['councilor_main_count'],
                $result['main_event_count'],
                $result['partners_count'],
                $error_message
            );

            self::log("Sync completed with results: " . $response_message, "info");

            wp_send_json_success([
                'message' => $response_message,
                'result' => $result
            ]);
        } catch (Exception $e) {
            $error_msg = 'Error syncing participants: ' . $e->getMessage();
            self::log($error_msg, "error");
            self::log("Exception trace: " . $e->getTraceAsString(), "error");

            wp_send_json_error([
                'message' => $error_msg
            ]);
        }
    }

    /**
     * AJAX handler for getting main event participants
     */
    public static function ajax_get_main_event_participants() {
        self::log("AJAX get participants request received", "info");

        // Check nonce
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'lci_admin_nonce')) {
            self::log("Invalid nonce in get participants request", "error");
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            self::log("Permission denied for user: " . get_current_user_id(), "error");
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        try {
            self::log("Starting get participants process", "info");

            // Ensure the table exists
            self::create_tables();

            // Direct check to verify data exists in the table
            global $wpdb;
            $table_name = $wpdb->prefix . 'lci2025_main_event';
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            self::log("Direct database check: Table has $count records", "info");

            if ($count > 0) {
                $sample = $wpdb->get_row("SELECT * FROM $table_name LIMIT 1", ARRAY_A);
                self::log("Sample record from direct query: " . print_r($sample, true), "info");
            }

            // Get query parameters
            $search = isset($_REQUEST['search']) ? sanitize_text_field($_REQUEST['search']) : '';
            $event_package = isset($_REQUEST['event_package']) ? sanitize_text_field($_REQUEST['event_package']) : '';
            $country = isset($_REQUEST['country']) ? sanitize_text_field($_REQUEST['country']) : '';
            $payment_status = isset($_REQUEST['payment_status']) ? sanitize_text_field($_REQUEST['payment_status']) : '';
            $per_page = isset($_REQUEST['per_page']) ? intval($_REQUEST['per_page']) : -1; // -1 means no pagination

            // Log the filter parameters
            self::log("Filter parameters - Search: '$search', Package: '$event_package', Country: '$country', Status: '$payment_status', No pagination (all records)", "info");

            // No offset needed for no pagination
            $offset = 0;

            // Get participants
            $result = self::get_main_event_participants([
                'search' => $search,
                'event_package' => $event_package,
                'country' => $country,
                'payment_status' => $payment_status,
                'limit' => $per_page,
                'offset' => $offset
            ]);

            // If no participants found but we know they exist, add a manual check
            if (empty($result['participants']) && $count > 0) {
                self::log("No participants returned from get_main_event_participants but direct query shows $count records", "warning");

                // Try a direct query as a fallback
                global $wpdb;
                $table_name = $wpdb->prefix . 'lci2025_main_event';
                $direct_participants = $wpdb->get_results("SELECT * FROM $table_name", ARRAY_A);

                self::log("Direct query fallback found " . count($direct_participants) . " participants", "info");

                if (!empty($direct_participants)) {
                    $result['participants'] = $direct_participants;
                    $result['total'] = $count;
                    $result['source'] = 'direct_query';
                }
            }

            $response_data = [
                'participants' => $result['participants'],
                'total' => $result['total'],
                'source' => $result['source'],
                'direct_count' => $count // Add the direct count for debugging
            ];

            self::log("Returning " . count($result['participants']) . " participants, total: " . $result['total'], "info");

            // Log the first participant if available
            if (!empty($result['participants'])) {
                self::log("First participant in response: " . print_r($result['participants'][0], true), "info");
            } else {
                self::log("No participants found in the database", "warning");
            }

            wp_send_json_success($response_data);
        } catch (Exception $e) {
            $error_msg = 'Error getting participants: ' . $e->getMessage();
            self::log($error_msg, "error");
            self::log("Exception trace: " . $e->getTraceAsString(), "error");

            wp_send_json_error([
                'message' => $error_msg
            ]);
        }
    }

    /**
     * AJAX handler for updating a main event participant
     */
    public static function ajax_update_main_event_participant() {
        self::log("AJAX update participant request received", "info");

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            self::log("Invalid nonce in update request", "error");
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            self::log("Permission denied for user: " . get_current_user_id(), "error");
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        // Get participant ID
        $participant_id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        self::log("Updating participant ID: $participant_id", "info");

        if (!$participant_id) {
            self::log("Invalid participant ID", "error");
            wp_send_json_error(['message' => 'Invalid participant ID']);
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_main_event';

        // Check if participant exists
        $participant = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $participant_id
        ));

        if (!$participant) {
            self::log("Participant not found with ID: $participant_id", "error");
            wp_send_json_error(['message' => 'Participant not found']);
            return;
        }

        self::log("Found participant to update: " . print_r($participant, true), "info");

        // Get fields to update
        $fields = [
            'first_name', 'last_name', 'email', 'phone', 'country',
            'payment_status', 'event_package', 'user_id'
        ];

        $data = [];
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                if ($field === 'user_id') {
                    // Ensure user_id is an integer
                    $data[$field] = intval($_POST[$field]);
                } else {
                    $data[$field] = sanitize_text_field($_POST[$field]);
                }
            }
        }

        self::log("Update data: " . print_r($data, true), "info");

        // Add updated_at timestamp
        $data['updated_at'] = current_time('mysql');

        // Update participant
        $result = $wpdb->update(
            $table_name,
            $data,
            ['id' => $participant_id]
        );

        self::log("Update SQL: " . $wpdb->last_query, "info");

        if ($result === false) {
            $error = $wpdb->last_error ?: "Unknown error";
            self::log("Error updating participant: " . $error, "error");
            wp_send_json_error([
                'message' => 'Error updating participant: ' . $error
            ]);
            return;
        }

        // Get the updated participant
        $updated_participant = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $participant_id
        ), ARRAY_A);

        self::log("Participant updated successfully: " . print_r($updated_participant, true), "info");

        wp_send_json_success([
            'message' => 'Participant updated successfully',
            'participant' => $updated_participant
        ]);
    }

    /**
     * AJAX handler for deleting a main event participant
     */
    public static function ajax_delete_main_event_participant() {
        self::log("AJAX delete participant request received", "info");

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            self::log("Invalid nonce in delete request", "error");
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            self::log("Permission denied for user: " . get_current_user_id(), "error");
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        // Get participant ID
        $participant_id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        self::log("Deleting participant ID: $participant_id", "info");

        if (!$participant_id) {
            self::log("Invalid participant ID", "error");
            wp_send_json_error(['message' => 'Invalid participant ID']);
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_main_event';

        // Check if participant exists before deleting
        $participant = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $participant_id
        ));

        if (!$participant) {
            self::log("Participant not found with ID: $participant_id", "error");
            wp_send_json_error(['message' => 'Participant not found']);
            return;
        }

        self::log("Found participant to delete: " . print_r($participant, true), "info");

        // Delete participant
        $result = $wpdb->delete(
            $table_name,
            ['id' => $participant_id],
            ['%d']
        );

        self::log("Delete SQL: " . $wpdb->last_query, "info");

        if ($result === false) {
            $error = $wpdb->last_error ?: "Unknown error";
            self::log("Error deleting participant: " . $error, "error");
            wp_send_json_error([
                'message' => 'Error deleting participant: ' . $error
            ]);
            return;
        }

        self::log("Participant deleted successfully", "info");

        wp_send_json_success([
            'message' => 'Participant deleted successfully'
        ]);
    }

    /**
     * AJAX handler for clearing the main event table
     */
    public static function ajax_clear_main_event_table() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_main_event';

        self::log("Clearing main event table", "info");

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            self::log("Table does not exist, cannot clear", "error");
            wp_send_json_error(['message' => 'Table does not exist']);
            return;
        }

        // Truncate the table
        $result = $wpdb->query("TRUNCATE TABLE $table_name");
        self::log("Truncate query executed", "info");

        if ($result === false) {
            $error = $wpdb->last_error ?: "Unknown error";
            self::log("Error clearing table: " . $error, "error");
            wp_send_json_error([
                'message' => 'Error clearing table: ' . $error
            ]);
            return;
        }

        self::log("Table cleared successfully", "info");

        wp_send_json_success([
            'message' => 'Main event table cleared successfully'
        ]);
    }

    /**
     * AJAX handler for recreating the main event table
     */
    public static function ajax_recreate_main_event_table() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        try {
            self::log("Recreating main event table", "info");

            global $wpdb;
            $table_name = $wpdb->prefix . 'lci2025_main_event';

            // Force drop the table
            $wpdb->query("DROP TABLE IF EXISTS $table_name");
            self::log("Table dropped", "info");

            // Create the table with the latest structure
            self::create_tables(true);

            // Verify the table was created
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if ($table_exists) {
                self::log("Table recreated successfully", "info");

                wp_send_json_success([
                    'message' => 'Main event table recreated successfully'
                ]);
            } else {
                throw new Exception("Failed to recreate table");
            }
        } catch (Exception $e) {
            self::log("Error recreating table: " . $e->getMessage(), "error");

            wp_send_json_error([
                'message' => 'Error recreating table: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for getting all countries from the main event table
     */
    public static function ajax_get_main_event_countries() {
        // Check nonce
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        try {
            self::log("Getting all countries from main event table", "info");

            global $wpdb;
            $table_name = $wpdb->prefix . 'lci2025_main_event';

            // Check if table exists
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
                self::log("Table does not exist, cannot get countries", "error");
                wp_send_json_error(['message' => 'Table does not exist']);
                return;
            }

            // Get all unique countries
            $query = "SELECT DISTINCT country FROM $table_name WHERE country IS NOT NULL AND country != '' ORDER BY country ASC";
            $countries = $wpdb->get_col($query);

            self::log("Found " . count($countries) . " unique countries", "info");

            wp_send_json_success([
                'countries' => $countries
            ]);
        } catch (Exception $e) {
            $error_msg = 'Error getting countries: ' . $e->getMessage();
            self::log($error_msg, "error");

            wp_send_json_error([
                'message' => $error_msg
            ]);
        }
    }

    /**
     * Get registration data for a specific user
     *
     * @param int $user_id WordPress user ID
     * @return array|false Registration data or false if not found
     */
    public static function get_user_registration_data($user_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_main_event';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        if (!$table_exists) {
            self::log("Main event table does not exist when getting user registration data for user ID: $user_id", "warning");
            return false;
        }

        // Get registration data
        $registration = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE user_id = %d ORDER BY order_date DESC LIMIT 1",
                $user_id
            ),
            ARRAY_A
        );

        if ($registration) {
            self::log("Found registration data for user ID: $user_id in main event table", "info");
        } else {
            self::log("No registration data found for user ID: $user_id in main event table", "info");
        }

        return $registration;
    }
}
