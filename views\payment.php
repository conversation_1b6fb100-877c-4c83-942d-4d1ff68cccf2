<?php
/**
 * Payment view template for LCI 2025 Dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Payment gateways section removed

// Get product information if specified
$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
$product = $product_id ? wc_get_product($product_id) : null;

// Get cart items if no specific product is selected
$cart_items = array();
$cart_total = 0;
if (!$product && function_exists('WC')) {
    $cart_items = WC()->cart->get_cart();
    $cart_total = WC()->cart->get_cart_total();
}

// Get current user
$current_user = wp_get_current_user();
$user_data = lci_get_user_data($current_user->ID);
?>

<div class="lci-payment-container">
    <h2 class="lci-section-title">Complete Your Payment</h2>

    <?php if ($product) : ?>
    <!-- Single product payment -->
    <div class="lci-product-summary">
        <h3>Order Summary</h3>
        <div class="lci-product-details">
            <?php if ($product->get_image_id()) : ?>
            <div class="lci-product-image">
                <img src="<?php echo wp_get_attachment_image_url($product->get_image_id(), 'thumbnail'); ?>" alt="<?php echo esc_attr($product->get_name()); ?>">
            </div>
            <?php endif; ?>
            <div class="lci-product-info">
                <h4><?php echo esc_html($product->get_name()); ?></h4>
                <p class="lci-product-price"><?php echo wp_kses_post($product->get_price_html()); ?></p>
                <?php if ($product->get_short_description()) : ?>
                <div class="lci-product-description">
                    <?php echo wp_kses_post($product->get_short_description()); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php elseif (!empty($cart_items)) : ?>
    <!-- Cart items payment -->
    <div class="lci-cart-summary">
        <h3>Order Summary</h3>
        <div class="lci-cart-items">
            <?php foreach ($cart_items as $cart_item_key => $cart_item) :
                $cart_product = $cart_item['data'];
                $quantity = $cart_item['quantity'];
            ?>
            <div class="lci-cart-item">
                <?php if ($cart_product->get_image_id()) : ?>
                <div class="lci-cart-item-image">
                    <img src="<?php echo wp_get_attachment_image_url($cart_product->get_image_id(), 'thumbnail'); ?>" alt="<?php echo esc_attr($cart_product->get_name()); ?>">
                </div>
                <?php endif; ?>
                <div class="lci-cart-item-details">
                    <div class="lci-cart-item-name"><?php echo esc_html($cart_product->get_name()); ?></div>
                    <div class="lci-cart-item-quantity">Quantity: <?php echo esc_html($quantity); ?></div>
                </div>
                <div class="lci-cart-item-price"><?php echo wp_kses_post(WC()->cart->get_product_subtotal($cart_product, $quantity)); ?></div>
            </div>
            <?php endforeach; ?>

            <div class="lci-cart-total">
                <strong>Total:</strong> <?php echo wp_kses_post($cart_total); ?>
            </div>
        </div>
    </div>
    <?php else : ?>
    <div class="lci-notice lci-notice-warning">
        <p>No products selected for payment. Please add products to your cart first.</p>
    </div>
    <?php return; endif; ?>

    <!-- Customer Information -->
    <div class="lci-customer-info">
        <h3>Customer Information</h3>
        <div class="lci-customer-details">
            <div class="lci-customer-name">
                <strong>Name:</strong> <?php echo esc_html($user_data['billing']['first_name'] . ' ' . $user_data['billing']['last_name']); ?>
            </div>
            <div class="lci-customer-email">
                <strong>Email:</strong> <?php echo esc_html($user_data['billing']['email']); ?>
            </div>
            <?php if (!empty($user_data['billing']['phone'])) : ?>
            <div class="lci-customer-phone">
                <strong>Phone:</strong> <?php echo esc_html($user_data['billing']['phone']); ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <?php /* Payment gateway check removed */ ?>
    <form id="lci-payment-form">
        <?php wp_nonce_field('lci_payment_nonce', 'lci_payment_security'); ?>
        <?php if ($product) : ?>
        <input type="hidden" name="product_id" value="<?php echo esc_attr($product_id); ?>">
        <input type="hidden" name="quantity" value="1">
        <?php endif; ?>

        <?php
        // Add Revolut parameters if they exist in the URL
        if (isset($_GET['revolut_public_id'])) :
        ?>
        <input type="hidden" name="revolut_public_id" value="<?php echo esc_attr($_GET['revolut_public_id']); ?>">
        <?php
        // Log for debugging
        error_log('Added revolut_public_id to payment form: ' . esc_attr($_GET['revolut_public_id']));
        endif;
        ?>

        <?php if (isset($_GET['revolut_pay_redirected'])) : ?>
        <input type="hidden" name="revolut_pay_redirected" value="<?php echo esc_attr($_GET['revolut_pay_redirected']); ?>">
        <?php
        // Log for debugging
        error_log('Added revolut_pay_redirected to payment form: ' . esc_attr($_GET['revolut_pay_redirected']));
        endif;
        ?>

        <?php
        // Check for Revolut parameters in session
        if (session_id() || session_start()) :
            if (isset($_SESSION['revolut_public_id'])) :
        ?>
        <input type="hidden" name="revolut_public_id" value="<?php echo esc_attr($_SESSION['revolut_public_id']); ?>">
        <?php
            // Log for debugging
            error_log('Added revolut_public_id from session to payment form: ' . esc_attr($_SESSION['revolut_public_id']));
            endif;

            if (isset($_SESSION['revolut_pay_redirected'])) :
        ?>
        <input type="hidden" name="revolut_pay_redirected" value="<?php echo esc_attr($_SESSION['revolut_pay_redirected']); ?>">
        <?php
            // Log for debugging
            error_log('Added revolut_pay_redirected from session to payment form: ' . esc_attr($_SESSION['revolut_pay_redirected']));
            endif;
        endif;
        ?>

        <!-- Payment methods section removed as requested -->

        <div class="lci-payment-actions">
            <a href="<?php echo esc_url(wc_get_checkout_url()); ?>" class="lci-btn lci-btn-primary lci-payment-button">
                <i class="fas fa-lock me-2"></i> Proceed to Checkout
            </a>
        </div>
    </form>

    <div id="lci-payment-messages"></div>

    <?php
    // Hook for payment methods to add their own forms
    do_action('lci_dashboard_after_payment_methods');
    ?>
</div>

<script>
jQuery(document).ready(function($) {
    // Form submission is now handled by direct link to checkout
    $('#lci-payment-form').on('submit', function(e) {
        e.preventDefault();
        window.location.href = '<?php echo esc_url(wc_get_checkout_url()); ?>';
        return false;
    });
});
</script>
