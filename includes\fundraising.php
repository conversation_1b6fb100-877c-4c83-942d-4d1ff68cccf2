<?php
/**
 * Fundraising functionality for LCI 2025 Dashboard
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Get the percentage of funds from category 22 that will go to support young Circlers
 *
 * @return int The percentage (default 20%)
 */
function lci_get_fundraising_percentage() {
    // Get the percentage from options, default to 20%
    return 20; // Fixed at 20% as requested
}

/**
 * Get the total fundraising amount from products in the cart
 *
 * @return float The total fundraising amount
 */
function lci_get_category_22_total_value() {
    // Always use category 22 as specified
    $category_id = 22;
    $total_value = 0;

    // Make sure WooCommerce is active and cart is available
    if (!function_exists('WC') || !isset(WC()->cart)) {
        return $total_value;
    }

    // Get cart contents
    $cart_items = WC()->cart->get_cart();

    // Loop through cart items
    foreach ($cart_items as $cart_item_key => $cart_item) {
        $product_id = $cart_item['product_id'];
        $quantity = $cart_item['quantity'];

        // Check if product is in category 22
        if (has_term($category_id, 'product_cat', $product_id)) {
            // Get the fundraising amount from product meta
            $fundraising_amount = get_post_meta($product_id, '_fundraising_amount', true);

            // If no specific fundraising amount is set, use 20% of the product price as fallback
            if (empty($fundraising_amount)) {
                $product = wc_get_product($product_id);
                $price = $product ? $product->get_price() : 0;
                $fundraising_amount = $price * 0.2; // 20% of price as fallback
            }

            // Multiply by quantity
            $line_fundraising = (float) $fundraising_amount * $quantity;
            $total_value += $line_fundraising;

            // Debug info
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log(sprintf('LCI Fundraising: Product ID %d has fundraising amount %f, quantity %d, total %f',
                    $product_id, $fundraising_amount, $quantity, $line_fundraising));
            }
        } else {
            // Debug info
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log(sprintf('LCI Fundraising: Product ID %d is NOT in category 22', $product_id));
            }
        }
    }

    // Debug info
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log(sprintf('LCI Fundraising: Total fundraising amount from cart: %f', $total_value));
    }

    return $total_value;
}

/**
 * Get the fundraising message
 *
 * @return string The formatted message
 */
function lci_get_fundraising_message() {
    // Get the total fundraising amount directly from the cart items
    $fundraising_value = lci_get_category_22_total_value();

    // Format the value with 2 decimal places and currency symbol
    $formatted_value = number_format($fundraising_value, 2) . ' €';

    // We're specifically using category 22

    return sprintf(
        '%s will be used to support a young Circler attending the LCI 2025 AGM in Brasov!',
        $formatted_value
    );
}

/**
 * Display the fundraising message
 */
function lci_display_fundraising_message() {
    // Check if cart is empty
    if (function_exists('WC') && WC()->cart && WC()->cart->is_empty()) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Fundraising: Cart is empty, not displaying message');
        }
        return;
    }

    // Always use category 22 as specified
    $category_id = 22;
    $has_category_22_products = false;

    // Check if there are any products from category 22 in the cart
    if (function_exists('WC') && isset(WC()->cart)) {
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product_id = $cart_item['product_id'];
            if (has_term($category_id, 'product_cat', $product_id)) {
                $has_category_22_products = true;
                break;
            }
        }
    }

    // Only show the message if there are category 22 products in the cart
    if (!$has_category_22_products) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Fundraising: No category 22 products in cart, not displaying message');
        }
        return;
    }

    // Get the total fundraising amount directly from the cart items
    $fundraising_value = lci_get_category_22_total_value();

    // Don't show the message if the fundraising value is zero
    if ($fundraising_value <= 0) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Fundraising: Fundraising value is zero, not displaying message');
        }
        return;
    }

    // Format the value with 2 decimal places and currency symbol
    $formatted_value = number_format($fundraising_value, 2) . ' €';

    // We're specifically using category 22

    // Format the message with the value highlighted
    $message = sprintf(
        '<strong style="font-size: 1em; color: #36b1dc;">%s</strong> will be used to support a young Circler attending the LCI 2025 AGM in Brasov!',
        $formatted_value
    );

    echo '<div id="lci-fundraising-message" class="lci-fundraising-message" style="background: linear-gradient(135deg, #f0f7fb 0%, #e6f3f9 100%); padding: 12px 16px; margin: 0; border-top: 1px solid rgba(54, 177, 220, 0.1); font-size: 0.9rem; text-align: center; color: #333; box-shadow: 0 -2px 10px rgba(0,0,0,0.03);">';
    echo '<i class="fas fa-heart" style="color: #fab33a; margin-right: 8px; animation: pulse 1.5s infinite;"></i> ';
    echo '<span class="fundraising-message-text" style="font-weight: 500;">' . $message . '</span>';
    echo '</div>';

    // Add the pulse animation
    echo '<style>
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>';
}

/**
 * Add fundraising amount field to product edit page
 */
function lci_add_fundraising_amount_field() {
    // Add a custom field to the product data meta box
    woocommerce_wp_text_input(
        array(
            'id' => '_fundraising_amount',
            'label' => __('Fundraising Amount (€)', 'lci-2025-dashboard'),
            'desc_tip' => true,
            'description' => __('Enter the amount from this product that will go to fundraising. If left empty, 20% of the product price will be used.', 'lci-2025-dashboard'),
            'type' => 'number',
            'custom_attributes' => array(
                'step' => '0.01',
                'min' => '0'
            )
        )
    );
}
add_action('woocommerce_product_options_pricing', 'lci_add_fundraising_amount_field');

/**
 * Save fundraising amount field
 */
function lci_save_fundraising_amount_field($product_id) {
    // Save the fundraising amount field
    $fundraising_amount = isset($_POST['_fundraising_amount']) ? wc_clean(wp_unslash($_POST['_fundraising_amount'])) : '';
    update_post_meta($product_id, '_fundraising_amount', $fundraising_amount);
}
add_action('woocommerce_process_product_meta', 'lci_save_fundraising_amount_field');
