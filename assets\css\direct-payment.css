/**
 * LCI Direct Payment Styles
 */

/* Payment Form */
.lci-payment-form {
    margin-bottom: 30px;
}

.lci-payment-methods {
    margin-bottom: 20px;
}

.lci-payment-method {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.lci-payment-method-header {
    padding: 15px;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.lci-payment-method-header:hover {
    background-color: #f0f0f0;
}

.lci-payment-method-input {
    margin-right: 10px;
}

.lci-payment-method-label {
    display: flex;
    flex-direction: column;
    flex: 1;
    cursor: pointer;
}

.lci-payment-method-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
}

.lci-payment-method-description {
    padding: 15px;
    background-color: #fff;
    border-top: 1px solid #ddd;
    display: none;
}

.lci-payment-button {
    background-color: #36b1dc;
    color: #fff;
    border: none;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    text-transform: uppercase;
    width: 100%;
    max-width: 300px;
}

.lci-payment-button:hover {
    background-color: #2c99c2;
}

.lci-payment-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Payment Messages */
.lci-payment-messages {
    margin-bottom: 20px;
}

.lci-notice {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.lci-notice p {
    margin: 0;
}

.lci-notice-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.lci-notice-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.lci-notice-info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* Payment Instructions */
.lci-payment-instructions {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: none;
}

.lci-payment-instructions h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

.lci-payment-instructions p {
    margin-bottom: 15px;
}

.lci-payment-instructions ul.bacs-accounts {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
}

.lci-payment-instructions ul.bacs-accounts li {
    padding: 10px;
    margin-bottom: 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Order Details */
.lci-order-details {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: none;
}

/* Mobile Styles */
@media (max-width: 768px) {
    .lci-payment-method-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .lci-payment-method-input {
        margin-bottom: 10px;
    }
    
    .lci-payment-button {
        width: 100%;
        max-width: none;
    }
}