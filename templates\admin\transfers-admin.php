<?php
/**
 * LCI 2025 Transfers Admin Template
 * Modern 2025 UX/UI design with AlpineJS
 */

// Get transfer statistics from the database
global $wpdb;
$table_name = $wpdb->prefix . 'lci_transfer_bookings';

// Count bookings by status
$total = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
$pending = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'pending'");
$confirmed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'confirmed'");
$completed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'completed'");
$cancelled = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'cancelled'");

// Get transfer types for filtering
$transfer_types = array(
    'bucharest_to_brasov' => 'Bucharest Airport to Brasov (One Way)',
    'brasov_to_bucharest' => 'Brasov to Bucharest Airport (One Way)',
    'bucharest_to_brasov_round' => 'Bucharest Airport to Brasov (Round Trip)',
    'bucharest_to_pretour_hotel' => 'Bucharest Airport to Main Pretour Hotel',
    'brasov_airport_to_venue' => 'Brasov Airport to Main Event Venue (One Way)'
);

// Convert data to JSON for Alpine.js
$stats = [
    'total' => (int)$total,
    'pending' => (int)$pending,
    'confirmed' => (int)$confirmed,
    'completed' => (int)$completed,
    'cancelled' => (int)$cancelled
];

$transfer_types_json = json_encode($transfer_types);
$stats_json = json_encode($stats);

// Include Tailwind CSS via CDN
wp_enqueue_style('tailwind-css', 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css', array(), '2.2.19');

// Add inline script to ensure AlpineJS data is available
?>
<script>
// Make sure data is defined globally
window.lciAdmin = {
    ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
    nonce: '<?php echo wp_create_nonce('lci_custom_ajax_nonce'); ?>'
};

// Define stats and transfer types globally
window.lciStats = <?php echo $stats_json; ?>;
window.lciTransferTypes = <?php echo $transfer_types_json; ?>;

// Debug info
console.log('Transfer admin template loaded');
console.log('Stats:', window.lciStats);
console.log('Transfer types:', window.lciTransferTypes);
</script>

<div class="wrap" x-data="{
    // Data
    bookings: [],
    filteredBookings: [],
    paginatedBookings: [],
    isLoading: true,
    showStatusModal: false,
    showDetailsModal: false,
    showDeleteModal: false,
    selectedBooking: null,
    newStatus: '',
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    filters: {
        status: '',
        type: '',
        date: '',
        search: ''
    },
    stats: window.lciStats || {
        total: 0,
        pending: 0,
        confirmed: 0,
        completed: 0,
        cancelled: 0
    },
    transferTypes: window.lciTransferTypes || {},

    // Initialize
    init() {
        this.loadBookings();
    },

    // Load bookings
    loadBookings() {
        this.isLoading = true;

        console.log('Loading bookings...');

        // Create params for the request
        const params = new URLSearchParams();
        params.append('action', 'lci_get_transfer_bookings');

        // Log the params for debugging
        console.log('Request params:', {
            action: 'lci_get_transfer_bookings'
        });

        fetch(window.lciAdmin.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.bookings = data.data.bookings;
                this.applyFilters();
                this.updateStats(data.data.stats);
            } else {
                console.error('Error loading bookings:', data.data.message);
            }
        })
        .catch(error => {
            console.error('Error loading bookings:', error);
        })
        .finally(() => {
            this.isLoading = false;
        });
    },

    // Update stats
    updateStats(newStats) {
        if (newStats) {
            this.stats = newStats;
        } else {
            // Calculate stats from filtered bookings
            this.stats.total = this.bookings.length;
            this.stats.pending = this.bookings.filter(b => b.status === 'pending').length;
            this.stats.confirmed = this.bookings.filter(b => b.status === 'confirmed').length;
            this.stats.completed = this.bookings.filter(b => b.status === 'completed').length;
            this.stats.cancelled = this.bookings.filter(b => b.status === 'cancelled').length;
        }
    },

    // Apply filters
    applyFilters() {
        this.filteredBookings = this.bookings.filter(booking => {
            // Status filter
            if (this.filters.status && booking.status !== this.filters.status) {
                return false;
            }

            // Type filter
            if (this.filters.type && booking.transfer_type !== this.filters.type) {
                return false;
            }

            // Date filter
            if (this.filters.date) {
                const filterDate = new Date(this.filters.date).toISOString().split('T')[0];
                const arrivalDate = booking.arrival_date ? new Date(booking.arrival_date).toISOString().split('T')[0] : null;
                const departureDate = booking.departure_date ? new Date(booking.departure_date).toISOString().split('T')[0] : null;

                if (arrivalDate !== filterDate && departureDate !== filterDate) {
                    return false;
                }
            }

            // Search filter
            if (this.filters.search) {
                const search = this.filters.search.toLowerCase();

                // Get product names from JSON string if available
                let productNames = [];
                if (booking.product_names) {
                    try {
                        productNames = JSON.parse(booking.product_names);
                    } catch (e) {
                        console.error('Error parsing product names:', e);
                    }
                }

                const searchFields = [
                    booking.user_name,
                    booking.email,
                    booking.arrival_flight,
                    booking.departure_flight,
                    ...productNames
                ].filter(Boolean); // Remove null/undefined values

                return searchFields.some(field => field.toLowerCase().includes(search));
            }

            return true;
        });

        // Update pagination
        this.totalPages = Math.ceil(this.filteredBookings.length / this.perPage);
        this.currentPage = 1; // Reset to first page when filters change
        this.updatePaginatedBookings();
    },

    // Reset filters
    resetFilters() {
        this.filters = {
            status: '',
            type: '',
            date: '',
            search: ''
        };
        this.applyFilters();
    },

    // Update paginated bookings
    updatePaginatedBookings() {
        const start = (this.currentPage - 1) * this.perPage;
        const end = start + this.perPage;
        this.paginatedBookings = this.filteredBookings.slice(start, end);
    },

    // Pagination methods
    prevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.updatePaginatedBookings();
        }
    },

    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.updatePaginatedBookings();
        }
    },

    goToPage(page) {
        this.currentPage = page;
        this.updatePaginatedBookings();
    },

    // Get pagination pages array
    get paginationPages() {
        const pages = [];
        const maxPages = 5; // Show at most 5 page numbers

        if (this.totalPages <= maxPages) {
            // Show all pages
            for (let i = 1; i <= this.totalPages; i++) {
                pages.push(i);
            }
        } else {
            // Show a subset of pages
            if (this.currentPage <= 3) {
                // Near the start
                for (let i = 1; i <= 5; i++) {
                    pages.push(i);
                }
            } else if (this.currentPage >= this.totalPages - 2) {
                // Near the end
                for (let i = this.totalPages - 4; i <= this.totalPages; i++) {
                    pages.push(i);
                }
            } else {
                // In the middle
                for (let i = this.currentPage - 2; i <= this.currentPage + 2; i++) {
                    pages.push(i);
                }
            }
        }

        return pages;
    },

    // Modal methods
    openStatusModal(booking) {
        this.selectedBooking = booking;
        this.newStatus = booking.status;
        this.showStatusModal = true;
    },

    closeStatusModal() {
        this.showStatusModal = false;
        this.selectedBooking = null;
        this.newStatus = '';
    },

    openBookingDetails(booking) {
        this.selectedBooking = booking;
        this.showDetailsModal = true;
    },

    closeDetailsModal() {
        this.showDetailsModal = false;
        this.selectedBooking = null;
    },

    openDeleteModal(booking) {
        this.selectedBooking = booking;
        this.showDeleteModal = true;
        // If the details modal is open, close it
        if (this.showDetailsModal) {
            this.showDetailsModal = false;
        }
    },

    closeDeleteModal() {
        this.showDeleteModal = false;
        this.selectedBooking = null;
    },

    // Update booking status
    updateBookingStatus() {
        if (!this.selectedBooking || !this.newStatus) {
            return;
        }

        this.isLoading = true;

        // AJAX call to update status
        const statusParams = new URLSearchParams();
        statusParams.append('action', 'lci_update_transfer_status');
        statusParams.append('booking_id', this.selectedBooking.id);
        statusParams.append('status', this.newStatus);

        fetch(window.lciAdmin.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: statusParams.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update booking in the local array
                const index = this.bookings.findIndex(b => b.id === this.selectedBooking.id);
                if (index !== -1) {
                    this.bookings[index].status = this.newStatus;
                }

                // Re-apply filters and update pagination
                this.applyFilters();

                // Show success message
                this.showNotification('Status updated successfully', 'success');
            } else {
                console.error('Error updating status:', data.data.message);
                this.showNotification('Error updating status', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating status:', error);
            this.showNotification('Error updating status', 'error');
        })
        .finally(() => {
            this.isLoading = false;
            this.closeStatusModal();
        });
    },

    // Delete booking
    deleteBooking() {
        if (!this.selectedBooking) {
            return;
        }

        this.isLoading = true;

        // AJAX call to delete booking
        const deleteParams = new URLSearchParams();
        deleteParams.append('action', 'lci_delete_transfer_booking');
        deleteParams.append('booking_id', this.selectedBooking.id);

        console.log('Deleting booking with data:', {
            action: 'lci_delete_transfer_booking',
            booking_id: this.selectedBooking.id
        });

        fetch(window.lciAdmin.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: deleteParams.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove booking from the local array
                this.bookings = this.bookings.filter(b => b.id !== this.selectedBooking.id);

                // Re-apply filters and update pagination
                this.applyFilters();

                // Update stats
                this.stats.total--;
                if (this.selectedBooking.status === 'pending') this.stats.pending--;
                if (this.selectedBooking.status === 'confirmed') this.stats.confirmed--;
                if (this.selectedBooking.status === 'completed') this.stats.completed--;
                if (this.selectedBooking.status === 'cancelled') this.stats.cancelled--;

                // Show success message
                this.showNotification('Booking deleted successfully', 'success');
            } else {
                console.error('Error deleting booking:', data.data.message);
                this.showNotification('Error deleting booking: ' + data.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting booking:', error);
            this.showNotification('Error deleting booking', 'error');
        })
        .finally(() => {
            this.isLoading = false;
            this.closeDeleteModal();
        });
    },

    // Helper methods
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    },

    getTransferTypeLabel(type) {
        return this.transferTypes[type] || type;
    },

    showNotification(message, type = 'info') {
        // Simple notification using alert for now
        alert(message);
    },

    // Recreate database tables
    recreateTables() {
        if (!confirm('WARNING: This will delete and recreate all transfer tables. All existing data will be lost. Are you sure you want to continue?')) {
            return;
        }

        this.isLoading = true;

        // Create params for the request
        const params = new URLSearchParams();
        params.append('action', 'lci_recreate_transfer_tables');
        params.append('nonce', window.lciAdmin.nonce);

        fetch(window.lciAdmin.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Transfer tables recreated successfully. Reloading page...', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                console.error('Error recreating tables:', data.data.message);
                this.showNotification('Error recreating tables: ' + data.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error recreating tables:', error);
            this.showNotification('Error recreating tables', 'error');
        })
        .finally(() => {
            this.isLoading = false;
        });
    },

    // Diagnose orders
    diagnoseOrders() {
        if (!confirm('This will diagnose recent orders to see what transfer products they contain. Check the error logs for detailed results. Continue?')) {
            return;
        }

        this.isLoading = true;

        // Call the diagnose_orders action
        const params = new URLSearchParams();
        params.append('action', 'lci_custom_ajax');
        params.append('custom_action', 'diagnose_orders');
        params.append('nonce', window.lciAdmin.nonce);

        fetch(window.lciAdmin.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(data.data.message + ' Check error logs for detailed results.', 'success');
            } else {
                console.error('Error diagnosing orders:', data.data.message);
                this.showNotification('Error diagnosing orders: ' + data.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error diagnosing orders:', error);
            this.showNotification('Error diagnosing orders', 'error');
        })
        .finally(() => {
            this.isLoading = false;
        });
    },

    // Process existing orders
    processExistingOrders() {
        if (!confirm('This will process existing orders to create transfer bookings. Do you want to continue?')) {
            return;
        }

        this.isLoading = true;

        // Call the process_existing_orders action
        const params = new URLSearchParams();
        params.append('action', 'lci_custom_ajax');
        params.append('custom_action', 'process_existing_orders');
        params.append('nonce', window.lciAdmin.nonce);

        fetch(window.lciAdmin.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(data.data.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                console.error('Error processing orders:', data.data.message);
                this.showNotification('Error processing orders: ' + data.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error processing orders:', error);
            this.showNotification('Error processing orders', 'error');
        })
        .finally(() => {
            this.isLoading = false;
        });
    },

    // Clean up duplicate bookings
    cleanupDuplicates() {
        if (!confirm('This will clean up duplicate transfer bookings. Do you want to continue?')) {
            return;
        }

        this.isLoading = true;

        // Reset the cleanup flag to force it to run again
        const params = new URLSearchParams();
        params.append('action', 'lci_custom_ajax');
        params.append('custom_action', 'reset_transfer_cleanup_flag');
        params.append('nonce', window.lciAdmin.nonce);

        fetch(window.lciAdmin.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Duplicate cleanup initiated. Reloading page...', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                console.error('Error cleaning duplicates:', data.data.message);
                this.showNotification('Error cleaning duplicates: ' + data.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error cleaning duplicates:', error);
            this.showNotification('Error cleaning duplicates', 'error');
        })
        .finally(() => {
            this.isLoading = false;
        });
    }
}" x-init="init()">
    <!-- Page Header with Modern Design -->
    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl shadow-lg p-6 mb-8">
        <div class="flex justify-between items-center">
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 rounded-full p-3 mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                    </svg>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-white mb-1">Transfer Management</h1>
                    <p class="text-blue-100">Manage and track all transfer bookings</p>
                </div>
            </div>

            <!-- Admin Tools -->
            <div class="flex space-x-2">
                <button
                    @click="diagnoseOrders()"
                    class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                    :disabled="isLoading"
                >
                    <span x-show="isLoading" class="mr-2">⟳</span>
                    Diagnose Orders
                </button>

                <button
                    @click="processExistingOrders()"
                    class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                    :disabled="isLoading"
                >
                    <span x-show="isLoading" class="mr-2">⟳</span>
                    Process Existing Orders
                </button>

                <button
                    @click="cleanupDuplicates()"
                    class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
                    :disabled="isLoading"
                >
                    <span x-show="isLoading" class="mr-2">⟳</span>
                    Clean Duplicates
                </button>

                <button
                    @click="recreateTables()"
                    class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                    :disabled="isLoading"
                >
                    <span x-show="isLoading" class="mr-2">⟳</span>
                    Recreate Tables
                </button>
            </div>
        </div>
    </div>

    <!-- Dashboard Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <!-- Total Bookings -->
        <div class="bg-white rounded-xl shadow-md p-6 border-t-4 border-gray-800 transform transition-transform hover:scale-105">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 uppercase tracking-wider">Total Bookings</p>
                    <h2 class="mt-2 text-3xl font-extrabold text-gray-900" x-text="stats.total">0</h2>
                </div>
                <div class="bg-gray-100 rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Pending Bookings -->
        <div class="bg-white rounded-xl shadow-md p-6 border-t-4 border-yellow-500 transform transition-transform hover:scale-105">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 uppercase tracking-wider">Pending</p>
                    <h2 class="mt-2 text-3xl font-extrabold text-gray-900" x-text="stats.pending">0</h2>
                </div>
                <div class="bg-yellow-100 rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Confirmed Bookings -->
        <div class="bg-white rounded-xl shadow-md p-6 border-t-4 border-blue-500 transform transition-transform hover:scale-105">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 uppercase tracking-wider">Confirmed</p>
                    <h2 class="mt-2 text-3xl font-extrabold text-gray-900" x-text="stats.confirmed">0</h2>
                </div>
                <div class="bg-blue-100 rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Completed Bookings -->
        <div class="bg-white rounded-xl shadow-md p-6 border-t-4 border-green-500 transform transition-transform hover:scale-105">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 uppercase tracking-wider">Completed</p>
                    <h2 class="mt-2 text-3xl font-extrabold text-gray-900" x-text="stats.completed">0</h2>
                </div>
                <div class="bg-green-100 rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Cancelled Bookings -->
        <div class="bg-white rounded-xl shadow-md p-6 border-t-4 border-red-500 transform transition-transform hover:scale-105">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 uppercase tracking-wider">Cancelled</p>
                    <h2 class="mt-2 text-3xl font-extrabold text-gray-900" x-text="stats.cancelled">0</h2>
                </div>
                <div class="bg-red-100 rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Filter Bookings</h2>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Status Filter -->
            <div>
                <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="status-filter" x-model="filters.status" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <option value="">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>

            <!-- Transfer Type Filter -->
            <div>
                <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-1">Transfer Type</label>
                <select id="type-filter" x-model="filters.type" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <option value="">All Types</option>
                    <template x-for="(label, value) in transferTypes" :key="value">
                        <option :value="value" x-text="label"></option>
                    </template>
                </select>
            </div>

            <!-- Date Filter -->
            <div>
                <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input type="date" id="date-filter" x-model="filters.date" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
            </div>

            <!-- Search Filter -->
            <div>
                <label for="search-filter" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <div class="relative">
                    <input type="text" id="search-filter" x-model="filters.search" placeholder="Search name, email..." class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 pl-10">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Actions -->
        <div class="flex justify-end mt-4 space-x-2">
            <button @click="resetFilters()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors">
                Reset Filters
            </button>
            <button @click="applyFilters()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors">
                Apply Filters
            </button>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div x-show="isLoading" class="flex justify-center my-8">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>

    <!-- Bookings Table -->
    <div x-show="!isLoading" class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Transfer Bookings</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Transfer Type
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Arrival
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Departure
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="booking in paginatedBookings" :key="booking.id">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900" x-text="booking.user_name"></div>
                                        <div class="text-sm text-gray-500" x-text="booking.email"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900" x-text="getTransferTypeLabel(booking.transfer_type)"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900" x-text="formatDate(booking.arrival_date)"></div>
                                <div class="text-sm text-gray-500" x-text="booking.arrival_time"></div>
                                <div x-show="booking.arrival_flight" class="text-xs text-gray-500" x-text="'Flight: ' + booking.arrival_flight"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <template x-if="booking.departure_date">
                                    <div>
                                        <div class="text-sm text-gray-900" x-text="formatDate(booking.departure_date)"></div>
                                        <div class="text-sm text-gray-500" x-text="booking.departure_time"></div>
                                        <div x-show="booking.departure_flight" class="text-xs text-gray-500" x-text="'Flight: ' + booking.departure_flight"></div>
                                    </div>
                                </template>
                                <template x-if="!booking.departure_date">
                                    <div class="text-sm text-gray-500">N/A</div>
                                </template>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span :class="{
                                    'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full': true,
                                    'bg-yellow-100 text-yellow-800': booking.status === 'pending',
                                    'bg-blue-100 text-blue-800': booking.status === 'confirmed',
                                    'bg-green-100 text-green-800': booking.status === 'completed',
                                    'bg-red-100 text-red-800': booking.status === 'cancelled'
                                }" x-text="booking.status.charAt(0).toUpperCase() + booking.status.slice(1)"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button @click="openBookingDetails(booking)" class="text-blue-600 hover:text-blue-900 mr-2">
                                    View
                                </button>
                                <button @click="openStatusModal(booking)" class="text-indigo-600 hover:text-indigo-900 mr-2">
                                    Update Status
                                </button>
                                <button @click="openDeleteModal(booking)" class="text-red-600 hover:text-red-900">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    </template>

                    <!-- Empty State -->
                    <tr x-show="paginatedBookings.length === 0">
                        <td colspan="6" class="px-6 py-10 text-center text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <p>No transfer bookings found matching your filters.</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                <button @click="prevPage()" :disabled="currentPage === 1" :class="{'opacity-50 cursor-not-allowed': currentPage === 1}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </button>
                <button @click="nextPage()" :disabled="currentPage === totalPages" :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium" x-text="(currentPage - 1) * perPage + 1"></span>
                        to
                        <span class="font-medium" x-text="Math.min(currentPage * perPage, filteredBookings.length)"></span>
                        of
                        <span class="font-medium" x-text="filteredBookings.length"></span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <button @click="prevPage()" :disabled="currentPage === 1" :class="{'opacity-50 cursor-not-allowed': currentPage === 1}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Previous</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </button>
                        <template x-for="page in paginationPages" :key="page">
                            <button @click="goToPage(page)" :class="{'bg-blue-50 border-blue-500 text-blue-600': currentPage === page, 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50': currentPage !== page}" class="relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Next</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- Status Update Modal -->
    <div x-show="showStatusModal" class="fixed inset-0 z-50 overflow-y-auto" x-cloak>
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Update Booking Status
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">
                                    Update the status for booking #<span x-text="selectedBooking?.id"></span>
                                </p>
                            </div>
                            <div class="mt-4">
                                <label for="status-select" class="block text-sm font-medium text-gray-700">Status</label>
                                <select id="status-select" x-model="newStatus" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                    <option value="pending">Pending</option>
                                    <option value="confirmed">Confirmed</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="updateBookingStatus()" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Update
                    </button>
                    <button @click="closeStatusModal()" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div x-show="showDeleteModal" class="fixed inset-0 z-50 overflow-y-auto" x-cloak>
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Delete Transfer Booking
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">
                                    Are you sure you want to delete booking #<span x-text="selectedBooking?.id"></span>? This action cannot be undone.
                                </p>
                                <p class="text-sm text-gray-500 mt-2">
                                    <span class="font-medium">User:</span> <span x-text="selectedBooking?.user_name"></span><br>
                                    <span class="font-medium">Email:</span> <span x-text="selectedBooking?.email"></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="deleteBooking()" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Delete
                    </button>
                    <button @click="closeDeleteModal()" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Details Modal -->
    <div x-show="showDetailsModal" class="fixed inset-0 z-50 overflow-y-auto" x-cloak>
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6">
                    <div class="flex justify-between items-start">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            Booking Details #<span x-text="selectedBooking?.id"></span>
                        </h3>
                        <button @click="closeDetailsModal()" class="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <span class="sr-only">Close</span>
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div class="mt-4 border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- User Information -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">User Information</h4>
                                <div class="space-y-2">
                                    <p class="text-sm text-gray-900"><span class="font-medium">Name:</span> <span x-text="selectedBooking?.user_name"></span></p>
                                    <p class="text-sm text-gray-900"><span class="font-medium">Email:</span> <span x-text="selectedBooking?.email"></span></p>
                                    <p class="text-sm text-gray-900"><span class="font-medium">WordPress User ID:</span> <span x-text="selectedBooking?.user_id > 0 ? selectedBooking?.user_id : 'Guest'"></span></p>
                                    <p class="text-sm text-gray-900"><span class="font-medium">WooCommerce Order:</span> <span x-text="selectedBooking?.order_id > 0 ? '#' + selectedBooking?.order_id : 'Not linked'"></span></p>
                                </div>
                            </div>

                            <!-- Transfer Information -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Transfer Information</h4>
                                <div class="space-y-2">
                                    <p class="text-sm text-gray-900"><span class="font-medium">Type:</span> <span x-text="getTransferTypeLabel(selectedBooking?.transfer_type)"></span></p>
                                    <p class="text-sm text-gray-900"><span class="font-medium">Status:</span>
                                        <span :class="{
                                            'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full': true,
                                            'bg-yellow-100 text-yellow-800': selectedBooking?.status === 'pending',
                                            'bg-blue-100 text-blue-800': selectedBooking?.status === 'confirmed',
                                            'bg-green-100 text-green-800': selectedBooking?.status === 'completed',
                                            'bg-red-100 text-red-800': selectedBooking?.status === 'cancelled'
                                        }" x-text="selectedBooking?.status.charAt(0).toUpperCase() + selectedBooking?.status.slice(1)"></span>
                                    </p>
                                    <p class="text-sm text-gray-900"><span class="font-medium">Created:</span> <span x-text="formatDate(selectedBooking?.created_at)"></span></p>
                                </div>
                            </div>

                            <!-- Arrival Details -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Arrival Details</h4>
                                <div class="space-y-2">
                                    <p class="text-sm text-gray-900"><span class="font-medium">Date:</span> <span x-text="formatDate(selectedBooking?.arrival_date)"></span></p>
                                    <p class="text-sm text-gray-900"><span class="font-medium">Time:</span> <span x-text="selectedBooking?.arrival_time"></span></p>
                                    <p class="text-sm text-gray-900"><span class="font-medium">Flight:</span> <span x-text="selectedBooking?.arrival_flight || 'N/A'"></span></p>
                                </div>
                            </div>

                            <!-- Departure Details -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Departure Details</h4>
                                <div class="space-y-2" x-show="selectedBooking?.departure_date">
                                    <p class="text-sm text-gray-900"><span class="font-medium">Date:</span> <span x-text="formatDate(selectedBooking?.departure_date)"></span></p>
                                    <p class="text-sm text-gray-900"><span class="font-medium">Time:</span> <span x-text="selectedBooking?.departure_time"></span></p>
                                    <p class="text-sm text-gray-900"><span class="font-medium">Flight:</span> <span x-text="selectedBooking?.departure_flight || 'N/A'"></span></p>
                                </div>
                                <p class="text-sm text-gray-500" x-show="!selectedBooking?.departure_date">No departure details available for this booking.</p>
                            </div>
                        </div>

                        <!-- Special Requests -->
                        <div class="mt-4 bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Special Requests</h4>
                            <p class="text-sm text-gray-900" x-text="selectedBooking?.special_requests || 'No special requests'"></p>
                        </div>

                        <!-- Products -->
                        <div class="mt-4 bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Products</h4>
                            <div x-show="selectedBooking?.product_names">
                                <ul class="divide-y divide-gray-200">
                                    <template x-for="(productName, index) in JSON.parse(selectedBooking?.product_names || '[]')" :key="index">
                                        <li class="py-2">
                                            <p class="text-sm text-gray-900" x-text="productName"></p>
                                        </li>
                                    </template>
                                </ul>
                            </div>
                            <p class="text-sm text-gray-500" x-show="!selectedBooking?.product_names">No products associated with this booking.</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="openStatusModal(selectedBooking)" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Update Status
                    </button>
                    <button @click="openDeleteModal(selectedBooking)" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Delete
                    </button>
                    <button @click="closeDetailsModal()" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>