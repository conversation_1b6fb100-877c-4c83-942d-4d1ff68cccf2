/**
 * Bootstrap Modal Fix
 * 
 * This script fixes issues with multiple modal backdrops in Bootstrap 5
 */

(function() {
    // Function to remove all modal backdrops
    function removeAllBackdrops() {
        document.querySelectorAll('.modal-backdrop').forEach(function(backdrop) {
            backdrop.remove();
        });
    }
    
    // Function to fix body padding
    function fixBodyPadding() {
        document.body.style.paddingRight = '0';
        document.body.style.overflow = document.body.classList.contains('modal-open') ? 'hidden' : '';
    }
    
    // Function to clean up modal issues
    function cleanupModalIssues() {
        // Keep only one backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        if (backdrops.length > 1) {
            backdrops.forEach(function(backdrop, index) {
                if (index > 0) backdrop.remove();
            });
        }
        
        // Fix body padding
        fixBodyPadding();
    }
    
    // Run on DOM ready
    document.addEventListener('DOMContentLoaded', function() {
        // Initial cleanup
        removeAllBackdrops();
        
        // Handle all modal events
        ['show.bs.modal', 'shown.bs.modal', 'hide.bs.modal', 'hidden.bs.modal'].forEach(function(event) {
            document.body.addEventListener(event, function() {
                setTimeout(cleanupModalIssues, 0);
            });
        });
        
        // Periodically check and clean up
        setInterval(cleanupModalIssues, 500);
        
        // Override Bootstrap's modal show method
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const originalShow = bootstrap.Modal.prototype.show;
            bootstrap.Modal.prototype.show = function() {
                removeAllBackdrops();
                originalShow.apply(this, arguments);
                setTimeout(cleanupModalIssues, 50);
            };
        }
    });
})();
