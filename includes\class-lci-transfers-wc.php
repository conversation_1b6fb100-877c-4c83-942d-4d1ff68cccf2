<?php
/**
 * LCI Transfers WooCommerce Integration
 *
 * Handles WooCommerce integration for transfers
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class LCI_Transfers_WC {
    /**
     * Constructor
     */
    public function __construct() {
        // Add custom fields to WooCommerce products
        add_action('woocommerce_product_options_general_product_data', array($this, 'add_transfer_fields'));
        add_action('woocommerce_process_product_meta', array($this, 'save_transfer_fields'));

        // Add custom fields to cart item data
        add_filter('woocommerce_add_cart_item_data', array($this, 'add_transfer_cart_item_data'), 10, 3);

        // Display custom fields in cart and checkout
        add_filter('woocommerce_get_item_data', array($this, 'display_transfer_cart_item_data'), 10, 2);

        // Add custom fields to order item meta - DISABLED to avoid duplication with main transfer class
        // add_action('woocommerce_checkout_create_order_line_item', array($this, 'add_transfer_order_item_meta'), 10, 4);

        // Display custom fields in order details
        add_action('woocommerce_order_item_meta_start', array($this, 'display_transfer_order_item_meta'), 10, 3);

        // Update booking status when order status changes
        add_action('woocommerce_order_status_changed', array($this, 'update_booking_status_on_order_change'), 10, 4);
    }

    /**
     * Add transfer fields to WooCommerce product data
     */
    public function add_transfer_fields() {
        global $woocommerce, $post;

        echo '<div class="options_group">';

        // Transfer Type field
        woocommerce_wp_select(
            array(
                'id'          => '_transfer_type',
                'label'       => __('Transfer Type', 'lci-2025-dashboard'),
                'description' => __('Select the type of transfer', 'lci-2025-dashboard'),
                'desc_tip'    => true,
                'options'     => array(
                    ''                => __('Select a type', 'lci-2025-dashboard'),
                    'airport_to_hotel' => __('Airport to Hotel (One Way)', 'lci-2025-dashboard'),
                    'hotel_to_airport' => __('Hotel to Airport (One Way)', 'lci-2025-dashboard'),
                    'round_trip' => __('Airport ↔ Hotel (Round Trip)', 'lci-2025-dashboard'),
                    'bucharest_to_brasov' => __('Bucharest to Brasov (One Way)', 'lci-2025-dashboard'),
                    'brasov_to_bucharest' => __('Brasov to Bucharest (One Way)', 'lci-2025-dashboard'),
                    'bucharest_brasov_round' => __('Bucharest ↔ Brasov (Round Trip)', 'lci-2025-dashboard'),
                    'local_transfer' => __('Local Transfer (One Way)', 'lci-2025-dashboard'),
                    'local_round_trip' => __('Local Transfer (Round Trip)', 'lci-2025-dashboard'),
                    'brasov_airport_to_venue' => __('Brasov Airport to Venue (One Way)', 'lci-2025-dashboard'),
                    'venue_to_brasov_airport' => __('Venue to Brasov Airport (One Way)', 'lci-2025-dashboard'),
                    'brasov_airport_round' => __('Brasov Airport ↔ Venue (Round Trip)', 'lci-2025-dashboard'),
                )
            )
        );

        // Transfer Date Range fields
        woocommerce_wp_text_input(
            array(
                'id'          => '_transfer_date_start',
                'label'       => __('Available From', 'lci-2025-dashboard'),
                'description' => __('The first date this transfer is available (YYYY-MM-DD)', 'lci-2025-dashboard'),
                'desc_tip'    => true,
                'type'        => 'date',
                'placeholder' => '2025-08-15',
            )
        );

        woocommerce_wp_text_input(
            array(
                'id'          => '_transfer_date_end',
                'label'       => __('Available Until', 'lci-2025-dashboard'),
                'description' => __('The last date this transfer is available (YYYY-MM-DD)', 'lci-2025-dashboard'),
                'desc_tip'    => true,
                'type'        => 'date',
                'placeholder' => '2025-08-25',
            )
        );

        // Max Passengers field
        woocommerce_wp_text_input(
            array(
                'id'          => '_transfer_max_passengers',
                'label'       => __('Max Passengers', 'lci-2025-dashboard'),
                'description' => __('Maximum number of passengers per booking', 'lci-2025-dashboard'),
                'desc_tip'    => true,
                'type'        => 'number',
                'custom_attributes' => array(
                    'step' => '1',
                    'min'  => '1',
                    'max'  => '50',
                ),
                'default'     => '4',
            )
        );

        echo '</div>';
    }

    /**
     * Save transfer fields
     */
    public function save_transfer_fields($post_id) {
        // Save Transfer Type
        $transfer_type = isset($_POST['_transfer_type']) ? sanitize_text_field($_POST['_transfer_type']) : '';
        update_post_meta($post_id, '_transfer_type', $transfer_type);

        // Save Transfer Date Range
        $transfer_date_start = isset($_POST['_transfer_date_start']) ? sanitize_text_field($_POST['_transfer_date_start']) : '';
        update_post_meta($post_id, '_transfer_date_start', $transfer_date_start);

        $transfer_date_end = isset($_POST['_transfer_date_end']) ? sanitize_text_field($_POST['_transfer_date_end']) : '';
        update_post_meta($post_id, '_transfer_date_end', $transfer_date_end);

        // Save Max Passengers
        $transfer_max_passengers = isset($_POST['_transfer_max_passengers']) ? absint($_POST['_transfer_max_passengers']) : 4;
        update_post_meta($post_id, '_transfer_max_passengers', $transfer_max_passengers);
    }

    /**
     * Add transfer data to cart item
     */
    public function add_transfer_cart_item_data($cart_item_data, $product_id, $variation_id) {
        // Get transfer type
        $transfer_type = get_post_meta($product_id, '_transfer_type', true);

        if (!empty($transfer_type)) {
            $cart_item_data['transfer_type'] = $transfer_type;

            // Add transfer date range
            $transfer_date_start = get_post_meta($product_id, '_transfer_date_start', true);
            $transfer_date_end = get_post_meta($product_id, '_transfer_date_end', true);

            if (!empty($transfer_date_start)) {
                $cart_item_data['transfer_date_start'] = $transfer_date_start;
            }

            if (!empty($transfer_date_end)) {
                $cart_item_data['transfer_date_end'] = $transfer_date_end;
            }

            // Add max passengers
            $transfer_max_passengers = get_post_meta($product_id, '_transfer_max_passengers', true);
            if (!empty($transfer_max_passengers)) {
                $cart_item_data['transfer_max_passengers'] = $transfer_max_passengers;
            }
        }

        return $cart_item_data;
    }

    /**
     * Display transfer data in cart
     */
    public function display_transfer_cart_item_data($item_data, $cart_item) {
        if (isset($cart_item['transfer_type'])) {
            $transfer_type_label = '';

            $transfer_types = array(
                'airport_to_hotel' => __('Airport to Hotel (One Way)', 'lci-2025-dashboard'),
                'hotel_to_airport' => __('Hotel to Airport (One Way)', 'lci-2025-dashboard'),
                'round_trip' => __('Airport ↔ Hotel (Round Trip)', 'lci-2025-dashboard'),
                'bucharest_to_brasov' => __('Bucharest to Brasov (One Way)', 'lci-2025-dashboard'),
                'brasov_to_bucharest' => __('Brasov to Bucharest (One Way)', 'lci-2025-dashboard'),
                'bucharest_brasov_round' => __('Bucharest ↔ Brasov (Round Trip)', 'lci-2025-dashboard'),
                'local_transfer' => __('Local Transfer (One Way)', 'lci-2025-dashboard'),
                'local_round_trip' => __('Local Transfer (Round Trip)', 'lci-2025-dashboard'),
                'brasov_airport_to_venue' => __('Brasov Airport to Venue (One Way)', 'lci-2025-dashboard'),
                'venue_to_brasov_airport' => __('Venue to Brasov Airport (One Way)', 'lci-2025-dashboard'),
                'brasov_airport_round' => __('Brasov Airport ↔ Venue (Round Trip)', 'lci-2025-dashboard'),
            );

            $transfer_type_label = isset($transfer_types[$cart_item['transfer_type']]) ?
                $transfer_types[$cart_item['transfer_type']] :
                $cart_item['transfer_type'];

            $item_data[] = array(
                'key'   => __('Transfer Type', 'lci-2025-dashboard'),
                'value' => $transfer_type_label
            );

            // Add date range if available
            if (isset($cart_item['transfer_date_start']) && isset($cart_item['transfer_date_end'])) {
                $item_data[] = array(
                    'key'   => __('Available Dates', 'lci-2025-dashboard'),
                    'value' => date('M j, Y', strtotime($cart_item['transfer_date_start'])) . ' - ' . date('M j, Y', strtotime($cart_item['transfer_date_end']))
                );
            }
        }

        return $item_data;
    }

    /**
     * Add transfer data to order item meta
     */
    public function add_transfer_order_item_meta($item, $cart_item_key, $values, $order) {
        if (isset($values['transfer_booking_id'])) {
            $item->add_meta_data('Transfer Booking ID', $values['transfer_booking_id']);
        }

        if (isset($values['transfer_type'])) {
            $item->add_meta_data('Transfer Type', $values['transfer_type']);
        }

        if (isset($values['arrival_date'])) {
            $item->add_meta_data('Arrival Date', $values['arrival_date']);
        }

        if (isset($values['arrival_time'])) {
            $item->add_meta_data('Arrival Time', $values['arrival_time']);
        }

        if (isset($values['arrival_flight'])) {
            $item->add_meta_data('Arrival Flight', $values['arrival_flight']);
        }

        if (isset($values['departure_date'])) {
            $item->add_meta_data('Departure Date', $values['departure_date']);
        }

        if (isset($values['departure_time'])) {
            $item->add_meta_data('Departure Time', $values['departure_time']);
        }

        if (isset($values['departure_flight'])) {
            $item->add_meta_data('Departure Flight', $values['departure_flight']);
        }

        if (isset($values['passengers'])) {
            $item->add_meta_data('Passengers', $values['passengers']);
        }

        if (isset($values['special_requests']) && !empty($values['special_requests'])) {
            $item->add_meta_data('Special Requests', $values['special_requests']);
        }

        // Add date range if available (for product-specific data)
        if (isset($values['transfer_date_start']) && isset($values['transfer_date_end'])) {
            $item->add_meta_data('Available Dates', date('M j, Y', strtotime($values['transfer_date_start'])) . ' - ' . date('M j, Y', strtotime($values['transfer_date_end'])));
        }

        // Add max passengers if available (for product-specific data)
        if (isset($values['transfer_max_passengers'])) {
            $item->add_meta_data('Max Passengers', $values['transfer_max_passengers']);
        }
    }

    /**
     * Display transfer data in order details
     */
    public function display_transfer_order_item_meta($item_id, $item, $order) {
        $transfer_type = wc_get_order_item_meta($item_id, 'Transfer Type', true);

        if (!empty($transfer_type)) {
            $transfer_type_label = '';

            $transfer_types = array(
                'airport_to_hotel' => __('Airport to Hotel (One Way)', 'lci-2025-dashboard'),
                'hotel_to_airport' => __('Hotel to Airport (One Way)', 'lci-2025-dashboard'),
                'round_trip' => __('Airport ↔ Hotel (Round Trip)', 'lci-2025-dashboard'),
                'bucharest_to_brasov' => __('Bucharest to Brasov (One Way)', 'lci-2025-dashboard'),
                'brasov_to_bucharest' => __('Brasov to Bucharest (One Way)', 'lci-2025-dashboard'),
                'bucharest_brasov_round' => __('Bucharest ↔ Brasov (Round Trip)', 'lci-2025-dashboard'),
                'local_transfer' => __('Local Transfer (One Way)', 'lci-2025-dashboard'),
                'local_round_trip' => __('Local Transfer (Round Trip)', 'lci-2025-dashboard'),
                'brasov_airport_to_venue' => __('Brasov Airport to Venue (One Way)', 'lci-2025-dashboard'),
                'venue_to_brasov_airport' => __('Venue to Brasov Airport (One Way)', 'lci-2025-dashboard'),
                'brasov_airport_round' => __('Brasov Airport ↔ Venue (Round Trip)', 'lci-2025-dashboard'),
            );

            $transfer_type_label = isset($transfer_types[$transfer_type]) ?
                $transfer_types[$transfer_type] :
                $transfer_type;

            echo '<div class="transfer-details" style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">';

            // Display booking ID if available
            $booking_id = wc_get_order_item_meta($item_id, 'Transfer Booking ID', true);
            if (!empty($booking_id)) {
                echo '<p><strong>' . __('Transfer Booking', 'lci-2025-dashboard') . ':</strong> #' . esc_html($booking_id) . '</p>';
            }

            echo '<p><strong>' . __('Transfer Type', 'lci-2025-dashboard') . ':</strong> ' . esc_html($transfer_type_label) . '</p>';

            // Display arrival details
            $arrival_date = wc_get_order_item_meta($item_id, 'Arrival Date', true);
            $arrival_time = wc_get_order_item_meta($item_id, 'Arrival Time', true);
            $arrival_flight = wc_get_order_item_meta($item_id, 'Arrival Flight', true);

            if (!empty($arrival_date) && !empty($arrival_time)) {
                echo '<p><strong>' . __('Arrival', 'lci-2025-dashboard') . ':</strong> ' .
                     date('F j, Y', strtotime($arrival_date)) . ' at ' .
                     date('g:i A', strtotime($arrival_time));

                if (!empty($arrival_flight)) {
                    echo ' (Flight: ' . esc_html($arrival_flight) . ')';
                }

                echo '</p>';
            }

            // Display departure details
            $departure_date = wc_get_order_item_meta($item_id, 'Departure Date', true);
            $departure_time = wc_get_order_item_meta($item_id, 'Departure Time', true);
            $departure_flight = wc_get_order_item_meta($item_id, 'Departure Flight', true);

            if (!empty($departure_date) && !empty($departure_time)) {
                echo '<p><strong>' . __('Departure', 'lci-2025-dashboard') . ':</strong> ' .
                     date('F j, Y', strtotime($departure_date)) . ' at ' .
                     date('g:i A', strtotime($departure_time));

                if (!empty($departure_flight)) {
                    echo ' (Flight: ' . esc_html($departure_flight) . ')';
                }

                echo '</p>';
            }

            // Display passengers
            $passengers = wc_get_order_item_meta($item_id, 'Passengers', true);
            if (!empty($passengers)) {
                echo '<p><strong>' . __('Passengers', 'lci-2025-dashboard') . ':</strong> ' . esc_html($passengers) . '</p>';
            }

            // Display special requests
            $special_requests = wc_get_order_item_meta($item_id, 'Special Requests', true);
            if (!empty($special_requests)) {
                echo '<p><strong>' . __('Special Requests', 'lci-2025-dashboard') . ':</strong> ' . esc_html($special_requests) . '</p>';
            }

            // Display available dates if available (for product-specific data)
            $available_dates = wc_get_order_item_meta($item_id, 'Available Dates', true);
            if (!empty($available_dates)) {
                echo '<p><strong>' . __('Available Dates', 'lci-2025-dashboard') . ':</strong> ' . esc_html($available_dates) . '</p>';
            }

            // Display max passengers if available (for product-specific data)
            $max_passengers = wc_get_order_item_meta($item_id, 'Max Passengers', true);
            if (!empty($max_passengers)) {
                echo '<p><strong>' . __('Max Passengers', 'lci-2025-dashboard') . ':</strong> ' . esc_html($max_passengers) . '</p>';
            }

            echo '</div>';
        }
    }

    /**
     * Update booking status when order status changes
     */
    public function update_booking_status_on_order_change($order_id, $from_status, $to_status, $order) {
        // Only process completed and cancelled orders
        if ($to_status !== 'completed' && $to_status !== 'cancelled') {
            return;
        }

        // Get all order items
        $items = $order->get_items();

        foreach ($items as $item) {
            $booking_id = $item->get_meta('Transfer Booking ID');

            if ($booking_id) {
                global $wpdb;
                $table_name = $wpdb->prefix . 'lci_transfer_bookings';

                // Update booking status based on order status
                $new_status = ($to_status === 'completed') ? 'confirmed' : 'cancelled';

                $wpdb->update(
                    $table_name,
                    array('status' => $new_status),
                    array('id' => $booking_id),
                    array('%s'),
                    array('%d')
                );
            }
        }
    }
}

// Initialize the class
new LCI_Transfers_WC();
