/**
 * LCI Direct Payment Handler
 */
(function($) {
    'use strict';
    
    // Initialize payment form
    function initPaymentForm() {
        // Handle payment method selection
        $('.lci-payment-method-input').on('change', function() {
            const selectedMethod = $(this).val();
            
            // Show/hide payment method descriptions
            $('.lci-payment-method-description').hide();
            $('#lci-payment-method-description-' + selectedMethod).show();
            
            // Update button text
            if (selectedMethod === 'bacs') {
                $('.lci-payment-button').text('Complete Bank Transfer Order');
            } else {
                $('.lci-payment-button').text('Proceed to Payment');
            }
        });
        
        // Handle form submission
        $('.lci-payment-form').on('submit', function(e) {
            e.preventDefault();
            
            const form = $(this);
            const submitButton = form.find('.lci-payment-button');
            const messageContainer = $('.lci-payment-messages');
            const selectedMethod = form.find('.lci-payment-method-input:checked').val();
            
            if (!selectedMethod) {
                messageContainer.html('<div class="lci-notice lci-notice-error"><p>Please select a payment method.</p></div>');
                return;
            }
            
            // Disable button and show loading message
            submitButton.prop('disabled', true).text(lciDirectPayment.i18n.processingPayment);
            messageContainer.html('<div class="lci-notice lci-notice-info"><p>' + lciDirectPayment.i18n.processingPayment + '</p></div>');
            
            // Get form data
            const formData = new FormData(form[0]);
            formData.append('action', 'lci_process_direct_payment');
            formData.append('security', lciDirectPayment.nonce);
            
            // Process payment
            $.ajax({
                url: lciDirectPayment.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        // Handle different payment types
                        if (response.data.payment_type === 'bacs') {
                            // Show bank transfer instructions
                            messageContainer.html('<div class="lci-notice lci-notice-success"><p>' + lciDirectPayment.i18n.paymentSuccess + '</p></div>');
                            $('.lci-payment-instructions').html(response.data.instructions).show();
                            
                            // Reset button
                            submitButton.prop('disabled', false).text('Complete Bank Transfer Order');
                            
                            // Hide form
                            form.find('.lci-payment-methods').hide();
                            form.find('.lci-payment-button').hide();
                            
                            // Show order details
                            if (response.data.order_id) {
                                $('.lci-order-details').html('<p>Order #' + response.data.order_id + ' has been created. Please use this as a reference when making your bank transfer.</p>').show();
                            }
                        } else if (response.data.payment_type === 'revolut' && response.data.redirect_url) {
                            // Show redirect message
                            messageContainer.html('<div class="lci-notice lci-notice-info"><p>' + lciDirectPayment.i18n.redirecting + '</p></div>');
                            
                            // Redirect to payment gateway
                            setTimeout(function() {
                                window.location.href = response.data.redirect_url;
                            }, 1000);
                        } else {
                            // Generic success
                            messageContainer.html('<div class="lci-notice lci-notice-success"><p>' + lciDirectPayment.i18n.paymentSuccess + '</p></div>');
                            submitButton.prop('disabled', false).text('Payment Completed');
                        }
                    } else {
                        // Show error message
                        messageContainer.html('<div class="lci-notice lci-notice-error"><p>' + (response.data.message || lciDirectPayment.i18n.paymentError) + '</p></div>');
                        submitButton.prop('disabled', false).text('Try Again');
                    }
                },
                error: function() {
                    // Show error message
                    messageContainer.html('<div class="lci-notice lci-notice-error"><p>' + lciDirectPayment.i18n.paymentError + '</p></div>');
                    submitButton.prop('disabled', false).text('Try Again');
                }
            });
        });
        
        // Trigger change on the checked payment method
        $('.lci-payment-method-input:checked').trigger('change');
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        initPaymentForm();
    });
})(jQuery);