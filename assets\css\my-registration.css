/**
 * My Registration page styles with UX2025 design principles
 */

/* Card styles */
.registration-card {
    border-radius: 8px 8px 0 0 !important;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease;
    overflow: hidden;
    margin-bottom: 0;
    margin-top: 1rem;
}

.registration-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12) !important;
}

.registration-card-header {
    background: linear-gradient(135deg, var(--primary-color, #36b1dc) 0%, #4fc3f7 100%);
    color: white !important;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.registration-card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(45deg);
}

.registration-card-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    color: white !important;
}

.registration-card-header h5 i {
    margin-right: 0.75rem;
    font-size: 1.5rem;
    color: white !important;
}

.registration-card-body {
    padding: 1.5rem;
    background: white;
}

/* Accommodation styles */
.accommodation-container {
    margin-top: 1rem;
}

.accommodation-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color, #36b1dc);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
    animation: fadeInUp 0.5s ease-out forwards;
}

.accommodation-item:hover {
    background: #f0f7fa;
    transform: translateX(5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
}

.accommodation-item-icon {
    background: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: var(--primary-color, #36b1dc);
    font-size: 1.25rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    flex-shrink: 0;
}

.accommodation-item-details {
    flex-grow: 1;
}

.accommodation-item-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #333;
}

.accommodation-item-info {
    color: #666;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.accommodation-item-info i {
    margin-right: 0.5rem;
    color: var(--primary-color, #36b1dc);
}

.accommodation-item-badge {
    background: rgba(54, 177, 220, 0.1);
    color: var(--primary-color, #36b1dc);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.accommodation-item-actions {
    margin-left: 1rem;
}

.accommodation-empty {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    border: 2px dashed #dee2e6;
    animation: fadeIn 0.5s ease-out forwards;
}

.accommodation-empty i {
    font-size: 2.5rem;
    color: #adb5bd;
    margin-bottom: 1rem;
    display: block;
}

.accommodation-empty p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* Tour styles - Matching tours-experience-content design */
.tour-container {
    margin-top: 1rem;
}

.tours-experiences-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.tours-experience-card {
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
    animation: fadeInUp 0.5s ease-out forwards;
    animation-delay: calc(var(--card-index, 0) * 0.1s);
    opacity: 0;
}

.tours-experience-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.tour-type-ribbon {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, var(--primary-color, #36b1dc) 0%, #2a8fb3 100%);
    color: white;
    padding: 6px 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3);
    letter-spacing: 0.5px;
    text-transform: uppercase;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
}

.tour-type-ribbon.pretour {
    background: linear-gradient(135deg, #fab33a 0%, #f59f0a 100%);
    box-shadow: 0 4px 15px rgba(250, 179, 58, 0.3);
}

.tours-experience-content {
    display: flex;
    padding: .7rem;
    gap: 1rem;
    position: relative;
    z-index: 2;
    align-items: center;
    justify-content: center;
}

.tours-experience-image {
    flex: 0 0 180px;
    height: 180px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.tours-experience-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.tours-experience-card:hover .tours-experience-image img {
    transform: scale(1.1);
}

.tours-experience-no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e6f7ff 0%, #cceeff 100%);
    color: var(--primary-color, #36b1dc);
    font-size: 3rem;
}

.tours-experience-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 1rem;
}

.tours-experience-header {
    margin-bottom: 0.2rem;
    text-align: center;
    width: 100%;
}

.tours-experience-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin: 0 0 1rem 0;
    line-height: 1.3;
    position: relative;
    display: inline-block;
    text-align: center;
    letter-spacing: 0.5px;
}

.tours-experience-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color, #36b1dc), #2a8fb3);
    transition: width 0.3s ease;
}

.tours-experience-card:hover .tours-experience-title::after {
    width: 100%;
}

.tour-short-description {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.8;
    margin-bottom: 0; /* Removed margin-bottom */
    max-height: 120px;
    overflow-y: auto;
    padding-right: 10px;
    text-align: justify;
    /* Premium scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color, #36b1dc) #f0f0f0;
}

.tour-short-description::-webkit-scrollbar {
    width: 6px;
}

.tour-short-description::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 10px;
}

.tour-short-description::-webkit-scrollbar-thumb {
    background-color: var(--primary-color, #36b1dc);
    border-radius: 10px;
}

.tour-short-description p {
    margin-bottom: 0.75rem;
}

.tour-info-row {
    display: flex;
    justify-content: space-between; /* Changed from center to space-between */
    align-items: center;
    margin-top: 0; /* Removed margin-top */
    width: 100%; /* Ensure full width */
    flex-wrap: wrap;
}

.tour-dates {
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    color: #666;
    background-color: white;
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    float: left; /* Added float left */
    margin-right: auto; /* Push to the left */
}

.tour-dates:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
}

.tour-dates i {
    color: var(--primary-color, #36b1dc);
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.tours-experience-status {
    position: relative;
    padding: 0.6rem 1.2rem;
    border-radius: 30px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    z-index: 2;
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
    float: right; /* Added float right */
    margin-left: auto; /* Push to the right */
}

.tours-experience-status:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.tours-experience-status-completed {
    background-color: rgba(40, 167, 69, 0.2);
    color: #155724;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.tours-experience-status-processing {
    background-color: rgba(13, 202, 240, 0.2);
    color: #055160;
    border: 1px solid rgba(13, 202, 240, 0.3);
}

.tours-experience-status-on-hold {
    background-color: rgba(255, 193, 7, 0.2);
    color: #664d03;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.tour-empty {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    border: 2px dashed #dee2e6;
    animation: fadeIn 0.5s ease-out forwards;
}

.tour-empty i {
    font-size: 3rem;
    color: #adb5bd;
    margin-bottom: 1rem;
    display: block;
}

.tour-empty p {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

/* Responsive styles for tours */
@media (max-width: 992px) {
    .tours-experience-content {
        flex-direction: column;
    }

    .tours-experience-image {
        width: 100%;
        height: 220px;
        flex: none;
    }

    .tours-experience-title {
        text-align: center;
        display: block;
        width: 100%;
    }

    .tours-experience-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
}

@media (max-width: 576px) {
    .tour-info-row {
        flex-direction: column;
        gap: 10px;
    }

    .tours-experience-status {
        align-self: center;
        margin-top: 5px;
        width: 100%;
        justify-content: center;
        float: none; /* Remove float on mobile */
        margin-left: 0; /* Reset margin */
    }

    .tour-dates {
        width: 100%;
        justify-content: center;
        float: none; /* Remove float on mobile */
        margin-right: 0; /* Reset margin */
    }
}

/* Button styles */
.btn-ux2025 {
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    border: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
    gap: 0.5rem;
}

.btn-ux2025:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.btn-ux2025 i {
    margin-right: 0.5rem;
    color: #fff !important;
}

.btn-ux2025-primary {
    background: var(--primary-color, #36b1dc);
    color: white !important;
}

.btn-ux2025-primary:hover {
    background: #2a8fb3;
    color: white !important;
}

.btn-ux2025-primary:hover i {
    color: white !important;
}

.btn-ux2025-outline {
    background: white;
    color: var(--primary-color, #36b1dc);
    border: 2px solid var(--primary-color, #36b1dc);
}

/* Ensure buttons are properly positioned */
.accommodation-container .btn-ux2025,
.tour-container .btn-ux2025 {
    margin-top: 1.5rem;
    width: 100%;
}

/* Small icon class */
.small-icon {
    font-size: 0.85rem !important;
}

/* Remove text decoration */
.no-text-decoration {
    text-decoration: none !important;
}

/* Bottom-only rounded corners */
.bottom-rounded-only {
    border-radius: 0 0 8px 8px !important;
}

/* Profile fields container */
.profile-fields-container,
.event-details-container {
    margin-bottom: 0;
}

/* Info line styling with right-aligned values */
.lci-info-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.lci-info-line i,
.lci-info-line strong {
    margin-right: 0.5rem;
}

.lci-info-line span {
    margin-left: auto;
    font-weight: 500;
    color: #333;
    text-align: right;
}

.lci-info-line .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 30px;
    font-weight: 600;
    min-width: 80px;
    text-align: center;
}

.btn-ux2025-outline:hover {
    background: var(--primary-color, #36b1dc);
    color: white;
}

.btn-ux2025-sm {
    padding: 0.4rem 1rem;
    font-size: 0.875rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tour-container {
        grid-template-columns: 1fr;
    }

    .accommodation-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .accommodation-item-icon {
        margin-bottom: 1rem;
    }

    .accommodation-item-actions {
        margin-left: 0;
        margin-top: 1rem;
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
}
