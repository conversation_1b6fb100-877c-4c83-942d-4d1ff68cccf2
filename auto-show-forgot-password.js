/**
 * Auto-show Forgot Password Form
 * 
 * This script checks for the show_forgot=1 parameter in the URL
 * and automatically shows the forgot password form if present.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Check if we should show the forgot password form
    const urlParams = new URLSearchParams(window.location.search);
    const showForgot = urlParams.get('show_forgot');
    
    if (showForgot === '1') {
        // Find the forgot password link and click it
        const forgotLink = document.getElementById('lci-show-forgot');
        if (forgotLink) {
            // Wait a short time to ensure all scripts are loaded
            setTimeout(function() {
                forgotLink.click();
            }, 500);
        }
    }
});
