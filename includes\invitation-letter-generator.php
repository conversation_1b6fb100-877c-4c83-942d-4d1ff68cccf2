<?php
/**
 * Invitation Letter Generator
 *
 * Handles the generation of invitation letters for visa applications
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Generate a QR code for the invitation letter
 *
 * @param string $unique_id Unique identifier for the invitation
 * @return string URL to the generated QR code image
 */
function generate_invitation_qr_code($unique_id) {
    try {
        error_log('Starting QR code generation for ID: ' . $unique_id);

        // Create directory if it doesn't exist
        $upload_dir = wp_upload_dir();
        $qr_dir = $upload_dir['basedir'] . '/invitation-qrcodes/';

        if (!file_exists($qr_dir)) {
            if (!mkdir($qr_dir, 0755, true)) {
                error_log('Failed to create directory: ' . $qr_dir);
                throw new Exception('Failed to create QR code directory');
            }
        }

        // Generate QR code file name
        $qr_filename = 'qr_' . $unique_id . '.png';
        $qr_path = $qr_dir . $qr_filename;
        $qr_url = $upload_dir['baseurl'] . '/invitation-qrcodes/' . $qr_filename;

        // Generate QR code
        $verification_url = home_url('/verify-invitation?id=' . $unique_id);
        error_log('Verification URL: ' . $verification_url);

        // Check if phpqrcode library exists
        $qrcode_path = LCI2025_PATH . 'lib/phpqrcode/qrlib.php';
        if (file_exists($qrcode_path)) {
            require_once($qrcode_path);
            error_log('QR code library found at: ' . $qrcode_path);
        } else {
            error_log('QR code library not found at: ' . $qrcode_path);

            // Try alternative paths
            $alt_paths = [
                LCI2025_PATH . 'lib/phpqrcode/qrcode.php',
                ABSPATH . 'wp-content/plugins/lci-2025-dashboard/lib/phpqrcode/qrlib.php',
                ABSPATH . 'wp-content/plugins/lci-2025-dashboard/lib/phpqrcode/qrcode.php'
            ];

            $found = false;
            foreach ($alt_paths as $path) {
                if (file_exists($path)) {
                    require_once($path);
                    error_log('QR code library found at alternative path: ' . $path);
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                error_log('QR code library not found in any expected location');
                throw new Exception('QR code library not found');
            }
        }

        // If phpqrcode library is available, generate the QR code
        if (class_exists('QRcode')) {
            error_log('Generating QR code using QRcode class');
            QRcode::png($verification_url, $qr_path, 'M', 10, 2);

            if (file_exists($qr_path)) {
                error_log('QR code generated successfully at: ' . $qr_path);
                return $qr_url;
            } else {
                error_log('QR code file not created at: ' . $qr_path);
            }
        } else {
            error_log('QRcode class not available');
        }

        // If we get here, try to create a simple image with text
        error_log('Falling back to simple image generation');
        $img = imagecreatetruecolor(200, 200);
        $bg = imagecolorallocate($img, 255, 255, 255);
        $textcolor = imagecolorallocate($img, 0, 0, 0);
        imagefilledrectangle($img, 0, 0, 200, 200, $bg);
        imagestring($img, 5, 40, 90, 'Verification ID:', $textcolor);
        imagestring($img, 5, 40, 110, $unique_id, $textcolor);
        imagepng($img, $qr_path);
        imagedestroy($img);

        if (file_exists($qr_path)) {
            error_log('Simple image generated successfully');
            return $qr_url;
        }

        // Final fallback to a placeholder image
        error_log('Using placeholder image as final fallback');
        return 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp';

    } catch (Exception $e) {
        error_log('QR Code Generation Error: ' . $e->getMessage());
        return 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp';
    }
}

/**
 * Handle AJAX request to generate invitation letter
 */
function generate_invitation_letter_ajax() {
    // Enable error reporting for debugging
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);

    try {
        // Check nonce for security
        check_ajax_referer('generate_invitation_letter_nonce', 'security');

        // Get current user
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error(['message' => 'User not logged in']);
            return;
        }

        // Log the POST data for debugging
        error_log('Invitation Letter POST data: ' . print_r($_POST, true));

        // Get form data
        $passport_number = isset($_POST['passport_number']) ? sanitize_text_field($_POST['passport_number']) : '';
        $nationality = isset($_POST['nationality']) ? sanitize_text_field($_POST['nationality']) : '';
        $place_of_birth = isset($_POST['place_of_birth']) ? sanitize_text_field($_POST['place_of_birth']) : '';
        $date_of_birth = isset($_POST['date_of_birth']) ? sanitize_text_field($_POST['date_of_birth']) : '';
        $passport_issue_date = isset($_POST['passport_issue_date']) ? sanitize_text_field($_POST['passport_issue_date']) : '';
        $passport_expiry_date = isset($_POST['passport_expiry_date']) ? sanitize_text_field($_POST['passport_expiry_date']) : '';
        $arrival_date = isset($_POST['arrival_date']) ? sanitize_text_field($_POST['arrival_date']) : '';
        $departure_date = isset($_POST['departure_date']) ? sanitize_text_field($_POST['departure_date']) : '';

        // Validate required fields
        if (empty($passport_number) || empty($nationality) || empty($place_of_birth) ||
            empty($date_of_birth) || empty($passport_issue_date) || empty($passport_expiry_date) ||
            empty($arrival_date) || empty($departure_date)) {
            wp_send_json_error(['message' => 'All fields are required']);
            return;
        }

        // Get user data
        $user = get_userdata($user_id);
        $full_name = $user->display_name;
        $country = get_user_meta($user_id, 'country', true);

        // For testing, if country is empty, use nationality
        if (empty($country)) {
            $country = $nationality;
        }

        // Generate a unique ID for this invitation
        $unique_id = uniqid('lci2025_', true);

        // Check if directories exist, create if not
        $upload_dir = wp_upload_dir();
        $letters_dir = $upload_dir['basedir'] . '/invitation-letters/';
        $qrcodes_dir = $upload_dir['basedir'] . '/invitation-qrcodes/';

        if (!file_exists($letters_dir)) {
            mkdir($letters_dir, 0755, true);
            error_log('Created directory: ' . $letters_dir);
        }

        if (!file_exists($qrcodes_dir)) {
            mkdir($qrcodes_dir, 0755, true);
            error_log('Created directory: ' . $qrcodes_dir);
        }

        // Generate QR code
        error_log('Generating QR code...');
        $qr_code_url = generate_invitation_qr_code($unique_id);
        error_log('QR code URL: ' . $qr_code_url);

        // Determine user type based on purchased products
        error_log('Determining user type...');
        $user_type = determine_user_type($user_id);
        error_log('User type: ' . $user_type);

        // For testing, if user type is unknown, default to participant
        if ($user_type === 'unknown') {
            $user_type = 'participant';
            error_log('User type defaulted to participant for testing');
        }

        // Generate PDF based on user type
        error_log('Generating PDF...');
        if ($user_type === 'delegate') {
            $pdf_url = generate_delegate_invitation_letter(
                $full_name,
                $country,
                $passport_number,
                $nationality,
                $place_of_birth,
                $date_of_birth,
                $passport_issue_date,
                $passport_expiry_date,
                $arrival_date,
                $departure_date,
                $qr_code_url,
                $unique_id
            );
        } else {
            $pdf_url = generate_participant_invitation_letter(
                $full_name,
                $country,
                $passport_number,
                $nationality,
                $place_of_birth,
                $date_of_birth,
                $passport_issue_date,
                $passport_expiry_date,
                $arrival_date,
                $departure_date,
                $qr_code_url,
                $unique_id
            );
        }

        error_log('PDF URL: ' . $pdf_url);

        // If PDF generation failed
        if (empty($pdf_url)) {
            wp_send_json_error(['message' => 'Failed to generate PDF']);
            return;
        }

        // Save PDF URL and invitation data to user meta
        update_user_meta($user_id, 'invitation_letter_pdf', $pdf_url);
        update_user_meta($user_id, 'invitation_letter_generated_date', current_time('mysql'));
        update_user_meta($user_id, 'invitation_letter_unique_id', $unique_id);

        // Return success
        wp_send_json_success(['pdf_url' => $pdf_url]);

    } catch (Exception $e) {
        error_log('Invitation Letter Error: ' . $e->getMessage());
        wp_send_json_error(['message' => 'An error occurred: ' . $e->getMessage()]);
    }
}
add_action('wp_ajax_generate_invitation_letter', 'generate_invitation_letter_ajax');

/**
 * Determine user type based on purchased products
 *
 * @param int $user_id User ID
 * @return string User type ('delegate', 'participant', or 'unknown')
 */
function determine_user_type($user_id) {
    // Get user's orders
    $customer_orders = get_posts(array(
        'numberposts' => -1,
        'meta_key'    => '_customer_user',
        'meta_value'  => $user_id,
        'post_type'   => 'shop_order',
        'post_status' => array('wc-completed', 'wc-processing')
    ));

    $is_delegate = false;
    $is_participant = false;

    // Loop through orders
    foreach ($customer_orders as $order) {
        $order_obj = wc_get_order($order->ID);

        // Loop through order items
        foreach ($order_obj->get_items() as $item) {
            $product_id = $item->get_product_id();

            // Check product IDs
            if ($product_id == 40) {
                $is_delegate = true;
            } else if ($product_id == 41 || $product_id == 42) {
                $is_participant = true;
            }
        }
    }

    // Determine user type
    if ($is_delegate) {
        return 'delegate';
    } else if ($is_participant) {
        return 'participant';
    } else {
        return 'unknown';
    }
}

/**
 * Generate invitation letter for delegates
 *
 * @param string $full_name User's full name
 * @param string $country User's country
 * @param string $passport_number User's passport number
 * @param string $nationality User's nationality
 * @param string $place_of_birth User's place of birth
 * @param string $date_of_birth User's date of birth
 * @param string $passport_issue_date User's passport issue date
 * @param string $passport_expiry_date User's passport expiry date
 * @param string $arrival_date User's arrival date
 * @param string $departure_date User's departure date
 * @param string $qr_code_url URL to the QR code image
 * @param string $unique_id Unique identifier for the invitation
 * @return string URL to the generated PDF
 */
function generate_delegate_invitation_letter(
    $full_name,
    $country,
    $passport_number,
    $nationality,
    $place_of_birth,
    $date_of_birth,
    $passport_issue_date,
    $passport_expiry_date,
    $arrival_date,
    $departure_date,
    $qr_code_url,
    $unique_id
) {
    try {
        error_log('Starting PDF generation for delegate invitation letter');

        // Check for PDF libraries
        $use_tcpdf = false;
        $use_mpdf = false;
        $use_dompdf = false;

        // Try TCPDF
        $tcpdf_path = LCI2025_PATH . 'lib/tcpdf/tcpdf.php';
        if (file_exists($tcpdf_path)) {
            require_once($tcpdf_path);
            $use_tcpdf = true;
            error_log('Using TCPDF library from: ' . $tcpdf_path);
        } else {
            error_log('TCPDF not found at: ' . $tcpdf_path);

            // Try alternative paths for TCPDF
            $alt_tcpdf_paths = [
                ABSPATH . 'wp-content/plugins/lci-2025-dashboard/lib/tcpdf/tcpdf.php',
                ABSPATH . 'wp-content/plugins/tcpdf/tcpdf.php'
            ];

            foreach ($alt_tcpdf_paths as $path) {
                if (file_exists($path)) {
                    require_once($path);
                    $use_tcpdf = true;
                    error_log('Using TCPDF from alternative path: ' . $path);
                    break;
                }
            }
        }

        // Try mPDF if TCPDF not available
        if (!$use_tcpdf) {
            $mpdf_path = LCI2025_PATH . 'lib/mpdf/vendor/autoload.php';
            if (file_exists($mpdf_path)) {
                require_once($mpdf_path);
                $use_mpdf = true;
                error_log('Using mPDF library from: ' . $mpdf_path);
            } else {
                error_log('mPDF not found at: ' . $mpdf_path);

                // Try alternative paths for mPDF
                $alt_mpdf_paths = [
                    ABSPATH . 'wp-content/plugins/lci-2025-dashboard/lib/mpdf/vendor/autoload.php',
                    ABSPATH . 'wp-content/plugins/mpdf/vendor/autoload.php'
                ];

                foreach ($alt_mpdf_paths as $path) {
                    if (file_exists($path)) {
                        require_once($path);
                        $use_mpdf = true;
                        error_log('Using mPDF from alternative path: ' . $path);
                        break;
                    }
                }
            }
        }

        // Try Dompdf if neither TCPDF nor mPDF is available
        if (!$use_tcpdf && !$use_mpdf) {
            $dompdf_path = LCI2025_PATH . 'lib/dompdf/autoload.inc.php';
            if (file_exists($dompdf_path)) {
                require_once($dompdf_path);
                $use_dompdf = true;
                error_log('Using Dompdf library from: ' . $dompdf_path);
            } else {
                error_log('Dompdf not found at: ' . $dompdf_path);

                // Try alternative paths for Dompdf
                $alt_dompdf_paths = [
                    ABSPATH . 'wp-content/plugins/lci-2025-dashboard/lib/dompdf/autoload.inc.php',
                    ABSPATH . 'wp-content/plugins/dompdf/autoload.inc.php'
                ];

                foreach ($alt_dompdf_paths as $path) {
                    if (file_exists($path)) {
                        require_once($path);
                        $use_dompdf = true;
                        error_log('Using Dompdf from alternative path: ' . $path);
                        break;
                    }
                }
            }
        }

        // If no PDF library is available, create a simple HTML file
        if (!$use_tcpdf && !$use_mpdf && !$use_dompdf) {
            error_log('No PDF library available, creating HTML file instead');

            // Generate unique filename
            $filename = 'invitation_delegate_' . sanitize_title($full_name) . '_' . time() . '.html';
            $upload_dir = wp_upload_dir();
            $html_path = $upload_dir['basedir'] . '/invitation-letters/' . $filename;
            $html_url = $upload_dir['baseurl'] . '/invitation-letters/' . $filename;

            // Create directory if it doesn't exist
            if (!file_exists($upload_dir['basedir'] . '/invitation-letters/')) {
                mkdir($upload_dir['basedir'] . '/invitation-letters/', 0755, true);
            }

            // Save HTML file
            file_put_contents($html_path, $html);

            // Store invitation data in database for verification
            global $wpdb;
            $table_name = $wpdb->prefix . 'lci_invitation_letters';

            // Create table if it doesn't exist
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
                $charset_collate = $wpdb->get_charset_collate();
                $sql = "CREATE TABLE $table_name (
                    id mediumint(9) NOT NULL AUTO_INCREMENT,
                    unique_id varchar(100) NOT NULL,
                    user_id bigint(20) NOT NULL,
                    full_name varchar(100) NOT NULL,
                    passport_number varchar(50) NOT NULL,
                    nationality varchar(100) NOT NULL,
                    user_type varchar(20) NOT NULL,
                    generated_date datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
                    pdf_url varchar(255) NOT NULL,
                    PRIMARY KEY  (id),
                    UNIQUE KEY unique_id (unique_id)
                ) $charset_collate;";

                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                dbDelta($sql);
            }

            // Insert data
            $wpdb->insert(
                $table_name,
                array(
                    'unique_id' => $unique_id,
                    'user_id' => get_current_user_id(),
                    'full_name' => $full_name,
                    'passport_number' => $passport_number,
                    'nationality' => $nationality,
                    'user_type' => 'delegate',
                    'pdf_url' => $html_url
                )
            );

            return $html_url;
        }

    // Format dates for display
    $current_date = date('F j, Y');
    $formatted_date_of_birth = date('F j, Y', strtotime($date_of_birth));
    $formatted_passport_issue_date = date('F j, Y', strtotime($passport_issue_date));
    $formatted_passport_expiry_date = date('F j, Y', strtotime($passport_expiry_date));
    $formatted_arrival_date = date('F j, Y', strtotime($arrival_date));
    $formatted_departure_date = date('F j, Y', strtotime($departure_date));

    // HTML template for delegate invitation letter
    $html = '<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Visa Invitation - Delegate - LCI AGM 2025</title>
<style>
@font-face {
    font-family: "DejaVu Sans";
    src: url("https://kendo.cdn.telerik.com/2017.2.621/styles/fonts/DejaVu/DejaVuSans.ttf") format("truetype");
}
@font-face {
    font-family: "DejaVu Sans";
    font-weight: bold;
    src: url("https://kendo.cdn.telerik.com/2017.2.621/styles/fonts/DejaVu/DejaVuSans-Bold.ttf") format("truetype");
}
body { font-family: "DejaVu Sans", Arial, sans-serif; font-size: 12px; line-height: 1.4; margin: 20px; color: #333; }
h1 { text-align: center; color: #36b1dc; font-size: 18px; margin-top: 10px; margin-bottom: 10px; }
h2 { text-align: center; color: #36b1dc; font-size: 14px; margin-top: 10px; margin-bottom: 8px; }
.logo { text-align: center; margin-bottom: 15px; }
.signature { margin-top: 30px; }
.page-break { page-break-before: always; }
.footer { text-align: center; font-size: 10px; margin-top: 30px; color: #777; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
table, th, td { border: 1px solid #36b1dc; }
td { padding: 5px; font-size: 11px; }
th { background-color: #36b1dc; color: #fff; padding: 5px; text-align: left; font-size: 11px; }
ul { list-style: none; padding: 0; margin: 5px 0; }
ul li { margin-bottom: 5px; font-size: 11px; }
p { margin: 5px 0; font-size: 12px; }
</style>
</head>
<body>

<div class="logo">
<img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp" alt="LCI AGM 2025" width="120">
</div>

<h1>Invitation Letter for Visa Application</h1>

<p><strong>Date:</strong> ' . $current_date . '</p>

<p>To whom it may concern,</p>

<p>On behalf of the Organizing Committee of the Ladies\' Circle International Annual General Meeting 2025 (AGM 2025), we are pleased to formally invite <strong>' . $full_name . '</strong> to attend as an official <strong>Delegate</strong> representing her national Circle.</p>

<p>The AGM 2025 will take place in Brașov, Romania, between <strong>21 August 2025 and 24 August 2025</strong>. The official venue and accommodation is the prestigious <strong>Hotel Aro Palace, Bulevardul Eroilor 27, Brașov 500030, Romania</strong>.</p>

<h2>Participant Identification</h2>

<table>
<tr><td width="40%"><strong>Full Name:</strong></td><td width="60%">' . $full_name . '</td></tr>
<tr><td><strong>Passport Number:</strong></td><td>' . $passport_number . '</td></tr>
<tr><td><strong>Nationality:</strong></td><td>' . $nationality . '</td></tr>
<tr><td><strong>Place of Birth:</strong></td><td>' . $place_of_birth . '</td></tr>
<tr><td><strong>Date of Birth:</strong></td><td>' . $formatted_date_of_birth . '</td></tr>
<tr><td><strong>Passport Issue Date:</strong></td><td>' . $formatted_passport_issue_date . '</td></tr>
<tr><td><strong>Passport Expiry Date:</strong></td><td>' . $formatted_passport_expiry_date . '</td></tr>
<tr><td><strong>Accommodation:</strong></td><td>Hotel Aro Palace, Brașov, Romania</td></tr>
<tr><td><strong>Stay Period:</strong></td><td>' . $formatted_arrival_date . ' – ' . $formatted_departure_date . '</td></tr>
</table>

<h2>About Ladies\' Circle International</h2>

<p>Ladies\' Circle International (LCI) is a dynamic network founded in 1936 in the United Kingdom, today present in over 40 countries. It promotes international friendship, service to communities, leadership development, and global understanding among women aged 18 to 45. www.ladiescircleinternational.org</p>

<h2>About the Ladies\' Circle International AGM 2025</h2>

<ul>
<li>21 August 2025: Opening Ceremony, Parade, Welcome Party</li>
<li>22 August 2025: AGM Conference, Lunch, Bars & Pubs Parties (Old Town Brașov)</li>
<li>23 August 2025: Gala Dinner & Open Bar (Kronwell Ballroom)</li>
<li>24 August 2025: Farewell</li>
</ul>

<h2>Importance of Attendance</h2>

<p>As a Delegate, <strong>' . $full_name . '</strong> will represent her national Circle, participate in votes on international matters, contribute to leadership discussions, and foster cross-cultural collaboration within the organization.</p>

<h2>Guarantees and Legal Compliance</h2>

<p>The Organizing Committee guarantees that <strong>' . $full_name . '</strong> will comply fully with Romanian immigration regulations, participate exclusively in AGM 2025 activities, and depart Romania as scheduled after the event.</p>

<p><strong>Disclaimer:</strong> This invitation does not guarantee visa issuance. Visa applications are subject to Romanian law and consular authority decisions.</p>

<h2>Contact Information</h2>

<p><strong>Cristina Lengyel</strong> - Event Convenor<br>Phone: +40 722 410 579<br>Email: <EMAIL></p>

<h2>Event Verification</h2>
<p>For further verification, please scan the QR code below to visit the official AGM 2025 event page:</p>
<div style="text-align: center; margin: 10px 0;">
  <img src="' . $qr_code_url . '" alt="AGM 2025 Event QR Code" width="100">
</div>


<div class="footer">
 LCI 2025 BRASOV SRL / CUI: 50683317 / J2024030241002 Str. Poarta Schei 33 Ap. 1, ROMANIA</div>

<div class="page-break"></div>

<!-- Romanian Page -->

<div class="logo">
<img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp" alt="LCI AGM 2025" width="120">
</div>

<h1>Scrisoare de invitație pentru aplicare viză</h1>

<p><strong>Data:</strong> ' . $current_date . '</p>

<p>Către cine este interesat,</p>

<p>În numele Comitetului de Organizare al Ladies\' Circle International Annual General Meeting 2025 (AGM 2025), avem plăcerea de a invita în mod oficial pe <strong>' . $full_name . '</strong> să participe ca <strong>Delegat oficial</strong> reprezentând cercul său național.</p>

<p>AGM 2025 va avea loc în Brașov, România, între <strong>21 august 2025 și 24 august 2025</strong>, la prestigiosul <strong>Hotel Aro Palace, Bulevardul Eroilor 27, Brașov 500030, România</strong>.</p>

<h2>Date de identificare participant</h2>

<table>
<tr><td width="40%"><strong>Nume complet:</strong></td><td width="60%">' . $full_name . '</td></tr>
<tr><td><strong>Număr pașaport:</strong></td><td>' . $passport_number . '</td></tr>
<tr><td><strong>Naționalitate:</strong></td><td>' . $nationality . '</td></tr>
<tr><td><strong>Locul nașterii:</strong></td><td>' . $place_of_birth . '</td></tr>
<tr><td><strong>Data nașterii:</strong></td><td>' . $formatted_date_of_birth . '</td></tr>
<tr><td><strong>Data emiterii pașaportului:</strong></td><td>' . $formatted_passport_issue_date . '</td></tr>
<tr><td><strong>Data expirării pașaportului:</strong></td><td>' . $formatted_passport_expiry_date . '</td></tr>
<tr><td><strong>Cazare:</strong></td><td>Hotel Aro Palace, Brașov, România</td></tr>
<tr><td><strong>Perioada sejurului:</strong></td><td>' . $formatted_arrival_date . ' – ' . $formatted_departure_date . '</td></tr>
</table>

<h2>Despre Ladies\' Circle International</h2>

<p>Ladies\' Circle International (LCI) este o rețea globală dinamică, fondată în 1936 în Regatul Unit, prezentă astăzi în peste 40 de țări. Organizația promovează prietenia internațională, dezvoltarea personală, serviciul comunitar și înțelegerea globală între femeile cu vârste între 18 și 45 de ani. www.ladiescircleinternational.org </p>

<h2>Despre AGM 2025</h2>

<ul>
<li>21 August 2025: Ceremonia de deschidere, Paradă, Petrecere de bun venit</li>
<li>22 August 2025: Conferința AGM, Prânz, Petreceri în barurile și pub-urile din Centrul Vechi Brașov</li>
<li>23 August 2025: Cină de Gală și Open Bar (Kronwell Ballroom)</li>
<li>24 August 2025: Rămas bun</li>
</ul>

<h2>Importanța participării</h2>

<p>În calitate de Delegat, ' . $full_name . ' va reprezenta cercul său național, va participa la voturi pe probleme organizaționale internaționale, va contribui la dezbateri de leadership și va sprijini colaborarea interculturală în cadrul organizației.</p>

<h2>Garanții și conformitate legală</h2>

<p>Comitetul de Organizare garantează că <strong>' . $full_name . '</strong> va respecta integral reglementările de imigrare ale României, va participa exclusiv la activitățile AGM 2025 și va părăsi România conform programului stabilit.</p>

<p><strong>Notă:</strong> Această invitație nu garantează automat acordarea vizei. Cererile de viză sunt procesate conform legislației române și deciziilor autorităților consulare.</p>

<h2>Date de contact</h2>

<p><strong>Cristina Lengyel</strong> - Coordonator Eveniment<br>Telefon: +40 722 410 579<br>Email: <EMAIL></p>

<h2>Verificare eveniment</h2>
<p>Pentru verificare suplimentară, vă rugăm să scanați codul QR de mai jos pentru a vizita pagina oficială a evenimentului AGM 2025:</p>
<div style="text-align: center; margin: 10px 0;">
  <img src="' . $qr_code_url . '" alt="Cod QR Eveniment AGM 2025" width="100">
</div>

<div class="footer">
 LCI 2025 BRASOV SRL / CUI: 50683317 / J2024030241002 Str. Poarta Schei 33 Ap. 1, ROMANIA</div>

</body>
</html>';

    // Generate PDF
    if ($use_tcpdf) {
        // Using TCPDF
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // Set document information
        $pdf->SetCreator('LCI 2025 AGM');
        $pdf->SetAuthor('Ladies Circle International');
        $pdf->SetTitle('Official Invitation Letter - Delegate');
        $pdf->SetSubject('Visa Invitation Letter');

        // Remove header/footer
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // Set margins
        $pdf->SetMargins(15, 15, 15);

        // Add a page
        $pdf->AddPage();

        // Output HTML content
        $pdf->writeHTML($html, true, false, true, false, '');

        // Generate unique filename
        $filename = 'invitation_delegate_' . sanitize_title($full_name) . '_' . time() . '.pdf';
        $upload_dir = wp_upload_dir();
        $pdf_path = $upload_dir['basedir'] . '/invitation-letters/' . $filename;
        $pdf_url = $upload_dir['baseurl'] . '/invitation-letters/' . $filename;

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir['basedir'] . '/invitation-letters/')) {
            mkdir($upload_dir['basedir'] . '/invitation-letters/', 0755, true);
        }

        // Save PDF
        $pdf->Output($pdf_path, 'F');
    } else {
        // Using mPDF
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 15,
            'margin_bottom' => 15
        ]);

        // Set document information
        $mpdf->SetCreator('LCI 2025 AGM');
        $mpdf->SetAuthor('Ladies Circle International');
        $mpdf->SetTitle('Official Invitation Letter - Delegate');
        $mpdf->SetSubject('Visa Invitation Letter');

        // Write HTML content
        $mpdf->WriteHTML($html);

        // Generate unique filename
        $filename = 'invitation_delegate_' . sanitize_title($full_name) . '_' . time() . '.pdf';
        $upload_dir = wp_upload_dir();
        $pdf_path = $upload_dir['basedir'] . '/invitation-letters/' . $filename;
        $pdf_url = $upload_dir['baseurl'] . '/invitation-letters/' . $filename;

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir['basedir'] . '/invitation-letters/')) {
            mkdir($upload_dir['basedir'] . '/invitation-letters/', 0755, true);
        }

        // Save PDF
        $mpdf->Output($pdf_path, \Mpdf\Output\Destination::FILE);
    }

    // Store invitation data in database for verification
    global $wpdb;
    $table_name = $wpdb->prefix . 'lci_invitation_letters';

    // Create table if it doesn't exist
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        $charset_collate = $wpdb->get_charset_collate();
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            unique_id varchar(100) NOT NULL,
            user_id bigint(20) NOT NULL,
            full_name varchar(100) NOT NULL,
            passport_number varchar(50) NOT NULL,
            nationality varchar(100) NOT NULL,
            user_type varchar(20) NOT NULL,
            generated_date datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            pdf_url varchar(255) NOT NULL,
            PRIMARY KEY  (id),
            UNIQUE KEY unique_id (unique_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    // Insert data
    $wpdb->insert(
        $table_name,
        array(
            'unique_id' => $unique_id,
            'user_id' => get_current_user_id(),
            'full_name' => $full_name,
            'passport_number' => $passport_number,
            'nationality' => $nationality,
            'user_type' => 'delegate',
            'pdf_url' => $pdf_url
        )
    );

    return $pdf_url;
    } catch (Exception $e) {
        error_log('Delegate PDF Generation Error: ' . $e->getMessage());

        // Create a simple HTML file as fallback
        $filename = 'invitation_delegate_' . sanitize_title($full_name) . '_' . time() . '.html';
        $upload_dir = wp_upload_dir();
        $html_path = $upload_dir['basedir'] . '/invitation-letters/' . $filename;
        $html_url = $upload_dir['baseurl'] . '/invitation-letters/' . $filename;

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir['basedir'] . '/invitation-letters/')) {
            mkdir($upload_dir['basedir'] . '/invitation-letters/', 0755, true);
        }

        // Save HTML file
        file_put_contents($html_path, $html);

        return $html_url;
    }
}

/**
 * Generate invitation letter for participants
 *
 * @param string $full_name User's full name
 * @param string $country User's country
 * @param string $passport_number User's passport number
 * @param string $nationality User's nationality
 * @param string $place_of_birth User's place of birth
 * @param string $date_of_birth User's date of birth
 * @param string $passport_issue_date User's passport issue date
 * @param string $passport_expiry_date User's passport expiry date
 * @param string $arrival_date User's arrival date
 * @param string $departure_date User's departure date
 * @param string $qr_code_url URL to the QR code image
 * @param string $unique_id Unique identifier for the invitation
 * @return string URL to the generated PDF
 */
function generate_participant_invitation_letter(
    $full_name,
    $country,
    $passport_number,
    $nationality,
    $place_of_birth,
    $date_of_birth,
    $passport_issue_date,
    $passport_expiry_date,
    $arrival_date,
    $departure_date,
    $qr_code_url,
    $unique_id
) {
    try {
        error_log('Starting PDF generation for participant invitation letter');

        // Check for PDF libraries
        $use_tcpdf = false;
        $use_mpdf = false;
        $use_dompdf = false;

        // Try TCPDF
        $tcpdf_path = LCI2025_PATH . 'lib/tcpdf/tcpdf.php';
        if (file_exists($tcpdf_path)) {
            require_once($tcpdf_path);
            $use_tcpdf = true;
            error_log('Using TCPDF library from: ' . $tcpdf_path);
        } else {
            error_log('TCPDF not found at: ' . $tcpdf_path);

            // Try alternative paths for TCPDF
            $alt_tcpdf_paths = [
                ABSPATH . 'wp-content/plugins/lci-2025-dashboard/lib/tcpdf/tcpdf.php',
                ABSPATH . 'wp-content/plugins/tcpdf/tcpdf.php'
            ];

            foreach ($alt_tcpdf_paths as $path) {
                if (file_exists($path)) {
                    require_once($path);
                    $use_tcpdf = true;
                    error_log('Using TCPDF from alternative path: ' . $path);
                    break;
                }
            }
        }

        // Try mPDF if TCPDF not available
        if (!$use_tcpdf) {
            $mpdf_path = LCI2025_PATH . 'lib/mpdf/vendor/autoload.php';
            if (file_exists($mpdf_path)) {
                require_once($mpdf_path);
                $use_mpdf = true;
                error_log('Using mPDF library from: ' . $mpdf_path);
            } else {
                error_log('mPDF not found at: ' . $mpdf_path);

                // Try alternative paths for mPDF
                $alt_mpdf_paths = [
                    ABSPATH . 'wp-content/plugins/lci-2025-dashboard/lib/mpdf/vendor/autoload.php',
                    ABSPATH . 'wp-content/plugins/mpdf/vendor/autoload.php'
                ];

                foreach ($alt_mpdf_paths as $path) {
                    if (file_exists($path)) {
                        require_once($path);
                        $use_mpdf = true;
                        error_log('Using mPDF from alternative path: ' . $path);
                        break;
                    }
                }
            }
        }

        // Try Dompdf if neither TCPDF nor mPDF is available
        if (!$use_tcpdf && !$use_mpdf) {
            $dompdf_path = LCI2025_PATH . 'lib/dompdf/autoload.inc.php';
            if (file_exists($dompdf_path)) {
                require_once($dompdf_path);
                $use_dompdf = true;
                error_log('Using Dompdf library from: ' . $dompdf_path);
            } else {
                error_log('Dompdf not found at: ' . $dompdf_path);

                // Try alternative paths for Dompdf
                $alt_dompdf_paths = [
                    ABSPATH . 'wp-content/plugins/lci-2025-dashboard/lib/dompdf/autoload.inc.php',
                    ABSPATH . 'wp-content/plugins/dompdf/autoload.inc.php'
                ];

                foreach ($alt_dompdf_paths as $path) {
                    if (file_exists($path)) {
                        require_once($path);
                        $use_dompdf = true;
                        error_log('Using Dompdf from alternative path: ' . $path);
                        break;
                    }
                }
            }
        }

        // If no PDF library is available, create a simple HTML file
        if (!$use_tcpdf && !$use_mpdf && !$use_dompdf) {
            error_log('No PDF library available, creating HTML file instead');

            // Generate unique filename
            $filename = 'invitation_participant_' . sanitize_title($full_name) . '_' . time() . '.html';
            $upload_dir = wp_upload_dir();
            $html_path = $upload_dir['basedir'] . '/invitation-letters/' . $filename;
            $html_url = $upload_dir['baseurl'] . '/invitation-letters/' . $filename;

            // Create directory if it doesn't exist
            if (!file_exists($upload_dir['basedir'] . '/invitation-letters/')) {
                mkdir($upload_dir['basedir'] . '/invitation-letters/', 0755, true);
            }

            // Save HTML file
            file_put_contents($html_path, $html);

            // Store invitation data in database for verification
            global $wpdb;
            $table_name = $wpdb->prefix . 'lci_invitation_letters';

            // Create table if it doesn't exist
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
                $charset_collate = $wpdb->get_charset_collate();
                $sql = "CREATE TABLE $table_name (
                    id mediumint(9) NOT NULL AUTO_INCREMENT,
                    unique_id varchar(100) NOT NULL,
                    user_id bigint(20) NOT NULL,
                    full_name varchar(100) NOT NULL,
                    passport_number varchar(50) NOT NULL,
                    nationality varchar(100) NOT NULL,
                    user_type varchar(20) NOT NULL,
                    generated_date datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
                    pdf_url varchar(255) NOT NULL,
                    PRIMARY KEY  (id),
                    UNIQUE KEY unique_id (unique_id)
                ) $charset_collate;";

                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                dbDelta($sql);
            }

            // Insert data
            $wpdb->insert(
                $table_name,
                array(
                    'unique_id' => $unique_id,
                    'user_id' => get_current_user_id(),
                    'full_name' => $full_name,
                    'passport_number' => $passport_number,
                    'nationality' => $nationality,
                    'user_type' => 'participant',
                    'pdf_url' => $html_url
                )
            );

            return $html_url;
        }

    // Format dates for display
    $current_date = date('F j, Y');
    $formatted_date_of_birth = date('F j, Y', strtotime($date_of_birth));
    $formatted_passport_issue_date = date('F j, Y', strtotime($passport_issue_date));
    $formatted_passport_expiry_date = date('F j, Y', strtotime($passport_expiry_date));
    $formatted_arrival_date = date('F j, Y', strtotime($arrival_date));
    $formatted_departure_date = date('F j, Y', strtotime($departure_date));

    // HTML template for participant invitation letter
    $html = '<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Visa Invitation - Participant - LCI AGM 2025</title>
<style>
@font-face {
    font-family: "DejaVu Sans";
    src: url("https://kendo.cdn.telerik.com/2017.2.621/styles/fonts/DejaVu/DejaVuSans.ttf") format("truetype");
}
@font-face {
    font-family: "DejaVu Sans";
    font-weight: bold;
    src: url("https://kendo.cdn.telerik.com/2017.2.621/styles/fonts/DejaVu/DejaVuSans-Bold.ttf") format("truetype");
}
body { font-family: "DejaVu Sans", Arial, sans-serif; font-size: 12px; line-height: 1.4; margin: 10px; color: #333; }
h1 { text-align: center; color: #36b1dc; font-size: 18px; margin-top: 5px; margin-bottom: 5px; }
h2 { text-align: center; color: #36b1dc; font-size: 14px; margin-top: 5px; margin-bottom: 4px; }
.logo { text-align: center; margin-bottom: 5px; }
.signature { margin-top: 10px; }
.page-break { page-break-before: always; }
.footer { text-align: center; font-size: 5px; margin-top: 5px; color: #777; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
table, th, td { border: 1px solid #36b1dc; }
td { padding: 5px; font-size: 11px; }
th { background-color: #36b1dc; color: #fff; padding: 5px; text-align: left; font-size: 11px; }
ul { list-style: none; padding: 0; margin: 5px 0; }
ul li { margin-bottom: 5px; font-size: 11px; }
p { margin: 5px 0; font-size: 12px; }
</style>
</head>
<body>

<div class="logo">
<img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2025/04/26142759/LCI_Pack.png" alt="LCI AGM 2025" width="120">
</div>

<h1>Invitation Letter for Visa Application</h1>

<p><strong>Date:</strong> ' . $current_date . '</p>

<p>To whom it may concern,</p>

<p>On behalf of the Organizing Committee of the Ladies\' Circle International Annual General Meeting 2025 (AGM 2025), we are pleased to formally invite <strong>' . $full_name . '</strong> to attend as a <strong>Participant</strong>.</p>

<p>The AGM 2025 will take place in Brașov, Romania, between <strong>21 August 2025 and 24 August 2025</strong> at the prestigious <strong>Hotel Aro Palace, Bulevardul Eroilor 27, Brașov 500030, Romania</strong>.</p>

<h2>Participant Identification</h2>

<table>
<tr><td width="40%"><strong>Full Name:</strong></td><td width="60%">' . $full_name . '</td></tr>
<tr><td><strong>Passport Number:</strong></td><td>' . $passport_number . '</td></tr>
<tr><td><strong>Nationality:</strong></td><td>' . $nationality . '</td></tr>
<tr><td><strong>Place of Birth:</strong></td><td>' . $place_of_birth . '</td></tr>
<tr><td><strong>Date of Birth:</strong></td><td>' . $formatted_date_of_birth . '</td></tr>
<tr><td><strong>Passport Issue Date:</strong></td><td>' . $formatted_passport_issue_date . '</td></tr>
<tr><td><strong>Passport Expiry Date:</strong></td><td>' . $formatted_passport_expiry_date . '</td></tr>
<tr><td><strong>Accommodation:</strong></td><td>Hotel Aro Palace, Brașov, Romania</td></tr>
<tr><td><strong>Stay Period:</strong></td><td>' . $formatted_arrival_date . ' – ' . $formatted_departure_date . '</td></tr>
</table>

<h2>About Ladies\' Circle International</h2>

<p>Ladies\' Circle International (LCI) is a dynamic network founded in 1936 in the United Kingdom, today present in over 40 countries. It promotes international friendship, service to communities, leadership development, and global understanding among women aged 18 to 45. www.ladiescircleinternational.org</p>

<h2>About the Ladies\' Circle International AGM 2025</h2>

<ul>
<li>21 August 2025: Opening Ceremony, Parade, Welcome Party</li>
<li>22 August 2025: AGM Conference, Lunch, Bars & Pubs Parties (Old Town Brașov)</li>
<li>23 August 2025: Gala Dinner & Open Bar (Kronwell Ballroom)</li>
<li>24 August 2025: Farewell</li>
</ul>

<h2>Importance of Attendance</h2>

<p><strong>' . $full_name . '</strong> is attending as a registered participant to experience international friendship, cultural exchange, personal development, and to support his national association in the event\'s celebrations and discussions.</p>

<h2>Guarantees and Legal Compliance</h2>

<p>The Organizing Committee guarantees that <strong>' . $full_name . '</strong> will comply fully with Romanian immigration regulations, participate exclusively in AGM 2025 activities, and depart Romania as scheduled after the event.</p>

<p><strong>Disclaimer:</strong> This invitation does not guarantee visa issuance. Visa applications are subject to Romanian law and consular authority decisions.</p>

<h2>Contact Information</h2>

<p><strong>Cristina Lengyel</strong> - Event Convenor<br>Phone: +40 722 410 579<br>Email: <EMAIL></p>

<h2>Event Verification</h2>
<p>For further verification, please scan the QR code below to visit the official AGM 2025 event page:</p>
<div style="text-align: center; margin: 10px 0;">
  <img src="' . $qr_code_url . '" alt="AGM 2025 Event QR Code" width="100">
</div>


<div class="footer">
 LCI 2025 BRASOV SRL / CUI: 50683317 / J2024030241002 Str. Poarta Schei 33 Ap. 1, ROMANIA

</div>

<div class="page-break"></div>

<!-- Romanian Page -->

<div class="logo">
<img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2025/04/26142759/LCI_Pack.png" alt="LCI AGM 2025" width="120">
</div>

<h1>Scrisoare de invitație pentru aplicare viză</h1>

<p><strong>Data:</strong> ' . $current_date . '</p>

<p>Către cine este interesat,</p>

<p>În numele Comitetului de Organizare al Ladies\' Circle International Annual General Meeting 2025 (AGM 2025), avem plăcerea de a invita în mod oficial pe <strong>' . $full_name . '</strong> să participe ca <strong>Participant</strong>.</p>

<p>AGM 2025 va avea loc în Brașov, România, între <strong>21 august 2025 și 24 august 2025</strong>, la prestigiosul <strong>Hotel Aro Palace, Bulevardul Eroilor 27, Brașov 500030, România</strong>.</p>

<h2>Date de identificare participant</h2>

<table>
<tr><td width="40%"><strong>Nume complet:</strong></td><td width="60%">' . $full_name . '</td></tr>
<tr><td><strong>Număr pașaport:</strong></td><td>' . $passport_number . '</td></tr>
<tr><td><strong>Naționalitate:</strong></td><td>' . $nationality . '</td></tr>
<tr><td><strong>Locul nașterii:</strong></td><td>' . $place_of_birth . '</td></tr>
<tr><td><strong>Data nașterii:</strong></td><td>' . $formatted_date_of_birth . '</td></tr>
<tr><td><strong>Data emiterii pașaportului:</strong></td><td>' . $formatted_passport_issue_date . '</td></tr>
<tr><td><strong>Data expirării pașaportului:</strong></td><td>' . $formatted_passport_expiry_date . '</td></tr>
<tr><td><strong>Cazare:</strong></td><td>Hotel Aro Palace, Brașov, România</td></tr>
<tr><td><strong>Perioada sejurului:</strong></td><td>' . $formatted_arrival_date . ' – ' . $formatted_departure_date . '</td></tr>
</table>

<h2>Despre Ladies\' Circle International</h2>

<p>Ladies\' Circle International (LCI) este o rețea globală dinamică, fondată în 1936 în Regatul Unit, prezentă astăzi în peste 40 de țări. Organizația promovează prietenia internațională, dezvoltarea personală, serviciul comunitar și înțelegerea globală între femeile cu vârste între 18 și 45 de ani. www.ladiescircleinternational.org</p>

<h2>Despre AGM 2025</h2>

<ul>
<li>21 August 2025: Ceremonia de deschidere, Paradă, Petrecere de bun venit</li>
<li>22 August 2025: Conferința AGM, Prânz, Petreceri în barurile și pub-urile din Centrul Vechi Brașov</li>
<li>23 August 2025: Cină de Gală și Open Bar (Kronwell Ballroom)</li>
<li>24 August 2025: Rămas bun</li>
</ul>

<h2>Importanța participării</h2>

<p><strong>' . $full_name . '</strong> va participa ca participant înregistrat pentru a experimenta prietenia internațională, schimbul cultural, dezvoltarea personală și pentru a sprijini asociatia natioanla pe care o reprezinta în cadrul evenimentului.</p>

<h2>Garanții și conformitate legală</h2>

<p>Comitetul de Organizare garantează că <strong>' . $full_name . '</strong> va respecta integral reglementările de imigrare ale României, va participa exclusiv la activitățile AGM 2025 și va părăsi România conform programului stabilit.</p>

<p><strong>Notă:</strong> Această invitație nu garantează automat acordarea vizei. Cererile de viză sunt procesate conform legislației române și deciziilor autorităților consulare.</p>

<h2>Date de contact</h2>

<p><strong>Cristina Lengyel</strong> - Coordonator Eveniment<br>Telefon: +40 722 410 579<br>Email: <EMAIL></p>

<h2>Verificare eveniment</h2>
<p>Pentru verificare suplimentară, vă rugăm să scanați codul QR de mai jos pentru a vizita pagina oficială a evenimentului AGM 2025:</p>
<div style="text-align: center; margin: 10px 0;">
  <img src="' . $qr_code_url . '" alt="Cod QR Eveniment AGM 2025" width="100">
</div>


<div class="footer">
 LCI 2025 BRASOV SRL / CUI: 50683317 / J2024030241002 Str. Poarta Schei 33 Ap. 1, ROMANIA</div>

</body>
</html>';

    // Generate PDF
    if ($use_tcpdf) {
        // Using TCPDF
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // Set document information
        $pdf->SetCreator('LCI 2025 AGM');
        $pdf->SetAuthor('Ladies Circle International');
        $pdf->SetTitle('Official Invitation Letter - Participant');
        $pdf->SetSubject('Visa Invitation Letter');

        // Remove header/footer
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // Set margins
        $pdf->SetMargins(15, 15, 15);

        // Add a page
        $pdf->AddPage();

        // Output HTML content
        $pdf->writeHTML($html, true, false, true, false, '');

        // Generate unique filename
        $filename = 'invitation_participant_' . sanitize_title($full_name) . '_' . time() . '.pdf';
        $upload_dir = wp_upload_dir();
        $pdf_path = $upload_dir['basedir'] . '/invitation-letters/' . $filename;
        $pdf_url = $upload_dir['baseurl'] . '/invitation-letters/' . $filename;

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir['basedir'] . '/invitation-letters/')) {
            mkdir($upload_dir['basedir'] . '/invitation-letters/', 0755, true);
        }

        // Save PDF
        $pdf->Output($pdf_path, 'F');
    } else {
        // Using mPDF
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 15,
            'margin_bottom' => 15
        ]);

        // Set document information
        $mpdf->SetCreator('LCI 2025 AGM');
        $mpdf->SetAuthor('Ladies Circle International');
        $mpdf->SetTitle('Official Invitation Letter - Participant');
        $mpdf->SetSubject('Visa Invitation Letter');

        // Write HTML content
        $mpdf->WriteHTML($html);

        // Generate unique filename
        $filename = 'invitation_participant_' . sanitize_title($full_name) . '_' . time() . '.pdf';
        $upload_dir = wp_upload_dir();
        $pdf_path = $upload_dir['basedir'] . '/invitation-letters/' . $filename;
        $pdf_url = $upload_dir['baseurl'] . '/invitation-letters/' . $filename;

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir['basedir'] . '/invitation-letters/')) {
            mkdir($upload_dir['basedir'] . '/invitation-letters/', 0755, true);
        }

        // Save PDF
        $mpdf->Output($pdf_path, \Mpdf\Output\Destination::FILE);
    }

    // Store invitation data in database for verification
    global $wpdb;
    $table_name = $wpdb->prefix . 'lci_invitation_letters';

    // Insert data
    $wpdb->insert(
        $table_name,
        array(
            'unique_id' => $unique_id,
            'user_id' => get_current_user_id(),
            'full_name' => $full_name,
            'passport_number' => $passport_number,
            'nationality' => $nationality,
            'user_type' => 'participant',
            'pdf_url' => $pdf_url
        )
    );

    return $pdf_url;
    } catch (Exception $e) {
        error_log('Participant PDF Generation Error: ' . $e->getMessage());

        // Create a simple HTML file as fallback
        $filename = 'invitation_participant_' . sanitize_title($full_name) . '_' . time() . '.html';
        $upload_dir = wp_upload_dir();
        $html_path = $upload_dir['basedir'] . '/invitation-letters/' . $filename;
        $html_url = $upload_dir['baseurl'] . '/invitation-letters/' . $filename;

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir['basedir'] . '/invitation-letters/')) {
            mkdir($upload_dir['basedir'] . '/invitation-letters/', 0755, true);
        }

        // Save HTML file
        file_put_contents($html_path, $html);

        return $html_url;
    }
}
