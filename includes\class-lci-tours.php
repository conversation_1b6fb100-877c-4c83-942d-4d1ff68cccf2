<?php
/**
 * LCI 2025 Dashboard Tours Handler
 *
 * Manages tour-related operations for the LCI 2025 Dashboard plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class LCI_Tours {
    // Product IDs for different tours
    const MAIN_PRETOUR_ID = 743;
    const LEGENDS_WILDLIFE_ID = 744;
    const ROYAL_ELEGANCE_ID = 745;
    const BRASOV_HIGHLIGHTS_ID = 746;

    /**
     * Initialize the tours handler
     */
    public static function init() {
        // Register activation hook for table creation
        register_activation_hook(LCI2025_PLUGIN_FILE, [__CLASS__, 'create_tables']);

        // Add AJAX handlers
        add_action('wp_ajax_lci_sync_tour_participants', [__CLASS__, 'ajax_sync_tour_participants']);
    }

    /**
     * Create the necessary database tables for tours
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Main Pretour table
        $table_name = $wpdb->prefix . 'lci2025_tour_main_pretour';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            participant_id BIGINT(20) UNSIGNED NOT NULL,
            unique_reg_id VARCHAR(12) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(50),
            country VARCHAR(100),
            order_date DATETIME,
            payment_status VARCHAR(50) NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY unique_reg_id (unique_reg_id),
            KEY participant_id (participant_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Legends & Wildlife table
        $table_name = $wpdb->prefix . 'lci2025_tour_legends_wildlife';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            participant_id BIGINT(20) UNSIGNED NOT NULL,
            unique_reg_id VARCHAR(12) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(50),
            country VARCHAR(100),
            order_date DATETIME,
            payment_status VARCHAR(50) NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY unique_reg_id (unique_reg_id),
            KEY participant_id (participant_id)
        ) $charset_collate;";

        dbDelta($sql);

        // Royal Elegance table
        $table_name = $wpdb->prefix . 'lci2025_tour_royal_elegance';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            participant_id BIGINT(20) UNSIGNED NOT NULL,
            unique_reg_id VARCHAR(12) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(50),
            country VARCHAR(100),
            order_date DATETIME,
            payment_status VARCHAR(50) NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY unique_reg_id (unique_reg_id),
            KEY participant_id (participant_id)
        ) $charset_collate;";

        dbDelta($sql);

        // Brasov Highlights table
        $table_name = $wpdb->prefix . 'lci2025_tour_brasov_highlights';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            participant_id BIGINT(20) UNSIGNED NOT NULL,
            unique_reg_id VARCHAR(12) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(50),
            country VARCHAR(100),
            order_date DATETIME,
            payment_status VARCHAR(50) NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY unique_reg_id (unique_reg_id),
            KEY participant_id (participant_id)
        ) $charset_collate;";

        dbDelta($sql);
    }

    /**
     * Check if tour tables exist and get WooCommerce data availability
     *
     * @return array Associative array of tour tables and their status
     */
    public static function check_tour_tables() {
        global $wpdb;

        $tables = [
            'main_pretour' => $wpdb->prefix . 'lci2025_tour_main_pretour',
            'legends_wildlife' => $wpdb->prefix . 'lci2025_tour_legends_wildlife',
            'royal_elegance' => $wpdb->prefix . 'lci2025_tour_royal_elegance',
            'brasov_highlights' => $wpdb->prefix . 'lci2025_tour_brasov_highlights'
        ];

        $result = [];

        foreach ($tables as $key => $table) {
            // Check if table exists
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") === $table;
            $count = 0;

            if ($exists) {
                $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
            }

            // Get product ID for this tour
            $product_id = self::get_product_id_by_tour_type($key);

            // Check if WooCommerce data is available
            $wc_data_available = false;
            $wc_data_count = 0;

            if ($product_id) {
                // Get orders with this product
                $orders = wc_get_orders([
                    'limit' => -1,
                    'status' => ['processing', 'completed', 'on-hold'],
                    'return' => 'ids',
                ]);

                foreach ($orders as $order_id) {
                    $order = wc_get_order($order_id);

                    // Check if order contains the tour product
                    foreach ($order->get_items() as $item) {
                        if ($item->get_product_id() == $product_id) {
                            $wc_data_available = true;
                            $wc_data_count++;
                            break;
                        }
                    }
                }
            }

            $result[$key] = [
                'exists' => $exists,
                'count' => $count,
                'wc_data_available' => $wc_data_available,
                'wc_data_count' => $wc_data_count,
                'needs_sync' => $exists && ($count === 0) && $wc_data_available
            ];
        }

        return $result;
    }

    /**
     * Get tour participants
     *
     * @param string $tour_type Tour type (main_pretour, legends_wildlife, royal_elegance, brasov_highlights)
     * @param bool $force_woocommerce Whether to force getting data from WooCommerce
     * @return array Array with participants data and source information
     */
    public static function get_tour_participants($tour_type, $force_woocommerce = false) {
        global $wpdb;

        // Validate tour type
        $valid_tour_types = ['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'];
        if (!in_array($tour_type, $valid_tour_types)) {
            return [
                'participants' => [],
                'source' => 'none',
                'table_exists' => false,
                'table_has_data' => false
            ];
        }

        $table_name = $wpdb->prefix . 'lci2025_tour_' . $tour_type;
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        $table_has_data = false;

        if ($table_exists) {
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            $table_has_data = $count > 0;
        }

        // Determine data source and get participants
        if ($force_woocommerce || !$table_exists || !$table_has_data) {
            // Get data from WooCommerce
            $participants = self::get_tour_participants_from_woocommerce($tour_type);
            $source = 'woocommerce';
        } else {
            // Get data from tour table
            $participants = $wpdb->get_results("SELECT * FROM $table_name ORDER BY order_date DESC");
            $source = 'database';
        }

        return [
            'participants' => $participants,
            'source' => $source,
            'table_exists' => $table_exists,
            'table_has_data' => $table_has_data
        ];
    }

    /**
     * Get tour participants from WooCommerce orders
     *
     * @param string $tour_type Tour type (main_pretour, legends_wildlife, royal_elegance, brasov_highlights)
     * @return array Array of tour participants
     */
    public static function get_tour_participants_from_woocommerce($tour_type) {
        global $wpdb;
        $participants_table = $wpdb->prefix . 'lci2025_participants';

        // Get product ID based on tour type
        $product_id = self::get_product_id_by_tour_type($tour_type);

        if (!$product_id) {
            return [];
        }

        // Get all orders containing this product
        $orders = wc_get_orders([
            'limit' => -1,
            'status' => ['processing', 'completed', 'on-hold'],
            'return' => 'ids',
            'orderby' => 'date',
            'order' => 'DESC',
        ]);

        $participants = [];

        foreach ($orders as $order_id) {
            $order = wc_get_order($order_id);

            // Check if order contains the tour product
            $has_tour = false;
            foreach ($order->get_items() as $item) {
                if ($item->get_product_id() == $product_id) {
                    $has_tour = true;
                    break;
                }
            }

            if ($has_tour) {
                // Get participant data from order
                $country_code = $order->get_billing_country();
                $country_name = LCI_Participant::get_country_name($country_code);
                $email = $order->get_billing_email();

                // Check if this participant exists in the main participants table
                $participant_data = null;
                if ($email) {
                    $participant_data = $wpdb->get_row($wpdb->prepare(
                        "SELECT id, unique_reg_id, original_reg_id FROM $participants_table WHERE email = %s LIMIT 1",
                        $email
                    ));
                }

                $participant = [
                    'id' => 0, // Temporary ID
                    'participant_id' => $participant_data ? $participant_data->id : 0,
                    'unique_reg_id' => $participant_data ? $participant_data->unique_reg_id : '',
                    'original_reg_id' => $participant_data ? $participant_data->original_reg_id : '',
                    'order_id' => $order_id,
                    'first_name' => $order->get_billing_first_name(),
                    'last_name' => $order->get_billing_last_name(),
                    'email' => $email,
                    'phone' => $order->get_billing_phone(),
                    'country' => $country_name,
                    'order_date' => $order->get_date_created() ? $order->get_date_created()->date('Y-m-d H:i:s') : '',
                    'payment_status' => $order->get_status(),
                    'created_at' => '',
                    'updated_at' => '',
                    'from_woocommerce' => true
                ];

                $participants[] = (object) $participant;
            }
        }

        // Sort participants by order_date in descending order
        usort($participants, function($a, $b) {
            $date_a = strtotime($a->order_date);
            $date_b = strtotime($b->order_date);

            if ($date_a == $date_b) {
                return 0;
            }

            return ($date_a > $date_b) ? -1 : 1; // Descending order
        });

        return $participants;
    }

    /**
     * Clear tour table
     *
     * @param string $tour_type Tour type (main_pretour, legends_wildlife, royal_elegance, brasov_highlights)
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public static function clear_tour_table($tour_type) {
        global $wpdb;

        // Validate tour type
        $valid_tour_types = ['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'];
        if (!in_array($tour_type, $valid_tour_types)) {
            return new WP_Error('invalid_tour_type', 'Invalid tour type');
        }

        $table_name = $wpdb->prefix . 'lci2025_tour_' . $tour_type;

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            // Table doesn't exist, create it
            return new WP_Error('table_not_exists', 'Tour table does not exist');
        }

        // Clear the table
        $result = $wpdb->query("TRUNCATE TABLE $table_name");

        if ($result === false) {
            return new WP_Error('db_error', 'Failed to clear tour table: ' . $wpdb->last_error);
        }

        return true;
    }

    /**
     * Get product ID by tour type
     *
     * @param string $tour_type Tour type (main_pretour, legends_wildlife, royal_elegance, brasov_highlights)
     * @return int|false Product ID or false if invalid tour type
     */
    public static function get_product_id_by_tour_type($tour_type) {
        switch ($tour_type) {
            case 'main_pretour':
                return self::MAIN_PRETOUR_ID;
            case 'legends_wildlife':
                return self::LEGENDS_WILDLIFE_ID;
            case 'royal_elegance':
                return self::ROYAL_ELEGANCE_ID;
            case 'brasov_highlights':
                return self::BRASOV_HIGHLIGHTS_ID;
            default:
                return false;
        }
    }

    /**
     * Sync tour participants
     *
     * @param int $product_id Product ID
     * @param string $tour_type Tour type (main_pretour, legends_wildlife, royal_elegance, brasov_highlights)
     * @return array Result with success_count, error_count, and total_count
     */
    public static function sync_tour_participants($product_id, $tour_type) {
        global $wpdb;

        // Validate tour type
        $valid_tour_types = ['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'];
        if (!in_array($tour_type, $valid_tour_types)) {
            return [
                'success_count' => 0,
                'error_count' => 0,
                'total_count' => 0,
                'errors' => ['Invalid tour type']
            ];
        }

        $table_name = $wpdb->prefix . 'lci2025_tour_' . $tour_type;
        $participants_table = $wpdb->prefix . 'lci2025_participants';

        // Check if tables exist
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            // Create tour tables
            self::create_tables();
        }

        // Get WooCommerce orders with this tour
        $wc_participants = self::get_tour_participants_from_woocommerce($tour_type);

        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'total_count' => count($wc_participants),
            'errors' => [],
            'synced_from_main' => 0,
            'synced_from_wc' => 0
        ];

        // Clear the tour table
        $wpdb->query("TRUNCATE TABLE $table_name");

        foreach ($wc_participants as $participant) {
            // Check if we have participant data from the main table
            if (!empty($participant->email) && $participant->participant_id > 0) {
                // We have a match in the main participants table
                $result['synced_from_main']++;
            } else {
                // No match in the main participants table, using WooCommerce data only
                $result['synced_from_wc']++;
            }

            // Insert participant into tour table
            $insert_data = [
                'participant_id' => $participant->participant_id,
                'unique_reg_id' => $participant->unique_reg_id,
                'first_name' => $participant->first_name,
                'last_name' => $participant->last_name,
                'email' => $participant->email,
                'phone' => $participant->phone,
                'country' => $participant->country,
                'order_date' => $participant->order_date,
                'payment_status' => $participant->payment_status,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ];

            $insert_result = $wpdb->insert($table_name, $insert_data);

            if ($insert_result) {
                $result['success_count']++;
            } else {
                $result['error_count']++;
                $result['errors'][] = "Failed to insert participant {$participant->email}: {$wpdb->last_error}";
            }
        }

        return $result;
    }

    /**
     * Get missing participants count
     *
     * @param int $product_id Product ID
     * @param string $tour_type Tour type (main_pretour, legends_wildlife, royal_elegance, brasov_highlights)
     * @return int Number of participants missing from tour table
     */
    public static function get_missing_participants_count($product_id, $tour_type) {
        global $wpdb;

        // Validate tour type
        $valid_tour_types = ['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'];
        if (!in_array($tour_type, $valid_tour_types)) {
            return 0;
        }

        $table_name = $wpdb->prefix . 'lci2025_tour_' . $tour_type;
        $participants_table = $wpdb->prefix . 'lci2025_participants';

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            // Table doesn't exist, count all participants with this tour
            return $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM $participants_table WHERE tours LIKE %s",
                    '%' . $wpdb->esc_like('"product_id":' . $product_id) . '%'
                )
            );
        }

        // Count participants in main table with this tour
        $main_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $participants_table WHERE tours LIKE %s",
                '%' . $wpdb->esc_like('"product_id":' . $product_id) . '%'
            )
        );

        // Count participants in tour table
        $tour_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

        // Return difference (missing participants)
        return max(0, $main_count - $tour_count);
    }

    /**
     * AJAX handler for syncing tour participants
     */
    public static function ajax_sync_tour_participants() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Check required parameters
        if (!isset($_POST['tour_type']) || !isset($_POST['product_id'])) {
            wp_send_json_error(['message' => 'Missing required parameters.']);
        }

        $tour_type = sanitize_text_field($_POST['tour_type']);
        $product_id = intval($_POST['product_id']);

        // Validate tour type
        $valid_tour_types = ['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'];
        if (!in_array($tour_type, $valid_tour_types)) {
            wp_send_json_error(['message' => 'Invalid tour type.']);
        }

        // Sync tour participants
        $result = self::sync_tour_participants($product_id, $tour_type);

        wp_send_json_success([
            'message' => sprintf(
                'Sync completed. %d participants synced successfully, %d errors.',
                $result['success_count'],
                $result['error_count']
            ),
            'success_count' => $result['success_count'],
            'error_count' => $result['error_count'],
            'total_count' => $result['total_count'],
            'errors' => $result['errors']
        ]);
    }
}
