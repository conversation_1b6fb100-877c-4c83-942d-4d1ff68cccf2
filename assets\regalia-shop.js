/**
 * LCI 2025 Regalia Shop JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    const miniCartCount = document.querySelector('.mini-cart-count');
    const miniCartTotal = document.querySelector('.mini-cart-total');

    // Add to cart functionality
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const originalText = this.innerHTML;

            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Adding...';
            this.disabled = true;

            // Add to cart via AJAX
            const formData = new FormData();
            formData.append('action', 'lci-dashboard-add-to-cart');
            formData.append('product_id', productId);
            formData.append('quantity', 1);
            formData.append('security', lci_ajax.nonce);

            fetch(lci_ajax.ajax_url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update mini cart
                    if (data.data && data.data.cart_count !== undefined) {
                        // Use the standard mini-cart update mechanism
                        updateMiniCart(data.data.cart_count, data.data.cart_total);
                    }
                } else {
                    // Show error
                    showLciModal(
                        'Error',
                        data.data.message || 'Could not add product to cart',
                        'error'
                    );
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                showLciModal(
                    'Error',
                    'Could not add product to cart. Please try again.',
                    'error'
                );
            })
            .finally(() => {
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;
            });
        });
    });

    // Debounce function to prevent multiple rapid calls
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }

    // Function to update mini cart
    const updateMiniCart = debounce(function(count, total) {
        if (miniCartCount) {
            miniCartCount.textContent = count;

            if (count > 0) {
                miniCartCount.classList.remove('d-none');
            } else {
                miniCartCount.classList.add('d-none');
            }
        }

        if (miniCartTotal && total) {
            miniCartTotal.innerHTML = total;

            if (count > 0) {
                miniCartTotal.classList.remove('d-none');
            } else {
                miniCartTotal.classList.add('d-none');
            }
        }

        // Dispatch event to update Alpine.js mini cart component
        // Use a flag to prevent multiple updates
        if (!window.isUpdatingMiniCart) {
            window.isUpdatingMiniCart = true;

            window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
                detail: {
                    cart_count: count,
                    cart_total: total
                }
            }));

            // Reset the flag after a delay
            setTimeout(function() {
                window.isUpdatingMiniCart = false;
            }, 500);
        }
    }, 300);

    // Product image hover effect
    const productCards = document.querySelectorAll('.product-card');

    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const image = this.querySelector('.product-image');
            if (image) {
                image.style.transform = 'scale(1.05)';
            }
        });

        card.addEventListener('mouseleave', function() {
            const image = this.querySelector('.product-image');
            if (image) {
                image.style.transform = 'scale(1)';
            }
        });
    });
});
