/**
 * Fundraising message refresh functionality
 */
(function() {
    'use strict';

    // Function to check cart total and hide/show fundraising message
    function refreshFundraisingMessage() {
        // Get the fundraising message element
        const fundraisingMessage = document.getElementById('lci-fundraising-message');
        if (!fundraisingMessage) {
            return;
        }

        // Get the mini cart container
        const miniCartContainer = document.getElementById('mini-cart-container');
        if (!miniCartContainer || !miniCartContainer.__x) {
            return;
        }

        try {
            // Get the cart data from Alpine.js
            const data = miniCartContainer.__x.getUnobservedData();
            const count = data.count || 0;

            // Check if cart is empty
            if (count === 0) {
                // Cart is empty, hide fundraising message
                fundraisingMessage.style.display = 'none';
            } else {
                // Cart has items, show fundraising message
                fundraisingMessage.style.display = '';
            }
        } catch (error) {
            console.error('Error checking cart data for fundraising message:', error);
        }
    }

    // Make the function globally available
    window.refreshFundraisingMessage = refreshFundraisingMessage;

    // Listen for cart updates
    window.addEventListener('lci:cartUpdated', refreshFundraisingMessage);

    // Listen for mini cart initialization
    document.addEventListener('mini-cart:initialized', refreshFundraisingMessage);

    // Initial check
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(refreshFundraisingMessage, 500);
    });
})();
