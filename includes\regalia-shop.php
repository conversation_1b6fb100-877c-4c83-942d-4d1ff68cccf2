<?php
/**
 * LCI 2025 Dashboard Regalia Shop Functions
 *
 * Handles regalia shop functionality
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

/**
 * Clear contributor count transient when a new order is placed
 */
add_action('woocommerce_new_order', 'lci_clear_contributor_count_transient');
add_action('woocommerce_order_status_changed', 'lci_clear_contributor_count_transient');

function lci_clear_contributor_count_transient() {
    delete_transient('lci_regalia_contributor_count');
}

/**
 * Add to cart AJAX handler
 */
add_action('wp_ajax_lci-dashboard-add-to-cart', 'lci_dashboard_add_to_cart_handler');
add_action('wp_ajax_nopriv_lci-dashboard-add-to-cart', 'lci_dashboard_add_to_cart_handler');

function lci_dashboard_add_to_cart_handler() {
    // Verify nonce
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci-dashboard-add-to-cart-nonce')) {
        wp_send_json_error(['message' => 'Security check failed.']);
        return;
    }

    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        wp_send_json_error(['message' => 'WooCommerce is not active.']);
        return;
    }

    // Get product ID and quantity
    $product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;
    $quantity = isset($_POST['quantity']) ? absint($_POST['quantity']) : 1;

    // Get number of nights (for accommodation)
    $nights = isset($_POST['nights']) ? absint($_POST['nights']) : 1;

    // For simple products, check if already in cart
    $is_simple_product = true; // Assume simple product initially

    // Check if this is a variable product by looking for variation attributes in POST data
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'attribute_') === 0) {
            $is_simple_product = false; // This is a variable product
            break;
        }
    }

    // Only check for duplicates with simple products
    if ($is_simple_product) {
        $cart_item_key_to_update = null;
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            if ($cart_item['product_id'] == $product_id && $cart_item['variation_id'] == 0) {
                $cart_item_key_to_update = $cart_item_key;
                break;
            }
        }

        // If simple product is already in cart, update its quantity
        if ($cart_item_key_to_update) {
            // Get current quantity
            $current_quantity = WC()->cart->get_cart_item($cart_item_key_to_update)['quantity'];

            // Set new quantity (current + new)
            WC()->cart->set_quantity($cart_item_key_to_update, $current_quantity + $quantity);

            // Get updated cart info
            $cart_count = WC()->cart->get_cart_contents_count();
            $cart_total = WC()->cart->get_cart_total();

            wp_send_json_success([
                'message' => 'Product quantity updated in cart.',
                'cart_count' => $cart_count,
                'cart_total' => $cart_total,
            ]);
            return;
        }
    }

    // Validate product ID
    if (!$product_id) {
        wp_send_json_error(['message' => 'Invalid product ID.']);
        return;
    }

    // Check if product exists
    $product = wc_get_product($product_id);
    if (!$product) {
        wp_send_json_error(['message' => 'Product not found.']);
        return;
    }

    // Check if product is in stock
    if (!$product->is_in_stock()) {
        wp_send_json_error(['message' => 'Product is out of stock.']);
        return;
    }

    // Check if it's a variable product
    $variation_id = 0;
    $variation_attributes = array();

    if ($product->is_type('variable')) {
        // Extract variation attributes from POST data
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'attribute_') === 0) {
                $variation_attributes[$key] = $value;
            }
        }

        // Find matching variation ID
        if (!empty($variation_attributes)) {
            $data_store = WC_Data_Store::load('product');
            $variation_id = $data_store->find_matching_product_variation($product, $variation_attributes);

            if (!$variation_id) {
                wp_send_json_error(['message' => 'No matching variation found.']);
                return;
            }

            // Check if variation is in stock
            $variation = wc_get_product($variation_id);
            if (!$variation || !$variation->is_in_stock()) {
                wp_send_json_error(['message' => 'Selected variation is out of stock.']);
                return;
            }
        }
    }

    // Track success of adding to cart
    $success = false;
    $cart_item_keys = [];

    // For accommodation products, add multiple times based on nights
    if ($nights > 1) {
        // Add product to cart multiple times based on number of nights
        for ($i = 0; $i < $nights; $i++) {
            if ($variation_id > 0) {
                // Add variable product to cart
                $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $variation_attributes);
            } else {
                // Add simple product to cart
                $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $variation_attributes);
            }

            if ($cart_item_key) {
                $cart_item_keys[] = $cart_item_key;
                $success = true;
            }
        }

        // Set the last cart item key as the main one for response
        if (!empty($cart_item_keys)) {
            $cart_item_key = end($cart_item_keys);
        } else {
            $cart_item_key = false;
        }

        // Debug log
        error_log('Adding accommodation for ' . $nights . ' nights: product_id=' . $product_id .
                  ', variation_id=' . $variation_id .
                  ', results=' . print_r($cart_item_keys, true));
    } else {
        // Standard single product add to cart
        if ($variation_id > 0) {
            // Add to cart normally without checking for duplicates
            $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $variation_attributes);
        } else {
            // For simple products, check if already in cart and update quantity
            $cart_item_key_to_update = null;
            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                // For simple products, check only product ID
                if ($cart_item['product_id'] == $product_id && $cart_item['variation_id'] == 0) {
                    $cart_item_key_to_update = $cart_item_key;
                    break;
                }
            }

            // If simple product is already in cart, update its quantity
            if ($cart_item_key_to_update) {
                // Get current quantity
                $current_quantity = WC()->cart->get_cart_item($cart_item_key_to_update)['quantity'];

                // Set new quantity (current + new)
                WC()->cart->set_quantity($cart_item_key_to_update, $current_quantity + $quantity);

                // Set success flag
                $cart_item_key = $cart_item_key_to_update;
            } else {
                // Add to cart normally
                $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $variation_attributes);
            }
        }
    }

    // Get updated cart info
    $cart_count = WC()->cart->get_cart_contents_count();
    $cart_total = WC()->cart->get_cart_total();

    // Create success message based on nights
    $message = ($nights > 1)
        ? 'Product added to cart for ' . $nights . ' nights.'
        : 'Product added to cart.';

    wp_send_json_success([
        'message' => $message,
        'cart_count' => $cart_count,
        'cart_total' => $cart_total,
        'nights' => $nights,
    ]);
}

/**
 * Get regalia products
 *
 * @param int $category_id Category ID
 * @return array Array of products
 */
function lci_get_regalia_products($category_id = 22) {
    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        return [];
    }

    // Get products from category
    $args = [
        'status' => 'publish',
        'limit' => -1,
        'category' => [$category_id],
    ];

    return wc_get_products($args);
}

/**
 * Get user's regalia orders
 *
 * @param int $user_id User ID
 * @param int $category_id Category ID
 * @return array Array of orders
 */
function lci_get_user_regalia_orders($user_id, $category_id = 22) {
    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        return [];
    }

    // Get all customer orders
    $customer_orders = wc_get_orders([
        'customer' => $user_id,
        'limit' => -1,
        'status' => ['processing', 'completed', 'on-hold'],
        'orderby' => 'date',
        'order' => 'DESC',
    ]);

    $user_orders = [];

    // Filter orders to only include those with regalia products
    foreach ($customer_orders as $order) {
        $has_regalia = false;
        $regalia_items = [];

        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $product = wc_get_product($product_id);

            if ($product) {
                $categories = $product->get_category_ids();

                if (in_array($category_id, $categories)) {
                    $has_regalia = true;
                    $regalia_items[] = [
                        'id' => $product_id,
                        'name' => $item->get_name(),
                        'quantity' => $item->get_quantity(),
                        'total' => $item->get_total(),
                        'image' => wp_get_attachment_url($product->get_image_id()),
                    ];
                }
            }
        }

        if ($has_regalia) {
            $user_orders[] = [
                'id' => $order->get_id(),
                'date' => $order->get_date_created()->date('Y-m-d H:i:s'),
                'status' => $order->get_status(),
                'total' => $order->get_total(),
                'items' => $regalia_items,
            ];
        }
    }

    return $user_orders;
}

/**
 * Get status color
 *
 * @param string $status Order status
 * @return string CSS color class
 */
function lci_get_order_status_color($status) {
    switch ($status) {
        case 'completed':
            return 'success';
        case 'processing':
            return 'info';
        case 'on-hold':
            return 'warning';
        case 'cancelled':
            return 'danger';
        case 'refunded':
            return 'secondary';
        default:
            return 'primary';
    }
}
