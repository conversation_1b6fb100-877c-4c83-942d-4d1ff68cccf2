<?php
/**
 * Regalia Shop View
 *
 * Displays products from WooCommerce category ID 22 (Regalia)
 * Includes a mini cart and shows user's previous orders from this category
 */

// Enqueue the regalia shop CSS
wp_enqueue_style('regalia-shop-css', LCI2025_URL . 'assets/css/regalia-shop.css', array(), LCI2025_VERSION);

// Enqueue the regalia shop fix script
wp_enqueue_script('regalia-shop-fix', LCI2025_URL . 'assets/js/regalia-shop-fix.js', array('jquery', 'alpine-js'), LCI2025_VERSION, true);

// Enqueue the Alpine.js fix script
wp_enqueue_script('fix-alpine', LCI2025_URL . 'assets/js/fix-alpine.js', array('jquery', 'alpine-js'), LCI2025_VERSION, true);

// Enqueue the quantity buttons fix script
wp_enqueue_script('fix-quantity-buttons', LCI2025_URL . 'assets/js/fix-quantity-buttons.js', array('jquery'), LCI2025_VERSION, true);

// Enqueue the quantity debug fix script
wp_enqueue_script('fix-quantity-debug', LCI2025_URL . 'assets/js/fix-quantity-debug.js', array('jquery'), LCI2025_VERSION, true);

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_email = $current_user->user_email;

// Category ID for Regalia products
// Allow overriding the category ID via URL parameter
$regalia_category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 22;

// Get user's orders containing products from the regalia category
$user_orders = [];
$regalia_products = [];

// Check if WooCommerce is active
if (class_exists('WooCommerce')) {
    // Get all customer orders
    $customer_orders = wc_get_orders([
        'customer' => $user_id,
        'limit' => -1,
        'status' => ['processing', 'completed', 'on-hold'],
        'orderby' => 'date',
        'order' => 'DESC',
    ]);

    // Get all products from the regalia category
    // Debug information
    echo '<!-- Debug: Attempting to get products from category ID: ' . $regalia_category_id . ' -->';

    // Try multiple methods to get products
    $regalia_products = array();

    // Method 1: Using WP_Query with tax_query
    $args = array(
        'post_type'      => 'product',
        'posts_per_page' => -1,
        'post_status'    => 'publish',
        'tax_query'      => array(
            array(
                'taxonomy' => 'product_cat',
                'field'    => 'term_id',
                'terms'    => $regalia_category_id,
            ),
        ),
    );

    $products_query = new WP_Query($args);

    if ($products_query->have_posts()) {
        while ($products_query->have_posts()) {
            $products_query->the_post();
            $product_id = get_the_ID();
            $product = wc_get_product($product_id);
            if ($product) {
                $regalia_products[] = $product;
            }
        }
        wp_reset_postdata();
    }

    echo '<!-- Debug Method 1: Found ' . count($regalia_products) . ' products -->';

    // If no products found, try Method 2: Using wc_get_products
    if (empty($regalia_products)) {
        $args = array(
            'status' => 'publish',
            'limit'  => -1,
            'category' => array($regalia_category_id),
        );

        $method2_products = wc_get_products($args);
        if (!empty($method2_products)) {
            $regalia_products = $method2_products;
        }

        echo '<!-- Debug Method 2: Found ' . count($regalia_products) . ' products -->';
    }

    // If still no products found, try Method 3: Using get_posts
    if (empty($regalia_products)) {
        $args = array(
            'post_type'      => 'product',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
            'tax_query'      => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'slug',
                    'terms'    => get_term_by('id', $regalia_category_id, 'product_cat') ? get_term_by('id', $regalia_category_id, 'product_cat')->slug : '',
                ),
            ),
        );

        $posts = get_posts($args);
        foreach ($posts as $post) {
            $product = wc_get_product($post->ID);
            if ($product) {
                $regalia_products[] = $product;
            }
        }

        echo '<!-- Debug Method 3: Found ' . count($regalia_products) . ' products -->';
    }

    // Method 4: Last resort - try to get all products and filter by category name
    if (empty($regalia_products)) {
        // Get all product categories
        $product_categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
        ));

        // Look for a category that might be the regalia category
        $possible_regalia_categories = array();
        foreach ($product_categories as $category) {
            if (stripos($category->name, 'regalia') !== false ||
                stripos($category->name, 'merchandise') !== false ||
                stripos($category->name, 'shop') !== false) {
                $possible_regalia_categories[] = $category->term_id;
            }
        }

        echo '<!-- Debug: Possible regalia categories found: ' . implode(', ', $possible_regalia_categories) . ' -->';

        // Try each possible category
        foreach ($possible_regalia_categories as $cat_id) {
            $args = array(
                'post_type'      => 'product',
                'posts_per_page' => -1,
                'post_status'    => 'publish',
                'tax_query'      => array(
                    array(
                        'taxonomy' => 'product_cat',
                        'field'    => 'term_id',
                        'terms'    => $cat_id,
                    ),
                ),
            );

            $products_query = new WP_Query($args);

            if ($products_query->have_posts()) {
                while ($products_query->have_posts()) {
                    $products_query->the_post();
                    $product_id = get_the_ID();
                    $product = wc_get_product($product_id);
                    if ($product) {
                        $regalia_products[] = $product;
                    }
                }
                wp_reset_postdata();
            }
        }

        echo '<!-- Debug Method 4: Found ' . count($regalia_products) . ' products -->';
    }

    // Method 5: Absolute last resort - just get some products to display
    if (empty($regalia_products)) {
        $args = array(
            'post_type'      => 'product',
            'posts_per_page' => 10,
            'post_status'    => 'publish',
        );

        $products_query = new WP_Query($args);

        if ($products_query->have_posts()) {
            while ($products_query->have_posts()) {
                $products_query->the_post();
                $product_id = get_the_ID();
                $product = wc_get_product($product_id);
                if ($product) {
                    $regalia_products[] = $product;
                }
            }
            wp_reset_postdata();
        }

        echo '<!-- Debug Method 5: Found ' . count($regalia_products) . ' products as last resort -->';
    }

    // Filter orders to only include those with regalia products
    foreach ($customer_orders as $order) {
        $has_regalia = false;
        $regalia_items = [];

        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $product = wc_get_product($product_id);

            if ($product) {
                $categories = $product->get_category_ids();

                if (in_array($regalia_category_id, $categories)) {
                    $has_regalia = true;
                    $regalia_items[] = [
                        'id' => $product_id,
                        'name' => $item->get_name(),
                        'quantity' => $item->get_quantity(),
                        'total' => $item->get_total(),
                        'image' => wp_get_attachment_url($product->get_image_id()),
                    ];
                }
            }
        }

        if ($has_regalia) {
            $user_orders[] = [
                'id' => $order->get_id(),
                'date' => $order->get_date_created()->date('Y-m-d H:i:s'),
                'status' => $order->get_status(),
                'total' => $order->get_total(),
                'items' => $regalia_items,
            ];
        }
    }
}

// Get cart contents count
$cart_count = WC()->cart ? WC()->cart->get_cart_contents_count() : 0;

// Get cart total
$cart_total = WC()->cart ? WC()->cart->get_cart_total() : 0;
?>

<div class="regalia-shop-container">
    <!-- Header with title and mini cart -->
    <div class="mb-4">
        <!-- Title - centered on mobile -->
        <div class="text-center text-md-start mb-3">
            <h2 class="text-primary mb-0 h3"><i class="fas fa-tshirt me-2"></i> LCI 2025 Regalia Shop</h2>
        </div>

        <!-- Mini Cart Button - below title on mobile -->
        <div class="d-flex justify-content-center justify-content-md-end">
            <?php echo lci_get_mini_cart_html(); ?>
        </div>
    </div>

    <?php if (isset($_GET['category_id'])): ?>
    <div class="mb-3">
        <a href="<?php echo esc_url(add_query_arg('tab', 'regalia-shop', remove_query_arg('category_id'))); ?>" class="btn btn-sm btn-outline-secondary d-inline-flex align-items-center">
            <i class="fas fa-arrow-left me-2"></i> Back to All Categories
        </a>
    </div>
    <?php endif; ?>

    <!-- Products Section -->
    <div class="mb-5">
        <?php if (empty($regalia_products)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No regalia products are currently available. Please check back later.
            </div>

            <!-- Debug information -->
            <div class="alert alert-warning mt-3">
                <h5><i class="fas fa-bug me-2"></i> Debug Information</h5>
                <p>Attempting to retrieve products from category ID: <?php echo $regalia_category_id; ?></p>

                <?php
                // Check if the category exists
                $term = get_term_by('id', $regalia_category_id, 'product_cat');
                if ($term) {
                    echo '<p>Category found: ' . esc_html($term->name) . ' (ID: ' . esc_html($term->term_id) . ')</p>';

                    // Get all product categories for comparison
                    $product_categories = get_terms(array(
                        'taxonomy' => 'product_cat',
                        'hide_empty' => false,
                    ));

                    if (!empty($product_categories) && !is_wp_error($product_categories)) {
                        echo '<p>Available product categories:</p>';
                        echo '<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-2 mb-3">';
                        foreach ($product_categories as $category) {
                            $current_url = add_query_arg(array('tab' => 'regalia-shop'), remove_query_arg('category_id'));
                            $category_url = add_query_arg(array('category_id' => $category->term_id), $current_url);

                            echo '<div class="col">';
                            echo '<a href="' . esc_url($category_url) . '" class="btn btn-outline-primary btn-sm d-block text-start overflow-hidden">';
                            echo esc_html($category->name) . ' <span class="badge bg-secondary float-end">' . esc_html($category->term_id) . '</span>';
                            echo '</a>';
                            echo '</div>';
                        }
                        echo '</div>';
                    } else {
                        echo '<p>No product categories found or error retrieving categories.</p>';
                    }
                } else {
                    echo '<p>Category with ID ' . esc_html($regalia_category_id) . ' not found. Please check the category ID.</p>';
                }
                ?>
            </div>
        <?php else: ?>


            <?php
            // Get fundraising progress data from options
            $fundraising_goal = get_option('lci_regalia_fundraising_goal', 1500); // Default €1500
            $fundraising_current = get_option('lci_regalia_fundraising_current', 0); // Default €0
            $fundraising_percentage = min(100, ($fundraising_current / $fundraising_goal) * 100);
            $participants_funded = floor($fundraising_current / $fundraising_goal);
            $additional_progress = ($fundraising_current % $fundraising_goal) / $fundraising_goal * 100;

            // Get the stored contributor count from the options
            $contributor_count = get_option('lci_regalia_contributor_count', 0);

            // Display the CTA and progress bar
            echo '<div class="regalia-fundraising-cta mb-4">';
            echo '<div class="regalia-cta-message">';
            echo '<p class="regalia-cta-headline"><i class="fas fa-heart me-2" style="color: #fab33a;"></i> Your Purchase Makes a Difference!</p>';
            echo '<p class="regalia-cta-text">All profits from Regalia sales until May will be used to offer a <strong>free event participation to a young Circler</strong>. If the funds raised exceed the cost of one participation, we will open additional spots to support even more young members!</p>';
            echo '</div>';

            echo '<div class="regalia-progress-container">';
            echo '<div class="regalia-progress-stats">';
            echo '<div class="regalia-progress-raised">€' . number_format($fundraising_current, 0, '.', ',') . ' <span>raised</span></div>';
            echo '<div class="regalia-progress-goal">€' . number_format($fundraising_goal, 0, '.', ',') . ' <span>goal per participant</span></div>';
            echo '</div>';

            echo '<div class="regalia-progress-bar-container">';
            echo '<div class="regalia-progress-bar" style="width: ' . $fundraising_percentage . '%"></div>';

            // Add milestone markers for each full participant funded
            if ($participants_funded > 0) {
                for ($i = 1; $i <= $participants_funded; $i++) {
                    $milestone_position = min(100, ($i * 100) / $participants_funded);
                    echo '<div class="regalia-milestone" style="left: ' . $milestone_position . '%"><i class="fas fa-user-graduate"></i></div>';
                }
            }

            echo '</div>';

            echo '<div class="regalia-impact-message">';

            // Debug output for admins
            if (current_user_can('manage_options')) {
                echo '<div style="background: #f8f8f8; border: 1px solid #ddd; padding: 10px; margin: 10px 0;">';
                echo '<p><strong>Debug Info:</strong></p>';
                echo '<p>Contributor Count: ' . $contributor_count . '</p>';
                echo '<p>Participants Funded: ' . $participants_funded . '</p>';
                echo '<p>Additional Progress: ' . round($additional_progress) . '%</p>';
                echo '</div>';
            }

            if ($participants_funded > 0) {
                echo '<p><strong>' . $participants_funded . ' young ' . ($participants_funded == 1 ? 'Circler' : 'Circlers') . '</strong> will attend thanks to your generosity! ' .
                     ($additional_progress > 0 ? 'We\'re ' . round($additional_progress) . '% of the way to sponsoring another!' : '') . '</p>';
            } else {
                if ($contributor_count > 0) {
                    echo '<p class="regalia-contributor-count"><i class="fas fa-users text-primary me-1"></i> Join the <strong>' . $contributor_count . ' ' . ($contributor_count == 1 ? 'Circler' : 'Circlers / Parteners') . '</strong> who ' . ($contributor_count == 1 ? 'has' : 'have') . ' already contributed and help a young Circler attend LCI 2025 AGM in Brasov!</p>';
                } else {
                    echo '<p class="regalia-contributor-count"><i class="fas fa-heart text-danger me-1"></i> Be the first to help a young Circler attend this transformative event!</p>';
                }
            }

            // Only show additional contributor info if we have funded participants
            if ($participants_funded > 0 && $contributor_count > 0) {
                echo '<p class="regalia-contributor-count"><i class="fas fa-users text-primary me-1"></i> <strong>' . $contributor_count . ' ' . ($contributor_count == 1 ? 'Circler has' : 'Circlers have') . '</strong> contributed so far!</p>';
            }
            echo '</div>';

            echo '</div>';
            echo '</div>'; // Close the main regalia-fundraising-cta div
            ?>

            <div class="regalia-products-container">
                <div class="regalia-products-grid">
                <?php foreach ($regalia_products as $product): ?>
                    <div class="regalia-product-card">
                        <div class="regalia-product-image">
                            <img src="<?php echo esc_url(wp_get_attachment_url($product->get_image_id())); ?>" alt="<?php echo esc_attr($product->get_name()); ?>">

                            <!-- Badges -->
                            <div class="regalia-badges-left">
                                <?php if ($product->is_on_sale()): ?>
                                <div class="regalia-badge regalia-badge-sale">
                                    <i class="fas fa-percentage mr-1"></i>Sale!
                                </div>
                                <?php endif; ?>
                                <?php if ($product->is_featured()): ?>
                                <div class="regalia-badge regalia-badge-featured">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="regalia-product-content">
                            <h3 class="regalia-product-title"><?php echo esc_html($product->get_name()); ?></h3>

                            <div class="regalia-product-description">
                                <?php echo wp_kses_post($product->get_description()); ?>
                            </div>

                            <?php if ($product->is_type('variable')): ?>
                            <div class="regalia-product-variants">
                                <i class="fas fa-layer-group mr-1"></i>
                                <span>
                                <?php
                                    $variations = $product->get_available_variations();
                                    $count = count($variations);
                                    echo $count . ' ' . ($count == 1 ? 'variant' : 'variants') . ' available';
                                ?>
                                </span>
                            </div>
                            <?php endif; ?>

                            <div class="regalia-product-actions">
                                <div class="regalia-product-price">
                                    <?php if ($product->is_on_sale()): ?>
                                    <div class="regalia-price-sale">
                                        <span class="regalia-price-new"><?php echo wp_kses_post(wc_price($product->get_sale_price())); ?></span>
                                        <span class="regalia-price-old"><?php echo wp_kses_post(wc_price($product->get_regular_price())); ?></span>
                                    </div>
                                    <?php else: ?>
                                    <span class="regalia-price-regular"><?php echo wp_kses_post($product->get_price_html()); ?></span>
                                    <?php endif; ?>
                                </div>

                                <?php if ($product->is_in_stock()): ?>
                                    <?php if ($product->is_type('variable')): ?>
                                    <button type="button" class="regalia-btn regalia-btn-primary show-variations-btn" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                                        <i class="fas fa-shopping-cart"></i>
                                        <span>Order</span>
                                    </button>
                                    <?php else: ?>
                                    <button type="button" class="regalia-btn regalia-btn-primary show-quantity-btn" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                                        <i class="fas fa-shopping-cart"></i>
                                        <span>Order</span>
                                    </button>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <button class="regalia-btn regalia-btn-disabled" disabled>
                                        <i class="fas fa-times-circle"></i>
                                        <span>Out of Stock</span>
                                    </button>
                                <?php endif; ?>
                            </div>

                            <!-- Variation/Quantity Card -->
                            <?php if ($product->is_type('variable')): ?>
                            <!-- Variable Product Card -->
                            <div class="regalia-variation-card">
                                <div class="regalia-variation-header">
                                    <h4>Select Options</h4>
                                    <button type="button" class="close-variation-card">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <div class="regalia-variation-options">
                                    <?php
                                    $variation_attributes = $product->get_variation_attributes();
                                    foreach ($variation_attributes as $attribute_name => $attribute_values) {
                                        $attribute_label = wc_attribute_label($attribute_name);
                                        echo '<div class="regalia-variation-option">';
                                        echo '<label>' . esc_html($attribute_label) . '</label>';
                                        echo '<select class="variation-dropdown" data-attribute="' . esc_attr(sanitize_title($attribute_name)) . '">';
                                        echo '<option value="">Choose ' . esc_html($attribute_label) . '</option>';

                                        foreach ($attribute_values as $attribute_value) {
                                            echo '<option value="' . esc_attr($attribute_value) . '">' . esc_html($attribute_value) . '</option>';
                                        }

                                        echo '</select>';
                                        echo '</div>';
                                    }
                                    ?>

                                    <div class="regalia-variation-option">
                                        <label>Quantity</label>
                                        <div class="regalia-quantity">
                                            <button type="button" class="quantity-minus">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" value="1" min="1" class="quantity-input-field">
                                            <button type="button" class="quantity-plus">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="regalia-variation-actions">
                                    <button type="button" class="regalia-btn regalia-btn-primary add-variable-to-cart-btn" data-product-id="<?php echo esc_attr($product->get_id()); ?>" style="width: 100%; color: #fff;">
                                        <i class="fas fa-shopping-cart" style="color: #fff;"></i>
                                        <span>Confirm</span>
                                    </button>
                                </div>
                            </div>
                            <?php else: ?>
                            <!-- Simple Product Quantity Card -->
                            <div class="regalia-variation-card regalia-quantity-card">
                                <div class="regalia-variation-header">
                                    <h4>Select Quantity</h4>
                                    <button type="button" class="close-variation-card">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <div class="regalia-variation-options">
                                    <div class="regalia-variation-option">
                                        <label>Quantity</label>
                                        <div class="regalia-quantity">
                                            <button type="button" class="quantity-minus">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" value="1" min="1" class="quantity-input-field">
                                            <button type="button" class="quantity-plus">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="regalia-variation-actions">
                                    <button type="button" class="regalia-btn regalia-btn-primary add-to-cart-btn" data-product-id="<?php echo esc_attr($product->get_id()); ?>" style="width: 100%; color: #fff;">
                                        <i class="fas fa-shopping-cart" style="color: #fff;"></i>
                                        <span>Confirm</span>
                                    </button>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div id="sentinel" class="h-20"></div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Previous Orders Section -->
    <?php if (!empty($user_orders)): ?>
    <div class="previous-orders-section mt-5">
        <h3 class="h5 mb-4 text-primary"><i class="fas fa-shopping-bag me-2"></i> My Previous Orders</h3>
        <div class="accordion" id="ordersAccordion">
            <?php foreach ($user_orders as $index => $order): ?>
                <div class="accordion-item border-0 mb-3 shadow-sm rounded-4 overflow-hidden">
                    <h2 class="accordion-header" id="heading<?php echo $index; ?>">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $index; ?>" aria-expanded="false" aria-controls="collapse<?php echo $index; ?>">
                            <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                <div>
                                    <span class="fw-bold">Order #<?php echo esc_html($order['id']); ?></span>
                                    <span class="ms-3 text-muted"><?php echo date('F j, Y', strtotime($order['date'])); ?></span>
                                </div>
                                <div>
                                    <span class="badge bg-<?php echo esc_attr(lci_get_order_status_color($order['status'])); ?>">
                                        <?php echo esc_html(wc_get_order_status_name($order['status'])); ?>
                                    </span>
                                    <span class="ms-3 fw-bold"><?php echo wc_price($order['total']); ?></span>
                                </div>
                            </div>
                        </button>
                    </h2>
                    <div id="collapse<?php echo $index; ?>" class="accordion-collapse collapse" aria-labelledby="heading<?php echo $index; ?>" data-bs-parent="#ordersAccordion">
                        <div class="accordion-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th style="width: 80px">Image</th>
                                            <th>Product</th>
                                            <th class="text-center">Quantity</th>
                                            <th class="text-end">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($order['items'] as $item): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($item['image']): ?>
                                                        <img src="<?php echo esc_url($item['image']); ?>" alt="<?php echo esc_attr($item['name']); ?>" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-light d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo esc_html($item['name']); ?></td>
                                                <td class="text-center"><?php echo esc_html($item['quantity']); ?></td>
                                                <td class="text-end"><?php echo wc_price($item['total']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php else: ?>
    <div class="previous-orders-section mt-5">
        <h3 class="h5 mb-4 text-primary"><i class="fas fa-shopping-bag me-2"></i> My Previous Orders</h3>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> You haven't purchased any regalia items yet.
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- We're using the mini cart button at the top of the page instead of including another mini cart here -->
<!-- Add to Cart Confirmation Modal is still needed -->
<?php echo lci_get_add_to_cart_confirm_html(); ?>

<style>
/* Import Open Sans font */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap');

/* Modal Backdrop Fix - Inline Critical CSS */
.modal-backdrop + .modal-backdrop { display: none !important; }
body > .modal-backdrop:nth-child(n+2) { display: none !important; }
body.modal-open { overflow: hidden !important; padding-right: 0 !important; }

/* Regalia Shop Styles - 2025 UX */
.regalia-shop-container {
    font-family: 'Open Sans', sans-serif;
}

/* Mobile Responsive Fixes */
@media (max-width: 767.98px) {
    .regalia-shop-container h2 {
        font-size: 1.5rem;
    }

    /* Mini cart button styling for mobile */
    #mini-cart-button {
        margin-bottom: 1rem;
        width: auto;
        min-width: 200px;
    }

    /* Contributor count text justified on mobile */
    .regalia-contributor-count {
        text-align: justify;
        display: block;
        width: 100%;
    }
}

/* Progress Bar Container */
.regalia-progress-bar-container {
    height: 1rem;
    background-color: #e2e8f0;
    border-radius: 0.75rem;
    position: relative;
    margin-bottom: 0.2rem;
    overflow: hidden;
}

/* Product Card Styles */
.product-card {
    transition: all 0.3s ease;
    border-radius: 1rem;
    position: relative;
    overflow: hidden;
}

.product-card:hover, .lci-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(54, 177, 220, 0.15) !important;
}

.product-card::before, .lci-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #36b1dc, #2d93b7);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover::before, .lci-product-card:hover::before {
    opacity: 1;
}

.product-image-container {
    height: 220px;
    overflow: hidden;
    background-color: #f8f9fa;
    position: relative;
}

.lci-product-card .product-image-container {
    height: 250px;
}

.product-image-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background: linear-gradient(to top, rgba(0,0,0,0.05), transparent);
    pointer-events: none;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.5s ease;
    padding: 10px;
}

.lci-product-card .product-image {
    object-fit: contain;
    background-color: white;
}

.product-card:hover .product-image, .lci-product-card:hover .product-image {
    transform: scale(1.05);
}

.product-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    height: 48px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    position: relative;
}

.product-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: #36b1dc;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.product-card:hover .product-title::after {
    width: 60px;
}

.product-description {
    color: #6c757d;
    font-size: 0.9rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    margin-bottom: 1rem;
}

.product-price {
    font-size: 1.1rem;
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: rgba(54, 177, 220, 0.1);
    border-radius: 0.5rem;
    margin-bottom: 0;
}

.lci-product-card .product-price {
    font-size: 1.2rem;
    font-weight: 600;
    color: #36b1dc;
}

/* New Product Card Styles */
.product-card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

.product-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-0.25rem);
}

.product-card .aspect-square {
    position: relative;
    padding-bottom: 100%;
}

.product-card .aspect-square img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 1rem;
    transition: transform 0.3s ease;
}

.product-card:hover .aspect-square img {
    transform: scale(1.05);
}

.product-card .product-title {
    font-weight: 500;
    color: #1a202c;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 0.5rem;
}

.product-card .text-primary,
.bg-primary {
    color: #36b1dc !important;
    background-color: #36b1dc !important;
}

.product-card .badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
}

.product-card .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-card .text-justify {
    text-align: justify;
}

/* Variants Count Styles */
.variants-count {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.variants-count span {
    display: inline-block;
}

/* Variation Card Styles */
.variation-card {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem;
    box-shadow: 0 -10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 50;
}

.variation-card:not(.d-none) {
    display: block;
}

.variation-card .quantity-input {
    display: flex;
    align-items: center;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
}

.variation-card .quantity-input button {
    padding: 0.5rem 0.75rem;
    color: #4a5568;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.variation-card .quantity-input button:hover {
    background-color: #f7fafc;
}

.variation-card .quantity-input input {
    width: 3rem;
    text-align: center;
    border: none;
    border-left: 1px solid #e2e8f0;
    border-right: 1px solid #e2e8f0;
    padding: 0.5rem 0;
    -moz-appearance: textfield;
}

.variation-card .quantity-input input::-webkit-outer-spin-button,
.variation-card .quantity-input input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Button Styles */
.btn-primary, .add-to-cart-btn, .add-variable-to-cart-btn, .show-variations-btn {
    background-color: #36b1dc;
    border-color: #36b1dc;
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
    color: #fff !important;
}

.btn-primary i, .add-to-cart-btn i, .add-variable-to-cart-btn i, .show-variations-btn i {
    color: #fff !important;
}

.btn-primary:hover, .add-to-cart-btn:hover, .add-variable-to-cart-btn:hover, .show-variations-btn:hover {
    background-color: #2d93b7;
    border-color: #2d93b7;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(45, 147, 183, 0.3);
}

.btn-primary:active, .add-to-cart-btn:active, .add-variable-to-cart-btn:active, .show-variations-btn:active {
    transform: translateY(0);
}

.btn-primary::after, .add-to-cart-btn::after, .add-variable-to-cart-btn::after, .show-variations-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.btn-primary:focus:not(:active)::after,
.add-to-cart-btn:focus:not(:active)::after,
.add-variable-to-cart-btn:focus:not(:active)::after,
.show-variations-btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.5;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

/* Variations Styles */
.variations-container {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border-left: 3px solid #36b1dc;
    transition: all 0.3s ease;
    transform-origin: bottom center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.variations-container.show-variations {
    animation: slideUp 0.4s ease forwards;
}

@keyframes slideUp {
    0% {
        opacity: 0;
        transform: translateY(20px) scaleY(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scaleY(1);
    }
}

.variations-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #36b1dc;
    display: flex;
    align-items: center;
}

.variations-title::before {
    content: '\f0c9';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 0.5rem;
}

.variation-select label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
}

.variation-select label::before {
    content: '\f105';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 0.5rem;
    color: #36b1dc;
    font-size: 0.7rem;
}

.form-select.variation-dropdown {
    border-color: #e9ecef;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.form-select.variation-dropdown:focus {
    border-color: #36b1dc;
    box-shadow: 0 0 0 0.25rem rgba(54, 177, 220, 0.25);
}

/* Mini Cart Styles */
.mini-cart-container {
    display: flex;
    align-items: center;
}

.mini-cart-total {
    font-weight: 600;
    color: #36b1dc;
    background-color: rgba(54, 177, 220, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
}

/* Order Accordion Styles */
.previous-orders-section {
    border-top: 1px solid #e9ecef;
    padding-top: 2rem;
}

.accordion-item {
    transition: all 0.3s ease;
}

.accordion-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05) !important;
}

.accordion-button {
    padding: 1rem 1.25rem;
    border-radius: 0.5rem !important;
}

.accordion-button:not(.collapsed) {
    background-color: rgba(54, 177, 220, 0.1);
    color: #36b1dc;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: rgba(54, 177, 220, 0.25);
}

.accordion-body {
    padding: 1.25rem;
    background-color: #fafafa;
}

/* Loading Animation */
@keyframes shimmer {
    0% { background-position: -1000px 0; }
    100% { background-position: 1000px 0; }
}

.loading-shimmer {
    background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
    background-size: 1000px 100%;
    animation: shimmer 2s infinite linear;
}

/* Mini Cart Modal Styles */
.mini-cart-items-container {
    max-height: 400px;
    overflow-y: auto;
}

.cart-items-list {
    max-height: 300px;
    overflow-y: auto;
}

.mini-cart-item {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.mini-cart-item:last-child {
    border-bottom: none;
}

.mini-cart-item-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 0.5rem;
}

.mini-cart-item-details {
    flex: 1;
}

.mini-cart-item-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.mini-cart-item-price {
    color: #36b1dc;
    font-weight: 600;
}

.mini-cart-item-quantity {
    background-color: rgba(54, 177, 220, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.8rem;
}

.mini-cart-item-remove {
    color: #dc3545;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mini-cart-item-remove:hover {
    transform: scale(1.2);
}

.mini-cart-modal-total {
    font-size: 1.2rem;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
    .product-image-container {
        height: 180px;
    }

    .product-title {
        height: auto;
        max-height: 48px;
    }

    .product-description {
        -webkit-line-clamp: 2;
    }

    .mini-cart-item-image {
        width: 50px;
        height: 50px;
    }

    .mini-cart-item-name {
        font-size: 0.8rem;
    }
}
</style>

<script>
// Define ajaxurl if it's not already defined
var ajaxurl = ajaxurl || '<?php echo admin_url('admin-ajax.php'); ?>';
var lci_nonce = '<?php echo wp_create_nonce('lci-dashboard-add-to-cart-nonce'); ?>';
var lci_ajax_object = {
    ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
    nonce: '<?php echo wp_create_nonce('lci_mini_cart_nonce'); ?>',
    add_to_cart_nonce: '<?php echo wp_create_nonce('lci-dashboard-add-to-cart'); ?>'
};

document.addEventListener('DOMContentLoaded', function() {
    // Fix for multiple modal backdrops - more aggressive approach
    // Handle all modal events
    ['show.bs.modal', 'shown.bs.modal', 'hide.bs.modal', 'hidden.bs.modal'].forEach(function(event) {
        document.body.addEventListener(event, function() {
            setTimeout(cleanupModalBackdrops, 0);
        });
    });

    // Periodically check and clean up backdrops
    setInterval(cleanupModalBackdrops, 500);

    // Initial cleanup
    cleanupModalBackdrops();

    // Mini cart is now handled by Alpine.js components

    // Function to clean up modal backdrops - more aggressive approach
    function cleanupModalBackdrops() {
        // Remove all but the first backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        if (backdrops.length > 1) {
            backdrops.forEach(function(backdrop, index) {
                if (index > 0) backdrop.remove();
            });
        }

        // Force the first backdrop to have the correct classes
        if (backdrops.length > 0) {
            const firstBackdrop = backdrops[0];
            firstBackdrop.className = 'modal-backdrop fade show';
            firstBackdrop.style.zIndex = '1040';
        }

        // Fix body padding
        document.body.style.paddingRight = '0';
    }

    // View cart button is now handled by Alpine.js components

    // Simple product - Order button functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const originalText = this.innerHTML;
            const productCard = this.closest('.regalia-product-card');

            if (!productCard) {
                // Console error removed
                return;
            }

            const productTitle = productCard.querySelector('.regalia-product-title');
            const productName = productTitle ? productTitle.textContent.trim() : 'Product';

            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';
            this.disabled = true;

            // Get quantity from input field if available
            const quantityCard = productCard.querySelector('.regalia-quantity-card');
            let quantity = 1;

            if (quantityCard) {
                const quantityInput = quantityCard.querySelector('.quantity-input-field');
                if (quantityInput) {
                    quantity = parseInt(quantityInput.value) || 1;
                }
            }

            // Add to cart via AJAX
            // Check if wc_add_to_cart_params exists
            if (typeof wc_add_to_cart_params === 'undefined') {
                // Console error removed
                alert('Could not add product to your order. WooCommerce is not properly initialized.');
                this.innerHTML = originalText;
                this.disabled = false;
                return;
            }

            // Use our custom AJAX endpoint
            const addToCartUrl = ajaxurl + '?action=lci-dashboard-add-to-cart';

            fetch(addToCartUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({
                    product_id: productId,
                    quantity: quantity,
                    security: lci_nonce
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Check if data is valid
                if (!data) {
                    throw new Error('Invalid response from server');
                }

                if (data.error) {
                    // Show error
                    alert(data.message || 'Error adding product to cart');
                } else if (data.success) {
                    // Hide the quantity card if it exists
                    if (quantityCard) {
                        quantityCard.classList.remove('active');
                    }
                    // Get product image and name for confirmation modal
                    const productImage = productCard.querySelector('.regalia-product-image img');
                    const productTitle = productCard.querySelector('.regalia-product-title');

                    // Get the cart data from the response
                    const cartCount = data.cart_count || 0;
                    const cartTotal = data.cart_total || '0.00 €';

                    // Update mini cart button if it exists
                    const miniCartButton = document.getElementById('mini-cart-button');
                    if (miniCartButton) {
                        const countBadge = miniCartButton.querySelector('.badge');
                        const totalText = miniCartButton.querySelector('.mini-cart-button-text');

                        if (countBadge) {
                            countBadge.textContent = cartCount;
                            countBadge.style.display = cartCount > 0 ? '' : 'none';
                        }

                        if (totalText) {
                            totalText.innerHTML = cartTotal;
                        }
                    }

                    // Update button to show "Added to cart!"
                    this.innerHTML = '<i class="fas fa-check me-2"></i> Added to cart!';
                    this.disabled = false;

                    // Reset button state after 2 seconds
                    setTimeout(() => {
                        this.innerHTML = originalText;
                    }, 2000);

                    // Show confirmation using Alpine.js modal
                    showAddToCartConfirmation(
                        productTitle ? productTitle.textContent : 'Product',
                        productImage ? productImage.src : ''
                    );

                    // Update mini cart in background with the cart data
                    loadMiniCartItems({
                        cart_count: cartCount,
                        cart_total: cartTotal
                    });

                    // Also trigger standard WooCommerce events for compatibility
                    jQuery(document.body).trigger('added_to_cart', [null, null, null]);
                    jQuery(document.body).trigger('updated_cart_totals');
                }
            })
            .catch(error => {
                // Console error removed
                alert('Could not add product to your order. Please try again.');

                // Hide the quantity card if it exists
                if (quantityCard) {
                    quantityCard.classList.remove('active');
                }
            })
            .finally(() => {
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;
            });
        });
    });

    // Show quantity button functionality for simple products
    const showQuantityButtons = document.querySelectorAll('.show-quantity-btn');

    showQuantityButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the product card and quantity card
            const productCard = this.closest('.regalia-product-card');

            if (!productCard) {
                // Console error removed
                return;
            }

            const quantityCard = productCard.querySelector('.regalia-quantity-card');

            if (!quantityCard) {
                // Console error removed
                return;
            }

            // Show the quantity card with animation
            quantityCard.classList.add('active');

            // Add event listener to close button if not already added
            const closeButton = quantityCard.querySelector('.close-variation-card');
            if (closeButton && !closeButton._hasClickListener) {
                closeButton.addEventListener('click', function() {
                    quantityCard.classList.remove('active');
                });
                closeButton._hasClickListener = true;
            }

            // Add event listeners to quantity buttons if not already added
            const quantityMinus = quantityCard.querySelector('.quantity-minus');
            const quantityPlus = quantityCard.querySelector('.quantity-plus');
            const quantityInput = quantityCard.querySelector('.quantity-input-field');

            if (quantityMinus && !quantityMinus._hasClickListener) {
                quantityMinus.addEventListener('click', function() {
                    const currentValue = parseInt(quantityInput.value);
                    if (currentValue > 1) {
                        quantityInput.value = currentValue - 1;
                    }
                });
                quantityMinus._hasClickListener = true;
            }

            if (quantityPlus && !quantityPlus._hasClickListener) {
                quantityPlus.addEventListener('click', function() {
                    const currentValue = parseInt(quantityInput.value);
                    quantityInput.value = currentValue + 1;
                });
                quantityPlus._hasClickListener = true;
            }
        });
    });

    // Show variations button functionality for variable products
    const showVariationsButtons = document.querySelectorAll('.show-variations-btn');

    showVariationsButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the product card and variation card
            const productCard = this.closest('.regalia-product-card');

            if (!productCard) {
                // Console error removed
                return;
            }

            const variationCard = productCard.querySelector('.regalia-variation-card');

            if (!variationCard) {
                // Console error removed
                return;
            }

            // Show the variation card with animation
            variationCard.classList.add('active');

            // Add event listener to close button if not already added
            const closeButton = variationCard.querySelector('.close-variation-card');
            if (closeButton && !closeButton._hasClickListener) {
                closeButton.addEventListener('click', function() {
                    // Add a closing class for animation
                    variationCard.classList.add('closing');

                    // Remove the active class after animation completes
                    setTimeout(() => {
                        variationCard.classList.remove('active');
                        variationCard.classList.remove('closing');
                    }, 700); // Match the animation duration (slightly longer to ensure completion)
                });
                closeButton._hasClickListener = true;
            }

            // Add event listeners to quantity buttons if not already added
            const quantityMinus = variationCard.querySelector('.quantity-minus');
            const quantityPlus = variationCard.querySelector('.quantity-plus');
            const quantityInput = variationCard.querySelector('.quantity-input-field');

            if (quantityMinus && !quantityMinus._hasClickListener) {
                quantityMinus.addEventListener('click', function() {
                    const currentValue = parseInt(quantityInput.value);
                    if (currentValue > 1) {
                        quantityInput.value = currentValue - 1;
                    }
                });
                quantityMinus._hasClickListener = true;
            }

            if (quantityPlus && !quantityPlus._hasClickListener) {
                quantityPlus.addEventListener('click', function() {
                    const currentValue = parseInt(quantityInput.value);
                    quantityInput.value = currentValue + 1;
                });
                quantityPlus._hasClickListener = true;
            }
        });
    });

    // Variable product - Confirm button functionality
    const addVariableToCartButtons = document.querySelectorAll('.add-variable-to-cart-btn');

    addVariableToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const productCard = this.closest('.regalia-product-card');
            const variationCard = this.closest('.regalia-variation-card');

            if (!productCard) {
                // Console error removed
                return;
            }

            const productTitle = productCard.querySelector('.regalia-product-title');
            const productName = productTitle ? productTitle.textContent.trim() : 'Product';
            const originalText = this.innerHTML;

            // Get selected variations
            const variationSelects = variationCard.querySelectorAll('.variation-dropdown');
            const variationData = {};
            let allVariationsSelected = true;

            variationSelects.forEach(select => {
                const attributeName = select.getAttribute('data-attribute');
                const attributeValue = select.value;

                if (!attributeValue) {
                    allVariationsSelected = false;
                    select.classList.add('border-red-500');
                } else {
                    select.classList.remove('border-red-500');
                    variationData[`attribute_${attributeName}`] = attributeValue;
                }
            });

            if (!allVariationsSelected) {
                alert('Please select all product options before ordering.');
                return;
            }

            // Get quantity
            const quantityInput = variationCard.querySelector('.quantity-input-field');
            const quantity = quantityInput ? parseInt(quantityInput.value) : 1;

            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';
            this.disabled = true;

            // Add to cart via AJAX
            // Check if wc_add_to_cart_params exists
            if (typeof wc_add_to_cart_params === 'undefined') {
                // Console error removed
                alert('Could not add product to your order. WooCommerce is not properly initialized.');
                this.innerHTML = originalText;
                this.disabled = false;
                return;
            }

            const formData = new URLSearchParams({
                product_id: productId,
                quantity: quantity,
                security: lci_nonce
            });

            // Add variation data
            for (const [key, value] of Object.entries(variationData)) {
                formData.append(key, value);
            }

            // Use our custom AJAX endpoint
            const addToCartUrl = ajaxurl + '?action=lci-dashboard-add-to-cart';

            fetch(addToCartUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Check if data is valid
                if (!data) {
                    throw new Error('Invalid response from server');
                }

                if (data.error) {
                    // Show error
                    alert(data.message || 'Error adding product to cart');
                } else if (data.success) {
                    // Hide the variation card
                    const variationCard = this.closest('.regalia-variation-card');
                    if (variationCard) {
                        variationCard.classList.remove('active');
                    }

                    // Get product image and name for confirmation modal
                    const productImage = productCard.querySelector('.regalia-product-image img');
                    const productTitle = productCard.querySelector('.regalia-product-title');

                    // Get the cart data from the response
                    const cartCount = data.cart_count || 0;
                    const cartTotal = data.cart_total || '0.00 €';

                    // Update mini cart button if it exists
                    const miniCartButton = document.getElementById('mini-cart-button');
                    if (miniCartButton) {
                        const countBadge = miniCartButton.querySelector('.badge');
                        const totalText = miniCartButton.querySelector('.mini-cart-button-text');

                        if (countBadge) {
                            countBadge.textContent = cartCount;
                            countBadge.style.display = cartCount > 0 ? '' : 'none';
                        }

                        if (totalText) {
                            totalText.innerHTML = cartTotal;
                        }
                    }

                    // Update button to show "Added to cart!"
                    this.innerHTML = '<i class="fas fa-check me-2"></i> Added to cart!';
                    this.disabled = false;

                    // Reset button state after 2 seconds
                    setTimeout(() => {
                        this.innerHTML = originalText;
                    }, 2000);

                    // Show confirmation using Alpine.js modal
                    showAddToCartConfirmation(
                        productTitle ? productTitle.textContent : 'Product',
                        productImage ? productImage.src : ''
                    );

                    // Update mini cart and fundraising message in background with the cart data
                    loadMiniCartItems({
                        cart_count: cartCount,
                        cart_total: cartTotal
                    });

                    // Also trigger standard WooCommerce events for compatibility
                    jQuery(document.body).trigger('added_to_cart', [null, null, null]);
                    jQuery(document.body).trigger('updated_cart_totals');
                }
            })
            .catch(error => {
                // Console error removed
                alert('Could not add product to your order. Please try again.');
            })
            .finally(() => {
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;
            });
        });
    });

    // Function to load mini cart items via AJAX
    function loadMiniCartItems(cartData) {
        // If we have cart data already, update the Alpine.js component directly
        if (cartData && cartData.cart_count !== undefined && cartData.cart_total) {
            // Try to find the Alpine.js mini cart component
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (miniCartContainer && miniCartContainer.__x) {
                try {
                    // Get the Alpine.js component data
                    const data = miniCartContainer.__x.getUnobservedData();

                    // Update the count and total
                    data.count = cartData.cart_count;
                    data.total = cartData.cart_total;

                    // Update mini cart data directly
                } catch (error) {
                    // Error handling
                }
            }
        }

        // Dispatch event to update Alpine.js mini cart component
        window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
            detail: cartData || {}
        }));

        // Also refresh the fundraising message
        if (typeof refreshFundraisingMessage === 'function') {
            refreshFundraisingMessage();
        } else {
            // Fallback: trigger the event that refreshFundraisingMessage listens for
            jQuery(document.body).trigger('updated_cart_totals');
        }
    }

    // Function to remove cart item - now handled by Alpine.js components
    function removeCartItem(cartItemKey) {
        // Dispatch event to update Alpine.js mini cart component
        window.dispatchEvent(new CustomEvent('lci:cartUpdated'));
    }

    // No need for additional event listeners since we're using the Alpine.js mini cart
});
</script>
