/**
 * Fix for mini-cart modal and scrolling issues on tours page
 */
(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        // Fix for multiple modal backdrops
        const fixModalBackdrops = function() {
            // Remove duplicate modal backdrops
            const backdrops = document.querySelectorAll('.modal-backdrop');
            if (backdrops.length > 1) {
                for (let i = 1; i < backdrops.length; i++) {
                    backdrops[i].remove();
                }
            }
        };

        // Fix for Alpine.js modals
        const fixAlpineModals = function() {
            // Ensure Alpine.js modals have proper z-index
            const alpineModals = document.querySelectorAll('.lci-modal-backdrop');
            alpineModals.forEach(function(modal) {
                modal.style.zIndex = '9999';
            });
        };

        // Fix for mini-cart modal
        const fixMiniCartModal = function() {
            // Ensure mini-cart modal is properly initialized
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (miniCartContainer && !miniCartContainer.__x) {
                // If Alpine.js is loaded but not initialized on the mini-cart
                if (window.Alpine) {
                    try {
                        // Try to initialize Alpine.js on the mini-cart
                        window.Alpine.initTree(miniCartContainer);
                        console.log('Mini-cart Alpine.js component initialized');
                    } catch (error) {
                        console.error('Error initializing mini-cart Alpine.js component:', error);
                    }
                }
            }
        };

        // Fix for add-to-cart confirmation modal
        const fixAddToCartModal = function() {
            // Ensure add-to-cart confirmation modal is properly initialized
            const addToCartConfirm = document.getElementById('add-to-cart-confirm');
            if (addToCartConfirm && !addToCartConfirm.__x) {
                // If Alpine.js is loaded but not initialized on the add-to-cart confirmation
                if (window.Alpine) {
                    try {
                        // Try to initialize Alpine.js on the add-to-cart confirmation
                        window.Alpine.initTree(addToCartConfirm);
                        console.log('Add-to-cart confirmation Alpine.js component initialized');
                    } catch (error) {
                        console.error('Error initializing add-to-cart confirmation Alpine.js component:', error);
                    }
                }
            }
        };

        // Fix for councilor warning modal
        const fixCouncilorWarningModal = function() {
            // Ensure councilor warning modal is properly initialized
            const councilWarningModal = document.getElementById('councilor-warning-modal');
            if (councilWarningModal && !councilWarningModal.__x) {
                // If Alpine.js is loaded but not initialized on the councilor warning
                if (window.Alpine) {
                    try {
                        // Try to initialize Alpine.js on the councilor warning
                        window.Alpine.initTree(councilWarningModal);
                        console.log('Councilor warning Alpine.js component initialized');
                    } catch (error) {
                        console.error('Error initializing councilor warning Alpine.js component:', error);
                    }
                }
            }
        };

        // Fix scrolling issues
        const fixScrolling = function() {
            // Remove all scroll-preventing classes from HTML and BODY
            const htmlElement = document.documentElement;
            const bodyElement = document.body;
            
            // Remove BeTheme classes
            htmlElement.classList.remove(
                'mfn-hidden-scrollbar',
                'mfn-cart-opened',
                'mfn-cart-showing',
                'mfn-active-cart',
                'mfn-hidden-overlay'
            );
            
            bodyElement.classList.remove(
                'mfn-hidden-overlay',
                'mfn-cart-opened',
                'mfn-cart-showing',
                'mfn-active-cart'
            );
            
            // Remove Bootstrap modal classes if no modal is actually visible
            if (!document.querySelector('.modal:visible')) {
                bodyElement.classList.remove('modal-open');
            }
            
            // Force scrolling to be enabled
            htmlElement.style.overflow = 'auto';
            htmlElement.style.overflowY = 'scroll';
            htmlElement.style.paddingRight = '0';
            htmlElement.style.marginRight = '0';
            
            bodyElement.style.overflow = 'auto';
            bodyElement.style.overflowY = 'visible';
            bodyElement.style.paddingRight = '0';
            bodyElement.style.marginRight = '0';
            bodyElement.style.position = '';
            bodyElement.style.top = '';
            
            // Remove any overlay elements
            const overlays = document.querySelectorAll('.mfn-cart-overlay, #mfn-cart-overlay, [class*="mfn-cart-overlay"]');
            overlays.forEach(overlay => {
                if (overlay) {
                    overlay.style.display = 'none';
                }
            });
        };

        // Ensure lci_ajax_object is defined
        if (typeof lci_ajax_object === 'undefined') {
            window.lci_ajax_object = {
                ajax_url: window.ajaxurl || '/wp-admin/admin-ajax.php',
                nonce: ''
            };
            console.log('lci_ajax_object defined with fallback values');
        }

        // Apply fixes
        fixModalBackdrops();
        fixAlpineModals();
        fixMiniCartModal();
        fixAddToCartModal();
        fixCouncilorWarningModal();
        fixScrolling();

        // Apply fixes again after a short delay to catch any late initializations
        setTimeout(function() {
            fixModalBackdrops();
            fixAlpineModals();
            fixMiniCartModal();
            fixAddToCartModal();
            fixCouncilorWarningModal();
            fixScrolling();
        }, 1000);

        // Listen for modal events
        document.addEventListener('show.bs.modal', function() {
            setTimeout(fixModalBackdrops, 0);
            setTimeout(fixScrolling, 100);
        });
        document.addEventListener('shown.bs.modal', function() {
            setTimeout(fixModalBackdrops, 0);
            setTimeout(fixScrolling, 100);
        });
        document.addEventListener('hide.bs.modal', function() {
            setTimeout(fixModalBackdrops, 0);
            setTimeout(fixScrolling, 100);
        });
        document.addEventListener('hidden.bs.modal', function() {
            setTimeout(fixModalBackdrops, 0);
            setTimeout(fixScrolling, 100);
        });

        // Listen for Alpine.js events
        document.addEventListener('alpine:initialized', function() {
            fixMiniCartModal();
            fixAddToCartModal();
            fixCouncilorWarningModal();
        });

        // Apply fix when the window is scrolled
        window.addEventListener('scroll', function() {
            fixScrolling();
        });

        // Apply fix periodically
        setInterval(function() {
            fixModalBackdrops();
            fixScrolling();
        }, 500);

        // Fix z-index issues with modals
        const style = document.createElement('style');
        style.textContent = `
            .modal-backdrop { z-index: 1050 !important; }
            .modal { z-index: 1055 !important; }
            .lci-modal-backdrop { z-index: 1060 !important; }
            body.modal-open { overflow: auto !important; padding-right: 0 !important; }
            .modal-backdrop + .modal-backdrop { display: none !important; }
            body > .modal-backdrop:nth-child(n+2) { display: none !important; }
            
            /* Ensure scrolling is always enabled */
            html, body {
                overflow-y: auto !important;
                overflow-x: hidden;
                padding-right: 0 !important;
                margin-right: 0 !important;
                height: auto !important;
                position: relative !important;
            }
            
            /* Fix for modals */
            .modal-open {
                overflow: auto !important;
                overflow-y: scroll !important;
                padding-right: 0 !important;
                position: relative !important;
                height: auto !important;
            }
            
            /* Override any BeTheme styles */
            html.mfn-hidden-scrollbar,
            html.mfn-cart-showing,
            html.mfn-active-cart,
            html.mfn-cart-opened,
            html.loaded.mfn-hidden-scrollbar,
            html.loaded.mfn-cart-showing,
            html.loaded.mfn-active-cart,
            html.loaded.mfn-cart-opened,
            html.mfn-hidden-scrollbar.loaded,
            html.mfn-cart-showing.loaded,
            html.mfn-active-cart.loaded,
            html.mfn-cart-opened.loaded {
                margin-right: 0 !important;
                overflow: auto !important;
                overflow-y: scroll !important;
                padding-right: 0 !important;
            }
        `;
        document.head.appendChild(style);
    });
})();
