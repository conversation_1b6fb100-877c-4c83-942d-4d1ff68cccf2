<?php
/**
 * My Visa view for LCI 2025 Dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Get user's country
$country_code = get_user_meta($user_id, 'billing_country', true);

// Get participant data if available
global $wpdb;
$participant_table = $wpdb->prefix . 'lci2025_participants';
$participant = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM $participant_table WHERE user_id = %d",
    $user_id
));

if ($participant) {
    // If we have participant data, use that country
    $country_name = $participant->country;
} else {
    // Otherwise, convert country code to name
    if (function_exists('WC')) {
        $countries = WC()->countries->get_countries();
        $country_name = isset($countries[$country_code]) ? $countries[$country_code] : $country_code;
    } else {
        // Fallback
        $country_name = $country_code;
    }
}

// List of countries that need a visa to enter Romania
// This is a simplified list and should be updated with accurate information
$visa_required_countries = [
    'AF', 'DZ', 'AO', 'AM', 'AZ', 'BD', 'BY', 'BJ', 'BT', 'BO', 'BW', 'BF', 'BI', 'KH',
    'CM', 'CV', 'CF', 'TD', 'CN', 'CO', 'KM', 'CG', 'CD', 'CI', 'CU', 'DJ', 'DM', 'DO',
    'EC', 'EG', 'GQ', 'ER', 'ET', 'FJ', 'GA', 'GM', 'GH', 'GN', 'GW', 'GY', 'HT', 'IN',
    'ID', 'IR', 'IQ', 'JM', 'JO', 'KZ', 'KE', 'KI', 'KP', 'KG', 'LA', 'LB', 'LS', 'LR',
    'LY', 'MG', 'MW', 'MV', 'ML', 'MR', 'MU', 'MN', 'MA', 'MZ', 'MM', 'NA', 'NP', 'NI',
    'NE', 'NG', 'OM', 'PK', 'PS', 'PG', 'PH', 'QA', 'RU', 'RW', 'WS', 'ST', 'SA', 'SN',
    'SC', 'SL', 'SB', 'SO', 'ZA', 'SS', 'LK', 'SD', 'SR', 'SZ', 'SY', 'TJ', 'TZ', 'TH',
    'TL', 'TG', 'TO', 'TT', 'TN', 'TM', 'TV', 'UG', 'UA', 'AE', 'UZ', 'VU', 'VE', 'VN',
    'YE', 'ZM', 'ZW'
];

// Check if user needs a visa
$needs_visa = in_array($country_code, $visa_required_countries);

// If we don't have a country code, we can't determine visa requirements
$unknown_country = empty($country_code);
?>

<div class="my-visa-container">
    <h2 class="text-center mb-4">Visa Information</h2>

    <?php if ($unknown_country) : ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i> <span>We couldn't determine your country of residence. Please update your profile with your country information.</span>
            <div class="mt-3">
                <a href="<?php echo add_query_arg('tab', 'profile'); ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-user-edit me-1"></i> Update Profile
                </a>
            </div>
        </div>
    <?php elseif (!$needs_visa) : ?>
        <div class="alert alert-success" style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); border: none; padding: 20px; position: relative; overflow: hidden; margin-bottom: 24px; border-left: 4px solid #36b1dc;">
            <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle at top right, rgba(54, 177, 220, 0.08), transparent 70%);"></div>
            <div style="position: absolute; bottom: 0; left: 0; width: 80px; height: 80px; background: radial-gradient(circle at bottom left, rgba(54, 177, 220, 0.05), transparent 70%);"></div>
            <div style="display: flex; align-items: flex-start; flex-wrap: wrap; position: relative; z-index: 1;">
                <div style="background-color: rgba(54, 177, 220, 0.1); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                    <i class="fas fa-check-circle" style="color: #36b1dc; font-size: 20px;"></i>
                </div>
                <div style="flex: 1;">
                    <span style="color: #2c3e50; font-size: 16px; line-height: 1.6; font-weight: 500; display: block;">Good news! As a citizen of <strong style="color: #36b1dc;"><?php echo esc_html($country_name); ?></strong>, you do not need a visa to enter Romania for the LCI 2025 AGM.</span>
                </div>
            </div>
        </div>

        <div class="mt-4" style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); overflow: hidden; margin-bottom: 24px;">
            <div style="background-color: #36b1dc; padding: 20px; color: white; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1), transparent 70%);"></div>
                <h5 style="margin: 0; font-weight: 600; font-size: 18px; display: flex; align-items: center; color: white; position: relative; z-index: 1;">
                    <i class="fas fa-info-circle me-3" style="color: white;"></i> Entry Requirements
                </h5>
            </div>
            <div style="padding: 24px; position: relative;">
                <div style="position: absolute; bottom: 0; left: 0; width: 120px; height: 120px; background: radial-gradient(circle at bottom left, rgba(54, 177, 220, 0.03), transparent 70%);"></div>
                <p style="color: #2c3e50; font-size: 16px; margin-bottom: 20px; position: relative; z-index: 1;">While you don't need a visa, please ensure you have the following:</p>
                <div style="position: relative; z-index: 1;">
                    <div style="display: flex; align-items: flex-start; margin-bottom: 16px; background-color: #f8f9fa; border-radius: 12px; padding: 16px; transition: all 0.3s ease;">
                        <div style="background-color: rgba(54, 177, 220, 0.1); width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                            <i class="fas fa-passport" style="color: #36b1dc; font-size: 20px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="color: #2c3e50; font-size: 16px; line-height: 1.6; font-weight: 500;">A valid passport or travel document</div>
                            <div style="color: #7f8c8d; font-size: 14px; margin-top: 4px;">Valid for at least 3 months beyond your planned stay</div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: flex-start; background-color: #f8f9fa; border-radius: 12px; padding: 16px; transition: all 0.3s ease;">
                        <div style="background-color: rgba(54, 177, 220, 0.1); width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                            <i class="fas fa-smile-beam" style="color: #36b1dc; font-size: 20px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <div style="color: #2c3e50; font-size: 16px; line-height: 1.6; font-weight: 500;">Joy and happiness</div>
                            <div style="color: #7f8c8d; font-size: 14px; margin-top: 4px;">For an amazing event in Romania!</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php else : ?>
        <!-- Visa Alert - 2025 UX/UI Style -->
        <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; position: relative; overflow: hidden; margin-bottom: 30px; border: 1px solid rgba(255, 255, 255, 0.8);">
            <!-- Decorative elements -->
            <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 193, 7, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
            <div class="position-absolute" style="bottom: -50px; left: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 193, 7, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

            <!-- Alert content with 3D effect -->
            <div style="position: relative; z-index: 1; transform: perspective(1000px) rotateX(2deg); transform-style: preserve-3d;">
                <div style="display: flex; align-items: flex-start; margin-bottom: 20px;">
                    <!-- 3D floating icon -->
                    <div style="position: relative; width: 60px; height: 60px; margin-right: 20px; flex-shrink: 0; transform-style: preserve-3d; perspective: 1000px;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1)); border-radius: 16px; transform: translateZ(-5px); box-shadow: 0 10px 30px rgba(255, 193, 7, 0.2);"></div>
                        <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7)); border-radius: 16px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.9), inset 0 -2px 3px rgba(0, 0, 0, 0.05); transform: translateZ(5px);">
                            <i class="fas fa-exclamation-triangle" style="color: #ffc107; font-size: 24px; filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.1));"></i>
                        </div>
                    </div>

                    <!-- Alert text -->
                    <div style="flex: 1;">
                        <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin-bottom: 10px; letter-spacing: -0.3px;">Visa Requirement</h3>
                        <p style="color: #2c3e50; font-size: 16px; line-height: 1.6; font-weight: 400; margin: 0;">As a citizen of <strong style="color: #ffc107; font-weight: 700;"><?php echo esc_html($country_name); ?></strong>, you will need a visa to enter Romania for the LCI 2025 AGM.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invitation Letter Buttons - Outside the alert box -->
        <?php
        // Check if user already has a generated invitation letter
        $user_id = get_current_user_id();
        $invitation_letter_url = get_user_meta($user_id, 'invitation_letter_pdf', true);

        if (empty($invitation_letter_url)) {
            // No invitation letter exists, show generate button
        ?>
            <div style="margin-bottom: 30px; text-align: center;">
                <a href="<?php echo add_query_arg('tab', 'generate-invitation'); ?>" class="position-relative" style="display: inline-block; text-decoration: none; transform-style: preserve-3d; perspective: 1000px;">
                    <!-- Button shadow/base -->
                    <div style="position: absolute; top: 5px; left: 0; right: 0; height: 100%; background: linear-gradient(to bottom, #e5a800, #d69e00); border-radius: 16px; transform: translateZ(-5px); filter: blur(2px); opacity: 0.7; z-index: -1;"></div>

                    <!-- Button main -->
                    <div style="background: linear-gradient(135deg, #ffc107, #e5a800); color: white; font-weight: 700; text-transform: uppercase; letter-spacing: 1px; padding: 20px 40px; border-radius: 16px; box-shadow: 0 10px 30px rgba(255, 193, 7, 0.3), inset 0 2px 0 rgba(255, 255, 255, 0.3); text-align: center; transform: translateZ(0); transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.2); min-width: 300px;">
                        <div class="d-flex align-items-center justify-content-center">
                            <div style="background: rgba(255, 255, 255, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.3);">
                                <i class="fas fa-file-pdf" style="color: white; font-size: 20px;"></i>
                            </div>
                            <span style="font-size: 18px;">GENERATE INVITATION LETTER</span>
                        </div>
                    </div>
                </a>
            </div>
        <?php
        } else {
            // Invitation letter exists, show download and manage buttons
        ?>
            <div style="margin-bottom: 30px;">
                <div class="row justify-content-center">
                    <div class="col-md-6 mb-3">
                        <a href="<?php echo esc_url($invitation_letter_url); ?>" target="_blank" class="position-relative" style="display: block; text-decoration: none; transform-style: preserve-3d; perspective: 1000px;">
                            <!-- Button shadow/base -->
                            <div style="position: absolute; top: 5px; left: 0; right: 0; height: 100%; background: linear-gradient(to bottom, #2980b9, #2573a7); border-radius: 16px; transform: translateZ(-5px); filter: blur(2px); opacity: 0.7; z-index: -1;"></div>

                            <!-- Button main -->
                            <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; font-weight: 700; text-transform: uppercase; letter-spacing: 1px; padding: 20px; border-radius: 16px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.3), inset 0 2px 0 rgba(255, 255, 255, 0.3); text-align: center; transform: translateZ(0); transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.2);">
                                <div class="d-flex align-items-center justify-content-center">
                                    <div style="background: rgba(255, 255, 255, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.3);">
                                        <i class="fas fa-download" style="color: white; font-size: 20px;"></i>
                                    </div>
                                    <span style="font-size: 16px;">DOWNLOAD INVITATION LETTER</span>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="<?php echo add_query_arg('tab', 'generate-invitation'); ?>" class="position-relative" style="display: block; text-decoration: none; transform-style: preserve-3d; perspective: 1000px;">
                            <!-- Button shadow/base -->
                            <div style="position: absolute; top: 5px; left: 0; right: 0; height: 100%; background: linear-gradient(to bottom, #e9ecef, #dee2e6); border-radius: 16px; transform: translateZ(-5px); filter: blur(2px); opacity: 0.7; z-index: -1;"></div>

                            <!-- Button main -->
                            <div style="background: linear-gradient(135deg, #ffffff, #f8f9fa); color: #36b1dc; font-weight: 700; text-transform: uppercase; letter-spacing: 1px; padding: 20px; border-radius: 16px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), inset 0 2px 0 rgba(255, 255, 255, 0.5); text-align: center; transform: translateZ(0); transition: all 0.3s ease; border: 1px solid rgba(54, 177, 220, 0.3);">
                                <div class="d-flex align-items-center justify-content-center">
                                    <div style="background: rgba(54, 177, 220, 0.1); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.5);">
                                        <i class="fas fa-cog" style="color: #36b1dc; font-size: 20px;"></i>
                                    </div>
                                    <span style="font-size: 16px;">MANAGE INVITATION LETTER</span>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        <?php
        }
        ?>
        </div>

        <div class="mt-4" style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); overflow: hidden; margin-bottom: 24px;">
            <div style="background-color: #ffc107; padding: 20px; color: white; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1), transparent 70%);"></div>
                <h5 style="margin: 0; font-weight: 600; font-size: 18px; display: flex; align-items: center; color: white; position: relative; z-index: 1;">
                    <i class="fas fa-passport me-3" style="color: white;"></i> Visa Application Process
                </h5>
            </div>
            <div style="padding: 24px; position: relative;">
                <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); margin-bottom: 24px; position: relative; overflow: hidden; border: 1px solid rgba(76, 175, 80, 0.2);">
                    <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle at top right, rgba(76, 175, 80, 0.05), transparent 70%);"></div>
                    <div style="position: absolute; bottom: 0; left: 0; width: 80px; height: 80px; background: radial-gradient(circle at bottom left, rgba(76, 175, 80, 0.03), transparent 70%);"></div>
                    <div style="display: flex; padding: 20px; position: relative; z-index: 1;">
                        <div style="background-color: rgba(76, 175, 80, 0.1); width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                            <i class="fas fa-laptop" style="color: #4caf50; font-size: 20px;"></i>
                        </div>
                        <div style="flex: 1;">
                            <h6 style="font-weight: 600; color: #2c3e50; margin-bottom: 8px; font-size: 18px;">Romania E-Visa Program</h6>
                            <p style="color: #7f8c8d; font-size: 15px; margin-bottom: 12px; line-height: 1.6;">Romania offers an electronic visa application system that simplifies the process.</p>
                            <a href="https://evisa.mae.ro/home?lang=en-US#" target="_blank" style="display: inline-flex; align-items: center; background-color: #4caf50; color: white; font-weight: 500; text-decoration: none; font-size: 14px; padding: 8px 16px; border-radius: 8px; transition: all 0.3s ease;">
                                Visit the official e-visa portal <i class="fas fa-external-link-alt ms-2" style="font-size: 12px;"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <p style="color: #2c3e50; font-size: 16px; margin-bottom: 24px; position: relative; z-index: 1;">Please follow these steps to apply for your Romanian visa:</p>

                <div style="position: relative; z-index: 1; margin-bottom: 24px;">
                    <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); margin-bottom: 24px; overflow: hidden; position: relative;">
                        <div style="position: absolute; top: 0; right: 0; width: 80px; height: 80px; background: radial-gradient(circle at top right, rgba(255, 193, 7, 0.05), transparent 70%);"></div>
                        <div style="display: flex; padding: 24px; position: relative; z-index: 1;">
                            <div style="background-color: rgba(255, 193, 7, 0.1); width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                                <span style="color: #ffc107; font-weight: bold; font-size: 20px;">1</span>
                            </div>
                            <div style="flex: 1;">
                                <h6 style="font-weight: 600; color: #2c3e50; margin-bottom: 8px; font-size: 18px;"><i class="fas fa-search me-2" style="color: #ffc107;"></i> Find your nearest Romanian embassy or consulate</h6>
                                <p style="color: #7f8c8d; font-size: 15px; line-height: 1.6; margin-bottom: 0;">Contact the Romanian embassy or consulate in your country to get specific information about the visa application process.</p>
                            </div>
                        </div>
                    </div>

                    <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); margin-bottom: 24px; overflow: hidden; position: relative;">
                        <div style="position: absolute; top: 0; right: 0; width: 80px; height: 80px; background: radial-gradient(circle at top right, rgba(255, 193, 7, 0.05), transparent 70%);"></div>
                        <div style="display: flex; padding: 24px; position: relative; z-index: 1;">
                            <div style="background-color: rgba(255, 193, 7, 0.1); width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                                <span style="color: #ffc107; font-weight: bold; font-size: 20px;">2</span>
                            </div>
                            <div style="flex: 1;">
                                <h6 style="font-weight: 600; color: #2c3e50; margin-bottom: 8px; font-size: 18px;"><i class="fas fa-file-alt me-2" style="color: #ffc107;"></i> Prepare your documents</h6>
                                <p style="color: #7f8c8d; font-size: 15px; line-height: 1.6; margin-bottom: 16px;">Typically, you will need:</p>
                                <div style="background-color: #f8f9fa; border-radius: 12px; padding: 20px;">
                                    <div style="display: flex; flex-direction: column; gap: 16px; color: #2c3e50; font-size: 15px;">
                                    <div style="display: flex; align-items: flex-start;">
                                        <div style="background-color: rgba(255, 193, 7, 0.1); width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-passport" style="color: #ffc107; font-size: 16px;"></i>
                                        </div>
                                        <div style="flex: 1; padding-top: 4px;">
                                            <span style="color: #2c3e50; line-height: 1.5; display: block;">Valid passport (valid for at least 3 months beyond your planned stay)</span>
                                        </div>
                                    </div>
                                    <div style="display: flex; align-items: flex-start;">
                                        <div style="background-color: rgba(255, 193, 7, 0.1); width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-file-alt" style="color: #ffc107; font-size: 16px;"></i>
                                        </div>
                                        <div style="flex: 1; padding-top: 4px;">
                                            <span style="color: #2c3e50; line-height: 1.5; display: block;">Visa application form</span>
                                        </div>
                                    </div>
                                    <div style="display: flex; align-items: flex-start;">
                                        <div style="background-color: rgba(255, 193, 7, 0.1); width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-id-card" style="color: #ffc107; font-size: 16px;"></i>
                                        </div>
                                        <div style="flex: 1; padding-top: 4px;">
                                            <span style="color: #2c3e50; line-height: 1.5; display: block;">Passport-sized photos</span>
                                        </div>
                                    </div>
                                    <div style="display: flex; align-items: flex-start;">
                                        <div style="background-color: rgba(255, 193, 7, 0.1); width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-heartbeat" style="color: #ffc107; font-size: 16px;"></i>
                                        </div>
                                        <div style="flex: 1; padding-top: 4px;">
                                            <span style="color: #2c3e50; line-height: 1.5; display: block;">Travel insurance</span>
                                        </div>
                                    </div>
                                    <div style="display: flex; align-items: flex-start;">
                                        <div style="background-color: rgba(255, 193, 7, 0.1); width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-hotel" style="color: #ffc107; font-size: 16px;"></i>
                                        </div>
                                        <div style="flex: 1; padding-top: 4px;">
                                            <span style="color: #2c3e50; line-height: 1.5; display: block;">Proof of accommodation in Romania</span>
                                        </div>
                                    </div>
                                    <div style="display: flex; align-items: flex-start;">
                                        <div style="background-color: rgba(255, 193, 7, 0.1); width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-money-bill-wave" style="color: #ffc107; font-size: 16px;"></i>
                                        </div>
                                        <div style="flex: 1; padding-top: 4px;">
                                            <span style="color: #2c3e50; line-height: 1.5; display: block;">Proof of sufficient financial means</span>
                                        </div>
                                    </div>
                                    <div style="display: flex; align-items: flex-start;">
                                        <div style="background-color: rgba(255, 193, 7, 0.1); width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-plane" style="color: #ffc107; font-size: 16px;"></i>
                                        </div>
                                        <div style="flex: 1; padding-top: 4px;">
                                            <span style="color: #2c3e50; line-height: 1.5; display: block;">Return ticket or travel itinerary</span>
                                        </div>
                                    </div>
                                    <div style="display: flex; align-items: flex-start;">
                                        <div style="background-color: rgba(255, 193, 7, 0.1); width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-ticket-alt" style="color: #ffc107; font-size: 16px;"></i>
                                        </div>
                                        <div style="flex: 1; padding-top: 4px;">
                                            <span style="color: #2c3e50; line-height: 1.5; display: block;">LCI 2025 AGM registration confirmation</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); overflow: hidden; position: relative;">
                        <div style="position: absolute; top: 0; right: 0; width: 80px; height: 80px; background: radial-gradient(circle at top right, rgba(255, 193, 7, 0.05), transparent 70%);"></div>
                        <div style="display: flex; padding: 24px; position: relative; z-index: 1;">
                            <div style="background-color: rgba(255, 193, 7, 0.1); width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                                <span style="color: #ffc107; font-weight: bold; font-size: 20px;">3</span>
                            </div>
                            <div style="flex: 1;">
                                <h6 style="font-weight: 600; color: #2c3e50; margin-bottom: 8px; font-size: 18px;"><i class="fas fa-calendar-alt me-2" style="color: #ffc107;"></i> Submit your application early</h6>
                                <p style="color: #7f8c8d; font-size: 15px; line-height: 1.6; margin-bottom: 0;">Visa processing can take several weeks. We recommend applying at least 8-12 weeks before your planned travel date.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                <div style="background-color: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); padding: 24px; margin-top: 24px; border: 1px solid rgba(255, 255, 255, 0.3);">
                    <h3 style="font-weight: 700; color: #2c3e50; margin-bottom: 16px; font-size: 20px; text-align: center;">Supporting Documents</h3>
                    <p style="color: #7f8c8d; font-size: 16px; margin-bottom: 24px; text-align: center;">You can download the following documents to support your visa application:</p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 24px; justify-items: center;">
                        <!-- Document 1: Invitation Letter -->
                        <div style="background-color: rgba(248, 249, 250, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 24px; text-align: center; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05); transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.4); width: 100%; max-width: 320px;">
                            <div style="width: 80px; height: 80px; background-color: rgba(220, 53, 69, 0.1); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; border: 1px solid rgba(220, 53, 69, 0.2);">
                                <i class="fas fa-file-pdf" style="color: #dc3545; font-size: 32px;"></i>
                            </div>
                            <h4 style="font-weight: 600; color: #2c3e50; margin-bottom: 20px; font-size: 20px;">Official Invitation Letter</h4>

                            <?php if (empty($invitation_letter_url)) : ?>
                                <a href="#" id="generate-invitation-letter-card" style="display: inline-block; background-color: rgba(255, 193, 7, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); color: white; padding: 12px 24px; border-radius: 12px; font-size: 16px; font-weight: 600; text-decoration: none; transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.2); box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);">
                                    <i class="fas fa-file-pdf me-2"></i> Generate Letter
                                </a>
                            <?php else : ?>
                                <a href="<?php echo esc_url($invitation_letter_url); ?>" target="_blank" style="display: inline-block; background-color: rgba(54, 177, 220, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); color: white; padding: 12px 24px; border-radius: 12px; font-size: 16px; font-weight: 600; text-decoration: none; transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.2); box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3);">
                                    <i class="fas fa-download me-2"></i> Download
                                </a>
                            <?php endif; ?>
                        </div>

                        <!-- Document 2: Registration Confirmation -->
                        <div style="background-color: rgba(248, 249, 250, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 24px; text-align: center; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05); transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.4); width: 100%; max-width: 320px;">
                            <div style="width: 80px; height: 80px; background-color: rgba(220, 53, 69, 0.1); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; border: 1px solid rgba(220, 53, 69, 0.2);">
                                <i class="fas fa-file-pdf" style="color: #dc3545; font-size: 32px;"></i>
                            </div>
                            <h4 style="font-weight: 600; color: #2c3e50; margin-bottom: 20px; font-size: 20px;">Registration Confirmation</h4>
                            <a href="#" style="display: inline-block; background-color: rgba(54, 177, 220, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); color: white; padding: 12px 24px; border-radius: 12px; font-size: 16px; font-weight: 600; text-decoration: none; transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.2); box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3);">
                                <i class="fas fa-download me-2"></i> Download
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4" style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); overflow: hidden; margin-bottom: 24px; position: relative;">
            <div style="background-color: #36b1dc; padding: 24px; color: white; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1), transparent 70%);"></div>
                <div style="display: flex; align-items: center; position: relative; z-index: 1;">
                    <div style="background-color: rgba(255, 255, 255, 0.2); width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                        <i class="fas fa-question-circle" style="color: white; font-size: 24px;"></i>
                    </div>
                    <h5 style="margin: 0; font-weight: 600; font-size: 20px; color: white;">Need Assistance?</h5>
                </div>
            </div>
            <div style="padding: 24px; text-align: center; position: relative;">
                <div style="position: absolute; bottom: 0; left: 0; width: 120px; height: 120px; background: radial-gradient(circle at bottom left, rgba(54, 177, 220, 0.03), transparent 70%);"></div>
                <p style="color: #2c3e50; font-size: 16px; margin-bottom: 24px; line-height: 1.6; position: relative; z-index: 1;">If you need any assistance with your visa application, please don't hesitate to contact us:</p>
                <div style="display: flex; gap: 16px; justify-content: center; flex-wrap: wrap; position: relative; z-index: 1;">
                    <a href="mailto:<EMAIL>" style="display: inline-flex; align-items: center; justify-content: center; padding: 16px 24px; background-color: #36b1dc; color: white; font-weight: 600; border-radius: 12px; text-decoration: none; box-shadow: 0 8px 15px rgba(54, 177, 220, 0.2); transition: all 0.3s ease; min-width: 200px; font-size: 16px;">
                        <i class="fas fa-envelope me-2" style="color: white;"></i> Email Visa Support
                    </a>
                    <a href="<?php echo add_query_arg('tab', 'support'); ?>" style="display: inline-flex; align-items: center; justify-content: center; padding: 16px 24px; background-color: white; color: #36b1dc; font-weight: 600; border-radius: 12px; text-decoration: none; box-shadow: 0 8px 15px rgba(0, 0, 0, 0.05); transition: all 0.3s ease; border: 2px solid #36b1dc; min-width: 200px; font-size: 16px;">
                        <i class="fas fa-headset me-2" style="color: #36b1dc;"></i> Contact Support
                    </a>
                </div>
            </div>
        </div>


    <?php endif; ?>
</div>

<style>
    .my-visa-container h2 {
        color: var(--primary-color, #36b1dc);
        font-weight: 600;
    }

    .visa-steps {
        counter-reset: visa-step;
        list-style: none;
        padding-left: 0;
    }

    .visa-steps li {
        position: relative;
        margin-bottom: 1.5rem;
        padding-left: 2.5rem;
    }

    .visa-steps li:before {
        counter-increment: visa-step;
        content: counter(visa-step);
        position: absolute;
        left: 0;
        top: 0;
        width: 1.8rem;
        height: 1.8rem;
        background-color: var(--primary-color, #36b1dc);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .visa-steps h6 {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .btn-primary {
        background-color: var(--primary-color, #36b1dc);
        border-color: var(--primary-color, #36b1dc);
    }

    .btn-primary:hover {
        background-color: #2a8fb3;
        border-color: #2a8fb3;
    }

    .btn-outline-primary {
        color: var(--primary-color, #36b1dc);
        border-color: var(--primary-color, #36b1dc);
    }

    .btn-outline-primary:hover {
        background-color: var(--primary-color, #36b1dc);
        border-color: var(--primary-color, #36b1dc);
    }

    .text-primary {
        color: var(--primary-color, #36b1dc) !important;
    }

    .bg-primary {
        background-color: var(--primary-color, #36b1dc) !important;
    }
</style>
