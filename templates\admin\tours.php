<?php
// Pass missing counts to JavaScript
$missing_counts_json = json_encode($missing_counts);

// Check tour tables
$tour_tables = LCI_Tours::check_tour_tables();
$tour_tables_json = json_encode($tour_tables);
?>
<script type="text/javascript">
    // Make missing counts available to Alpine.js
    var lciToursMissingCounts = <?php echo $missing_counts_json; ?>;
    var lciTourTables = <?php echo $tour_tables_json; ?>;
</script>

<div class="wrap lci-admin-wrap">
    <div x-data="toursManager">
        <!-- Sync Modal -->
        <div x-show="showSyncModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center" x-transition>
            <div class="bg-white rounded-xl shadow-lg p-6 max-w-lg w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Tour Data Sync Required</h2>
                    <button @click="showSyncModal = false" class="text-gray-500 hover:text-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div class="mb-6">
                    <p class="text-gray-600 mb-4">The tour data tables need to be synchronized with the main participants database. This will ensure that all tour participants are properly tracked.</p>

                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    <span x-text="getMissingTablesMessage()"></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button @click="showSyncModal = false" class="btn btn-secondary">Cancel</button>
                    <button @click="syncAllTours()" class="btn btn-primary flex items-center" :disabled="isSyncingAll">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" :class="{'animate-spin': isSyncingAll}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        <span x-text="isSyncingAll ? 'Syncing...' : 'Sync All Tours'"></span>
                    </button>
                </div>
            </div>
        </div>
        <h1 class="text-3xl font-bold mb-6">Tour Participants</h1>

        <!-- Tabs -->
        <div class="bg-white rounded-xl shadow-neumorph mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex -mb-px">
                    <button
                        @click="activeTab = 'main_pretour'"
                        :class="{'border-primary text-primary': activeTab === 'main_pretour', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'main_pretour'}"
                        class="py-4 px-6 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200 ease-in-out"
                    >
                        Main Pretour
                        <span
                            x-show="missingCounts.main_pretour > 0"
                            class="ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800"
                            x-text="missingCounts.main_pretour"
                        ></span>
                    </button>

                    <button
                        @click="activeTab = 'legends_wildlife'"
                        :class="{'border-primary text-primary': activeTab === 'legends_wildlife', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'legends_wildlife'}"
                        class="py-4 px-6 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200 ease-in-out"
                    >
                        Legends & Wildlife Escape
                        <span
                            x-show="missingCounts.legends_wildlife > 0"
                            class="ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800"
                            x-text="missingCounts.legends_wildlife"
                        ></span>
                    </button>

                    <button
                        @click="activeTab = 'royal_elegance'"
                        :class="{'border-primary text-primary': activeTab === 'royal_elegance', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'royal_elegance'}"
                        class="py-4 px-6 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200 ease-in-out"
                    >
                        Royal Elegance & Sparkling Delights
                        <span
                            x-show="missingCounts.royal_elegance > 0"
                            class="ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800"
                            x-text="missingCounts.royal_elegance"
                        ></span>
                    </button>

                    <button
                        @click="activeTab = 'brasov_highlights'"
                        :class="{'border-primary text-primary': activeTab === 'brasov_highlights', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'brasov_highlights'}"
                        class="py-4 px-6 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200 ease-in-out"
                    >
                        Brasov Highlights
                        <span
                            x-show="missingCounts.brasov_highlights > 0"
                            class="ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800"
                            x-text="missingCounts.brasov_highlights"
                        ></span>
                    </button>
                </nav>
            </div>
        </div>

        <!-- Loading state -->
        <div x-show="isLoading" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
            <div class="spinner"></div>
            <span class="loading-text">Loading tour data...</span>
        </div>

        <!-- Missing participants notification -->
        <div
            x-show="!isLoading && getMissingCount() > 0"
            class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6"
        >
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        <span class="font-medium">Attention!</span> There are <span x-text="getMissingCount()"></span> participants who have purchased this tour but are not in the tour list.
                    </p>
                    <div class="mt-2">
                        <button
                            @click="syncTour()"
                            class="btn btn-sm btn-warning"
                            :disabled="isSyncing"
                        >
                            <span x-show="!isSyncing">Sync Now</span>
                            <span x-show="isSyncing" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Syncing...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tour participants table -->
        <div x-show="!isLoading" class="bg-white rounded-xl shadow-neumorph p-6">
            <div class="flex flex-col space-y-4 mb-6">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-medium text-gray-800" x-text="getTourTitle()"></h2>

                    <!-- Data source indicator -->
                    <div class="flex items-center">
                        <span class="text-sm text-gray-600 mr-2">Data source:</span>
                        <span
                            class="px-2 py-1 rounded text-xs font-medium"
                            :class="{
                                'bg-blue-100 text-blue-800': dataSource === 'database',
                                'bg-green-100 text-green-800': dataSource === 'woocommerce'
                            }"
                            x-text="dataSource === 'database' ? 'Database' : 'WooCommerce'"
                        ></span>

                        <!-- Toggle data source button -->
                        <button
                            @click="toggleDataSource()"
                            class="ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                            :class="{'bg-blue-100 hover:bg-blue-200': showWoocommerceData}"
                        >
                            <span x-text="showWoocommerceData ? 'Show Database Data' : 'Show WooCommerce Data'"></span>
                        </button>
                    </div>
                </div>

                <!-- Data source notification for WooCommerce data -->
                <div x-show="dataSource === 'woocommerce' && tableExists && !tableHasData" class="bg-blue-50 border-l-4 border-blue-400 p-3">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                Showing data from WooCommerce orders. Click "Sync Now" to save this data to the database.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <div class="relative">
                        <input
                            type="text"
                            x-model="searchQuery"
                            @input="filterParticipants()"
                            placeholder="Search participants..."
                            class="form-input pl-10 w-full md:w-64"
                        >
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                        </svg>
                    </div>

                    <button
                        @click="syncTour()"
                        class="btn btn-primary flex items-center"
                        :disabled="isSyncing"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" :class="{'animate-spin': isSyncing}">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        <span x-text="isSyncing ? 'Syncing...' : 'Sync Tour'"></span>
                    </button>

                    <button
                        @click="clearAndResyncTour()"
                        class="btn btn-danger flex items-center ml-2"
                        :disabled="isSyncing"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" :class="{'animate-spin': isSyncing}">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        <span x-text="isSyncing ? 'Processing...' : 'Clear & Resync'"></span>
                    </button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="text-left bg-gray-50">
                            <th class="p-3 font-medium text-gray-600">ID</th>
                            <th class="p-3 font-medium text-gray-600">Name</th>
                            <th class="p-3 font-medium text-gray-600">Email</th>
                            <th class="p-3 font-medium text-gray-600">Phone</th>
                            <th class="p-3 font-medium text-gray-600">Country</th>
                            <th class="p-3 font-medium text-gray-600">Order Date</th>
                            <th class="p-3 font-medium text-gray-600">Payment Status</th>
                            <th class="p-3 font-medium text-gray-600">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr x-show="isLoading">
                            <td colspan="9" class="p-3 text-center text-gray-500">
                                <div class="flex justify-center items-center space-x-2">
                                    <svg class="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>Loading participants...</span>
                                </div>
                            </td>
                        </tr>

                        <template x-if="!isLoading && Array.isArray(paginatedParticipants) && paginatedParticipants.length > 0">
                            <template x-for="(participant, index) in paginatedParticipants" :key="index">
                                <tr class="border-t border-gray-200 hover:bg-gray-50">
                                    <td class="p-3">
                                        <div class="font-medium" x-text="participant.id || participant.participant_id || '-'"></div>
                                    </td>
                                    <td class="p-3">
                                        <div class="font-medium text-primary" x-text="participant.unique_reg_id || '-'"></div>
                                    </td>
                                    <td class="p-3" x-text="(participant.first_name || '') + ' ' + (participant.last_name || '')"></td>
                                    <td class="p-3" x-text="participant.email || '-'"></td>
                                    <td class="p-3" x-text="participant.phone || '-'"></td>
                                    <td class="p-3" x-text="participant.country || '-'"></td>
                                    <td class="p-3" x-text="participant.order_date ? formatDate(participant.order_date) : '-'"></td>
                                    <td class="p-3">
                                        <span
                                            :class="{
                                                'px-2 py-1 rounded-full text-xs font-medium': true,
                                                'bg-green-100 text-green-800': participant.payment_status === 'completed',
                                                'bg-yellow-100 text-yellow-800': participant.payment_status === 'processing',
                                                'bg-red-100 text-red-800': participant.payment_status === 'on-hold',
                                                'bg-blue-100 text-blue-800': participant.payment_status === 'pending',
                                                'bg-gray-100 text-gray-800': !participant.payment_status || !['completed', 'processing', 'on-hold', 'pending'].includes(participant.payment_status)
                                            }"
                                            x-text="participant.payment_status || 'unknown'"
                                        ></span>
                                    </td>
                                    <td class="p-3">
                                        <a :href="'admin.php?page=lci-participant-detail&id=' + (participant.participant_id || participant.id)" class="btn btn-sm btn-primary">
                                            View
                                        </a>
                                    </td>
                                </tr>
                            </template>
                        </template>

                        <tr x-show="!isLoading && (!Array.isArray(paginatedParticipants) || paginatedParticipants.length === 0)">
                            <td colspan="9" class="p-3 text-center text-gray-500">
                                No participants found.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div x-show="!isLoading && Array.isArray(filteredParticipants) && filteredParticipants.length > 0" class="mt-4 flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    Showing <span x-text="Math.min((currentPage - 1) * perPage + 1, filteredParticipants.length)"></span>
                    to <span x-text="Math.min(currentPage * perPage, filteredParticipants.length)"></span>
                    of <span x-text="filteredParticipants.length"></span> participants
                </div>

                <div class="flex space-x-2">
                    <button
                        @click="currentPage--"
                        :disabled="currentPage === 1"
                        class="btn btn-sm btn-secondary"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                    >
                        Previous
                    </button>

                    <button
                        @click="currentPage++"
                        :disabled="currentPage === totalPages"
                        class="btn btn-sm btn-secondary"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
                    >
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
