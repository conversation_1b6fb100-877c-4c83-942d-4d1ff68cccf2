<?php
/**
 * Vanilla Mini Cart Template
 */
?>
<!-- Mini Cart Button -->
<button type="button" id="mini-cart-button" class="btn btn-primary d-flex align-items-center" style="color: #fff !important;">
    <i class="fas fa-shopping-bag me-2" style="color: #fff !important;"></i>
    <span class="mini-cart-button-text"><?php echo wp_kses_post($cart_total); ?></span>
    <?php if ($cart_count > 0): ?>
    <span id="mini-cart-count" class="badge rounded-pill ms-2" style="background-color: #fab33a;">
        <?php echo esc_html($cart_count); ?>
    </span>
    <?php else: ?>
    <span id="mini-cart-count" class="badge rounded-pill ms-2" style="background-color: #fab33a; display: none;">
        0
    </span>
    <?php endif; ?>
</button>

<!-- Mini Cart Modal -->
<div id="mini-cart-backdrop" class="lci-modal-backdrop" style="display: none;">
    <div id="mini-cart-modal" class="lci-modal lci-mini-cart-modal">
        <div class="lci-modal-header">
            <div class="lci-modal-logo-container">
                <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp" alt="LCI 2025 Logo" class="lci-modal-logo">
            </div>
            <h2 class="lci-modal-title">
                <i class="fas fa-shopping-bag me-2"></i> My LCI Goodies Bag
            </h2>
            <button id="mini-cart-close" class="lci-modal-close">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="lci-modal-body p-0">
            <div id="mini-cart-items" class="mini-cart-items-container">
                <!-- Cart items will be loaded here via AJAX -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading your items...</p>
                </div>
            </div>
        </div>

        <div class="lci-modal-footer">
            <div class="d-flex justify-content-between align-items-center w-100">
                <div class="mini-cart-total-container">
                    Total: <span id="mini-cart-total" class="mini-cart-modal-total"><?php echo wp_kses_post($cart_total); ?></span>
                </div>
                <div>
                    <button id="continue-shopping-btn" class="lci-btn lci-btn-secondary me-2">Continue Shopping</button>
                    <a href="<?php echo add_query_arg('tab', 'payment', remove_query_arg('product_id')); ?>" class="lci-btn lci-btn-primary">
                        <i class="fas fa-credit-card me-1"></i> Checkout
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
