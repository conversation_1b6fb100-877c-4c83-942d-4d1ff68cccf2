<?php
/**
 * Support Ticket Details view for LCI 2025 Dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_email = $current_user->user_email;

// Get ticket ID from URL
$ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : 0;

// Verify ticket belongs to user
if ($ticket_id && class_exists('LCI_Support')) {
    $ticket = LCI_Support::get_ticket_details($ticket_id);
    if (!$ticket || $ticket['user_id'] != $user_id) {
        $ticket_id = 0;
    }
}
?>

<script>
// Immediately hide all elements with x-show attribute
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('[x-show]').forEach(function(el) {
        el.style.display = 'none';
    });
});
</script>

<div class="support-container" x-data="supportTickets" x-init="loadTicketDetails(<?php echo $ticket_id; ?>)">
    <!-- Header Section - 2025 UX/UI Style -->
    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 40px 20px; position: relative; overflow: hidden; border-radius: 0 0 30px 30px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2); margin-bottom: 40px;">
        <!-- Animated background elements -->
        <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%); border-radius: 50%; animation: pulse 8s infinite alternate;"></div>
        <div class="position-absolute" style="bottom: -30px; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%); border-radius: 50%; animation: float 12s infinite alternate;"></div>
        <div class="position-absolute" style="top: 20%; right: 20%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%); border-radius: 50%; animation: float 7s infinite alternate-reverse;"></div>

        <!-- Glassmorphism overlay -->
        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>

        <div class="container position-relative" style="z-index: 2;">
            <div class="d-flex align-items-center justify-content-center flex-column text-center">
                <!-- Modern 3D icon with shadow and glow -->
                <div style="background: rgba(255, 255, 255, 0.2); width: 80px; height: 80px; border-radius: 24px; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); transform: perspective(500px) rotateX(10deg); border: 1px solid rgba(255, 255, 255, 0.3);">
                    <div style="position: relative;">
                        <i class="fas fa-ticket-alt" style="color: white; font-size: 36px; filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));"></i>
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.8), transparent 70%); filter: blur(8px); opacity: 0.5; animation: glow 3s infinite alternate;"></div>
                    </div>
                </div>

                <!-- Typography with modern styling -->
                <h1 style="color: white; font-size: 32px; font-weight: 800; margin: 0; letter-spacing: -0.5px; text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">Ticket Details</h1>
                <p style="color: rgba(255, 255, 255, 0.9); font-size: 18px; margin: 0; margin-top: 8px; max-width: 600px; font-weight: 300; letter-spacing: 0.5px;">View and respond to your support conversation</p>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div x-show="formSuccess" x-transition class="container mb-4">
        <div style="background: rgba(76, 175, 80, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px; border: 1px solid rgba(76, 175, 80, 0.2); display: flex; align-items: center;">
            <div style="background: rgba(76, 175, 80, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                <i class="fas fa-check-circle" style="color: #4caf50; font-size: 20px;"></i>
            </div>
            <div style="flex: 1;">
                <p style="color: #2c3e50; margin: 0;" x-text="successMessage"></p>
            </div>
            <button @click="formSuccess = false" style="background: none; border: none; color: #4caf50; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div x-show="formError" x-transition class="container mb-4">
        <div style="background: rgba(244, 67, 54, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px; border: 1px solid rgba(244, 67, 54, 0.2); display: flex; align-items: center;">
            <div style="background: rgba(244, 67, 54, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                <i class="fas fa-exclamation-circle" style="color: #f44336; font-size: 20px;"></i>
            </div>
            <div style="flex: 1;">
                <p style="color: #2c3e50; margin: 0;" x-text="errorMessage"></p>
            </div>
            <button @click="formError = false" style="background: none; border: none; color: #f44336; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <!-- Back to tickets button -->
                <div class="mb-4">
                    <a href="<?php echo add_query_arg('tab', 'support'); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i> Back to My Tickets
                    </a>
                </div>

                <!-- No loading indicator -->

                <!-- Ticket Details - 2025 UX/UI Style -->
                <div x-show="activeTicket" style="display: none;" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden;">
                    <!-- Decorative elements -->
                    <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                    <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                    <!-- Ticket header -->
                    <div class="position-relative" style="z-index: 1;">
                        <div class="d-flex align-items-center justify-content-between mb-2 flex-wrap">
                            <div class="d-flex align-items-center">
                                <span class="ticket-status me-3" :class="activeTicket && activeTicket.status ? getStatusClass(activeTicket.status) : ''" x-text="activeTicket && activeTicket.status ? getStatusText(activeTicket.status) : ''" style="font-size: 12px; padding: 4px 12px; border-radius: 50px; font-weight: 600;"></span>
                                <span style="color: #94a3b8; font-size: 14px;" x-text="activeTicket && activeTicket.ticket_id ? 'Ticket #' + activeTicket.ticket_id : ''"></span>
                            </div>
                        </div>

                        <h2 style="color: #2c3e50; font-size: 24px; font-weight: 700; margin: 0; letter-spacing: -0.3px;" x-text="activeTicket && activeTicket.subject ? activeTicket.subject : ''"></h2>

                        <div class="d-flex flex-wrap mt-3 mb-4">
                            <div class="me-4 mb-2">
                                <span style="color: #94a3b8;">Created:</span> <span x-text="activeTicket && activeTicket.created_at ? formatDate(activeTicket.created_at) : ''"></span>
                            </div>
                            <div class="me-4 mb-2">
                                <span style="color: #94a3b8;">Last Updated:</span> <span x-text="activeTicket && activeTicket.updated_at ? formatDate(activeTicket.updated_at) : ''"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Conversation thread -->
                    <div class="position-relative" style="z-index: 1;">
                        <!-- Original message -->
                        <div style="background: rgba(255, 255, 255, 0.5); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 20px; border: 1px solid rgba(255, 255, 255, 0.8); margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <span style="color: #2c3e50; font-weight: 600; font-size: 16px;">You</span>
                                    <span style="color: #94a3b8; font-size: 12px; margin-left: 8px;" x-text="activeTicket && activeTicket.created_at ? formatDate(activeTicket.created_at) : ''"></span>
                                </div>
                            </div>
                            <div style="color: #4b5563; font-size: 15px; line-height: 1.6; white-space: pre-wrap;" x-text="activeTicket && activeTicket.message ? activeTicket.message : ''"></div>

                            <!-- Attachment if any -->
                            <div class="mt-3" x-show="activeTicket && activeTicket.attachment_url">
                                <div style="display: inline-block;">
                                    <a :href="activeTicket && activeTicket.attachment_url ? activeTicket.attachment_url : '#'" target="_blank" class="d-inline-flex align-items-center" style="text-decoration: none; background: rgba(54, 177, 220, 0.1); padding: 8px 16px; border-radius: 8px; color: #36b1dc; font-weight: 500; transition: all 0.3s ease;">
                                        <i class="fas fa-paperclip me-2"></i>
                                        <span>View Attachment</span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Replies -->
                        <template x-if="activeTicket && activeTicket.replies && activeTicket.replies.length > 0">
                            <div>
                                <h4 style="color: #2c3e50; font-size: 18px; font-weight: 600; margin-bottom: 16px;">Replies</h4>

                                <template x-for="(reply, index) in activeTicket.replies" :key="index">
                                    <div :class="reply.is_admin ? 'ms-0 ms-md-5' : ''" style="background: rgba(255, 255, 255, 0.5); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 20px; border: 1px solid rgba(255, 255, 255, 0.8); margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div>
                                                <span style="font-weight: 600; font-size: 16px;" :style="reply.is_admin ? 'color: #36b1dc;' : 'color: #2c3e50;'" x-text="reply.is_admin ? 'Support Team' : 'You'"></span>
                                                <span style="color: #94a3b8; font-size: 12px; margin-left: 8px;" x-text="formatDate(reply.created_at)"></span>
                                            </div>
                                        </div>
                                        <div style="color: #4b5563; font-size: 15px; line-height: 1.6; white-space: pre-wrap;" x-text="reply.message"></div>

                                        <!-- Attachment if any -->
                                        <div class="mt-3" x-show="reply.attachment_url">
                                            <div style="display: inline-block;">
                                                <a :href="reply.attachment_url" target="_blank" class="d-inline-flex align-items-center" style="text-decoration: none; background: rgba(54, 177, 220, 0.1); padding: 8px 16px; border-radius: 8px; color: #36b1dc; font-weight: 500; transition: all 0.3s ease;">
                                                    <i class="fas fa-paperclip me-2"></i>
                                                    <span>View Attachment</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </template>

                        <!-- No replies message -->
                        <template x-if="activeTicket && (!activeTicket.replies || activeTicket.replies.length === 0)">
                            <div class="text-center py-5">
                                <div style="background: rgba(255, 255, 255, 0.5); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);">
                                    <i class="fas fa-comments" style="color: #a0aec0; font-size: 32px;"></i>
                                </div>
                                <p style="color: #64748b; font-size: 16px; margin: 0;">No replies yet. Our support team will respond to your ticket soon.</p>
                            </div>
                        </template>

                        <!-- Reply Form -->
                        <div class="mt-5" x-show="activeTicket" style="display: none;">
                            <h4 style="color: #2c3e50; font-size: 18px; font-weight: 600; margin-bottom: 16px;">Add a Reply</h4>

                            <form id="reply-form" class="position-relative">
                                <!-- Message field with floating label -->
                                <div class="position-relative mb-4">
                                    <label for="reply-message" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Your Reply*</label>
                                    <textarea id="reply-message" x-model="replyMessage" rows="4" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none; resize: vertical;"></textarea>
                                </div>

                                <!-- Attachment field with floating label -->
                                <div class="position-relative mb-5">
                                    <label for="reply-attachment" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Attachment (optional)</label>
                                    <input type="file" id="reply-attachment" name="attachment" style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease;">
                                    <div style="margin-top: 8px; font-size: 14px; color: #64748b;">You can attach a file to provide more information (max 5MB).</div>
                                </div>

                                <!-- Submit Button - 3D effect -->
                                <div class="text-center">
                                    <button type="button" @click="submitReply" :disabled="isSubmitting" class="position-relative" style="background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; font-weight: 700; text-transform: uppercase; letter-spacing: 1px; padding: 16px 32px; border-radius: 16px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.3), inset 0 2px 0 rgba(255, 255, 255, 0.3); border: none; font-size: 16px; cursor: pointer; transform-style: preserve-3d; perspective: 1000px; transition: all 0.3s ease; overflow: hidden; min-width: 250px;">
                                        <!-- Button shadow/base -->
                                        <div class="position-absolute" style="top: 5px; left: 0; right: 0; height: 100%; background: linear-gradient(to bottom, #2980b9, #2573a7); border-radius: 16px; transform: translateZ(-5px); filter: blur(2px); opacity: 0.7; z-index: -1;"></div>

                                        <!-- Button content -->
                                        <div class="d-flex align-items-center justify-content-center">
                                            <template x-if="isSubmitting">
                                                <div class="spinner-border spinner-border-sm text-white me-2" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </template>
                                            <template x-if="!isSubmitting">
                                                <i class="fas fa-paper-plane me-2"></i>
                                            </template>
                                            <span x-text="isSubmitting ? 'SENDING...' : 'SEND REPLY'"></span>
                                        </div>

                                        <!-- Button hover effect -->
                                        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1)); opacity: 0; transition: opacity 0.3s ease; z-index: 1;" onmouseover="this.style.opacity='1';" onmouseout="this.style.opacity='0';"></div>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Error message if ticket not found -->
                <div x-show="!activeTicket" style="display: none;" class="text-center py-5">
                    <div style="background: rgba(255, 255, 255, 0.5); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);">
                        <i class="fas fa-exclamation-triangle" style="color: #f44336; font-size: 32px;"></i>
                    </div>
                    <h3 style="color: #2c3e50; font-size: 24px; font-weight: 700; margin-bottom: 16px;">Ticket Not Found</h3>
                    <p style="color: #64748b; font-size: 16px; margin: 0 auto; max-width: 500px;">The ticket you're looking for doesn't exist or you don't have permission to view it.</p>
                    <div class="mt-4">
                        <a href="<?php echo add_query_arg('tab', 'support'); ?>" class="btn btn-primary">
                            <i class="fas fa-ticket-alt me-2"></i> View My Tickets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add animations -->
<style>
    @keyframes pulse {
        0% { transform: scale(1); opacity: 0.5; }
        100% { transform: scale(1.2); opacity: 0.2; }
    }
    @keyframes float {
        0% { transform: translateY(0) translateX(0); }
        50% { transform: translateY(-20px) translateX(10px); }
        100% { transform: translateY(10px) translateX(-10px); }
    }
    @keyframes glow {
        0% { opacity: 0.3; }
        100% { opacity: 0.7; }
    }
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    [x-cloak] {
        display: none !important;
    }
</style>

<script>
// Define the Alpine.js component
document.addEventListener('alpine:init', function() {
    // Make sure lciSupport is available
    if (typeof window.lciSupport === 'undefined') {
        console.error('lciSupport is not defined');
        window.lciSupport = {
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('lci_support_nonce'); ?>',
            messages: {
                ticketSubmitted: '<?php echo esc_js(__('Your ticket has been submitted successfully.', 'lci-2025-dashboard')); ?>',
                ticketError: '<?php echo esc_js(__('There was an error submitting your ticket. Please try again.', 'lci-2025-dashboard')); ?>',
                replySubmitted: '<?php echo esc_js(__('Your reply has been submitted successfully.', 'lci-2025-dashboard')); ?>',
                replyError: '<?php echo esc_js(__('There was an error submitting your reply. Please try again.', 'lci-2025-dashboard')); ?>'
            }
        };
    }

    Alpine.data('supportTickets', function() {
        return {
            activeTicket: null,
            replyMessage: '',
            isSubmitting: false,
            formSuccess: false,
            formError: false,
            errorMessage: '',
            successMessage: '',

            loadTicketDetails(ticketId) {
                if (!ticketId) {
                    this.activeTicket = null;
                    return;
                }

                fetch(window.lciSupport.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'lci_get_ticket_details',
                        nonce: window.lciSupport.nonce,
                        ticket_id: ticketId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.activeTicket = data.data.ticket;
                    } else {
                        this.showError(data.data.message || 'Error loading ticket details');
                    }
                })
                .catch(error => {
                    this.showError('Error loading ticket details. Please try again.');
                });
            },

            submitReply() {
                if (!this.replyMessage.trim()) {
                    this.showError('Please enter a reply message.');
                    return;
                }

                this.isSubmitting = true;

                const formData = new FormData();
                formData.append('action', 'lci_reply_to_ticket');
                formData.append('nonce', window.lciSupport.nonce);
                formData.append('ticket_id', this.activeTicket ? this.activeTicket.id : '');
                formData.append('message', this.replyMessage);

                const attachmentInput = document.getElementById('reply-attachment');
                if (attachmentInput && attachmentInput.files.length > 0) {
                    formData.append('attachment', attachmentInput.files[0]);
                }

                fetch(window.lciSupport.ajaxUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.showSuccess(window.lciSupport.messages.replySubmitted || 'Reply submitted successfully');
                        this.replyMessage = '';

                        // Reload ticket details to show the new reply
                        if (this.activeTicket && this.activeTicket.id) {
                            this.loadTicketDetails(this.activeTicket.id);
                        }

                        // Clear file input
                        if (attachmentInput) {
                            attachmentInput.value = '';
                        }
                    } else {
                        this.showError(data.data.message || 'Error submitting reply');
                    }
                    this.isSubmitting = false;
                })
                .catch(error => {
                    this.showError(window.lciSupport.messages.replyError || 'Error submitting reply. Please try again.');
                    this.isSubmitting = false;
                });
            },

            showSuccess(message) {
                this.successMessage = message;
                this.formSuccess = true;
                this.formError = false;

                setTimeout(() => {
                    this.formSuccess = false;
                }, 5000);
            },

            showError(message) {
                this.errorMessage = message;
                this.formError = true;
                this.formSuccess = false;

                setTimeout(() => {
                    this.formError = false;
                }, 5000);
            },

            getStatusClass(status) {
                if (!status) return 'bg-gray-100 text-gray-800';

                switch (status) {
                    case 'open': return 'bg-yellow-100 text-yellow-800';
                    case 'in-progress': return 'bg-blue-100 text-blue-800';
                    case 'resolved': return 'bg-green-100 text-green-800';
                    case 'closed': return 'bg-gray-100 text-gray-800';
                    default: return 'bg-gray-100 text-gray-800';
                }
            },

            getStatusText(status) {
                if (!status) return '';

                switch (status) {
                    case 'open': return 'Open';
                    case 'in-progress': return 'In Progress';
                    case 'resolved': return 'Resolved';
                    case 'closed': return 'Closed';
                    default: return status.charAt(0).toUpperCase() + status.slice(1);
                }
            },

            formatDate(dateString) {
                if (!dateString) return '';

                try {
                    const date = new Date(dateString);
                    return date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch (error) {
                    console.error('Error formatting date:', error);
                    return '';
                }
            }
        };
    });
});
</script>
