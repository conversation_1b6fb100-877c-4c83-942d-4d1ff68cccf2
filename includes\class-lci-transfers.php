<?php
/**
 * LCI Transfers Class
 * Handles transfer notifications and bookings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class LCI_Transfers {
    /**
     * Constructor
     */
    public function __construct() {
        // Create tables on plugin activation
        register_activation_hook(LCI2025_PLUGIN_FILE, array($this, 'create_tables'));

        // Also create tables on init to ensure they exist
        add_action('init', array($this, 'ensure_tables_exist'));

        // Run immediately to fix any issues
        $this->ensure_tables_exist();

        // Register AJAX handlers
        add_action('wp_ajax_lci_transfer_notification', array($this, 'handle_transfer_notification'));
        add_action('wp_ajax_nopriv_lci_transfer_notification', array($this, 'handle_transfer_notification'));

        // Register transfer booking AJAX handlers
        add_action('wp_ajax_lci_book_transfer', array($this, 'handle_transfer_booking'));
        add_action('wp_ajax_nopriv_lci_book_transfer', array($this, 'handle_transfer_booking'));

        // Register transfer wizard AJAX handlers
        add_action('wp_ajax_lci_get_transfer_products', array($this, 'get_transfer_products'));
        add_action('wp_ajax_nopriv_lci_get_transfer_products', array($this, 'get_transfer_products'));

        // Register admin AJAX handlers
        add_action('wp_ajax_lci_get_transfer_bookings', array($this, 'get_transfer_bookings_ajax'));
        add_action('wp_ajax_lci_update_transfer_status', array($this, 'update_transfer_status_ajax'));
        add_action('wp_ajax_lci_delete_transfer_booking', array($this, 'delete_transfer_booking_ajax'));
        add_action('wp_ajax_lci_send_transfer_notifications', array($this, 'send_transfer_notifications_ajax'));
        add_action('wp_ajax_lci_recreate_transfer_tables', array($this, 'recreate_transfer_tables_ajax'));

        // Register heartbeat handler to keep session alive
        add_action('wp_ajax_heartbeat', array($this, 'handle_heartbeat'));
        add_action('wp_ajax_nopriv_heartbeat', array($this, 'handle_heartbeat'));

        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Add admin notice for database issues
        add_action('admin_notices', array($this, 'show_database_status_notice'));
        add_action('admin_notices', array($this, 'show_cleanup_notice'));

        // Add filter to handle cart item data for transfers
        add_filter('woocommerce_add_cart_item_data', array($this, 'handle_transfer_cart_item_data'), 10, 3);

        // Add action to clean up duplicate bookings
        add_action('admin_init', array($this, 'clean_duplicate_bookings'));

        // Add custom AJAX handler for admin actions
        add_action('wp_ajax_lci_custom_ajax', array($this, 'handle_custom_ajax'));

        // Add hook for order status changes
        add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 4);

        // Add hook for payment complete
        add_action('woocommerce_payment_complete', array($this, 'handle_payment_complete'), 10, 1);

        // Add hook for order creation - alternative approach
        add_action('woocommerce_new_order', array($this, 'handle_new_order'), 10, 1);

        // Add hook for thank you page - final fallback
        add_action('woocommerce_thankyou', array($this, 'handle_new_order'), 10, 1);

        // Add filter to display transfer booking info in cart/checkout
        add_filter('woocommerce_get_item_data', array($this, 'display_transfer_booking_info_in_cart'), 10, 2);

        // Add hook to transfer cart data to order items
        add_action('woocommerce_checkout_create_order_line_item', array($this, 'add_transfer_data_to_order_items'), 10, 4);
    }

    /**
     * Display transfer booking info in cart
     *
     * @param array $item_data
     * @param array $cart_item
     * @return array
     */
    public function display_transfer_booking_info_in_cart($item_data, $cart_item) {
        if (isset($cart_item['transfer_booking_id'])) {
            // Check if this is an additional product
            if (isset($cart_item['is_additional_transfer_product']) && $cart_item['is_additional_transfer_product']) {
                $item_data[] = array(
                    'key'   => 'Transfer Booking',
                    'value' => 'Part of booking #' . $cart_item['transfer_booking_id']
                );
            } else {
                // This is the main product with all the details
                $item_data[] = array(
                    'key'   => 'Transfer Booking',
                    'value' => 'Booking #' . $cart_item['transfer_booking_id']
                );

                // Add transfer type
                if (!empty($cart_item['transfer_type'])) {
                    $transfer_types = array(
                        'bucharest_to_brasov' => 'Bucharest Airport to Brasov (One Way)',
                        'brasov_to_bucharest' => 'Brasov to Bucharest Airport (One Way)',
                        'bucharest_to_brasov_round' => 'Bucharest Airport to Brasov (Round Trip)',
                        'brasov_airport_to_venue' => 'Brasov Airport to Main Event Venue (One Way)',
                        'venue_to_brasov_airport' => 'Main Event Venue to Brasov Airport (One Way)',
                        'brasov_airport_to_venue_round' => 'Brasov Airport to Main Event Venue (Round Trip)'
                    );

                    $transfer_type = isset($transfer_types[$cart_item['transfer_type']]) ?
                        $transfer_types[$cart_item['transfer_type']] : $cart_item['transfer_type'];

                    $item_data[] = array(
                        'key'   => 'Transfer Type',
                        'value' => $transfer_type
                    );
                }
            }
        }

        return $item_data;
    }

    /**
     * Add transfer booking data to order line items
     *
     * @param WC_Order_Item_Product $item
     * @param string $cart_item_key
     * @param array $values
     * @param WC_Order $order
     */
    public function add_transfer_data_to_order_items($item, $cart_item_key, $values, $order) {
        // Check if this is a transfer booking item
        if (isset($values['transfer_booking_id'])) {
            // Add the booking ID to the order item meta
            $item->add_meta_data('transfer_booking_id', $values['transfer_booking_id']);

            // Log what we're adding
            error_log('TRANSFER DEBUG: Adding transfer data to order item - Booking ID: ' . $values['transfer_booking_id']);

            // If this is an additional product, just add the minimal data
            if (isset($values['is_additional_transfer_product']) && $values['is_additional_transfer_product']) {
                $item->add_meta_data('is_additional_transfer_product', true);
                $item->add_meta_data('is_primary_transfer_product', false);
                error_log('TRANSFER DEBUG: Added additional product data');
                return;
            }

            // For the main product, add all the booking details
            if (isset($values['is_primary_transfer_product'])) {
                $item->add_meta_data('is_primary_transfer_product', $values['is_primary_transfer_product']);
            }

            // IMPORTANT: Add the complete transfer booking data
            if (isset($values['transfer_booking_data'])) {
                $item->add_meta_data('transfer_booking_data', $values['transfer_booking_data']);
                error_log('TRANSFER DEBUG: Added complete transfer_booking_data to order item');
            }

            if (isset($values['transfer_type'])) {
                $item->add_meta_data('transfer_type', $values['transfer_type']);
            }

            if (isset($values['arrival_date'])) {
                $item->add_meta_data('arrival_date', $values['arrival_date']);
            }

            if (isset($values['arrival_time'])) {
                $item->add_meta_data('arrival_time', $values['arrival_time']);
            }

            if (isset($values['arrival_flight'])) {
                $item->add_meta_data('arrival_flight', $values['arrival_flight']);
            }

            if (isset($values['departure_date'])) {
                $item->add_meta_data('departure_date', $values['departure_date']);
            }

            if (isset($values['departure_time'])) {
                $item->add_meta_data('departure_time', $values['departure_time']);
            }

            if (isset($values['departure_flight'])) {
                $item->add_meta_data('departure_flight', $values['departure_flight']);
            }

            if (isset($values['passengers'])) {
                $item->add_meta_data('passengers', $values['passengers']);
            }

            if (isset($values['special_requests'])) {
                $item->add_meta_data('special_requests', $values['special_requests']);
            }

            if (isset($values['additional_product_ids'])) {
                $item->add_meta_data('additional_product_ids', $values['additional_product_ids']);
            }

            // Add display versions of key fields for admin readability
            if (isset($values['transfer_booking_id'])) {
                $item->add_meta_data('Transfer Booking ID', $values['transfer_booking_id'], true);
            }
            if (isset($values['transfer_type'])) {
                $item->add_meta_data('Transfer Type', $values['transfer_type'], true);
            }

            // Log that we've added the transfer data to the order item
            error_log('TRANSFER DEBUG: Successfully added all transfer booking data to order item for booking ID: ' . $values['transfer_booking_id']);
        }
    }

    /**
     * Check if the current user has purchased a specific product
     *
     * @param int $product_id The product ID to check for
     * @return bool True if the user has purchased the product, false otherwise
     */
    private function user_has_product($product_id) {
        // Get current user ID
        $user_id = get_current_user_id();

        // If user is not logged in, return false
        if (!$user_id) {
            return false;
        }

        // Get customer orders
        $customer_orders = wc_get_orders(array(
            'customer_id' => $user_id,
            'status' => array('wc-completed', 'wc-processing'),
            'limit' => -1,
        ));

        // Loop through orders to check for the product
        foreach ($customer_orders as $order) {
            foreach ($order->get_items() as $item) {
                if ($item->get_product_id() == $product_id) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Render the transfers tab content
     */
    public function render_transfers_tab() {
        // Check if user has purchased Main Pretour (product ID 743)
        $has_main_pretour = $this->user_has_product(743);

        // Pass this information to the view
        include_once LCI2025_PATH . 'views/transfers.php';
    }

    /**
     * Ensure tables exist and have the correct structure
     */
    public function ensure_tables_exist() {
        global $wpdb;

        // Check if tables exist
        $bookings_table = $wpdb->prefix . 'lci_transfer_bookings';
        $notifications_table = $wpdb->prefix . 'lci_transfer_notifications';

        $bookings_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$bookings_table'") === $bookings_table;
        $notifications_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$notifications_table'") === $notifications_table;

        // Create tables if they don't exist
        if (!$bookings_table_exists || !$notifications_table_exists) {
            error_log('Transfer tables missing. Creating them now...');
            $this->create_tables();
            return;
        }

        // Check and update table structure if needed
        $this->update_table_structure($bookings_table);
    }

    /**
     * Update table structure to match the latest schema
     *
     * @param string $table_name The name of the table to update
     */
    private function update_table_structure($table_name) {
        global $wpdb;

        error_log('Checking and updating table structure for ' . $table_name);

        // Get current columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
        $column_names = array_map(function($col) { return $col->Field; }, $columns);

        // Define required columns with their definitions
        $required_columns = [
            'user_id' => "bigint(20) DEFAULT 0",
            'user_name' => "varchar(100) DEFAULT ''",
            'order_id' => "bigint(20) DEFAULT 0",
            'email' => "varchar(100) NOT NULL",
            'transfer_type' => "varchar(50) NOT NULL",
            'arrival_date' => "date DEFAULT NULL",
            'arrival_time' => "time DEFAULT NULL",
            'arrival_flight' => "varchar(100) DEFAULT ''",
            'departure_date' => "date DEFAULT NULL",
            'departure_time' => "time DEFAULT NULL",
            'departure_flight' => "varchar(100) DEFAULT ''",
            'passengers' => "int(11) DEFAULT 1",
            'product_ids' => "text DEFAULT NULL",
            'product_names' => "text DEFAULT NULL",
            'special_requests' => "text DEFAULT ''",
            'status' => "varchar(20) DEFAULT 'pending' NOT NULL",
            'created_at' => "datetime DEFAULT CURRENT_TIMESTAMP NOT NULL",
            'updated_at' => "datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL"
        ];

        // Check for missing columns and add them
        foreach ($required_columns as $column_name => $definition) {
            if (!in_array($column_name, $column_names)) {
                error_log("Adding missing column $column_name to $table_name...");
                $wpdb->query("ALTER TABLE $table_name ADD COLUMN $column_name $definition");
                error_log("Column $column_name added with definition: $definition");
            }
        }

        // Check user_id column
        $user_id_column = array_filter($columns, function($col) { return $col->Field === 'user_id'; });
        if (!empty($user_id_column)) {
            $user_id_column = reset($user_id_column);
            if ($user_id_column->Null === 'NO' && $user_id_column->Default === null) {
                error_log('Updating user_id column to allow DEFAULT 0...');
                $wpdb->query("ALTER TABLE $table_name MODIFY user_id bigint(20) DEFAULT 0");
            }
        }

        // Check order_id column
        $order_id_column = array_filter($columns, function($col) { return $col->Field === 'order_id'; });
        if (!empty($order_id_column)) {
            $order_id_column = reset($order_id_column);
            if ($order_id_column->Default === null) {
                error_log('Updating order_id column to have DEFAULT 0...');
                $wpdb->query("ALTER TABLE $table_name MODIFY order_id bigint(20) DEFAULT 0");
            }
        }

        // Check for flight number columns and increase their size if needed
        $flight_columns = ['arrival_flight', 'departure_flight'];
        foreach ($flight_columns as $col_name) {
            $column = array_filter($columns, function($col) use ($col_name) { return $col->Field === $col_name; });
            if (!empty($column)) {
                $column = reset($column);
                if (strpos($column->Type, 'varchar(50)') !== false) {
                    error_log("Increasing size of $col_name column from varchar(50) to varchar(100)...");
                    $wpdb->query("ALTER TABLE $table_name MODIFY $col_name varchar(100) DEFAULT ''");
                }
            }
        }

        // Add indexes if they don't exist
        $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
        $index_names = array_map(function($idx) { return $idx->Key_name; }, $indexes);

        $required_indexes = ['user_id', 'order_id', 'status', 'transfer_type'];
        foreach ($required_indexes as $idx_name) {
            if (!in_array($idx_name, $index_names)) {
                error_log("Adding index for $idx_name column...");
                $wpdb->query("ALTER TABLE $table_name ADD INDEX ($idx_name)");
            }
        }

        // Get updated columns to verify changes
        $updated_columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
        $updated_column_names = array_map(function($col) { return $col->Field; }, $updated_columns);
        error_log('Updated table columns: ' . implode(', ', $updated_column_names));

        error_log('Table structure update completed.');
    }

    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Transfer notifications table
        $table_name = $wpdb->prefix . 'lci_transfer_notifications';

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            email varchar(100) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            notified tinyint(1) DEFAULT 0 NOT NULL,
            PRIMARY KEY  (id)
        ) $charset_collate;";

        // Transfer bookings table
        $bookings_table = $wpdb->prefix . 'lci_transfer_bookings';

        $bookings_sql = "CREATE TABLE $bookings_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) DEFAULT 0,
            user_name varchar(100) DEFAULT '',
            order_id bigint(20) DEFAULT 0,
            email varchar(100) NOT NULL,
            transfer_type varchar(50) NOT NULL,
            arrival_date date DEFAULT NULL,
            arrival_time time DEFAULT NULL,
            arrival_flight varchar(100) DEFAULT '',
            departure_date date DEFAULT NULL,
            departure_time time DEFAULT NULL,
            departure_flight varchar(100) DEFAULT '',
            passengers int(11) DEFAULT 1,
            product_ids text DEFAULT NULL,
            product_names text DEFAULT NULL,
            special_requests text DEFAULT '',
            status varchar(20) DEFAULT 'pending' NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY user_id (user_id),
            KEY order_id (order_id),
            KEY status (status),
            KEY transfer_type (transfer_type)
        ) $charset_collate;";

        // Log the SQL for debugging
        error_log('Creating transfer bookings table with SQL: ' . $bookings_sql);

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        // Create tables using dbDelta
        $sql_result = dbDelta($sql);
        $bookings_result = dbDelta($bookings_sql);

        // Log the results
        error_log('Transfer notifications table creation result: ' . print_r($sql_result, true));
        error_log('Transfer bookings table creation result: ' . print_r($bookings_result, true));

        // Verify tables were created
        $notifications_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        $bookings_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$bookings_table'") === $bookings_table;

        error_log('Notifications table exists: ' . ($notifications_table_exists ? 'Yes' : 'No'));
        error_log('Bookings table exists: ' . ($bookings_table_exists ? 'Yes' : 'No'));

        // Log table structures
        if ($bookings_table_exists) {
            $table_structure = $wpdb->get_results("DESCRIBE $bookings_table");
            error_log('Bookings table structure: ' . print_r($table_structure, true));
        }
    }

    /**
     * Handle transfer notification AJAX request
     */
    public function handle_transfer_notification() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_transfer_notification')) {
            wp_send_json_error('Invalid security token');
            exit;
        }

        // Get data
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
        $user_id = get_current_user_id();

        if (empty($email)) {
            wp_send_json_error('Email is required');
            exit;
        }

        // Save to database
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_notifications';

        // Check if already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE email = %s",
            $email
        ));

        if ($existing) {
            wp_send_json_success(array(
                'message' => 'You are already registered for transfer notifications'
            ));
            exit;
        }

        // Insert new record
        $result = $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'email' => $email,
                'created_at' => current_time('mysql'),
                'notified' => 0
            ),
            array('%d', '%s', '%s', '%d')
        );

        if ($result) {
            wp_send_json_success(array(
                'message' => 'You will be notified when transfer booking opens!'
            ));
        } else {
            wp_send_json_error('Failed to register for notifications');
        }

        exit;
    }

    /**
     * Handle transfer booking AJAX request
     */
    public function handle_transfer_booking() {
        // Check nonce - accept both the transfers-specific nonce and the general one
        if (!isset($_POST['nonce']) ||
            (!wp_verify_nonce($_POST['nonce'], 'lci_transfers_nonce') &&
             !wp_verify_nonce($_POST['nonce'], 'lci_ajax_nonce'))) {

            // Log the nonce for debugging ONLY for transfer booking
            error_log('TRANSFER DEBUG: Nonce verification failed. Received nonce: ' . (isset($_POST['nonce']) ? $_POST['nonce'] : 'NOT SET'));
            error_log('TRANSFER DEBUG: Expected nonce actions: lci_transfers_nonce or lci_ajax_nonce');

            wp_send_json_error(array(
                'message' => 'Invalid security token',
                'errors' => array('general' => 'Security verification failed')
            ));
            exit;
        }

        // Get current user
        $user_id = get_current_user_id();
        $current_user = wp_get_current_user();
        $email = '';
        $user_name = '';

        // Log user authentication status
        error_log('Transfer booking - User ID: ' . $user_id . ', Is user logged in: ' . ($user_id ? 'Yes' : 'No'));

        // Get user's information if logged in
        if ($user_id) {
            $email = $current_user->user_email;
            $user_name = $current_user->display_name;
            if (empty($user_name)) {
                $user_name = $current_user->user_login;
            }

            // Log user details
            error_log('Transfer booking - Logged in user details - Name: ' . $user_name . ', Email: ' . $email);
        }

        // If not logged in or email not available, check if email was provided in the form
        if (empty($email) && isset($_POST['email'])) {
            $email = sanitize_email($_POST['email']);
            error_log('Transfer booking - Using email from form: ' . $email);
        }

        // If still no email, use a placeholder (should not happen in normal operation)
        if (empty($email)) {
            $email = '<EMAIL>';
            error_log('Transfer booking - WARNING: No email available, using placeholder');
        }

        // Validate required fields
        $errors = array();
        $transfer_type = isset($_POST['transfer_type']) ? sanitize_text_field($_POST['transfer_type']) : '';
        $arrival_date = isset($_POST['arrival_date']) ? sanitize_text_field($_POST['arrival_date']) : '';
        $arrival_time = isset($_POST['arrival_time']) ? sanitize_text_field($_POST['arrival_time']) : '';
        $arrival_flight = isset($_POST['arrival_flight']) ? sanitize_text_field($_POST['arrival_flight']) : '';
        $departure_date = isset($_POST['departure_date']) ? sanitize_text_field($_POST['departure_date']) : '';
        $departure_time = isset($_POST['departure_time']) ? sanitize_text_field($_POST['departure_time']) : '';
        $departure_flight = isset($_POST['departure_flight']) ? sanitize_text_field($_POST['departure_flight']) : '';
        $passengers = isset($_POST['passengers']) ? intval($_POST['passengers']) : 1;
        $special_requests = isset($_POST['special_requests']) ? sanitize_textarea_field($_POST['special_requests']) : '';
        $product_ids = isset($_POST['products']) && is_array($_POST['products']) ? array_map('intval', $_POST['products']) : array();

        // Validate transfer type
        if (empty($transfer_type)) {
            $errors['transfer_type'] = 'Please select a valid transfer type';
        }

        // Validate arrival date
        if (empty($arrival_date)) {
            $errors['arrival_date'] = 'Please select an arrival date';
        } else {
            // Check if date is within valid range (August 12-20, 2025)
            $date_obj = new DateTime($arrival_date);
            $min_date = new DateTime('2025-08-12');
            $max_date = new DateTime('2025-08-20');

            if ($date_obj < $min_date || $date_obj > $max_date) {
                $errors['arrival_date'] = 'Please select a date between August 12 and August 20, 2025';
            }
        }

        // Validate arrival time
        if (empty($arrival_time)) {
            $errors['arrival_time'] = 'Please select an arrival time';
        }

        // Check if it's a round trip
        $is_round_trip = $transfer_type === 'bucharest_to_brasov_round' || $transfer_type === 'brasov_airport_to_venue_round';

        // Validate departure date and time for round trips
        if ($is_round_trip) {
            if (empty($departure_date)) {
                $errors['departure_date'] = 'Please select a departure date';
            } else {
                // Check if date is within valid range (August 24-27, 2025)
                $date_obj = new DateTime($departure_date);
                $min_date = new DateTime('2025-08-24');
                $max_date = new DateTime('2025-08-27');

                if ($date_obj < $min_date || $date_obj > $max_date) {
                    $errors['departure_date'] = 'Please select a date between August 24 and August 27, 2025';
                }
            }

            if (empty($departure_time)) {
                $errors['departure_time'] = 'Please select a departure time';
            }
        }

        // Validate passengers
        if ($passengers < 1 || $passengers > 10) {
            $errors['passengers'] = 'Please enter a valid number of passengers (1-10)';
        }

        // Validate product selection
        if (empty($product_ids)) {
            $errors['products'] = 'Please select at least one transfer option';
        }

        // Get product names for selected products
        $product_names = array();
        foreach ($product_ids as $product_id) {
            $product = wc_get_product($product_id);
            if ($product) {
                $product_names[] = $product->get_name();
            }
        }

        // Log validation results
        error_log('Transfer booking validation results: ' . print_r($errors, true));
        error_log('Transfer type: ' . $transfer_type);
        error_log('Is round trip: ' . ($is_round_trip ? 'Yes' : 'No'));

        // Check if departure-only or arrival-only
        $is_departure_only = $transfer_type === 'brasov_to_bucharest';
        $is_arrival_only = !$is_round_trip && !$is_departure_only;

        error_log('Is departure only: ' . ($is_departure_only ? 'Yes' : 'No'));
        error_log('Is arrival only: ' . ($is_arrival_only ? 'Yes' : 'No'));

        // If there are errors, return them
        if (!empty($errors)) {
            wp_send_json_error(array(
                'message' => 'Please correct the errors below',
                'errors' => $errors
            ));
            exit;
        }

        // Instead of saving to database now, we'll generate a temporary booking ID
        // and store all the data as cart item data. The actual booking will be created
        // after the order is placed.

        // Generate a unique temporary booking ID (negative to avoid conflicts with real IDs)
        $temp_booking_id = -time();

        // Log booking data that will be stored with the order
        error_log('Transfer booking - Preparing data for cart (NOT inserting to database yet):');
        error_log('User ID: ' . $user_id . ', User Name: ' . $user_name . ', Email: ' . $email);
        error_log('Transfer Type: ' . $transfer_type . ', Passengers: ' . $passengers);
        error_log('Product IDs: ' . json_encode($product_ids));
        error_log('Product Names: ' . json_encode($product_names));
        error_log('Temporary Booking ID: ' . $temp_booking_id . ' (Will be replaced with real ID after order)');

        // Prepare the booking data to store as cart meta
        $booking_data = array(
            'user_id' => $user_id ? $user_id : 0,
            'user_name' => $user_name,
            'email' => $email,
            'transfer_type' => $transfer_type,
            'passengers' => $passengers,
            'product_ids' => $product_ids,
            'product_names' => $product_names,
            'special_requests' => $special_requests,
            'is_departure_only' => $is_departure_only,
            'is_round_trip' => $is_round_trip
        );

        // Add arrival details if it's not a departure-only transfer
        if (!$is_departure_only) {
            $booking_data['arrival_date'] = !empty($arrival_date) ? $arrival_date : null;
            $booking_data['arrival_time'] = !empty($arrival_time) ? $arrival_time : null;
            $booking_data['arrival_flight'] = $arrival_flight;
        }

        // Add departure details if it's a round trip or departure-only transfer
        if ($is_round_trip || $is_departure_only) {
            $booking_data['departure_date'] = !empty($departure_date) ? $departure_date : null;
            $booking_data['departure_time'] = !empty($departure_time) ? $departure_time : null;
            $booking_data['departure_flight'] = $departure_flight;
        }

        // We'll use this temporary ID to track the booking in the cart
        $booking_id = $temp_booking_id;

        // Add products to cart - using a single cart item for all products
        $cart_items = array();
        $cart_error = false;

        // Get the first product ID to use as the main product
        $main_product_id = reset($product_ids);

        // Create a string of additional product IDs to store as custom data
        $additional_product_ids = array_slice($product_ids, 1);
        $additional_products_str = !empty($additional_product_ids) ? implode(',', $additional_product_ids) : '';

        // Log what we're doing
        error_log('Adding transfer booking to cart. Main product ID: ' . $main_product_id . ', Additional products: ' . $additional_products_str);
        error_log('IMPORTANT: Creating only ONE database record for booking ID: ' . $booking_id);

        // Add the main product to cart with all booking data
        $cart_item_key = WC()->cart->add_to_cart(
            $main_product_id,
            1,
            0,
            array(),
            array(
                'transfer_booking_id' => $booking_id,
                'transfer_booking_data' => $booking_data, // Store all booking data
                'transfer_type' => $transfer_type,
                'arrival_date' => $arrival_date,
                'arrival_time' => $arrival_time,
                'arrival_flight' => $arrival_flight,
                'departure_date' => $is_round_trip ? $departure_date : '',
                'departure_time' => $is_round_trip ? $departure_time : '',
                'departure_flight' => $is_round_trip ? $departure_flight : '',
                'passengers' => $passengers,
                'special_requests' => $special_requests,
                'additional_product_ids' => $additional_products_str,
                'is_primary_transfer_product' => true, // Mark this as the primary product
                'is_temp_booking_id' => true // Flag to indicate this is a temporary ID
            )
        );

        if (!$cart_item_key) {
            $cart_error = true;
        } else {
            $cart_items[] = $cart_item_key;

            // Add any additional products with a reference to the same booking ID
            // but with minimal data to avoid duplicate processing
            foreach ($additional_product_ids as $additional_id) {
                $additional_cart_key = WC()->cart->add_to_cart(
                    $additional_id,
                    1,
                    0,
                    array(),
                    array(
                        'transfer_booking_id' => $booking_id,
                        'is_additional_transfer_product' => true,
                        'is_primary_transfer_product' => false, // Mark this as NOT the primary product
                        'is_temp_booking_id' => true // Flag to indicate this is a temporary ID
                    )
                );

                if ($additional_cart_key) {
                    $cart_items[] = $additional_cart_key;
                }
            }
        }

        // Create booking and link it to the order once order is created
        add_action('woocommerce_checkout_order_processed', function($order_id, $posted_data, $order) use ($booking_data, $booking_id, $user_id) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'lci_transfer_bookings';

            // Log order creation with status
            error_log('TRANSFER DEBUG: Order processed hook triggered - Temp Booking ID: ' . $booking_id . ', Order ID: ' . $order_id . ', User ID: ' . $user_id . ', Order Status: ' . $order->get_status());

            // Dump booking data for debugging
            error_log('TRANSFER DEBUG: Booking data: ' . print_r($booking_data, true));

            // Special handling for on-hold orders
            if ($order->get_status() === 'on-hold') {
                error_log('TRANSFER DEBUG: Order is on-hold. Will create booking with on-hold status.');
            }

            // Verify the table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
            error_log('TRANSFER DEBUG: Table exists: ' . ($table_exists ? 'Yes' : 'No'));

            // Get order items to find our transfer products
            $order_items = $order->get_items();
            $transfer_data = null;

            error_log('TRANSFER DEBUG: Order has ' . count($order_items) . ' items');

            // Find the primary transfer product in the order
            foreach ($order_items as $item_id => $item) {
                $product_id = $item->get_product_id();
                error_log('TRANSFER DEBUG: Checking item ID: ' . $item_id . ', Product ID: ' . $product_id);

                $item_data = $item->get_meta_data();
                error_log('TRANSFER DEBUG: Item has ' . count($item_data) . ' meta entries');

                // Dump all meta data for debugging
                foreach ($item_data as $meta) {
                    $meta_data = $meta->get_data();
                    error_log('TRANSFER DEBUG: Meta key: ' . $meta_data['key'] . ', Value type: ' . gettype($meta_data['value']));
                }

                // Check for transfer_booking_id
                $booking_id_meta = $item->get_meta('transfer_booking_id', true);
                if ($booking_id_meta) {
                    error_log('TRANSFER DEBUG: Found transfer_booking_id: ' . $booking_id_meta);
                }

                // Check for transfer_booking_data
                $booking_data_meta = $item->get_meta('transfer_booking_data', true);
                if ($booking_data_meta) {
                    error_log('TRANSFER DEBUG: Found transfer_booking_data');
                    $transfer_data = $booking_data_meta;
                    break; // Found what we need
                }

                // Legacy check through all meta data
                foreach ($item_data as $meta) {
                    $meta_data = $meta->get_data();

                    // Check if this is our primary transfer product
                    if ($meta_data['key'] === 'transfer_booking_data') {
                        error_log('TRANSFER DEBUG: Found transfer_booking_data through legacy check');
                        $transfer_data = maybe_unserialize($meta_data['value']);
                        break 2; // Break out of both loops
                    }
                }
            }

            // If we found transfer data, create the booking
            if ($transfer_data) {
                error_log('Transfer booking - Found transfer data in order, creating booking');

                // Prepare data for insertion
                $data = array(
                    'user_id' => $user_id ? $user_id : 0,
                    'user_name' => isset($transfer_data['user_name']) ? $transfer_data['user_name'] : '',
                    'email' => isset($transfer_data['email']) ? $transfer_data['email'] : '',
                    'transfer_type' => isset($transfer_data['transfer_type']) ? $transfer_data['transfer_type'] : '',
                    'passengers' => isset($transfer_data['passengers']) ? $transfer_data['passengers'] : 1,
                    'product_ids' => isset($transfer_data['product_ids']) ? json_encode($transfer_data['product_ids']) : '[]',
                    'product_names' => isset($transfer_data['product_names']) ? json_encode($transfer_data['product_names']) : '[]',
                    'special_requests' => isset($transfer_data['special_requests']) ? $transfer_data['special_requests'] : '',
                    'status' => $order->get_status() === 'on-hold' ? 'on-hold' : 'confirmed', // Set status based on order status
                    'created_at' => current_time('mysql'),
                    'order_id' => $order_id
                );

                // Add arrival details if available
                if (isset($transfer_data['arrival_date'])) {
                    $data['arrival_date'] = $transfer_data['arrival_date'];
                }
                if (isset($transfer_data['arrival_time'])) {
                    $data['arrival_time'] = $transfer_data['arrival_time'];
                }
                if (isset($transfer_data['arrival_flight'])) {
                    $data['arrival_flight'] = $transfer_data['arrival_flight'];
                }

                // Add departure details if available
                if (isset($transfer_data['departure_date'])) {
                    $data['departure_date'] = $transfer_data['departure_date'];
                }
                if (isset($transfer_data['departure_time'])) {
                    $data['departure_time'] = $transfer_data['departure_time'];
                }
                if (isset($transfer_data['departure_flight'])) {
                    $data['departure_flight'] = $transfer_data['departure_flight'];
                }

                // Format specifiers
                $format = array(
                    '%d', // user_id
                    '%s', // user_name
                    '%s', // email
                    '%s', // transfer_type
                    '%d', // passengers
                    '%s', // product_ids
                    '%s', // product_names
                    '%s', // special_requests
                    '%s', // status
                    '%s', // created_at
                    '%d'  // order_id
                );

                // Add format specifiers for optional fields
                if (isset($data['arrival_date'])) $format[] = '%s';
                if (isset($data['arrival_time'])) $format[] = '%s';
                if (isset($data['arrival_flight'])) $format[] = '%s';
                if (isset($data['departure_date'])) $format[] = '%s';
                if (isset($data['departure_time'])) $format[] = '%s';
                if (isset($data['departure_flight'])) $format[] = '%s';

                // Log the data being inserted
                error_log('TRANSFER DEBUG: Inserting data into table: ' . $table_name);
                error_log('TRANSFER DEBUG: Data: ' . print_r($data, true));
                error_log('TRANSFER DEBUG: Format: ' . print_r($format, true));

                // Insert the data
                $result = $wpdb->insert($table_name, $data, $format);
                $db_error = $wpdb->last_error;

                // Log the result
                error_log('TRANSFER DEBUG: Insert result: ' . ($result ? 'Success' : 'Failed'));
                if (!empty($db_error)) {
                    error_log('TRANSFER DEBUG: Database error: ' . $db_error);
                }

                if ($result) {
                    $real_booking_id = $wpdb->insert_id;
                    $status = $order->get_status() === 'on-hold' ? 'on-hold' : 'confirmed';
                    error_log('TRANSFER DEBUG: Successfully created booking #' . $real_booking_id . ' for order #' . $order_id . ' with status: ' . $status);

                    // Add booking ID as order meta for reference
                    if ($order) {
                        $order->update_meta_data('transfer_booking_id', $real_booking_id);
                        $order->save();
                        error_log('Transfer booking - Added booking ID to order meta');

                        // Add a note to the order about the transfer booking
                        $note = sprintf('Transfer booking #%d created with status: %s', $real_booking_id, $status);
                        $order->add_order_note($note);
                        error_log('Transfer booking - Added note to order: ' . $note);
                    }
                } else {
                    error_log('Transfer booking - ERROR: Failed to create booking for order #' . $order_id);
                    error_log('Database error: ' . $db_error);
                }
            } else {
                error_log('Transfer booking - ERROR: Could not find transfer data in order #' . $order_id);
            }
        }, 10, 3);

        // No emails will be sent - WooCommerce will handle all email communication

        // Return success with redirect URL
        wp_send_json_success(array(
            'message' => 'Your transfer has been booked successfully! Redirecting to checkout...',
            'booking_id' => $booking_id,
            'redirect_url' => wc_get_checkout_url()
        ));

        exit;
    }

    /**
     * Handle heartbeat request to keep session alive
     */
    public function handle_heartbeat() {
        // Just return success to keep the session alive
        wp_send_json_success(array(
            'message' => 'Session kept alive'
        ));
        exit;
    }

    /**
     * Email functionality removed
     * All email communication will be handled by WooCommerce
     */
    private function send_booking_confirmation_email($email, $booking_id, $product_ids = null) {
        // Log that we're NOT sending an email (WooCommerce will handle it)
        error_log("Transfer booking email NOT sent for booking ID: $booking_id - WooCommerce will handle all email communication");

        // Return true to indicate success (no email was sent, as intended)
        return true;
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Transfer notifications page
        add_submenu_page(
            'lci-dashboard',
            'Transfer Notifications',
            'Transfer Notifications',
            'manage_options',
            'lci-transfer-notifications',
            array($this, 'render_admin_page')
        );

        // Transfer bookings page
        add_submenu_page(
            'lci-dashboard',
            'Transfer Bookings',
            'Transfer Bookings',
            'manage_options',
            'lci-transfer-bookings',
            array($this, 'render_bookings_admin_page')
        );

        // Add a debug message to help troubleshoot
        add_action('admin_notices', function() {
            if (isset($_GET['page']) && $_GET['page'] === 'lci-transfer-bookings') {
                echo '<div class="notice notice-info is-dismissible"><p>Transfer Bookings page loaded successfully.</p></div>';
            }
        });
    }

    /**
     * Render admin page
     */
    public function render_admin_page() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_notifications';

        // Get all notifications
        $notifications = $wpdb->get_results(
            "SELECT * FROM $table_name ORDER BY created_at DESC",
            ARRAY_A
        );

        // Count total and notified
        $total = count($notifications);
        $notified = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE notified = 1");

        ?>
        <div class="wrap">
            <h1>Transfer Notifications</h1>

            <div class="card">
                <h2>Summary</h2>
                <p>Total registrations: <strong><?php echo $total; ?></strong></p>
                <p>Notified: <strong><?php echo $notified; ?></strong></p>
                <p>Pending: <strong><?php echo $total - $notified; ?></strong></p>
            </div>

            <div class="card">
                <h2>Registered Users</h2>

                <?php if (empty($notifications)) : ?>
                    <p>No users have registered for transfer notifications yet.</p>
                <?php else : ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>User</th>
                                <th>Email</th>
                                <th>Registered On</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($notifications as $notification) :
                                $user = get_user_by('id', $notification['user_id']);
                                $user_name = $user ? $user->display_name : 'Guest';
                            ?>
                                <tr>
                                    <td><?php echo $notification['id']; ?></td>
                                    <td><?php echo $user_name; ?></td>
                                    <td><?php echo $notification['email']; ?></td>
                                    <td><?php echo date('F j, Y g:i a', strtotime($notification['created_at'])); ?></td>
                                    <td>
                                        <?php if ($notification['notified']) : ?>
                                            <span class="dashicons dashicons-yes" style="color: green;"></span> Notified
                                        <?php else : ?>
                                            <span class="dashicons dashicons-clock" style="color: orange;"></span> Pending
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>

            <div class="card">
                <h2>Send Notifications</h2>
                <p>Use this button to send notifications to all registered users who haven't been notified yet.</p>
                <button class="button button-primary" id="send-notifications">Send Notifications</button>
                <div id="notification-result" style="margin-top: 10px;"></div>
            </div>
        </div>

        <script>
            jQuery(document).ready(function($) {
                $('#send-notifications').on('click', function() {
                    $(this).prop('disabled', true).text('Sending...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'lci_send_transfer_notifications',
                            nonce: '<?php echo wp_create_nonce('lci_send_transfer_notifications'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $('#notification-result').html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            } else {
                                $('#notification-result').html('<div class="notice notice-error"><p>' + response.data + '</p></div>');
                                $('#send-notifications').prop('disabled', false).text('Send Notifications');
                            }
                        },
                        error: function() {
                            $('#notification-result').html('<div class="notice notice-error"><p>An error occurred. Please try again.</p></div>');
                            $('#send-notifications').prop('disabled', false).text('Send Notifications');
                        }
                    });
                });
            });
        </script>
        <?php
    }

    /**
     * Get transfer products based on user selections
     */
    public function get_transfer_products() {
        // Check nonce - accept both the transfers-specific nonce and the general one
        if (!isset($_POST['nonce']) ||
            (!wp_verify_nonce($_POST['nonce'], 'lci_transfers_nonce') &&
             !wp_verify_nonce($_POST['nonce'], 'lci_ajax_nonce'))) {
            wp_send_json_error(array(
                'message' => 'Invalid security token'
            ));
            exit;
        }

        // Get parameters
        $transfer_type = isset($_POST['transfer_type']) ? sanitize_text_field($_POST['transfer_type']) : '';
        $arrival_date = isset($_POST['arrival_date']) ? sanitize_text_field($_POST['arrival_date']) : '';
        $arrival_time = isset($_POST['arrival_time']) ? sanitize_text_field($_POST['arrival_time']) : '';
        $departure_date = isset($_POST['departure_date']) ? sanitize_text_field($_POST['departure_date']) : '';
        $departure_time = isset($_POST['departure_time']) ? sanitize_text_field($_POST['departure_time']) : '';
        $passengers = isset($_POST['passengers']) ? intval($_POST['passengers']) : 1;

        // Validate parameters
        if (empty($transfer_type) || empty($arrival_date) || empty($arrival_time)) {
            wp_send_json_error(array(
                'message' => 'Missing required parameters'
            ));
            exit;
        }

        // Get products from category ID 38 (Transfers)
        $args = array(
            'post_type'      => 'product',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
            'tax_query'      => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'term_id',
                    'terms'    => 38, // Transfer category ID
                ),
            ),
        );

        $products_query = new WP_Query($args);
        $products = array();

        if ($products_query->have_posts()) {
            while ($products_query->have_posts()) {
                $products_query->the_post();
                $product_id = get_the_ID();
                $product = wc_get_product($product_id);

                if ($product) {
                    // Get product meta
                    $product_transfer_type = get_post_meta($product_id, '_transfer_type', true);

                    // Filter products based on transfer type
                    $is_match = false;

                    switch ($transfer_type) {
                        case 'bucharest_to_brasov':
                            $is_match = ($product_transfer_type === 'airport_to_hotel' || $product_transfer_type === 'bucharest_to_brasov');
                            break;
                        case 'brasov_to_bucharest':
                            $is_match = ($product_transfer_type === 'hotel_to_airport' || $product_transfer_type === 'brasov_to_bucharest');
                            break;
                        case 'bucharest_to_brasov_round':
                            $is_match = ($product_transfer_type === 'bucharest_brasov_round' || $product_transfer_type === 'round_trip');
                            break;
                        case 'brasov_airport_to_venue':
                            $is_match = ($product_transfer_type === 'brasov_airport_to_venue' || $product_transfer_type === 'local_transfer');
                            break;
                        case 'venue_to_brasov_airport':
                            $is_match = ($product_transfer_type === 'venue_to_brasov_airport' || $product_transfer_type === 'local_transfer');
                            break;
                        case 'brasov_airport_to_venue_round':
                            $is_match = ($product_transfer_type === 'brasov_airport_round' || $product_transfer_type === 'local_round_trip');
                            break;
                    }

                    if ($is_match) {
                        // Check if product supports the number of passengers
                        $max_passengers = get_post_meta($product_id, '_transfer_max_passengers', true);
                        if (empty($max_passengers) || $passengers <= intval($max_passengers)) {
                            $products[] = array(
                                'id' => $product_id,
                                'name' => $product->get_name(),
                                'price' => $product->get_price(),
                                'price_html' => $product->get_price_html(),
                                'description' => $product->get_short_description(),
                                'image' => get_the_post_thumbnail_url($product_id, 'medium') ?: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121212/bucharest-1.jpg',
                                'transfer_type' => $product_transfer_type,
                                'max_passengers' => $max_passengers,
                                'selected' => false
                            );
                        }
                    }
                }
            }
            wp_reset_postdata();
        }

        // If no products found, return error
        if (empty($products)) {
            wp_send_json_error(array(
                'message' => 'No transfer products available for your selection. Please try different options or contact support.'
            ));
            exit;
        }

        // Return products
        wp_send_json_success(array(
            'products' => $products
        ));
        exit;
    }

    /**
     * Render bookings admin page
     */
    public function render_bookings_admin_page() {
        // Enqueue Alpine.js
        wp_enqueue_script('alpinejs', 'https://cdn.jsdelivr.net/npm/alpinejs@3.12.0/dist/cdn.min.js', array(), '3.12.0', true);

        // Note: We're not loading the external JS file anymore as all functionality is in the template

        // Pass data to JavaScript
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_bookings';

        // Get transfer types for filtering
        $transfer_types = array(
            'bucharest_to_brasov' => 'Bucharest Airport to Brasov (One Way)',
            'brasov_to_bucharest' => 'Brasov to Bucharest Airport (One Way)',
            'bucharest_to_brasov_round' => 'Bucharest Airport to Brasov (Round Trip)',
            'bucharest_to_pretour_hotel' => 'Bucharest Airport to Main Pretour Hotel',
            'brasov_airport_to_venue' => 'Brasov Airport to Main Event Venue (One Way)'
        );

        // Count bookings by status
        $total = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        $pending = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'pending'");
        $confirmed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'confirmed'");
        $completed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'completed'");
        $cancelled = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'cancelled'");

        $stats = array(
            'total' => (int)$total,
            'pending' => (int)$pending,
            'confirmed' => (int)$confirmed,
            'completed' => (int)$completed,
            'cancelled' => (int)$cancelled
        );

        // Note: We're now passing these variables directly in the template
        // instead of using wp_localize_script to avoid timing issues with Alpine.js

        // Include the admin template
        include_once(plugin_dir_path(dirname(__FILE__)) . 'templates/admin/transfers-admin.php');
    }

    /**
     * AJAX handler to get transfer bookings
     */
    public function get_transfer_bookings_ajax() {
        // Debug output
        error_log('AJAX request received: ' . json_encode($_POST));

        // For admin-only AJAX endpoints, we can check if user is logged in and has capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to access this data.'));
            exit;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_bookings';

        // Get all bookings
        $bookings = $wpdb->get_results(
            "SELECT * FROM $table_name ORDER BY created_at DESC",
            ARRAY_A
        );

        // Count bookings by status
        $total = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        $pending = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'pending'");
        $confirmed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'confirmed'");
        $completed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'completed'");
        $cancelled = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'cancelled'");

        $stats = array(
            'total' => (int)$total,
            'pending' => (int)$pending,
            'confirmed' => (int)$confirmed,
            'completed' => (int)$completed,
            'cancelled' => (int)$cancelled
        );

        wp_send_json_success(array(
            'bookings' => $bookings,
            'stats' => $stats
        ));
        exit;
    }

    /**
     * AJAX handler to update transfer status
     */
    public function update_transfer_status_ajax() {
        // For admin-only AJAX endpoints, we can check if user is logged in and has capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
            exit;
        }

        // Check required fields
        if (!isset($_POST['booking_id']) || !isset($_POST['status'])) {
            wp_send_json_error(array('message' => 'Missing required fields.'));
            exit;
        }

        $booking_id = intval($_POST['booking_id']);
        $status = sanitize_text_field($_POST['status']);

        // Validate status
        $valid_statuses = array('pending', 'confirmed', 'completed', 'cancelled');
        if (!in_array($status, $valid_statuses)) {
            wp_send_json_error(array('message' => 'Invalid status.'));
            exit;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_bookings';

        // Update status
        $result = $wpdb->update(
            $table_name,
            array('status' => $status),
            array('id' => $booking_id),
            array('%s'),
            array('%d')
        );

        if ($result === false) {
            wp_send_json_error(array('message' => 'Failed to update status.'));
            exit;
        }

        wp_send_json_success(array(
            'message' => 'Status updated successfully.',
            'booking_id' => $booking_id,
            'status' => $status
        ));
        exit;
    }

    /**
     * AJAX handler to delete a transfer booking
     */
    public function delete_transfer_booking_ajax() {
        // For admin-only AJAX endpoints, we can check if user is logged in and has capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
            exit;
        }

        // Check required fields
        if (!isset($_POST['booking_id'])) {
            wp_send_json_error(array('message' => 'Missing booking ID.'));
            exit;
        }

        $booking_id = intval($_POST['booking_id']);

        // Verify the booking exists before deleting
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_bookings';

        $booking = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $booking_id
        ));

        if (!$booking) {
            wp_send_json_error(array('message' => 'Booking not found.'));
            exit;
        }

        // Log the deletion
        error_log("Deleting transfer booking ID: $booking_id, User: " . $booking->user_name . ", Email: " . $booking->email);

        // Delete the booking
        $result = $wpdb->delete(
            $table_name,
            array('id' => $booking_id),
            array('%d')
        );

        if ($result === false) {
            wp_send_json_error(array('message' => 'Failed to delete booking.'));
            exit;
        }

        wp_send_json_success(array(
            'message' => 'Booking deleted successfully.',
            'booking_id' => $booking_id
        ));
        exit;
    }

    /**
     * AJAX handler to send transfer notifications
     * This function is intentionally disabled as per client request
     * No emails will be sent - WooCommerce will handle all email communication
     */
    public function send_transfer_notifications_ajax() {
        // For admin-only AJAX endpoints, we can check if user is logged in and has capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
            exit;
        }

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_send_transfer_notifications')) {
            wp_send_json_error('Invalid security token');
            exit;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_notifications';

        // Get all pending notifications
        $pending_notifications = $wpdb->get_results(
            "SELECT * FROM $table_name WHERE notified = 0",
            ARRAY_A
        );

        // Log that we're NOT sending emails (WooCommerce will handle it)
        error_log("Transfer notifications NOT sent - WooCommerce will handle all email communication");

        // Mark all as notified without actually sending emails
        foreach ($pending_notifications as $notification) {
            $wpdb->update(
                $table_name,
                array('notified' => 1),
                array('id' => $notification['id']),
                array('%d'),
                array('%d')
            );
        }

        // Return success
        wp_send_json_success(array(
            'message' => 'All notifications marked as sent. No emails were actually sent as per client request - WooCommerce will handle all email communication.',
            'count' => count($pending_notifications)
        ));
        exit;
    }

    /**
     * AJAX handler to recreate transfer tables
     * This is an admin-only function for troubleshooting database issues
     */
    public function recreate_transfer_tables_ajax() {
        // For admin-only AJAX endpoints, we can check if user is logged in and has capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
            exit;
        }

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error('Invalid security token');
            exit;
        }

        global $wpdb;

        // Get table names
        $bookings_table = $wpdb->prefix . 'lci_transfer_bookings';
        $notifications_table = $wpdb->prefix . 'lci_transfer_notifications';

        // Drop existing tables if they exist
        $wpdb->query("DROP TABLE IF EXISTS $bookings_table");
        $wpdb->query("DROP TABLE IF EXISTS $notifications_table");

        // Recreate tables
        $this->create_tables();

        // Check if tables were created successfully
        $bookings_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$bookings_table'") === $bookings_table;
        $notifications_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$notifications_table'") === $notifications_table;

        if ($bookings_table_exists && $notifications_table_exists) {
            wp_send_json_success(array(
                'message' => 'Transfer tables recreated successfully.',
                'bookings_table' => $bookings_table,
                'notifications_table' => $notifications_table
            ));
        } else {
            wp_send_json_error(array(
                'message' => 'Failed to recreate transfer tables.',
                'bookings_table_exists' => $bookings_table_exists,
                'notifications_table_exists' => $notifications_table_exists
            ));
        }
        exit;
    }

    /**
     * Show admin notice about database status
     */
    public function show_database_status_notice() {
        global $wpdb;
        $screen = get_current_screen();

        // Only show on transfer-related pages or plugin pages
        if (!isset($screen->id) || (strpos($screen->id, 'lci') === false && strpos($screen->id, 'transfer') === false)) {
            return;
        }

        $bookings_table = $wpdb->prefix . 'lci_transfer_bookings';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$bookings_table'") === $bookings_table;

        if (!$table_exists) {
            echo '<div class="notice notice-error"><p><strong>Transfer System Error:</strong> The transfer bookings table does not exist. Please click the "Recreate Tables" button to fix this issue.</p></div>';
            return;
        }

        // Check for required columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $bookings_table");
        $column_names = array_map(function($col) { return $col->Field; }, $columns);
        $required_columns = ['user_id', 'user_name', 'email', 'transfer_type', 'product_ids', 'product_names'];

        $missing_columns = array_diff($required_columns, $column_names);

        if (!empty($missing_columns)) {
            echo '<div class="notice notice-warning"><p><strong>Transfer System Warning:</strong> Some required columns are missing from the transfer bookings table: ' . implode(', ', $missing_columns) . '. The system will attempt to fix this automatically.</p></div>';

            // Try to fix the issue automatically
            $this->update_table_structure($bookings_table);
        }
    }

    /**
     * Handle transfer cart item data to prevent duplicate bookings
     *
     * @param array $cart_item_data Cart item data
     * @param int $product_id Product ID
     * @param int $variation_id Variation ID
     * @return array Modified cart item data
     */
    public function handle_transfer_cart_item_data($cart_item_data, $product_id, $variation_id) {
        // Check if this is a transfer product with booking ID
        if (isset($cart_item_data['transfer_booking_id'])) {
            $booking_id = $cart_item_data['transfer_booking_id'];
            $is_primary = isset($cart_item_data['is_primary_transfer_product']) && $cart_item_data['is_primary_transfer_product'];

            // Log what we're doing
            error_log('Processing transfer cart item - Product ID: ' . $product_id . ', Booking ID: ' . $booking_id . ', Is Primary: ' . ($is_primary ? 'Yes' : 'No'));

            // If this is not the primary product, make sure we don't create duplicate bookings
            if (!$is_primary) {
                // Check if this booking already exists in the database
                global $wpdb;
                $table_name = $wpdb->prefix . 'lci_transfer_bookings';

                // Count existing bookings with this ID
                $count = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $table_name WHERE id = %d",
                    $booking_id
                ));

                error_log('Found ' . $count . ' existing bookings with ID ' . $booking_id);

                // If we already have a booking with this ID, mark this as a secondary product
                if ($count > 0) {
                    $cart_item_data['is_secondary_product'] = true;
                    error_log('Marking product ' . $product_id . ' as secondary for booking ID ' . $booking_id);
                }
            }
        }

        return $cart_item_data;
    }

    /**
     * Clean up duplicate bookings in the database
     * This function is called on admin_init to fix any existing duplicate bookings
     */
    public function clean_duplicate_bookings() {
        // Only run this on the transfers admin page
        $screen = get_current_screen();
        if (!isset($screen) || !isset($screen->id) || $screen->id !== 'lci-2025_page_lci-transfers') {
            return;
        }

        // Check if we've already run this cleanup
        $cleanup_run = get_option('lci_transfer_duplicate_cleanup_run', false);
        if ($cleanup_run) {
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_bookings';

        // Find duplicate bookings based on user_id, email, and transfer_type
        $duplicates = $wpdb->get_results("
            SELECT user_id, email, transfer_type, COUNT(*) as count, MIN(id) as keep_id
            FROM $table_name
            GROUP BY user_id, email, transfer_type
            HAVING COUNT(*) > 1
        ");

        if (empty($duplicates)) {
            error_log('No duplicate transfer bookings found.');
            update_option('lci_transfer_duplicate_cleanup_run', true);
            return;
        }

        error_log('Found ' . count($duplicates) . ' sets of duplicate transfer bookings. Cleaning up...');

        $deleted_count = 0;

        foreach ($duplicates as $duplicate) {
            // Get all IDs for this set of duplicates
            $all_ids = $wpdb->get_col($wpdb->prepare("
                SELECT id
                FROM $table_name
                WHERE user_id = %d AND email = %s AND transfer_type = %s
                ORDER BY id ASC
            ", $duplicate->user_id, $duplicate->email, $duplicate->transfer_type));

            // Keep the first one, delete the rest
            $keep_id = array_shift($all_ids);

            if (!empty($all_ids)) {
                error_log('Keeping booking ID ' . $keep_id . ' and deleting duplicates: ' . implode(', ', $all_ids));

                // Delete the duplicates
                $deleted = $wpdb->query("
                    DELETE FROM $table_name
                    WHERE id IN (" . implode(',', $all_ids) . ")
                ");

                $deleted_count += $deleted;
            }
        }

        error_log('Deleted ' . $deleted_count . ' duplicate transfer bookings.');

        // Mark cleanup as complete
        update_option('lci_transfer_duplicate_cleanup_run', true);

        // Add a transient notice for the admin
        set_transient('lci_transfer_duplicate_cleanup_notice', array(
            'type' => 'success',
            'message' => 'Cleaned up ' . $deleted_count . ' duplicate transfer bookings.'
        ), 60);
    }

    /**
     * Show cleanup notice
     * Displays a notice when duplicate bookings have been cleaned up
     */
    public function show_cleanup_notice() {
        $notice = get_transient('lci_transfer_duplicate_cleanup_notice');

        if ($notice) {
            $type = isset($notice['type']) ? $notice['type'] : 'info';
            $message = isset($notice['message']) ? $notice['message'] : '';

            echo '<div class="notice notice-' . esc_attr($type) . ' is-dismissible"><p>' . esc_html($message) . '</p></div>';

            // Delete the transient so the notice doesn't show again
            delete_transient('lci_transfer_duplicate_cleanup_notice');
        }
    }

    /**
     * Handle custom AJAX requests
     * This function handles various custom AJAX actions for the admin interface
     */
    public function handle_custom_ajax() {
        // Check if user is logged in and has capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
            exit;
        }

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(array('message' => 'Invalid security token'));
            exit;
        }

        // Check if custom action is set
        if (!isset($_POST['custom_action'])) {
            wp_send_json_error(array('message' => 'No action specified'));
            exit;
        }

        $action = sanitize_text_field($_POST['custom_action']);

        switch ($action) {
            case 'reset_transfer_cleanup_flag':
                // Reset the cleanup flag to force it to run again
                delete_option('lci_transfer_duplicate_cleanup_run');
                error_log('Transfer duplicate cleanup flag reset. Cleanup will run on next page load.');
                wp_send_json_success(array('message' => 'Cleanup flag reset successfully.'));
                break;

            case 'process_existing_orders':
                // Process existing orders to create transfer bookings
                $result = $this->process_existing_orders();
                wp_send_json_success(array(
                    'message' => 'Processed existing orders: ' . $result['processed'] . ' orders, created ' . $result['created'] . ' bookings.',
                    'details' => $result
                ));
                break;

            case 'diagnose_orders':
                // Diagnose orders to see what transfer products they contain
                $result = $this->diagnose_orders();
                wp_send_json_success(array(
                    'message' => 'Diagnosis complete. Check logs for details.',
                    'details' => $result
                ));
                break;

            default:
                wp_send_json_error(array('message' => 'Unknown action: ' . $action));
                break;
        }

        exit;
    }

    /**
     * Diagnose orders to see what transfer products they contain
     *
     * @return array Result statistics
     */
    public function diagnose_orders() {
        global $wpdb;

        // Get all orders with transfer products from category 38
        $args = array(
            'limit' => 20, // Limit to 20 for diagnosis
            'status' => array('processing', 'completed', 'on-hold'),
            'date_created' => '>' . strtotime('-30 days'),
            'return' => 'ids',
        );

        $order_ids = wc_get_orders($args);
        error_log('TRANSFER DIAGNOSIS: Found ' . count($order_ids) . ' orders to diagnose');

        $stats = array(
            'total_orders' => count($order_ids),
            'orders_with_transfer_products' => 0,
            'orders_with_transfer_meta' => 0,
            'transfer_products_found' => array(),
            'details' => array()
        );

        foreach ($order_ids as $order_id) {
            $order = wc_get_order($order_id);
            if (!$order) continue;

            $order_details = array(
                'order_id' => $order_id,
                'status' => $order->get_status(),
                'date' => $order->get_date_created()->format('Y-m-d H:i:s'),
                'items' => array(),
                'has_transfer_products' => false,
                'has_transfer_meta' => false
            );

            // Check each item in the order
            foreach ($order->get_items() as $item_id => $item) {
                $product_id = $item->get_product_id();
                $product = wc_get_product($product_id);

                if (!$product) continue;

                // Check if product is in transfer category (ID 38)
                $product_categories = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'ids'));
                $is_transfer_product = in_array(38, $product_categories);

                // Get all meta data for this item
                $meta_data = $item->get_meta_data();
                $meta_keys = array();
                foreach ($meta_data as $meta) {
                    $meta_keys[] = $meta->get_data()['key'];
                }

                // Check for transfer-related meta
                $transfer_meta_keys = array_filter($meta_keys, function($key) {
                    return strpos($key, 'transfer') !== false || strpos($key, 'Transfer') !== false;
                });

                $item_details = array(
                    'product_id' => $product_id,
                    'product_name' => $product->get_name(),
                    'is_transfer_category' => $is_transfer_product,
                    'meta_keys' => $meta_keys,
                    'transfer_meta_keys' => $transfer_meta_keys
                );

                if ($is_transfer_product) {
                    $order_details['has_transfer_products'] = true;
                    $stats['transfer_products_found'][] = $product_id;
                }

                if (!empty($transfer_meta_keys)) {
                    $order_details['has_transfer_meta'] = true;
                }

                $order_details['items'][] = $item_details;

                error_log("TRANSFER DIAGNOSIS: Order #$order_id - Product #$product_id ({$product->get_name()}) - Transfer Category: " . ($is_transfer_product ? 'Yes' : 'No') . " - Transfer Meta: " . implode(', ', $transfer_meta_keys));
            }

            if ($order_details['has_transfer_products']) {
                $stats['orders_with_transfer_products']++;
            }

            if ($order_details['has_transfer_meta']) {
                $stats['orders_with_transfer_meta']++;
            }

            $stats['details'][] = $order_details;

            error_log("TRANSFER DIAGNOSIS: Order #$order_id summary - Has transfer products: " . ($order_details['has_transfer_products'] ? 'Yes' : 'No') . " - Has transfer meta: " . ($order_details['has_transfer_meta'] ? 'Yes' : 'No'));
        }

        // Get unique transfer products
        $stats['transfer_products_found'] = array_unique($stats['transfer_products_found']);

        error_log('TRANSFER DIAGNOSIS: Summary - Total orders: ' . $stats['total_orders'] . ', Orders with transfer products: ' . $stats['orders_with_transfer_products'] . ', Orders with transfer meta: ' . $stats['orders_with_transfer_meta']);
        error_log('TRANSFER DIAGNOSIS: Transfer products found: ' . implode(', ', $stats['transfer_products_found']));

        return $stats;
    }

    /**
     * Process existing orders
     * This function processes existing orders to create transfer bookings
     *
     * @return array Result statistics
     */
    public function process_existing_orders() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_bookings';

        // Get all orders with transfer products
        $args = array(
            'limit' => -1,
            'status' => array('processing', 'completed', 'on-hold'),
            'date_created' => '>' . strtotime('-30 days'),
            'return' => 'ids',
        );

        $order_ids = wc_get_orders($args);
        error_log('TRANSFER DEBUG: Found ' . count($order_ids) . ' orders to process');

        $stats = array(
            'processed' => 0,
            'created' => 0,
            'skipped' => 0,
            'errors' => 0,
            'details' => array()
        );

        foreach ($order_ids as $order_id) {
            // Check if this order already has a transfer booking
            $existing_booking = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE order_id = %d",
                $order_id
            ));

            if ($existing_booking) {
                error_log("TRANSFER DEBUG: Order #$order_id already has transfer booking #$existing_booking - skipping");
                $stats['skipped']++;
                $stats['details'][] = "Order #$order_id already has booking #$existing_booking";
                continue;
            }

            // Get the order
            $order = wc_get_order($order_id);
            if (!$order) {
                error_log("TRANSFER DEBUG: Could not find order #$order_id");
                $stats['errors']++;
                $stats['details'][] = "Could not find order #$order_id";
                continue;
            }

            // Process the order
            $stats['processed']++;

            // Call handle_new_order to create the booking
            $this->handle_new_order($order_id);

            // Check if a booking was created
            $booking_id = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE order_id = %d",
                $order_id
            ));

            if ($booking_id) {
                error_log("TRANSFER DEBUG: Created booking #$booking_id for order #$order_id");
                $stats['created']++;
                $stats['details'][] = "Created booking #$booking_id for order #$order_id";
            } else {
                error_log("TRANSFER DEBUG: No booking created for order #$order_id");
                $stats['details'][] = "No booking created for order #$order_id";
            }
        }

        return $stats;
    }

    /**
     * Handle new order
     * This function creates transfer bookings when a new order is created
     *
     * @param int $order_id Order ID
     */
    public function handle_new_order($order_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_bookings';

        // Log the new order event
        error_log("TRANSFER DEBUG: New order created #$order_id - Checking for transfer products");

        // Get the order
        $order = wc_get_order($order_id);
        if (!$order) {
            error_log("TRANSFER DEBUG: Could not find order #$order_id");
            return;
        }

        // Get order items
        $order_items = $order->get_items();
        error_log('TRANSFER DEBUG: Order has ' . count($order_items) . ' items');

        // Check if this order already has a transfer booking
        $existing_booking = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE order_id = %d",
            $order_id
        ));

        if ($existing_booking) {
            error_log("TRANSFER DEBUG: Order #$order_id already has transfer booking #$existing_booking");
            return;
        }

        // Find transfer products in the order
        $transfer_items = array();
        foreach ($order_items as $item_id => $item) {
            $product_id = $item->get_product_id();

            // Check if this is a transfer product - look for transfer_booking_id OR transfer_type
            $transfer_booking_id = $item->get_meta('transfer_booking_id', true);
            $transfer_type = $item->get_meta('transfer_type', true);
            $is_primary = $item->get_meta('is_primary_transfer_product', true);

            // Also check if product is in transfer category (ID 38) as fallback
            $product_categories = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'ids'));
            $is_transfer_category = in_array(38, $product_categories);

            if ($transfer_booking_id || $transfer_type || $is_transfer_category) {
                error_log("TRANSFER DEBUG: Found transfer product in order - Product ID: $product_id, Booking ID: $transfer_booking_id, Transfer Type: $transfer_type, Is Primary: " . ($is_primary ? 'Yes' : 'No') . ", In Transfer Category: " . ($is_transfer_category ? 'Yes' : 'No'));

                // Add to our list of transfer items
                $transfer_items[] = array(
                    'item_id' => $item_id,
                    'product_id' => $product_id,
                    'booking_id' => $transfer_booking_id,
                    'is_primary' => $is_primary,
                    'item' => $item,
                    'transfer_type' => $transfer_type,
                    'is_transfer_category' => $is_transfer_category
                );
            }
        }

        if (empty($transfer_items)) {
            error_log("TRANSFER DEBUG: No transfer products found in order #$order_id");
            return;
        }

        // Find the primary transfer item
        $primary_item = null;
        foreach ($transfer_items as $item) {
            if ($item['is_primary']) {
                $primary_item = $item;
                break;
            }
        }

        // If no primary item found, use the first item with transfer data
        if (!$primary_item) {
            foreach ($transfer_items as $item) {
                if ($item['transfer_type'] || $item['booking_id']) {
                    $primary_item = $item;
                    error_log("TRANSFER DEBUG: No primary item marked, using first item with transfer data - Product ID: " . $item['product_id']);
                    break;
                }
            }
        }

        // If still no primary item, use the first transfer category item
        if (!$primary_item && !empty($transfer_items)) {
            $primary_item = $transfer_items[0];
            error_log("TRANSFER DEBUG: Using first transfer category item as primary - Product ID: " . $primary_item['product_id']);
        }

        if (!$primary_item) {
            error_log("TRANSFER DEBUG: No suitable transfer product found in order #$order_id");
            return;
        }

        // Get transfer data from the primary item
        $item = $primary_item['item'];
        $transfer_data = array();

        // First, try to get the complete transfer_booking_data
        $complete_transfer_data = $item->get_meta('transfer_booking_data', true);
        if ($complete_transfer_data && is_array($complete_transfer_data)) {
            error_log("TRANSFER DEBUG: Found complete transfer_booking_data in order item");
            $transfer_data = $complete_transfer_data;
        } else {
            error_log("TRANSFER DEBUG: No complete transfer_booking_data found, collecting individual meta fields");

            // Collect all relevant meta data from individual fields
            $transfer_type = $item->get_meta('transfer_type', true);
            $arrival_date = $item->get_meta('arrival_date', true);
            $arrival_time = $item->get_meta('arrival_time', true);
            $arrival_flight = $item->get_meta('arrival_flight', true);
            $departure_date = $item->get_meta('departure_date', true);
            $departure_time = $item->get_meta('departure_time', true);
            $departure_flight = $item->get_meta('departure_flight', true);
            $passengers = $item->get_meta('passengers', true);
            $special_requests = $item->get_meta('special_requests', true);

            // Log what we found
            error_log("TRANSFER DEBUG: Individual meta fields - Transfer Type: $transfer_type, Arrival: $arrival_date $arrival_time, Departure: $departure_date $departure_time, Passengers: $passengers");

            // Build transfer_data from individual fields
            $transfer_data = array(
                'transfer_type' => $transfer_type,
                'arrival_date' => $arrival_date,
                'arrival_time' => $arrival_time,
                'arrival_flight' => $arrival_flight,
                'departure_date' => $departure_date,
                'departure_time' => $departure_time,
                'departure_flight' => $departure_flight,
                'passengers' => $passengers ? $passengers : 1,
                'special_requests' => $special_requests
            );

            // If we don't have a transfer_type from meta, try to determine it from the product
            if (empty($transfer_data['transfer_type']) && $primary_item['is_transfer_category']) {
                $product = wc_get_product($primary_item['product_id']);
                if ($product) {
                    $product_transfer_type = get_post_meta($primary_item['product_id'], '_transfer_type', true);
                    if ($product_transfer_type) {
                        $transfer_data['transfer_type'] = $product_transfer_type;
                        error_log("TRANSFER DEBUG: Using product transfer type: $product_transfer_type");
                    } else {
                        // Default based on product name or other logic
                        $transfer_data['transfer_type'] = 'bucharest_to_brasov'; // Default
                        error_log("TRANSFER DEBUG: Using default transfer type");
                    }
                }
            }
        }

        // Get user information
        $user_id = $order->get_user_id();
        $user_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
        $email = $order->get_billing_email();

        // Get product IDs
        $product_ids = array();
        $product_names = array();
        foreach ($transfer_items as $item) {
            $product_ids[] = $item['product_id'];
            $product = wc_get_product($item['product_id']);
            if ($product) {
                $product_names[] = $product->get_name();
            }
        }

        // Validate that we have minimum required data
        if (empty($transfer_data['transfer_type']) && empty($product_ids)) {
            error_log("TRANSFER DEBUG: Insufficient data to create booking for order #$order_id - no transfer type or products");
            return;
        }

        // Log the data we're about to use
        error_log("TRANSFER DEBUG: Ready to create booking for order #$order_id with data: " . print_r($transfer_data, true));

        // Prepare data for insertion
        $data = array(
            'user_id' => $user_id ? $user_id : 0,
            'user_name' => $user_name,
            'email' => $email,
            'transfer_type' => isset($transfer_data['transfer_type']) ? $transfer_data['transfer_type'] : '',
            'passengers' => isset($transfer_data['passengers']) ? $transfer_data['passengers'] : 1,
            'product_ids' => json_encode($product_ids),
            'product_names' => json_encode($product_names),
            'special_requests' => isset($transfer_data['special_requests']) ? $transfer_data['special_requests'] : '',
            'status' => $order->get_status() === 'on-hold' ? 'on-hold' : 'confirmed',
            'created_at' => current_time('mysql'),
            'order_id' => $order_id
        );

        // Add arrival details if available
        if (!empty($transfer_data['arrival_date'])) {
            $data['arrival_date'] = $transfer_data['arrival_date'];
        }
        if (!empty($transfer_data['arrival_time'])) {
            $data['arrival_time'] = $transfer_data['arrival_time'];
        }
        if (!empty($transfer_data['arrival_flight'])) {
            $data['arrival_flight'] = $transfer_data['arrival_flight'];
        }

        // Add departure details if available
        if (!empty($transfer_data['departure_date'])) {
            $data['departure_date'] = $transfer_data['departure_date'];
        }
        if (!empty($transfer_data['departure_time'])) {
            $data['departure_time'] = $transfer_data['departure_time'];
        }
        if (!empty($transfer_data['departure_flight'])) {
            $data['departure_flight'] = $transfer_data['departure_flight'];
        }

        // Format specifiers
        $format = array(
            '%d', // user_id
            '%s', // user_name
            '%s', // email
            '%s', // transfer_type
            '%d', // passengers
            '%s', // product_ids
            '%s', // product_names
            '%s', // special_requests
            '%s', // status
            '%s', // created_at
            '%d'  // order_id
        );

        // Add format specifiers for optional fields
        if (isset($data['arrival_date'])) $format[] = '%s';
        if (isset($data['arrival_time'])) $format[] = '%s';
        if (isset($data['arrival_flight'])) $format[] = '%s';
        if (isset($data['departure_date'])) $format[] = '%s';
        if (isset($data['departure_time'])) $format[] = '%s';
        if (isset($data['departure_flight'])) $format[] = '%s';

        // Log the data being inserted
        error_log('TRANSFER DEBUG: Inserting data into table: ' . $table_name);
        error_log('TRANSFER DEBUG: Data: ' . print_r($data, true));

        // Insert the data
        $result = $wpdb->insert($table_name, $data, $format);
        $db_error = $wpdb->last_error;

        // Log the result
        error_log('TRANSFER DEBUG: Insert result: ' . ($result ? 'Success' : 'Failed'));
        if (!empty($db_error)) {
            error_log('TRANSFER DEBUG: Database error: ' . $db_error);
        }

        if ($result) {
            $real_booking_id = $wpdb->insert_id;
            $status = $order->get_status() === 'on-hold' ? 'on-hold' : 'confirmed';
            error_log("TRANSFER DEBUG: Successfully created booking #$real_booking_id for order #$order_id with status: $status");

            // Add booking ID as order meta for reference
            $order->update_meta_data('transfer_booking_id', $real_booking_id);
            $order->save();

            // Add a note to the order about the transfer booking
            $note = sprintf('Transfer booking #%d created with status: %s', $real_booking_id, $status);
            $order->add_order_note($note);
        } else {
            error_log("TRANSFER DEBUG: Failed to create booking for order #$order_id");
        }
    }

    /**
     * Handle payment complete
     * This function updates transfer booking status when payment is completed
     *
     * @param int $order_id Order ID
     */
    public function handle_payment_complete($order_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_bookings';

        // Log the payment complete event
        error_log("Payment completed for order #$order_id");

        // Get the order
        $order = wc_get_order($order_id);
        if (!$order) {
            error_log("Could not find order #$order_id");
            return;
        }

        // Get the transfer booking ID from the order meta
        $booking_id = $order->get_meta('transfer_booking_id');

        if (!$booking_id) {
            // Check if there's a booking with this order ID
            $booking_id = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE order_id = %d",
                $order_id
            ));
        }

        if (!$booking_id) {
            error_log("No transfer booking found for order #$order_id");
            return;
        }

        // Update the booking status to confirmed
        $result = $wpdb->update(
            $table_name,
            array('status' => 'confirmed'),
            array('id' => $booking_id),
            array('%s'),
            array('%d')
        );

        if ($result !== false) {
            error_log("Transfer booking #$booking_id status updated to confirmed after payment completion");

            // Add a note to the order about the status change
            $note = sprintf('Transfer booking #%d status updated to confirmed after payment completion', $booking_id);
            $order->add_order_note($note);
            error_log("Added note to order #$order_id: $note");
        } else {
            error_log("Failed to update transfer booking #$booking_id status");
            error_log("Database error: " . $wpdb->last_error);
        }
    }

    /**
     * Handle order status changes
     * This function updates transfer booking status based on order status changes
     *
     * @param int $order_id Order ID
     * @param string $old_status Old order status
     * @param string $new_status New order status
     * @param WC_Order $order Order object
     */
    public function handle_order_status_change($order_id, $old_status, $new_status, $order) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci_transfer_bookings';

        // Log the status change
        error_log("Order #$order_id status changed from $old_status to $new_status");

        // Get the transfer booking ID from the order meta
        $booking_id = $order->get_meta('transfer_booking_id');

        if (!$booking_id) {
            // Check if there's a booking with this order ID
            $booking_id = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE order_id = %d",
                $order_id
            ));
        }

        if (!$booking_id) {
            error_log("No transfer booking found for order #$order_id");
            return;
        }

        // Update the booking status based on the order status
        $booking_status = 'pending';

        switch ($new_status) {
            case 'processing':
            case 'completed':
                $booking_status = 'confirmed';
                break;

            case 'cancelled':
            case 'refunded':
                $booking_status = 'cancelled';
                break;

            case 'on-hold':
                $booking_status = 'on-hold';
                break;

            case 'failed':
                $booking_status = 'failed';
                break;

            default:
                $booking_status = 'pending';
                break;
        }

        // Update the booking status
        $result = $wpdb->update(
            $table_name,
            array('status' => $booking_status),
            array('id' => $booking_id),
            array('%s'),
            array('%d')
        );

        if ($result !== false) {
            error_log("Transfer booking #$booking_id status updated to $booking_status");

            // Add a note to the order about the status change
            $note = sprintf('Transfer booking #%d status updated from %s to %s', $booking_id, $old_status, $booking_status);
            $order->add_order_note($note);
            error_log("Added note to order #$order_id: $note");
        } else {
            error_log("Failed to update transfer booking #$booking_id status");
            error_log("Database error: " . $wpdb->last_error);
        }
    }


}

// Initialize the class
new LCI_Transfers();
