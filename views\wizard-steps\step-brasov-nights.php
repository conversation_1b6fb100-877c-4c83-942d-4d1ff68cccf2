<?php
// This is a dedicated step for selecting the number of nights in Brasov
// It appears after the user selects pre-event or post-event accommodation in Brasov

// Determine if this is pre-event or post-event
$period_type = isset($wizard['brasov']['type']) ? $wizard['brasov']['type'] : '';
$is_pre_event = ($period_type === 'pre');
$is_post_event = ($period_type === 'post');

// Set default nights if not set
if (!isset($wizard['brasov']['nights'])) {
    $wizard['brasov']['nights'] = 1;
}
?>

<div style="max-width: 600px; margin: 0 auto; background-color: #f8f9fa; border-radius: 10px; padding: 30px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
    <h3 class="text-center mb-3" style="color: #00b2e3; font-size: 24px;">
        <i class="fas fa-moon me-2"></i> How many nights will you stay in Brasov 
        <?php echo $is_pre_event ? 'before' : 'after'; ?> the event?
    </h3>
    
    <div class="info-box" style="background-color: #fff3cd; border-color: #ffeeba; margin-bottom: 20px; padding: 15px; border-radius: 8px;">
        <i class="fas fa-info-circle me-2" style="color: #856404;"></i>
        <span>Select the number of nights you need accommodation in Brasov <?php echo $is_pre_event ? 'before the event starts' : 'after the event ends'; ?>.</span>
    </div>

    <div class="nights-selector" style="max-width: 300px; margin: 30px auto;">
        <div class="nights-selector-container" style="background: white; border: 2px solid #00b2e3; border-radius: 50px; padding: 5px; display: flex; align-items: center; justify-content: space-between;">
            <button type="button" class="nights-btn nights-btn-minus" id="brasov-nights-minus" <?php echo $wizard['brasov']['nights'] <= 1 ? 'disabled' : ''; ?> 
                    style="width: 40px; height: 40px; border-radius: 50%; background: <?php echo $wizard['brasov']['nights'] <= 1 ? '#e0e0e0' : '#00b2e3'; ?>; color: white; border: none; font-size: 18px; cursor: pointer; transition: all 0.2s ease;">
                <i class="fas fa-minus"></i>
            </button>
            <div class="nights-display" style="flex-grow: 1; text-align: center;">
                <span class="nights-count" id="brasov-nights-count" style="font-size: 24px; font-weight: bold; color: #00b2e3;"><?php echo $wizard['brasov']['nights']; ?></span>
                <span class="nights-label" id="brasov-nights-label" style="font-size: 16px; color: #555; margin-left: 5px;"><?php echo $wizard['brasov']['nights'] === 1 ? 'Night' : 'Nights'; ?></span>
            </div>
            <button type="button" class="nights-btn nights-btn-plus" id="brasov-nights-plus" 
                    style="width: 40px; height: 40px; border-radius: 50%; background: #00b2e3; color: white; border: none; font-size: 18px; cursor: pointer; transition: all 0.2s ease;">
                <i class="fas fa-plus"></i>
            </button>
        </div>
        
        <div style="text-align: center; margin-top: 15px; font-size: 14px; color: #666;">
            <i class="fas fa-calendar-alt me-1"></i> 
            <?php if ($is_pre_event): ?>
                Arrive before August 21, 2025
            <?php else: ?>
                Stay after August 24, 2025
            <?php endif; ?>
        </div>
    </div>

    <input type="hidden" name="brasov_nights" id="brasov-nights-input" value="<?php echo $wizard['brasov']['nights']; ?>">
    
    <!-- Continue Button -->
    <div class="text-center mt-4">
        <button type="submit" name="wizard_action" value="next" class="wizard-btn wizard-btn-primary" style="background: linear-gradient(to right, #00b2e3, #0099cc); color: white; border: none; padding: clamp(10px, 3vw, 12px) clamp(15px, 5vw, 24px); border-radius: 6px; font-weight: bold; cursor: pointer; box-shadow: 0 2px 5px rgba(0,178,227,0.3); transition: all 0.2s ease; font-size: clamp(14px, 3.5vw, 16px); width: 100%; max-width: 300px;">
            Continue to Accommodation Options <i class="fas fa-arrow-right ms-2"></i>
        </button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const minusBtn = document.getElementById('brasov-nights-minus');
    const plusBtn = document.getElementById('brasov-nights-plus');
    const countDisplay = document.getElementById('brasov-nights-count');
    const labelDisplay = document.getElementById('brasov-nights-label');
    const input = document.getElementById('brasov-nights-input');
    
    let nights = parseInt(input.value) || 1;
    
    function updateDisplay() {
        countDisplay.textContent = nights;
        labelDisplay.textContent = nights === 1 ? 'Night' : 'Nights';
        input.value = nights;
        
        // Update the minus button color based on disabled state
        minusBtn.disabled = nights <= 1;
        if (nights <= 1) {
            minusBtn.style.background = '#e0e0e0';
        } else {
            minusBtn.style.background = '#00b2e3';
        }
    }
    
    minusBtn.addEventListener('click', function() {
        if (nights > 1) {
            nights--;
            updateDisplay();
        }
    });
    
    plusBtn.addEventListener('click', function() {
        nights++;
        updateDisplay();
    });
});
</script>
