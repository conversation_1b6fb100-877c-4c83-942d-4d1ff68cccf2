/**
 * Fix for quantity buttons in regalia shop
 */
(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Quantity buttons fix loaded');
        
        // Function to fix quantity buttons
        function fixQuantityButtons() {
            // Find all quantity plus buttons
            const quantityPlusButtons = document.querySelectorAll('.quantity-plus');
            const quantityMinusButtons = document.querySelectorAll('.quantity-minus');
            
            // Remove existing event listeners by cloning and replacing the buttons
            quantityPlusButtons.forEach(button => {
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);
                
                // Add the correct event listener
                newButton.addEventListener('click', function() {
                    const quantityInput = this.parentNode.querySelector('.quantity-input-field');
                    if (quantityInput) {
                        // Get current value as a number
                        let currentValue = parseInt(quantityInput.value) || 1;
                        // Increment by exactly 1
                        quantityInput.value = currentValue + 1;
                    }
                });
            });
            
            // Do the same for minus buttons
            quantityMinusButtons.forEach(button => {
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);
                
                // Add the correct event listener
                newButton.addEventListener('click', function() {
                    const quantityInput = this.parentNode.querySelector('.quantity-input-field');
                    if (quantityInput) {
                        // Get current value as a number
                        let currentValue = parseInt(quantityInput.value) || 1;
                        // Decrement by exactly 1, but not below 1
                        if (currentValue > 1) {
                            quantityInput.value = currentValue - 1;
                        }
                    }
                });
            });
            
            console.log('Quantity buttons fixed');
        }
        
        // Fix quantity buttons on page load
        fixQuantityButtons();
        
        // Also fix quantity buttons when variations are shown
        document.querySelectorAll('.show-variations-btn, .show-quantity-btn').forEach(button => {
            button.addEventListener('click', function() {
                // Wait for the variation card to be shown
                setTimeout(fixQuantityButtons, 100);
            });
        });
    });
})();
