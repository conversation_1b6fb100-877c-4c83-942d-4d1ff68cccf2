/**
 * Revolut Popup Payment Handler
 */
(function($) {
    'use strict';
    
    // Payment popup window reference
    let paymentPopup = null;
    
    // Payment check interval
    let checkInterval = null;
    
    // Initialize Revolut popup payment
    function initRevolutPopup() {
        // Handle payment button click
        $('.lci-revolut-popup-button').on('click', function(e) {
            e.preventDefault();
            
            const button = $(this);
            const messageContainer = $('.lci-payment-messages');
            
            // Disable button and show loading message
            button.prop('disabled', true).text(lciRevolutPopup.i18n.processingPayment);
            messageContainer.html('<div class="lci-notice lci-notice-info"><p>' + lciRevolutPopup.i18n.processingPayment + '</p></div>');
            
            // Create payment
            $.ajax({
                url: lciRevolutPopup.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lci_create_revolut_payment',
                    security: lciRevolutPopup.nonce,
                    amount: button.data('amount') || 0,
                    description: button.data('description') || 'Payment',
                    product_id: button.data('product-id') || '',
                    quantity: button.data('quantity') || 1
                },
                success: function(response) {
                    if (response.success && response.data.token) {
                        // Open Revolut popup
                        openRevolutPopup(response.data.token, response.data.order_id);
                    } else {
                        // Show error message
                        messageContainer.html('<div class="lci-notice lci-notice-error"><p>' + (response.data.message || lciRevolutPopup.i18n.paymentFailed) + '</p></div>');
                        button.prop('disabled', false).text('Pay Now');
                    }
                },
                error: function() {
                    // Show error message
                    messageContainer.html('<div class="lci-notice lci-notice-error"><p>' + lciRevolutPopup.i18n.paymentFailed + '</p></div>');
                    button.prop('disabled', false).text('Pay Now');
                }
            });
        });
    }
    
    // Open Revolut popup
    function openRevolutPopup(token, orderId) {
        // Build popup URL
        const popupUrl = 'https://merchant.revolut.com/card-popup.html?token=' + token;
        
        // Open popup
        paymentPopup = window.open(popupUrl, 'revolut_payment', 'width=600,height=600,location=yes,resizable=yes,scrollbars=yes');
        
        // Check if popup was blocked
        if (!paymentPopup || paymentPopup.closed || typeof paymentPopup.closed === 'undefined') {
            $('.lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p>' + lciRevolutPopup.i18n.popupBlocked + '</p></div>');
            $('.lci-revolut-popup-button').prop('disabled', false).text('Pay Now');
            return;
        }
        
        // Start checking payment status
        checkInterval = setInterval(function() {
            if (paymentPopup.closed) {
                clearInterval(checkInterval);
                checkPaymentStatus(orderId);
            }
        }, 1000);
    }
    
    // Check payment status
    function checkPaymentStatus(orderId) {
        $.ajax({
            url: lciRevolutPopup.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lci_check_payment_status',
                security: lciRevolutPopup.nonce,
                order_id: orderId
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.status === 'completed') {
                        // Payment successful
                        $('.lci-payment-messages').html('<div class="lci-notice lci-notice-success"><p>' + lciRevolutPopup.i18n.paymentSuccess + '</p></div>');
                        
                        // Redirect to success page if provided
                        if (response.data.redirect_url) {
                            setTimeout(function() {
                                window.location.href = response.data.redirect_url;
                            }, 1500);
                        } else {
                            // Reload the page
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        }
                    } else if (response.data.status === 'failed') {
                        // Payment failed
                        $('.lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p>' + lciRevolutPopup.i18n.paymentFailed + '</p></div>');
                        $('.lci-revolut-popup-button').prop('disabled', false).text('Pay Now');
                    } else {
                        // Payment pending or processing
                        $('.lci-payment-messages').html('<div class="lci-notice lci-notice-info"><p>' + response.data.message + '</p></div>');
                        $('.lci-revolut-popup-button').prop('disabled', false).text('Pay Now');
                    }
                } else {
                    // Error checking payment status
                    $('.lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p>' + (response.data.message || lciRevolutPopup.i18n.paymentFailed) + '</p></div>');
                    $('.lci-revolut-popup-button').prop('disabled', false).text('Pay Now');
                }
            },
            error: function() {
                // Error checking payment status
                $('.lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p>' + lciRevolutPopup.i18n.paymentFailed + '</p></div>');
                $('.lci-revolut-popup-button').prop('disabled', false).text('Pay Now');
            }
        });
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        initRevolutPopup();
    });
})(jQuery);