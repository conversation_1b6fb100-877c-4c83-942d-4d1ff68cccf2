<?php
/**
 * Flights View - LCI Dashboard Integration
 * Displays flight booking widget within the dashboard
 */

// Check if Travelpayouts is configured
$api_token = get_option('lci_travelpayouts_api_token', '');
$marker = get_option('lci_travelpayouts_marker', '');

// Get user's location for better flight suggestions
$user_country = '';
$user_city = '';
$origin_placeholder = 'Your City';

if (is_user_logged_in()) {
    $user_id = get_current_user_id();

    // Try to get country and city from participant data
    global $wpdb;
    $participant = $wpdb->get_row($wpdb->prepare(
        "SELECT country, city FROM {$wpdb->prefix}lci2025_participants WHERE user_id = %d LIMIT 1",
        $user_id
    ));

    if ($participant) {
        if ($participant->country) {
            $user_country = $participant->country;
        }
        if ($participant->city) {
            $user_city = $participant->city;
            $origin_placeholder = $participant->city . ($participant->country ? ', ' . $participant->country : '');
        } elseif ($participant->country) {
            $origin_placeholder = $participant->country;
        }
    }
}
?>

<div class="lci-flights-dashboard">
    <?php if (empty($api_token) || empty($marker)): ?>
    <!-- Configuration Required Message -->
    <div class="alert alert-info rounded-4 mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle me-3 text-primary" style="font-size: 1.5rem;"></i>
            <div>
                <h5 class="mb-1">Flight Booking Coming Soon</h5>
                <p class="mb-0">We're setting up flight booking services to help you find the best deals to Romania. This feature will be available soon!</p>
            </div>
        </div>
    </div>
    <?php else: ?>

    <!-- Flight Booking Header -->
    <div class="text-center mb-4">
        <h2 class="h3 fw-bold text-primary mb-2">
            <i class="fas fa-plane me-2"></i>
            Book Your Flight to Romania
        </h2>
    </div>

    <!-- Flight Widget Container -->
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <div class="flight-widget-dashboard">
                <?php echo LCI_Travelpayouts::get_flight_widget($origin_placeholder); ?>
            </div>

            <!-- Debug Test Buttons -->
            <?php if (current_user_can('manage_options')): ?>
            <div class="debug-controls mt-3 p-3 bg-light rounded">
                <h6>🔧 Debug Controls (Admin Only)</h6>
                <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="testAutocomplete()">
                    Test Autocomplete
                </button>
                <button type="button" class="btn btn-sm btn-outline-success me-2" onclick="testFlightSearch()">
                    Test Flight Search
                </button>
                <button type="button" class="btn btn-sm btn-outline-info" onclick="showDebugInfo()">
                    Show Debug Info
                </button>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Flight Tips Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm rounded-4">
                <div class="card-body p-4">
                    <h4 class="card-title text-center mb-4">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        Flight Tips for Romania
                    </h4>

                    <div class="row g-4">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm rounded-4 h-100">
                                <div class="card-body p-4 d-flex align-items-center">
                                    <div class="me-4">
                                        <i class="fas fa-plane-departure text-primary" style="font-size: 2.5rem;"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="fw-bold mb-2">Airport Options</h6>
                                        <p class="text-muted mb-0">
                                            <strong>Bucharest (OTP):</strong> Major international hub, 2.5h to Brasov<br>
                                            <strong>Brasov (GHV):</strong> Closest to event, limited flights<br>
                                            <strong>Sibiu (SBZ):</strong> Alternative option, 1.5h to Brasov
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="card border-0 shadow-sm rounded-4 h-100">
                                <div class="card-body p-4 d-flex align-items-center">
                                    <div class="me-4">
                                        <i class="fas fa-calendar-alt text-success" style="font-size: 2.5rem;"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="fw-bold mb-2">Best Booking Time</h6>
                                        <p class="text-muted mb-0">
                                            Book 6-8 weeks in advance for best prices. Tuesday-Thursday departures are usually cheaper.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="card border-0 shadow-sm rounded-4 h-100">
                                <div class="card-body p-4 d-flex align-items-center">
                                    <div class="me-4">
                                        <i class="fas fa-suitcase text-info" style="font-size: 2.5rem;"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="fw-bold mb-2">Baggage Tips</h6>
                                        <p class="text-muted mb-0">
                                            Check airline baggage policies. Pack essentials in carry-on. Romania uses EU power plugs (Type C/F).
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="card border-0 shadow-sm rounded-4 h-100">
                                <div class="card-body p-4 d-flex align-items-center">
                                    <div class="me-4">
                                        <i class="fas fa-passport text-warning" style="font-size: 2.5rem;"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="fw-bold mb-2">Entry Requirements</h6>
                                        <p class="text-muted mb-0">
                                            EU citizens need only ID. Others may need visa - check requirements for your country before booking.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <?php endif; ?>
</div>

<style>
/* Dashboard-specific flight widget styling */
.lci-flights-dashboard {
    padding: 0;
}

.flight-widget-dashboard .lci-travel-widget {
    border-radius: 1rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    border: none;
}

.flight-widget-dashboard .widget-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.flight-widget-dashboard .travel-search-form {
    border-radius: 0.75rem;
}

.flight-widget-dashboard .form-control {
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    padding: 0.75rem;
}

.flight-widget-dashboard .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.flight-widget-dashboard .btn {
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
}



/* Responsive adjustments */
@media (max-width: 768px) {
    .flight-widget-dashboard .travel-search-form .form-row {
        grid-template-columns: 1fr;
    }
}

/* Integration with existing dashboard styles */
.lci-flights-dashboard .card {
    transition: transform 0.2s ease-in-out;
}

.lci-flights-dashboard .card:hover {
    transform: translateY(-2px);
}

.lci-flights-dashboard .bg-light {
    background-color: #f8f9fa !important;
    transition: background-color 0.2s ease-in-out;
}

.lci-flights-dashboard .bg-light:hover {
    background-color: #e9ecef !important;
}

.debug-controls {
    border: 2px dashed #007bff;
    background: #f8f9fa !important;
}
</style>

<script>
// Debug functions for testing
function testAutocomplete() {
    console.log('🧪 Testing autocomplete...');
    const input = $('#flight-origin');
    input.val('london').trigger('input');
    console.log('✅ Triggered autocomplete for "london"');
}

function testFlightSearch() {
    console.log('🧪 Testing flight search...');

    // Set test values
    $('#flight-origin').val('London, United Kingdom (LHR)').data('airport-code', 'LHR');
    $('#flight-destination').val('OTP');
    $('#flight-departure').val('2025-08-16');
    $('#flight-return').val('2025-08-25');

    // Trigger search
    $('#flight-search-form').trigger('submit');
    console.log('✅ Triggered flight search with test data');
}

function showDebugInfo() {
    console.log('🔍 Debug Information:');
    console.log('- lciTravelpayouts config:', typeof lciTravelpayouts !== 'undefined' ? lciTravelpayouts : 'NOT LOADED');
    console.log('- Flight origin field:', $('#flight-origin').length ? 'FOUND' : 'NOT FOUND');
    console.log('- Flight destination field:', $('#flight-destination').length ? 'FOUND' : 'NOT FOUND');
    console.log('- Current origin value:', $('#flight-origin').val());
    console.log('- Current destination value:', $('#flight-destination').val());
    console.log('- Airport code data:', $('#flight-origin').data('airport-code'));

    alert('Debug info logged to console. Press F12 to view.');
}
</script>
