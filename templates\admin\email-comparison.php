<div class="wrap lci-admin-wrap">
    <div x-data="emailComparison">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">Email Comparison</h1>
            
            <div class="flex space-x-2">
                <button @click="loadData" class="btn btn-primary flex items-center" :class="{'opacity-50 cursor-not-allowed': isLoading}">
                    <template x-if="!isLoading">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </template>
                    <template x-if="isLoading">
                        <svg class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </template>
                    <span x-text="isLoading ? 'Loading...' : 'Refresh'"></span>
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-xl shadow-neumorph p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Orders</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.total"></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-neumorph p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Email Match</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.match"></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-neumorph p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Email Mismatch</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.mismatch"></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-neumorph p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">No User Account</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.no_user"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and filters -->
        <div class="bg-white rounded-xl shadow-neumorph p-4 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="relative">
                    <input
                        type="text"
                        x-model="searchQuery"
                        @input="debouncedSearch"
                        placeholder="Search by name, email, or order ID..."
                        class="form-input pl-10 w-full md:w-80"
                    >
                    <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                </div>

                <div class="flex flex-wrap items-center space-x-2">
                    <!-- Status Filter -->
                    <div class="flex items-center space-x-2 mr-4">
                        <span class="text-sm text-gray-600">Status:</span>
                        <select
                            x-model="statusFilter"
                            @change="filterData()"
                            class="form-input py-1 pl-2 pr-8"
                        >
                            <option value="">All</option>
                            <option value="match">Email Match</option>
                            <option value="mismatch">Email Mismatch</option>
                            <option value="no_user">No User Account</option>
                        </select>
                    </div>

                    <!-- Items per page -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">Show:</span>
                        <select x-model="perPage" @change="updatePagination" class="form-input py-1 pl-2 pr-8">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading state -->
        <div x-show="isLoading" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
            <div class="spinner"></div>
            <span class="loading-text">Loading email comparison data...</span>
        </div>

        <!-- Email comparison table -->
        <div x-show="!isLoading" class="bg-white rounded-xl shadow-neumorph overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="text-left bg-gray-50">
                            <th @click="sort('order_id')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Order ID
                                    <svg x-show="sortField === 'order_id'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('last_name')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Name
                                    <svg x-show="sortField === 'last_name'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('billing_email')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Billing Email
                                    <svg x-show="sortField === 'billing_email'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('customer_email')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Customer Email
                                    <svg x-show="sortField === 'customer_email'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('status')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Status
                                    <svg x-show="sortField === 'status'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('user_id')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    User ID
                                    <svg x-show="sortField === 'user_id'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('order_date')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Order Date
                                    <svg x-show="sortField === 'order_date'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="item in paginatedData" :key="item.order_id">
                            <tr class="border-t border-gray-200 hover:bg-gray-50">
                                <td class="p-4">
                                    <div class="font-medium text-primary" x-text="item.order_id"></div>
                                </td>
                                <td class="p-4">
                                    <div x-text="item.first_name + ' ' + item.last_name"></div>
                                </td>
                                <td class="p-4">
                                    <div x-text="item.billing_email || 'N/A'"></div>
                                </td>
                                <td class="p-4">
                                    <div x-text="item.customer_email || 'N/A'"></div>
                                </td>
                                <td class="p-4">
                                    <span
                                        class="status-badge"
                                        :class="{
                                            'completed': item.status === 'match',
                                            'failed': item.status === 'mismatch',
                                            'pending': item.status === 'no_user'
                                        }"
                                        x-text="getStatusLabel(item.status)"
                                    ></span>
                                </td>
                                <td class="p-4">
                                    <div x-text="item.user_id || 'N/A'"></div>
                                </td>
                                <td class="p-4">
                                    <div x-text="item.order_date ? formatDate(item.order_date) : 'N/A'"></div>
                                </td>
                            </tr>
                        </template>

                        <tr x-show="filteredData.length === 0">
                            <td colspan="7" class="p-4 text-center text-gray-500">
                                No data found matching your search criteria.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="flex items-center justify-between p-4 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    Showing <span x-text="paginatedData.length"></span> of <span x-text="filteredData.length"></span> orders
                </div>
                <div class="flex space-x-1">
                    <button
                        @click="currentPage = Math.max(currentPage - 1, 1)"
                        :disabled="currentPage === 1"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                        class="px-3 py-1 text-gray-600 bg-gray-100 rounded hover:bg-gray-200"
                    >
                        Previous
                    </button>

                    <template x-for="page in Math.min(5, totalPages)" :key="page">
                        <button
                            @click="currentPage = page"
                            :class="{'bg-primary text-white': currentPage === page, 'bg-gray-100 text-gray-600 hover:bg-gray-200': currentPage !== page}"
                            class="px-3 py-1 rounded"
                            x-text="page"
                        ></button>
                    </template>

                    <template x-if="totalPages > 5 && currentPage < totalPages - 2">
                        <span class="px-3 py-1">...</span>
                    </template>

                    <template x-if="totalPages > 5 && currentPage < totalPages - 1">
                        <button
                            @click="currentPage = totalPages"
                            :class="{'bg-primary text-white': currentPage === totalPages, 'bg-gray-100 text-gray-600 hover:bg-gray-200': currentPage !== totalPages}"
                            class="px-3 py-1 rounded"
                            x-text="totalPages"
                        ></button>
                    </template>

                    <button
                        @click="currentPage = Math.min(currentPage + 1, totalPages)"
                        :disabled="currentPage === totalPages"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
                        class="px-3 py-1 text-gray-600 bg-gray-100 rounded hover:bg-gray-200"
                    >
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
