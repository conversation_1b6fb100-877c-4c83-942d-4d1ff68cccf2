<?php
// Debug information
if (isset($_GET['key']) && isset($_GET['login'])) {
    echo '<!-- Debug: Password reset parameters detected. Key: ' . esc_html($_GET['key']) . ', Login: ' . esc_html($_GET['login']) . ' -->';
}
?>
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card shadow-lg rounded-4 border-0 overflow-hidden position-relative">
        <div class="card-body p-5">
          <!-- Login Form -->
          <form id="lci-login-form" class="auth-view">
            <h2 class="text-center text-primary mb-4">Login to LCI 2025</h2>

            <input type="hidden" id="lci_nonce" value="<?php echo wp_create_nonce('logintoLCIAGM_nonce'); ?>">

            <div class="mb-3 input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-envelope text-primary"></i>
              </span>
              <input type="email" class="form-control border-0" placeholder="Email" id="lci-email" required>
            </div>

            <div class="mb-3 input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-lock text-primary"></i>
              </span>
              <input type="password" class="form-control border-0" placeholder="Password" id="lci-password" required>
            </div>

            <button type="submit" class="btn w-100 py-2 fw-bold text-white" style="background-color: #36b1dc;">Login</button>

            <div class="mt-3 text-center">
              <a href="#" id="lci-show-forgot" class="text-primary small">Forgot Password?</a>
              <span class="mx-2 text-muted">|</span>
              <a href="#" id="lci-show-magic" class="text-primary small">Login with Magic Link</a>
            </div>

            <div id="lci-auth-message" class="mt-3 text-center small"></div>
          </form>

          <!-- Forgot Password Form -->
          <form id="lci-forgot-form" class="auth-view d-none">
            <div class="text-center mb-4">
              <h2 class="text-primary mb-2">Forgot Password</h2>
              <p class="text-muted small">Enter your email address and we'll send you a link to reset your password.</p>
            </div>

            <input type="hidden" id="lci_forgot_nonce" value="<?php echo wp_create_nonce('lci_forgot_password_nonce'); ?>">

            <div class="mb-4 input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-envelope text-primary"></i>
              </span>
              <input type="email" class="form-control border-0" placeholder="Email Address" id="lci-forgot-email" required>
            </div>

            <button type="submit" class="btn w-100 py-2 fw-bold text-white mb-3" style="background-color: #36b1dc;">Send Reset Link</button>

            <div class="text-center">
              <a href="#" id="lci-show-login" class="text-primary small">Back to Login</a>
            </div>
          </form>

          <!-- Magic Link Form -->
          <form id="lci-magic-form" class="auth-view d-none">
            <div class="text-center mb-4">
              <h2 class="text-primary mb-2">Login with Magic Link</h2>
              <p class="text-muted small">Enter your email address and we'll send you a secure login link.</p>
            </div>

            <input type="hidden" id="lci_magic_nonce" value="<?php echo wp_create_nonce('lci_magic_link_nonce'); ?>">

            <div class="mb-4 input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-envelope text-primary"></i>
              </span>
              <input type="email" class="form-control border-0" placeholder="Email Address" id="lci-magic-email" required>
            </div>

            <button type="submit" class="btn w-100 py-2 fw-bold text-white mb-3" style="background-color: #36b1dc;">Send Magic Link</button>

            <div class="text-center">
              <a href="#" id="lci-show-login-3" class="text-primary small">Back to Login</a>
            </div>
          </form>

          <!-- Change Password Form -->
          <form id="lci-change-password-form" class="auth-view d-none">
            <div class="text-center mb-4">
              <h2 class="text-primary mb-2">Reset Password</h2>
              <p class="text-muted small">Create a new password for your account.</p>
            </div>

            <input type="hidden" id="lci-change-email" value="">
            <input type="hidden" id="lci-reset-key" value="">

            <div class="mb-3 input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-lock text-primary"></i>
              </span>
              <input type="password" class="form-control border-0" placeholder="New Password" id="lci-new-password" required>
              <button class="btn bg-white border-0 password-toggle" type="button">
                <i class="fas fa-eye"></i>
              </button>
            </div>

            <div class="mb-4 input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-lock text-primary"></i>
              </span>
              <input type="password" class="form-control border-0" placeholder="Confirm New Password" id="lci-confirm-password" required>
              <button class="btn bg-white border-0 password-toggle" type="button">
                <i class="fas fa-eye"></i>
              </button>
            </div>

            <button type="submit" class="btn w-100 py-2 fw-bold text-white mb-3" style="background-color: #36b1dc;">Save Password</button>

            <div id="lci-change-password-message" class="alert d-none mt-3"></div>

            <div class="text-center">
              <a href="#" id="lci-show-login-2" class="text-primary small">Back to Login</a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Tailwind CSS for animations and modern styling -->
<style>
  /* Card animation */
  .card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Form transitions */
  .auth-view {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
    transform: translateY(0);
  }

  .auth-view.d-none {
    opacity: 0;
    transform: translateY(20px);
    position: absolute;
    pointer-events: none;
  }

  /* Input field effects */
  .input-group {
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .input-group:focus-within {
    box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.3);
  }

  /* Button effects */
  .btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .btn:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
  }

  .btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
  }

  @keyframes ripple {
    0% {
      transform: scale(0, 0);
      opacity: 0.5;
    }
    20% {
      transform: scale(25, 25);
      opacity: 0.3;
    }
    100% {
      opacity: 0;
      transform: scale(40, 40);
    }
  }

  /* Password toggle animation */
  .password-toggle {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
  }

  .password-toggle:hover {
    color: #36b1dc;
    transform: scale(1.1);
  }

  .password-toggle-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background: rgba(54, 177, 220, 0.3);
    animation: password-toggle-ripple 0.6s ease-out;
  }

  @keyframes password-toggle-ripple {
    0% {
      width: 0;
      height: 0;
      opacity: 0.5;
    }
    100% {
      width: 150%;
      height: 150%;
      opacity: 0;
    }
  }

  /* Link hover effects */
  a.text-primary {
    position: relative;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  a.text-primary:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: -2px;
    left: 0;
    background-color: #36b1dc;
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
  }

  a.text-primary:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
  }
</style>
