<?php
/**
 * LCI Travelpayouts API Integration
 * Handles monetization through travel affiliate program
 */

if (!defined('ABSPATH')) {
    exit;
}

class LCI_Travelpayouts {

    /**
     * Travelpayouts API endpoints
     */
    const API_BASE_URL = 'https://api.travelpayouts.com/v1/';
    const WIDGET_BASE_URL = 'https://widgets.travelpayouts.com/';

    /**
     * API credentials (to be set in admin)
     */
    private static $api_token = '';
    private static $marker = '';
    private static $partner_id = '';

    /**
     * Initialize the class
     */
    public static function init() {
        // Load API credentials from WordPress options
        self::$api_token = get_option('lci_travelpayouts_api_token', '');
        self::$marker = get_option('lci_travelpayouts_marker', '');
        self::$partner_id = get_option('lci_travelpayouts_partner_id', '');

        // Add hooks
        add_action('wp_enqueue_scripts', [__CLASS__, 'enqueue_scripts']);
        add_action('wp_ajax_lci_get_flight_prices', [__CLASS__, 'ajax_get_flight_prices']);
        add_action('wp_ajax_nopriv_lci_get_flight_prices', [__CLASS__, 'ajax_get_flight_prices']);

        // Add admin hooks
        if (is_admin()) {
            add_action('admin_menu', [__CLASS__, 'add_admin_menu']);
            add_action('admin_init', [__CLASS__, 'register_settings']);
        }
    }

    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        // Load on LCI dashboard pages or pages with travel shortcodes
        $should_load = false;

        if (is_page()) {
            $post_content = get_post()->post_content;
            if (has_shortcode($post_content, 'lci_dashboard') ||
                has_shortcode($post_content, 'lci_travel_dashboard') ||
                has_shortcode($post_content, 'lci_flight_widget')) {
                $should_load = true;
            }
        }

        // Also check if we're on a dashboard page by URL
        if (!$should_load && isset($_GET['tab']) && $_GET['tab'] === 'flights') {
            $should_load = true;
        }

        if (!$should_load) {
            return;
        }

        wp_enqueue_script(
            'lci-travelpayouts',
            LCI2025_URL . 'assets/js/travelpayouts.js',
            ['jquery'],
            LCI2025_VERSION,
            true
        );

        wp_localize_script('lci-travelpayouts', 'lciTravelpayouts', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lci_travelpayouts_nonce'),
            'marker' => self::$marker,
            'partner_id' => self::$partner_id,
            'event_location' => [
                'city' => 'Brasov',
                'country' => 'Romania',
                'airport_code' => 'GHV', // Brasov-Ghimbav Airport
                'coordinates' => ['lat' => 45.6580, 'lng' => 25.6012]
            ],
            'event_dates' => [
                'start' => '2025-08-16',
                'end' => '2025-08-25'
            ]
        ]);
    }

    /**
     * Get flight prices from Travelpayouts API
     */
    public static function get_flight_prices($origin, $destination, $departure_date, $return_date = null) {
        if (empty(self::$api_token)) {
            return new WP_Error('no_api_token', 'Travelpayouts API token not configured');
        }

        $endpoint = self::API_BASE_URL . 'prices/cheap';
        $params = [
            'origin' => $origin,
            'destination' => $destination,
            'depart_date' => $departure_date,
            'token' => self::$api_token,
            'currency' => 'EUR',
            'limit' => 10
        ];

        if ($return_date) {
            $params['return_date'] = $return_date;
        }

        $url = $endpoint . '?' . http_build_query($params);

        $response = wp_remote_get($url, [
            'timeout' => 30,
            'headers' => [
                'Accept' => 'application/json'
            ]
        ]);

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('invalid_json', 'Invalid JSON response from API');
        }

        return $data;
    }



    /**
     * Generate affiliate tracking URL
     */
    public static function get_affiliate_url($base_url, $additional_params = []) {
        $params = array_merge([
            'marker' => self::$marker,
            'partner_id' => self::$partner_id
        ], $additional_params);

        return $base_url . '?' . http_build_query($params);
    }

    /**
     * AJAX handler for flight prices
     */
    public static function ajax_get_flight_prices() {
        check_ajax_referer('lci_travelpayouts_nonce', 'nonce');

        $origin = sanitize_text_field($_POST['origin'] ?? '');
        $destination = sanitize_text_field($_POST['destination'] ?? 'GHV'); // Default to Brasov
        $departure_date = sanitize_text_field($_POST['departure_date'] ?? '');
        $return_date = sanitize_text_field($_POST['return_date'] ?? '');

        if (empty($origin) || empty($departure_date)) {
            wp_send_json_error(['message' => 'Missing required parameters']);
        }

        $result = self::get_flight_prices($origin, $destination, $departure_date, $return_date);

        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        }

        wp_send_json_success($result);
    }



    /**
     * Generate flight search widget HTML
     */
    public static function get_flight_widget($origin_placeholder = 'Your City') {
        $marker = self::$marker;
        $partner_id = self::$partner_id;

        if (empty($marker)) {
            return '<p>Travelpayouts configuration required.</p>';
        }

        ob_start();
        ?>
        <div class="lci-travel-widget flight-widget">
            <h3 class="widget-title">Find Flights to Romania</h3>
            <div class="travel-search-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>From</label>
                        <input type="text" id="flight-origin" placeholder="<?php echo esc_attr($origin_placeholder); ?>" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>To</label>
                        <input type="text" id="flight-destination" value="Brasov, Romania" readonly class="form-control">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Departure</label>
                        <input type="date" id="flight-departure" value="2025-08-15" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Return</label>
                        <input type="date" id="flight-return" value="2025-08-26" class="form-control">
                    </div>
                </div>
                <button type="button" id="search-flights" class="btn btn-primary">Search Flights</button>
            </div>
            <div id="flight-results" class="travel-results"></div>
        </div>
        <?php
        return ob_get_clean();
    }



    /**
     * Add admin menu for Travelpayouts settings
     */
    public static function add_admin_menu() {
        add_submenu_page(
            'lci-dashboard',
            'Travel Monetization',
            'Travel Monetization',
            'manage_options',
            'lci-travelpayouts',
            [__CLASS__, 'render_admin_page']
        );
    }

    /**
     * Register settings
     */
    public static function register_settings() {
        register_setting('lci_travelpayouts_settings', 'lci_travelpayouts_api_token');
        register_setting('lci_travelpayouts_settings', 'lci_travelpayouts_marker');
        register_setting('lci_travelpayouts_settings', 'lci_travelpayouts_partner_id');
    }

    /**
     * Render admin settings page
     */
    public static function render_admin_page() {
        include LCI2025_PATH . 'templates/admin/travelpayouts-settings.php';
    }
}

// Initialize the class
LCI_Travelpayouts::init();
