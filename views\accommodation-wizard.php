<?php
// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(home_url('/login'));
    exit;
}

// Get AJAX URL and nonce for JavaScript
$ajax_url = admin_url('admin-ajax.php');
$nonce = wp_create_nonce('lci_ajax_nonce');

// Output Alpine.js and AJAX configuration directly
echo '<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>';
echo '<script>window.lci_dashboard_accommodation_ajax = { ajax_url: "' . $ajax_url . '", nonce: "' . $nonce . '" };</script>';

// Debug information for administrators
if (current_user_can('administrator')) {
    echo '<div class="alert alert-info mb-4">';
    echo '<h5><i class="fas fa-bug me-2"></i> Debug Information</h5>';
    echo '<p>This is the new accommodation wizard using Alpine.js.</p>';
    echo '<p>AJAX URL: ' . $ajax_url . '</p>';
    echo '<p>Nonce: ' . $nonce . '</p>';
    echo '</div>';
}
?>

<div class="accommodation-wizard-container shadow-lg rounded-lg transition-all duration-300 transform hover:shadow-xl" x-data="accommodationWizard()" x-cloak>
    <!-- Loading Overlay -->
    <div class="loading-overlay" x-show="loading" x-transition>
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
    </div>

    <!-- Wizard Header -->
    <div class="wizard-header">
        <h2 class="wizard-title" x-text="getStepTitle()">Accommodation Wizard</h2>

        <!-- Progress Bar -->
        <div class="wizard-progress">
            <div class="wizard-progress-bar">
                <div class="wizard-progress-bar-inner" :style="'width: ' + progress + '%'"></div>
            </div>
            <div class="wizard-steps">
                <template x-for="(step, index) in totalSteps" :key="index">
                    <div class="wizard-step" :class="{'active': currentStep >= index + 1, 'completed': currentStep > index + 1}">
                        <span x-text="index + 1"></span>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Wizard Content -->
    <div class="wizard-content transition-all duration-300">
        <!-- Step content will be dynamically shown based on currentStep -->

        <!-- Step 1: Initial Question -->
        <div class="wizard-step-content" x-show="currentStep === 1" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-x-4" x-transition:enter-end="opacity-100 transform translate-x-0" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 transform translate-x-0" x-transition:leave-end="opacity-0 transform -translate-x-4">
            <!-- For users with Main Pretour (ID 743) -->
            <template x-if="hasMainPretour">
                <div>
                    <h3 class="text-center mb-4">Accommodation for Main Pretour</h3>
                    <div class="info-box">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Will you arrive early in Bucharest and need accommodation before the tour starts?</span>
                    </div>

                    <div class="option-cards">
                        <div class="option-card"
                             :class="{'selected': bucharest.selected === 'yes'}"
                             @click="selectBucharest('yes')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/bucharest.jpg" alt="Yes" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">Yes, I need a place to stay in Bucharest</h4>
                                <p class="option-card-description">Before the Main Pretour starts</p>
                            </div>
                        </div>

                        <div class="option-card"
                             :class="{'selected': bucharest.selected === 'no'}"
                             @click="selectBucharest('no')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/brasov.jpg" alt="No" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">No, I need accommodation in Brasov</h4>
                                <p class="option-card-description">I only need accommodation in Brasov</p>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <!-- For users without Main Pretour -->
            <template x-if="!hasMainPretour">
                <div>
                    <h3 class="text-center mb-4">Do you need accommodation in Brasov?</h3>
                    <div class="info-box">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Please select if you need accommodation in Brasov</span>
                    </div>

                    <div class="option-cards">
                        <div class="option-card"
                             :class="{'selected': brasov.selected === 'yes'}"
                             @click="selectBrasovYesNo('yes')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/brasov.jpg" alt="Yes" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">Yes, I need accommodation in Brasov</h4>
                                <p class="option-card-description">Show me accommodation options</p>
                            </div>
                        </div>

                        <div class="option-card"
                             :class="{'selected': brasov.selected === 'no'}"
                             @click="selectBrasovYesNo('no')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/no-accommodation.jpg" alt="No" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">No, I don't need accommodation</h4>
                                <p class="option-card-description">I have my own accommodation</p>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Step 2: Nights Selection or Brasov Options -->
        <div class="wizard-step-content" x-show="currentStep === 2" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-x-4" x-transition:enter-end="opacity-100 transform translate-x-0" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 transform translate-x-0" x-transition:leave-end="opacity-0 transform -translate-x-4">
            <!-- For users with Main Pretour who selected Bucharest -->
            <template x-if="hasMainPretour && bucharest.selected === 'yes' && flowStep === 2">
                <div>
                    <h3 class="text-center mb-4">How many nights will you stay in Bucharest?</h3>
                    <div class="info-box">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Select the number of nights you need accommodation in Bucharest before the Main Pretour</span>
                    </div>

                    <div class="nights-selector">
                        <div class="nights-selector-container">
                            <button type="button" class="nights-btn nights-btn-minus" @click="decreaseNights('bucharest')" :disabled="bucharest.nights <= 1">
                                <i class="fas fa-minus"></i>
                            </button>
                            <div class="nights-display">
                                <span class="nights-count" x-text="bucharest.nights">1</span>
                                <span class="nights-label" x-text="bucharest.nights === 1 ? 'Night' : 'Nights'">Night</span>
                            </div>
                            <button type="button" class="nights-btn nights-btn-plus" @click="increaseNights('bucharest')">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </template>

            <!-- For users with Main Pretour who selected No for Bucharest, or users without Main Pretour -->
            <template x-if="(hasMainPretour && bucharest.selected === 'no' && flowStep === 4) || (!hasMainPretour && brasov.selected === 'yes' && flowStep === 1)">
                <div>
                    <h3 class="text-center mb-4">Brasov Accommodation Options</h3>
                    <div class="info-box">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Please select when you need accommodation in Brasov</span>
                    </div>

                    <div class="option-cards">
                        <div class="option-card"
                             :class="{'selected': brasov.type === 'pre'}"
                             @click="selectBrasovType('pre')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/pre-event.jpg" alt="Pre-event" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">Pre-event Accommodation</h4>
                                <p class="option-card-description">Stay in Brasov before the event</p>
                            </div>
                        </div>

                        <div class="option-card"
                             :class="{'selected': brasov.type === 'main'}"
                             @click="selectBrasovType('main')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/main-event.jpg" alt="Main Event" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">Main Event Accommodation</h4>
                                <p class="option-card-description">Stay in Brasov during the event</p>
                            </div>
                        </div>

                        <div class="option-card"
                             :class="{'selected': brasov.type === 'post'}"
                             @click="selectBrasovType('post')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/post-event.jpg" alt="Post-event" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">Post-event Accommodation</h4>
                                <p class="option-card-description">Stay in Brasov after the event</p>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <!-- For users who selected Pre-event or Post-event Brasov accommodation -->
            <template x-if="((hasMainPretour && flowStep === 5) || (!hasMainPretour && flowStep === 2)) && (brasov.type === 'pre' || brasov.type === 'post')">
                <div>
                    <h3 class="text-center mb-4" x-text="'How many nights will you stay in Brasov ' + (brasov.type === 'pre' ? 'before' : 'after') + ' the event?'"></h3>
                    <div class="info-box">
                        <i class="fas fa-info-circle me-2"></i>
                        <span x-text="'Select the number of nights you need accommodation in Brasov ' + (brasov.type === 'pre' ? 'before' : 'after') + ' the event'"></span>
                    </div>

                    <div class="nights-selector">
                        <div class="nights-selector-container">
                            <button type="button" class="nights-btn nights-btn-minus" @click="decreaseNights('brasov')" :disabled="brasov.nights <= 1">
                                <i class="fas fa-minus"></i>
                            </button>
                            <div class="nights-display">
                                <span class="nights-count" x-text="brasov.nights">1</span>
                                <span class="nights-label" x-text="brasov.nights === 1 ? 'Night' : 'Nights'">Night</span>
                            </div>
                            <button type="button" class="nights-btn nights-btn-plus" @click="increaseNights('brasov')">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Step 3: Product Selection -->
        <div class="wizard-step-content" x-show="currentStep === 3" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-x-4" x-transition:enter-end="opacity-100 transform translate-x-0" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 transform translate-x-0" x-transition:leave-end="opacity-0 transform -translate-x-4">
            <div class="text-center mb-4">
                <h3 x-text="getProductsTitle()">Accommodation Options</h3>
                <p class="text-muted">Select your preferred accommodation option</p>
            </div>

            <div class="loading-indicator text-center py-4" x-show="loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Loading accommodation options...</p>
            </div>

            <div class="alert alert-info" x-show="!loading && products.length === 0">
                <i class="fas fa-info-circle me-2"></i>
                No accommodation options are currently available for your selection. Please try different options or contact support.
            </div>

            <div class="product-cards" x-show="!loading && products.length > 0">
                <template x-for="(product, index) in products" :key="product.id">
                    <div class="product-card">
                        <div class="product-card-image">
                            <img :src="product.image" :alt="product.name">
                        </div>
                        <div class="product-card-content">
                            <h4 class="product-card-title" x-text="product.name"></h4>
                            <div class="product-card-description" x-text="product.description"></div>
                            <div class="product-card-price" x-html="product.price_html"></div>
                            <button
                                type="button"
                                class="product-card-btn"
                                :class="{'added': product.added}"
                                @click="addToCart(product.id)"
                                :disabled="product.added"
                            >
                                <template x-if="!product.added">
                                    <span>Add to Cart</span>
                                </template>
                                <template x-if="product.added">
                                    <span>Added to Cart</span>
                                </template>
                            </button>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Step 4: Final Step (only for users with Main Pretour) -->
        <div class="wizard-step-content" x-show="currentStep === 4" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-x-4" x-transition:enter-end="opacity-100 transform translate-x-0" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 transform translate-x-0" x-transition:leave-end="opacity-0 transform -translate-x-4">
            <template x-if="hasMainPretour">
                <div>
                    <h3 class="text-center mb-4">Brasov Accommodation Options</h3>
                    <div class="info-box">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Please select when you need accommodation in Brasov</span>
                    </div>

                    <div class="option-cards">
                        <div class="option-card"
                             :class="{'selected': brasov.type === 'pre'}"
                             @click="selectBrasovType('pre')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/pre-event.jpg" alt="Pre-event" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">Pre-event Accommodation</h4>
                                <p class="option-card-description">Stay in Brasov before the event</p>
                            </div>
                        </div>

                        <div class="option-card"
                             :class="{'selected': brasov.type === 'main'}"
                             @click="selectBrasovType('main')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/main-event.jpg" alt="Main Event" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">Main Event Accommodation</h4>
                                <p class="option-card-description">Stay in Brasov during the event</p>
                            </div>
                        </div>

                        <div class="option-card"
                             :class="{'selected': brasov.type === 'post'}"
                             @click="selectBrasovType('post')">
                            <div class="option-card-image">
                                <img src="<?php echo LCI2025_URL; ?>assets/images/post-event.jpg" alt="Post-event" onerror="this.src='https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'">
                            </div>
                            <div class="option-card-content">
                                <h4 class="option-card-title">Post-event Accommodation</h4>
                                <p class="option-card-description">Stay in Brasov after the event</p>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Wizard Navigation -->
    <div class="wizard-navigation">
        <button
            type="button"
            class="wizard-btn wizard-btn-secondary"
            @click="prevStep()"
            x-show="currentStep > 1"
            x-transition
        >
            <i class="fas fa-arrow-left me-2"></i> Back
        </button>

        <button
            type="button"
            class="wizard-btn wizard-btn-primary"
            @click="nextStep()"
            x-show="!isLastStep"
            x-transition
        >
            Next <i class="fas fa-arrow-right ms-2"></i>
        </button>

        <button
            type="button"
            class="wizard-btn wizard-btn-success"
            @click="completeWizard()"
            x-show="isLastStep"
            x-transition
        >
            Complete <i class="fas fa-check ms-2"></i>
        </button>
    </div>

    <!-- Success Modal -->
    <div class="wizard-modal" x-show="showModal" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-95">
        <div class="wizard-modal-content shadow-2xl transform transition-all">
            <div class="wizard-modal-header">
                <h3>Success!</h3>
                <button type="button" class="wizard-modal-close" @click="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="wizard-modal-body">
                <div class="wizard-modal-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <p>Accommodation added to your cart!</p>
                <div class="wizard-modal-progress">
                    <div class="wizard-modal-progress-bar" :style="'width: ' + modalProgress + '%'"></div>
                </div>
            </div>
            <div class="wizard-modal-footer">
                <button type="button" class="wizard-btn wizard-btn-primary" @click="closeModal()">
                    Continue
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Accommodation Wizard Alpine.js Component
function accommodationWizard() {
    return {
        // Wizard state
        currentStep: 1,
        flowStep: 1,
        totalSteps: 4,
        progress: 25,
        loading: false,
        showModal: false,
        modalProgress: 0,
        progressInterval: null,
        isLastStep: false,
        wizardCompleted: false,

        // User data
        hasMainPretour: false,
        bucharest: {
            selected: null, // 'yes' or 'no'
            nights: 1
        },
        brasov: {
            selected: null, // 'yes', 'no', or 'type'
            type: null, // 'pre', 'main', or 'post'
            nights: 1
        },

        // Products data
        products: [],
        selectedProduct: null,

        // Initialize the wizard
        init() {
            // Console log removed
            // Check if user has Main Pretour (product ID 743)
            this.checkUserProducts();

            // Update progress bar
            this.updateProgress();
        },

        // Check if user has purchased specific products
        async checkUserProducts() {
            this.loading = true;
            // Console log removed

            try {
                const response = await fetch(lci_dashboard_accommodation_ajax.ajax_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'check_user_products',
                        security: lci_dashboard_accommodation_ajax.nonce,
                        product_id: 743 // Main Pretour ID
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                // Console log removed

                if (data.success && data.data) {
                    this.hasMainPretour = data.data.has_product;
                    // Console log removed
                }
            } catch (error) {
                // Console error removed
                // Fallback to false if there's an error
                this.hasMainPretour = false;
            } finally {
                this.loading = false;
            }
        },

        // Select Bucharest option (Yes/No)
        selectBucharest(option) {
            // Console log removed
            this.bucharest.selected = option;

            // Auto-advance if they select 'no'
            if (option === 'no') {
                this.flowStep = 4; // Skip to Brasov options
                setTimeout(() => this.nextStep(), 300);
            } else if (option === 'yes') {
                this.flowStep = 2; // Go to nights selection
                setTimeout(() => this.nextStep(), 300);
            }
        },

        // Select Brasov Yes/No option
        selectBrasovYesNo(option) {
            // Console log removed
            this.brasov.selected = option;

            if (option === 'yes') {
                // If they select yes, change to type selection
                setTimeout(() => {
                    this.brasov.selected = 'type';
                    this.flowStep = 1; // Set flow step for Brasov type selection
                    this.nextStep();
                }, 300);
            } else if (option === 'no') {
                // If they select no, complete the wizard
                setTimeout(() => {
                    this.wizardCompleted = true;
                    // Redirect to accommodation page
                    window.location.href = '?tab=accommodation&wizard-completed=1';
                }, 1000);
            }
        },

        // Select Brasov type option (pre/main/post)
        selectBrasovType(option) {
            // Console log removed
            this.brasov.type = option;

            if (option === 'pre' || option === 'post') {
                // For pre/post event, go to nights selection
                this.flowStep = this.hasMainPretour ? 5 : 2;
                setTimeout(() => this.nextStep(), 300);
            } else if (option === 'main') {
                // For main event, go directly to products
                this.flowStep = this.hasMainPretour ? 6 : 3;
                setTimeout(() => {
                    this.loadProducts('brasov');
                    this.nextStep();
                }, 300);
            }
        },

        // Increase nights count
        increaseNights(location) {
            // Console log removed
            if (location === 'bucharest') {
                this.bucharest.nights++;
            } else {
                this.brasov.nights++;
            }
        },

        // Decrease nights count
        decreaseNights(location) {
            // Console log removed
            if (location === 'bucharest' && this.bucharest.nights > 1) {
                this.bucharest.nights--;
            } else if (location === 'brasov' && this.brasov.nights > 1) {
                this.brasov.nights--;
            }
        },

        // Get step title based on current step
        getStepTitle() {
            if (this.hasMainPretour) {
                // Titles for users with Main Pretour
                if (this.currentStep === 1) {
                    return 'Bucharest Accommodation';
                } else if (this.currentStep === 2) {
                    // Check if we're asking about Bucharest or Brasov nights
                    if (this.bucharest.selected === 'yes' && this.flowStep === 2) {
                        return 'Nights in Bucharest';
                    } else if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                        return 'Nights in Brasov';
                    } else {
                        return 'Brasov Accommodation';
                    }
                } else if (this.currentStep === 3) {
                    // Check if we're showing Bucharest or Brasov products
                    if (this.bucharest.selected === 'yes' && this.flowStep === 3) {
                        return 'Bucharest Accommodation Options';
                    } else {
                        return 'Brasov Accommodation Options';
                    }
                } else if (this.currentStep === 4) {
                    return 'Brasov Accommodation Type';
                }
            } else {
                // Titles for users without Main Pretour
                if (this.currentStep === 1) {
                    return 'Brasov Accommodation';
                } else if (this.currentStep === 2) {
                    if (this.brasov.selected === 'type') {
                        return 'Brasov Accommodation Type';
                    } else if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                        return 'Nights in Brasov';
                    }
                    return 'Brasov Accommodation Type';
                } else if (this.currentStep === 3) {
                    return 'Brasov Accommodation Options';
                }
            }

            return 'Accommodation Wizard';
        },

        // Get products title based on current selection
        getProductsTitle() {
            if (this.hasMainPretour && this.bucharest.selected === 'yes' && this.flowStep === 3) {
                return 'Bucharest Accommodation Options';
            } else if (this.brasov.type === 'pre') {
                return 'Pre-event Brasov Accommodation';
            } else if (this.brasov.type === 'main') {
                return 'Main Event Brasov Accommodation';
            } else if (this.brasov.type === 'post') {
                return 'Post-event Brasov Accommodation';
            }

            return 'Accommodation Options';
        },

        // Update progress bar
        updateProgress() {
            const totalSteps = this.hasMainPretour ? 4 : 3;
            this.totalSteps = totalSteps;
            this.progress = (this.currentStep / totalSteps) * 100;

            // Check if this is the last step
            this.isLastStep = (this.currentStep === totalSteps);
        },

        // Go to next step
        nextStep() {
            // Console log removed

            if (this.hasMainPretour) {
                // Logic for users with Main Pretour
                if (this.currentStep === 1) {
                    // From initial question to next step
                    this.currentStep++;
                } else if (this.currentStep === 2) {
                    // From nights selection or Brasov options to products
                    if (this.bucharest.selected === 'yes' && this.flowStep === 2) {
                        // From Bucharest nights to Bucharest products
                        this.flowStep = 3;
                        this.loadProducts('bucharest');
                        this.currentStep++;
                    } else if (this.brasov.type && this.flowStep === 5) {
                        // From Brasov nights to Brasov products
                        this.flowStep = 6;
                        this.loadProducts('brasov');
                        this.currentStep++;
                    }
                } else if (this.currentStep === 3 && this.bucharest.selected === 'yes') {
                    // From Bucharest products to Brasov options
                    this.flowStep = 4;
                    this.currentStep++;
                } else {
                    // Regular progression
                    this.currentStep++;
                }
            } else {
                // Logic for users without Main Pretour
                if (this.currentStep === 1) {
                    // From initial question to next step
                    this.currentStep++;
                } else if (this.currentStep === 2) {
                    // From Brasov type or nights to products
                    if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                        // From nights selection to products
                        this.flowStep = 3;
                        this.loadProducts('brasov');
                    } else if (this.brasov.type === 'main') {
                        // Already loaded products for main event
                        this.flowStep = 3;
                    }
                    this.currentStep++;
                } else {
                    // Regular progression
                    this.currentStep++;
                }
            }

            this.updateProgress();
            // Console log removed
        },

        // Go to previous step
        prevStep() {
            // Console log removed

            if (this.currentStep > 1) {
                if (this.hasMainPretour) {
                    // Logic for users with Main Pretour
                    if (this.currentStep === 4 && this.bucharest.selected === 'yes') {
                        // From Brasov options back to Bucharest products
                        this.flowStep = 3;
                        this.currentStep--;
                    } else if (this.currentStep === 3) {
                        // From products back to nights selection or initial question
                        if (this.bucharest.selected === 'yes' && this.flowStep === 3) {
                            // From Bucharest products to nights selection
                            this.flowStep = 2;
                            this.currentStep--;
                        } else if (this.flowStep === 6) {
                            // From Brasov products to nights selection
                            this.flowStep = 5;
                            this.currentStep--;
                        }
                    } else {
                        // Regular back navigation
                        this.currentStep--;
                    }
                } else {
                    // Logic for users without Main Pretour
                    if (this.currentStep === 3 && this.flowStep === 3) {
                        // From products back to nights selection or type selection
                        if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                            // Back to nights selection
                            this.flowStep = 2;
                        } else {
                            // Back to type selection
                            this.flowStep = 1;
                        }
                        this.currentStep--;
                    } else {
                        // Regular back navigation
                        this.currentStep--;
                    }
                }

                this.updateProgress();
            }

            // Console log removed
        },

        // Complete the wizard
        completeWizard() {
            // Console log removed
            this.wizardCompleted = true;
            window.location.href = '?tab=accommodation&wizard-completed=1';
        },

        // Show success modal
        showSuccessModal() {
            this.showModal = true;
            this.startModalProgress();
        },

        // Close modal
        closeModal() {
            this.showModal = false;
            this.stopModalProgress();
        },

        // Start modal progress bar
        startModalProgress() {
            this.modalProgress = 0;
            clearInterval(this.progressInterval);

            const duration = 3000; // 3 seconds
            const interval = 30; // Update every 30ms
            const steps = duration / interval;
            const increment = 100 / steps;

            this.progressInterval = setInterval(() => {
                this.modalProgress += increment;

                if (this.modalProgress >= 100) {
                    this.stopModalProgress();
                    this.closeModal();
                }
            }, interval);
        },

        // Stop modal progress bar
        stopModalProgress() {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        },

        // Load products based on selection
        async loadProducts(type) {
            this.loading = true;
            this.products = [];
            // Console log removed

            let categoryId;
            let metaQuery = {};

            // Determine which products to load
            if (type === 'bucharest') {
                // Bucharest accommodation (category ID 33)
                categoryId = 33;
                if (this.bucharest.nights > 0) {
                    metaQuery = {
                        key: '_nights',
                        value: this.bucharest.nights.toString(),
                        compare: '='
                    };
                }
                // Console log removed
            } else if (type === 'brasov') {
                // Brasov accommodation
                switch (this.brasov.type) {
                    case 'pre':
                        categoryId = 37; // Pre-event accommodation
                        metaQuery = {
                            key: '_timing',
                            value: 'before',
                            compare: '='
                        };
                        // Console log removed
                        break;
                    case 'main':
                        categoryId = 1; // Main event accommodation
                        // Console log removed
                        break;
                    case 'post':
                        categoryId = 37; // Post-event accommodation
                        metaQuery = {
                            key: '_timing',
                            value: 'after',
                            compare: '='
                        };
                        // Console log removed
                        break;
                    default:
                        // Console error removed
                        this.loading = false;
                        return;
                }
            } else {
                // Console error removed
                this.loading = false;
                return;
            }

            try {
                // Console log removed

                const response = await fetch(lci_dashboard_accommodation_ajax.ajax_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'get_accommodation_products',
                        security: lci_dashboard_accommodation_ajax.nonce,
                        category_id: categoryId,
                        meta_query: JSON.stringify(metaQuery)
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                // Console log removed

                if (data.success && data.data && data.data.products) {
                    this.products = data.data.products;
                    // Console log removed
                } else {
                    // Console error removed
                    // Fallback to empty array if there's an error
                    this.products = [];
                }
            } catch (error) {
                // Console error removed
                // Fallback to empty array if there's an error
                this.products = [];

                // Provide fallback products for testing if needed
                if (type === 'bucharest') {
                    this.products = [
                        {
                            id: 101,
                            name: 'Bucharest Hotel (Fallback)',
                            price: '€120',
                            price_html: '<span class="amount">€120</span> <small>per night</small>',
                            description: 'Fallback product for testing.',
                            image: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp',
                            added: false
                        }
                    ];
                } else if (type === 'brasov') {
                    this.products = [
                        {
                            id: 201,
                            name: 'Brasov Hotel (Fallback)',
                            price: '€100',
                            price_html: '<span class="amount">€100</span> <small>per night</small>',
                            description: 'Fallback product for testing.',
                            image: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp',
                            added: false
                        }
                    ];
                }
            } finally {
                this.loading = false;
            }
        },

        // Add product to cart
        async addToCart(productId) {
            const product = this.products.find(p => p.id === productId);
            if (!product) {
                // Console error removed
                return;
            }

            this.loading = true;
            // Console log removed

            try {
                const response = await fetch(lci_dashboard_accommodation_ajax.ajax_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'lci-dashboard-add-to-cart',
                        security: lci_dashboard_accommodation_ajax.nonce,
                        product_id: productId,
                        quantity: 1
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                // Console log removed

                if (data.success) {
                    // Show success modal
                    this.selectedProduct = product;
                    this.showSuccessModal();

                    // Mark product as added
                    product.added = true;

                    // Update mini cart if available
                    if (typeof updateMiniCart === 'function') {
                        try {
                            updateMiniCart(data.data.cart_count, data.data.cart_total);
                        } catch (e) {
                            // Console warning removed
                        }
                    }

                    // Console log removed
                } else {
                    // Console error removed
                    alert('Error adding product to cart. Please try again.');
                }
            } catch (error) {
                // Console error removed
                alert('Error adding product to cart. Please try again.');
            } finally {
                this.loading = false;
            }
        }
    };
}
</script>

<style>
/* Basic Styles for the Accommodation Wizard */
.accommodation-wizard-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    border-radius: 8px;
}

.loading-spinner {
    font-size: 2rem;
    color: #36b1dc;
}

/* Wizard Header */
.wizard-header {
    margin-bottom: 30px;
    text-align: center;
}

.wizard-title {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
}

/* Progress Bar */
.wizard-progress {
    margin-bottom: 30px;
}

.wizard-progress-bar {
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    margin-bottom: 10px;
    overflow: hidden;
}

.wizard-progress-bar-inner {
    height: 100%;
    background-color: #36b1dc;
    transition: width 0.3s ease;
}

.wizard-steps {
    display: flex;
    justify-content: space-between;
}

.wizard-step {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    color: #6c757d;
    transition: all 0.3s ease;
}

.wizard-step.active {
    background-color: #36b1dc;
    color: #fff;
}

.wizard-step.completed {
    background-color: #28a745;
    color: #fff;
}

/* Wizard Content */
.wizard-content {
    min-height: 300px;
    margin-bottom: 30px;
}

.wizard-step-content {
    display: none;
    animation: fadeIn 0.5s ease;
}

.wizard-step-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Wizard Navigation */
.wizard-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.wizard-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.wizard-btn:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    z-index: -2;
}

.wizard-btn:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    z-index: -1;
}

.wizard-btn:hover:before {
    width: 100%;
}

.wizard-btn-primary {
    background-color: #36b1dc;
    color: #fff;
    box-shadow: 0 2px 4px rgba(54, 177, 220, 0.3);
}

.wizard-btn-primary:hover {
    background-color: #2a8fb0;
    box-shadow: 0 4px 8px rgba(54, 177, 220, 0.5);
    transform: translateY(-2px);
}

.wizard-btn-secondary {
    background-color: #6c757d;
    color: #fff;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

.wizard-btn-secondary:hover {
    background-color: #5a6268;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.5);
    transform: translateY(-2px);
}

.wizard-btn-success {
    background-color: #28a745;
    color: #fff;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.wizard-btn-success:hover {
    background-color: #218838;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.5);
    transform: translateY(-2px);
}

/* Modal Styles */
.wizard-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.wizard-modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.wizard-modal-header {
    padding: 15px;
    background-color: #36b1dc;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wizard-modal-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.2rem;
    cursor: pointer;
}

.wizard-modal-body {
    padding: 20px;
    text-align: center;
}

.wizard-modal-icon {
    font-size: 3rem;
    color: #28a745;
    margin-bottom: 15px;
}

.wizard-modal-progress {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    margin-top: 20px;
    overflow: hidden;
}

.wizard-modal-progress-bar {
    height: 100%;
    background-color: #36b1dc;
    transition: width 0.1s linear;
}

.wizard-modal-footer {
    padding: 15px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

/* Option Cards */
.option-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    margin-bottom: 20px;
}

.option-card {
    width: 200px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
    position: relative;
    z-index: 1;
}

.option-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.option-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(54, 177, 220, 0.1) 0%, rgba(54, 177, 220, 0) 100%);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.option-card:hover:before {
    opacity: 1;
}

.option-card.selected {
    border-color: #36b1dc;
}

.option-card-image {
    height: 120px;
    overflow: hidden;
}

.option-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.option-card:hover .option-card-image img {
    transform: scale(1.05);
}

.option-card-content {
    padding: 15px;
}

.option-card-title {
    font-size: 1rem;
    margin-bottom: 5px;
    color: #333;
}

.option-card-description {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Nights Selector */
.nights-selector {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.nights-selector-container {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 10px 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.nights-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background-color: #36b1dc;
    color: #fff;
    font-size: 1.2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nights-btn:hover {
    background-color: #2a8fb0;
}

.nights-btn:disabled {
    background-color: #e9ecef;
    color: #adb5bd;
    cursor: not-allowed;
}

.nights-display {
    margin: 0 20px;
    text-align: center;
}

.nights-count {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    display: block;
}

.nights-label {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Product Cards */
.product-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
}

.product-card {
    width: 250px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-color: #fff;
    position: relative;
    z-index: 1;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.product-card:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #36b1dc, #2a8fb0);
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.product-card:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.product-card-image {
    height: 150px;
    overflow: hidden;
}

.product-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-card-image img {
    transform: scale(1.05);
}

.product-card-content {
    padding: 15px;
}

.product-card-title {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #333;
}

.product-card-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 15px;
    max-height: 80px;
    overflow: hidden;
}

.product-card-price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #36b1dc;
    margin-bottom: 15px;
}

.product-card-btn {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 4px;
    background-color: #36b1dc;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-card-btn:hover {
    background-color: #2a8fb0;
}

.product-card-btn.added {
    background-color: #28a745;
}

.product-card-btn.added:hover {
    background-color: #218838;
}

/* Info Box */
.info-box {
    background-color: #e9f7fb;
    border-left: 4px solid #36b1dc;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    display: flex;
    align-items: center;
}

.info-box i {
    font-size: 1.2rem;
    color: #36b1dc;
    margin-right: 10px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .option-cards {
        flex-direction: column;
        align-items: center;
    }

    .option-card {
        width: 100%;
        max-width: 300px;
    }

    .product-card {
        width: 100%;
        max-width: 300px;
    }
}
</style>
