<?php
/**
 * AJAX Handlers for LCI 2025 Dashboard
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get mini cart items for the regalia shop
 */
function lci_get_mini_cart_items() {
    // Debug info
    error_log('lci_get_mini_cart_items called');
    error_log('POST data: ' . print_r($_POST, true));

    // Check nonce - accept both 'nonce' and 'security' parameters
    $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
    $security = isset($_POST['security']) ? $_POST['security'] : '';

    // Try to verify with multiple nonce names
    $nonce_verified = false;

    // Try with lci_mini_cart_nonce
    if (wp_verify_nonce($nonce, 'lci_mini_cart_nonce') || wp_verify_nonce($security, 'lci_mini_cart_nonce')) {
        $nonce_verified = true;
    }

    // Try with lci-dashboard-add-to-cart-nonce
    if (!$nonce_verified && (wp_verify_nonce($nonce, 'lci-dashboard-add-to-cart-nonce') || wp_verify_nonce($security, 'lci-dashboard-add-to-cart-nonce'))) {
        $nonce_verified = true;
    }

    // If all verification attempts failed, return error
    if (!$nonce_verified) {
        error_log('Nonce verification failed - nonce: ' . $nonce . ', security: ' . $security);
        wp_send_json_error(['message' => 'Invalid security token']);
        return;
    }

    // First, deduplicate the actual WooCommerce cart
    if (function_exists('WC') && !WC()->cart->is_empty()) {
        // Get all cart items
        $cart = WC()->cart->get_cart();

        // Track processed product IDs
        $processed_products = [];
        $items_to_remove = [];

        // Find duplicate items
        foreach ($cart as $cart_item_key => $cart_item) {
            $product_id = $cart_item['product_id'];
            $variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;

            // Create a unique key for this product
            $unique_key = $product_id . '-' . $variation_id;

            // If we've already processed this product, mark for removal
            if (in_array($unique_key, $processed_products)) {
                $items_to_remove[] = $cart_item_key;
            } else {
                // Add to processed products
                $processed_products[] = $unique_key;
            }
        }

        // Remove duplicate items
        foreach ($items_to_remove as $cart_item_key) {
            WC()->cart->remove_cart_item($cart_item_key);
        }

        // If we removed any items, recalculate totals
        if (!empty($items_to_remove)) {
            WC()->cart->calculate_totals();
            error_log('Removed ' . count($items_to_remove) . ' duplicate items from cart');
        }
    }

    // Get cart contents
    $cart_items = [];
    $cart_total = 0;
    $processed_keys = []; // Track processed keys to prevent duplicates

    error_log('WC exists: ' . (function_exists('WC') ? 'yes' : 'no'));
    if (function_exists('WC')) {
        error_log('Cart is empty: ' . (WC()->cart->is_empty() ? 'yes' : 'no'));
    }

    if (function_exists('WC') && !WC()->cart->is_empty()) {
        error_log('Cart items count: ' . count(WC()->cart->get_cart()));

        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            error_log('Processing cart item: ' . $cart_item_key);

            // Skip if we've already processed this key
            if (in_array($cart_item_key, $processed_keys)) {
                error_log('Skipping duplicate cart item: ' . $cart_item_key);
                continue;
            }

            // Also skip if we've already processed this product ID
            $product_id = $cart_item['product_id'];
            $variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;
            $unique_product_key = $product_id . '-' . $variation_id;

            if (in_array($unique_product_key, $processed_keys)) {
                error_log('Skipping duplicate product: ' . $unique_product_key);
                continue;
            }

            // Add to processed keys
            $processed_keys[] = $cart_item_key;
            $processed_keys[] = $unique_product_key;

            $product = $cart_item['data'];

            if (!$product) {
                error_log('Product is null for cart item: ' . $cart_item_key);
                continue;
            }

            // Get product image
            $image_id = $product->get_image_id();
            $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : wc_placeholder_img_src();

            // Get product details
            $item = [
                'key' => $cart_item_key,
                'product_id' => $product_id,
                'variation_id' => $variation_id,
                'unique_key' => $unique_product_key, // Add a unique key for deduplication
                'name' => $product->get_name(),
                'price' => wc_price($product->get_price()),
                'quantity' => $cart_item['quantity'],
                'total' => wc_price($cart_item['line_total']),
                'image' => $image_url,
                'remove_url' => wc_get_cart_remove_url($cart_item_key)
            ];

            error_log('Adding item to cart_items: ' . print_r($item, true));
            $cart_items[] = $item;
        }

        $cart_total = WC()->cart->get_cart_total();
        error_log('Cart total: ' . $cart_total);
    }

    // EMERGENCY FIX: Group items by product ID only
    $unique_items = [];
    $processed_product_ids = [];

    foreach ($cart_items as $item) {
        $product_id = $item['product_id'];

        // Only add if we haven't processed this product ID yet
        if (!in_array($product_id, $processed_product_ids)) {
            $processed_product_ids[] = $product_id;
            $unique_items[] = $item;
        }
    }

    // Log how many items were removed
    error_log('EMERGENCY: Showing ' . count($unique_items) . ' unique products out of ' . count($cart_items) . ' total items');

    $response = [
        'items' => $unique_items,
        'total' => $cart_total,
        'count' => count($unique_items)
    ];

    error_log('Response: ' . print_r($response, true));

    wp_send_json_success($response);
}
add_action('wp_ajax_lci_get_mini_cart_items', 'lci_get_mini_cart_items');
add_action('wp_ajax_nopriv_lci_get_mini_cart_items', 'lci_get_mini_cart_items');

/**
 * Deduplicate cart AJAX handler
 */
function lci_deduplicate_cart_ajax() {
    // Check nonce
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci-dashboard-add-to-cart-nonce')) {
        wp_send_json_error(['message' => 'Security check failed.']);
        return;
    }

    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        wp_send_json_error(['message' => 'WooCommerce is not active.']);
        return;
    }

    // Call the deduplication function
    if (function_exists('lci_deduplicate_cart_items')) {
        lci_deduplicate_cart_items();

        // Get updated cart info
        $cart_count = WC()->cart->get_cart_contents_count();
        $cart_total = WC()->cart->get_cart_total();

        wp_send_json_success([
            'message' => 'Cart deduplicated.',
            'cart_count' => $cart_count,
            'cart_total' => $cart_total,
        ]);
    } else {
        wp_send_json_error(['message' => 'Deduplication function not available.']);
    }
}
add_action('wp_ajax_lci_deduplicate_cart', 'lci_deduplicate_cart_ajax');
add_action('wp_ajax_nopriv_lci_deduplicate_cart', 'lci_deduplicate_cart_ajax');

/**
 * Emergency cart rebuild AJAX handler
 * This completely clears the cart and rebuilds it with unique items
 */
function lci_emergency_rebuild_cart() {
    // Check nonce
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci-dashboard-add-to-cart-nonce')) {
        wp_send_json_error(['message' => 'Security check failed.']);
        return;
    }

    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        wp_send_json_error(['message' => 'WooCommerce is not active.']);
        return;
    }

    // Get the current cart items
    $cart = WC()->cart;
    if ($cart->is_empty()) {
        wp_send_json_success(['message' => 'Cart is already empty.']);
        return;
    }

    // EMERGENCY FIX: Group items by product ID only
    $unique_products = [];
    $processed_product_ids = [];

    // Find unique products by product ID only
    foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
        $product_id = $cart_item['product_id'];
        $variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;
        $variation_attributes = isset($cart_item['variation']) ? $cart_item['variation'] : [];
        $quantity = $cart_item['quantity'];

        // Only add if we haven't processed this product ID yet
        if (!in_array($product_id, $processed_product_ids)) {
            $processed_product_ids[] = $product_id;
            $unique_products[] = [
                'product_id' => $product_id,
                'variation_id' => $variation_id,
                'variation_attributes' => $variation_attributes,
                'quantity' => $quantity
            ];
        }
    }

    // Log how many items were removed
    error_log('EMERGENCY REBUILD: Keeping ' . count($unique_products) . ' unique products out of ' . count($cart->get_cart()) . ' total items');

    // Clear the cart
    $cart->empty_cart();

    // Add the unique products back to the cart
    foreach ($unique_products as $product) {
        $cart->add_to_cart(
            $product['product_id'],
            $product['quantity'],
            $product['variation_id'],
            $product['variation_attributes']
        );
    }

    // Get updated cart info
    $cart_count = $cart->get_cart_contents_count();
    $cart_total = $cart->get_cart_total();

    wp_send_json_success([
        'message' => 'Cart rebuilt successfully.',
        'cart_count' => $cart_count,
        'cart_total' => $cart_total,
    ]);
}
add_action('wp_ajax_lci_emergency_rebuild_cart', 'lci_emergency_rebuild_cart');
add_action('wp_ajax_nopriv_lci_emergency_rebuild_cart', 'lci_emergency_rebuild_cart');

/**
 * Remove item from cart
 */
function lci_remove_cart_item() {
    // Check nonce - accept both 'nonce' and 'security' parameters
    $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
    $security = isset($_POST['security']) ? $_POST['security'] : '';

    // Try to verify with multiple nonce names
    $nonce_verified = false;

    // Try with lci_mini_cart_nonce
    if (wp_verify_nonce($nonce, 'lci_mini_cart_nonce') || wp_verify_nonce($security, 'lci_mini_cart_nonce')) {
        $nonce_verified = true;
    }

    // Try with lci-dashboard-add-to-cart-nonce
    if (!$nonce_verified && (wp_verify_nonce($nonce, 'lci-dashboard-add-to-cart-nonce') || wp_verify_nonce($security, 'lci-dashboard-add-to-cart-nonce'))) {
        $nonce_verified = true;
    }

    // If all verification attempts failed, return error
    if (!$nonce_verified) {
        error_log('Remove cart item: Nonce verification failed - nonce: ' . $nonce . ', security: ' . $security);
        wp_send_json_error(['message' => 'Invalid security token']);
        return;
    }

    // Check if cart item key is provided
    if (!isset($_POST['cart_item_key'])) {
        wp_send_json_error(['message' => 'No cart item key provided']);
        return;
    }

    $cart_item_key = sanitize_text_field($_POST['cart_item_key']);

    // Remove the item from cart
    if (function_exists('WC') && WC()->cart->remove_cart_item($cart_item_key)) {
        // Force WooCommerce to recalculate totals
        WC()->cart->calculate_totals();

        // Get the updated cart data
        $cart_count = WC()->cart->get_cart_contents_count();
        $cart_total = WC()->cart->get_cart_total();

        // Debug info
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Cart: Item removed successfully');
            error_log('LCI Cart: New cart count: ' . $cart_count);
            error_log('LCI Cart: New cart total: ' . $cart_total);
        }

        wp_send_json_success([
            'message' => 'Item removed successfully',
            'cart_count' => $cart_count,
            'cart_total' => $cart_total
        ]);
    } else {
        wp_send_json_error(['message' => 'Failed to remove item from cart']);
    }
}
add_action('wp_ajax_lci_remove_cart_item', 'lci_remove_cart_item');
add_action('wp_ajax_nopriv_lci_remove_cart_item', 'lci_remove_cart_item');

/**
 * Get fundraising message via AJAX
 */
function lci_get_fundraising_message_ajax() {
    // Debug info
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Fundraising: lci_get_fundraising_message_ajax called');
    }

    // Check nonce - accept both 'nonce' and 'security' parameters
    $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
    $security = isset($_POST['security']) ? $_POST['security'] : '';

    // Try to verify with multiple nonce names
    $nonce_verified = false;

    // Try with lci_mini_cart_nonce
    if (wp_verify_nonce($nonce, 'lci_mini_cart_nonce') || wp_verify_nonce($security, 'lci_mini_cart_nonce')) {
        $nonce_verified = true;
    }

    // Try with lci-dashboard-add-to-cart-nonce
    if (!$nonce_verified && (wp_verify_nonce($nonce, 'lci-dashboard-add-to-cart-nonce') || wp_verify_nonce($security, 'lci-dashboard-add-to-cart-nonce'))) {
        $nonce_verified = true;
    }

    // If all verification attempts failed, return error
    if (!$nonce_verified) {
        error_log('Fundraising message: Nonce verification failed - nonce: ' . $nonce . ', security: ' . $security);
        wp_send_json_error(['message' => 'Invalid security token']);
        return;
    }

    // Get the current cart value for category 22 products
    if (defined('WP_DEBUG') && WP_DEBUG) {
        $total_value = lci_get_category_22_total_value();
        error_log('LCI Fundraising: Current category 22 total value: ' . $total_value);
    }

    // Check if cart is empty
    $cart_is_empty = function_exists('WC') && WC()->cart && WC()->cart->is_empty();

    // Check if there are any products from category 22 in the cart
    $has_category_22 = false;
    if (function_exists('WC') && isset(WC()->cart)) {
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product_id = $cart_item['product_id'];
            if (has_term(22, 'product_cat', $product_id)) {
                $has_category_22 = true;
                break;
            }
        }
    }

    // Also check if the fundraising value is greater than zero
    $fundraising_value = lci_get_category_22_total_value();
    $has_fundraising_value = $fundraising_value > 0;

    if ($cart_is_empty || !$has_category_22 || !$has_fundraising_value) {
        // If cart is empty or has no category 22 products, return empty string
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Fundraising: Cart is empty or has no category 22 products, returning empty string');
        }
        wp_send_json_success('');
        return;
    }

    // Start output buffering to capture the HTML
    ob_start();
    lci_display_fundraising_message();
    $message_html = ob_get_clean();

    // Debug info
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Fundraising: Message HTML length: ' . strlen($message_html));
    }

    // Send the HTML back
    wp_send_json_success($message_html);
}
add_action('wp_ajax_lci_get_fundraising_message', 'lci_get_fundraising_message_ajax');
add_action('wp_ajax_nopriv_lci_get_fundraising_message', 'lci_get_fundraising_message_ajax');

/**
 * Check if user has purchased specific products
 * Note: This was used by the accommodation wizard which has been removed,
 * but keeping the function in case it's used elsewhere
 */
function lci_check_user_products() {
    // Verify nonce - accept both 'nonce' and 'security' parameters
    $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
    $security = isset($_POST['security']) ? $_POST['security'] : '';

    if (!wp_verify_nonce($nonce, 'lci_ajax_nonce') && !wp_verify_nonce($security, 'lci_ajax_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'User not logged in'));
    }

    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $user_id = get_current_user_id();
    $has_product = false;

    // Get customer orders
    $customer_orders = wc_get_orders(array(
        'customer_id' => $user_id,
        'status' => array('wc-completed', 'wc-processing', 'wc-on-hold'),
        'limit' => -1
    ));

    // Check if user has the product
    foreach ($customer_orders as $order) {
        foreach ($order->get_items() as $item) {
            if ($item->get_product_id() == $product_id) {
                $has_product = true;
                break 2;
            }
        }
    }

    wp_send_json_success(array('has_product' => $has_product));
}
add_action('wp_ajax_check_user_products', 'lci_check_user_products');

/**
 * Get accommodation products
 * Note: This was used by the accommodation wizard which has been removed,
 * but keeping the function in case it's used elsewhere
 */
function lci_get_accommodation_products() {
    // Verify nonce - accept both 'nonce' and 'security' parameters
    $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
    $security = isset($_POST['security']) ? $_POST['security'] : '';

    if (!wp_verify_nonce($nonce, 'lci_ajax_nonce') && !wp_verify_nonce($security, 'lci_ajax_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }

    $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
    $meta_query = isset($_POST['meta_query']) ? json_decode(stripslashes($_POST['meta_query']), true) : array();

    // Build query args
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => 'product_cat',
                'field' => 'term_id',
                'terms' => $category_id,
            ),
        ),
    );

    // Add meta query if provided
    if (!empty($meta_query)) {
        $args['meta_query'] = array($meta_query);
    }

    $products_query = new WP_Query($args);
    $products = array();

    if ($products_query->have_posts()) {
        while ($products_query->have_posts()) {
            $products_query->the_post();
            global $product;

            $products[] = array(
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'price' => $product->get_price(),
                'price_html' => $product->get_price_html(),
                'description' => wp_trim_words(get_the_excerpt(), 20),
                'image' => get_the_post_thumbnail_url(get_the_ID(), 'medium') ?: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp',
                'url' => get_permalink(),
                'added' => false,
            );
        }
        wp_reset_postdata();
    }

    wp_send_json_success(array('products' => $products));
}
add_action('wp_ajax_get_accommodation_products', 'lci_get_accommodation_products');

/**
 * Get cart contents for checking if products are in cart
 */
function lci_get_cart_contents() {
    // Check nonce - accept both 'nonce' and 'security' parameters
    $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
    $security = isset($_POST['security']) ? $_POST['security'] : '';

    // Try to verify with multiple nonce names
    $nonce_verified = false;

    // Try with lci_mini_cart_nonce
    if (wp_verify_nonce($nonce, 'lci_mini_cart_nonce') || wp_verify_nonce($security, 'lci_mini_cart_nonce')) {
        $nonce_verified = true;
    }

    // Try with lci-dashboard-add-to-cart-nonce
    if (!$nonce_verified && (wp_verify_nonce($nonce, 'lci-dashboard-add-to-cart-nonce') || wp_verify_nonce($security, 'lci-dashboard-add-to-cart-nonce'))) {
        $nonce_verified = true;
    }

    // If all verification attempts failed, return error
    if (!$nonce_verified) {
        wp_send_json_error(['message' => 'Invalid security token']);
        return;
    }

    // Get cart contents
    $cart_items = [];
    $processed_keys = []; // Track processed keys to prevent duplicates

    if (function_exists('WC') && !WC()->cart->is_empty()) {
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            // Skip if we've already processed this key
            if (in_array($cart_item_key, $processed_keys)) {
                continue;
            }

            // Add to processed keys
            $processed_keys[] = $cart_item_key;

            $product = $cart_item['data'];
            $product_id = $cart_item['product_id'];

            if (!$product) {
                continue;
            }

            // Get product image
            $image_id = $product->get_image_id();
            $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : wc_placeholder_img_src();

            // Get product details
            $item = [
                'key' => $cart_item_key,
                'product_id' => $product_id,
                'name' => $product->get_name(),
                'price' => $product->get_price(),
                'quantity' => $cart_item['quantity'],
                'total' => $cart_item['line_total'],
                'image' => $image_url
            ];

            $cart_items[] = $item;
        }
    }

    // EMERGENCY FIX: Group items by product ID only
    $unique_items = [];
    $processed_product_ids = [];

    foreach ($cart_items as $item) {
        $product_id = $item['product_id'];

        // Only add if we haven't processed this product ID yet
        if (!in_array($product_id, $processed_product_ids)) {
            $processed_product_ids[] = $product_id;
            $unique_items[] = $item;
        }
    }

    // Log how many items were removed
    error_log('EMERGENCY: Showing ' . count($unique_items) . ' unique products out of ' . count($cart_items) . ' total items');

    wp_send_json_success([
        'cart_items' => $unique_items,
        'cart_count' => count($unique_items)
    ]);
}
add_action('wp_ajax_lci_get_cart_contents', 'lci_get_cart_contents');
add_action('wp_ajax_nopriv_lci_get_cart_contents', 'lci_get_cart_contents');
