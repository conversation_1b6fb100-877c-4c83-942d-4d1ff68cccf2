/**
 * Cart calculations helper functions
 */
(function() {
    // Make the functions globally available
    window.lciCartCalculations = {
        /**
         * Calculate the new total after removing an item
         * 
         * @param {string|object} currentTotal - The current cart total (string or HTML)
         * @param {string|object} itemTotal - The total of the item being removed
         * @returns {string} - The new total formatted as a string with Euro symbol
         */
        calculateNewTotal: function(currentTotal, itemTotal) {
            try {
                // Extract the numeric part from the current total
                let currentTotalValue = 0;
                if (typeof currentTotal === 'string') {
                    const currentTotalMatch = currentTotal.match(/([\d.,]+)/);
                    currentTotalValue = currentTotalMatch ? parseFloat(currentTotalMatch[0].replace(',', '.')) : 0;
                } else if (currentTotal && typeof currentTotal === 'object') {
                    // Handle case where total is HTML
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = currentTotal.toString();
                    const text = tempDiv.textContent || tempDiv.innerText;
                    const currentTotalMatch = text.match(/([\d.,]+)/);
                    currentTotalValue = currentTotalMatch ? parseFloat(currentTotalMatch[0].replace(',', '.')) : 0;
                }
                
                // Extract the numeric part from the item total
                let itemTotalValue = 0;
                if (itemTotal) {
                    if (typeof itemTotal === 'string') {
                        const itemTotalMatch = itemTotal.match(/([\d.,]+)/);
                        itemTotalValue = itemTotalMatch ? parseFloat(itemTotalMatch[0].replace(',', '.')) : 0;
                    } else if (itemTotal && typeof itemTotal === 'object') {
                        // Handle case where item total is HTML
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = itemTotal.toString();
                        const text = tempDiv.textContent || tempDiv.innerText;
                        const itemTotalMatch = text.match(/([\d.,]+)/);
                        itemTotalValue = itemTotalMatch ? parseFloat(itemTotalMatch[0].replace(',', '.')) : 0;
                    }
                }
                
                // Calculate the new total
                const newTotalValue = Math.max(0, currentTotalValue - itemTotalValue);
                
                // Format the new total (assuming Euro currency)
                return newTotalValue.toFixed(2) + ' €';
            } catch (error) {
                console.error('Error calculating new total:', error);
                return null; // Return null to indicate an error
            }
        }
    };
})();
