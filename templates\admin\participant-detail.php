<div class="wrap lci-admin-wrap">
    <!-- Hidden participant data for Alpine.js -->
    <script type="application/json" id="participant-data">
        <?php echo json_encode($participant); ?>
    </script>

    <div x-data="participantDetail">
        <!-- Header with back button -->
        <div class="flex items-center mb-6">
            <a href="<?php echo admin_url('admin.php?page=lci-participants'); ?>" class="mr-4 p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
            </a>
            <h1 class="text-3xl font-bold">Participant Details</h1>
        </div>

        <!-- Loading state -->
        <div x-show="isLoading" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
            <div class="spinner"></div>
            <span class="loading-text">Loading participant data...</span>
        </div>

        <!-- Participant details -->
        <div x-show="!isLoading" class="space-y-6">
            <!-- Participant header -->
            <div class="bg-white rounded-xl shadow-neumorph p-6">
                <div class="flex flex-col md:flex-row md:items-center justify-between">
                    <div class="flex items-center mb-4 md:mb-0">
                        <div class="mr-4">
                            <div class="w-24 h-24 rounded-full shadow-md overflow-hidden bg-white flex items-center justify-center">
                                <img
                                    :src="'https://www.gravatar.com/avatar/' + (participant.email ? participant.email.toLowerCase().trim().md5() : '') + '?s=96&d=https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'"
                                    alt="Participant Avatar"
                                    class="w-20 h-20 object-contain"
                                    @error="$event.target.src = 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'"
                                >
                            </div>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800" x-text="participant.first_name + ' ' + participant.last_name"></h2>
                            <div class="flex flex-col sm:flex-row sm:items-center mt-1 text-gray-600">
                                <div class="flex items-center mr-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                    <span x-text="participant.email"></span>
                                </div>
                                <div class="flex items-center mt-1 sm:mt-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                    </svg>
                                    <span x-text="participant.phone || 'No phone number'"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col items-start md:items-end space-y-2">
                        <div class="flex items-center">
                            <span class="text-sm text-gray-600 mr-2">LCI ID:</span>
                            <span class="font-medium text-primary" x-text="participant.unique_reg_id"></span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-600 mr-2">Original ID:</span>
                            <span class="text-gray-800" x-text="participant.original_reg_id"></span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-600 mr-2">Registration Type:</span>
                            <span class="font-medium" x-text="participant.registration_type"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs navigation -->
            <div class="bg-white rounded-xl shadow-neumorph p-4">
                <div class="flex overflow-x-auto">
                    <button
                        @click="activeTab = 'personal'"
                        :class="{'text-primary border-primary': activeTab === 'personal', 'text-gray-500 border-transparent hover:text-gray-700': activeTab !== 'personal'}"
                        class="px-4 py-2 font-medium border-b-2 transition-colors"
                    >
                        Personal Information
                    </button>
                    <button
                        @click="activeTab = 'registration'"
                        :class="{'text-primary border-primary': activeTab === 'registration', 'text-gray-500 border-transparent hover:text-gray-700': activeTab !== 'registration'}"
                        class="px-4 py-2 font-medium border-b-2 transition-colors"
                    >
                        Registration Details
                    </button>
                    <button
                        @click="activeTab = 'accommodation'"
                        :class="{'text-primary border-primary': activeTab === 'accommodation', 'text-gray-500 border-transparent hover:text-gray-700': activeTab !== 'accommodation'}"
                        class="px-4 py-2 font-medium border-b-2 transition-colors"
                    >
                        Accommodation
                    </button>
                    <button
                        @click="activeTab = 'tours'"
                        :class="{'text-primary border-primary': activeTab === 'tours', 'text-gray-500 border-transparent hover:text-gray-700': activeTab !== 'tours'}"
                        class="px-4 py-2 font-medium border-b-2 transition-colors"
                    >
                        Tours
                    </button>
                    <button
                        @click="activeTab = 'diet'"
                        :class="{'text-primary border-primary': activeTab === 'diet', 'text-gray-500 border-transparent hover:text-gray-700': activeTab !== 'diet'}"
                        class="px-4 py-2 font-medium border-b-2 transition-colors"
                    >
                        Diet & Allergies
                    </button>
                    <button
                        @click="activeTab = 'notes'"
                        :class="{'text-primary border-primary': activeTab === 'notes', 'text-gray-500 border-transparent hover:text-gray-700': activeTab !== 'notes'}"
                        class="px-4 py-2 font-medium border-b-2 transition-colors"
                    >
                        Admin Notes
                    </button>
                </div>
            </div>

            <!-- Tab content -->
            <div class="bg-white rounded-xl shadow-neumorph p-6">
                <!-- Personal Information Tab -->
                <div x-show="activeTab === 'personal'">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">Personal Information</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label">First Name</label>
                            <input type="text" x-model="participant.first_name" class="form-input">
                        </div>

                        <div>
                            <label class="form-label">Last Name</label>
                            <input type="text" x-model="participant.last_name" class="form-input">
                        </div>

                        <div>
                            <label class="form-label">Email</label>
                            <input type="email" x-model="participant.email" class="form-input">
                        </div>

                        <div>
                            <label class="form-label">Phone</label>
                            <input type="tel" x-model="participant.phone" class="form-input">
                        </div>

                        <div>
                            <label class="form-label">Country</label>
                            <input type="text" x-model="participant.country" class="form-input">
                        </div>

                        <div>
                            <label class="form-label">Association</label>
                            <input type="text" x-model="participant.association" class="form-input">
                        </div>

                        <div>
                            <label class="form-label">Club Number</label>
                            <input type="text" x-model="participant.club_no" class="form-input">
                        </div>

                        <div>
                            <label class="form-label">Club Position</label>
                            <input type="text" x-model="participant.club_position" class="form-input">
                        </div>
                    </div>
                </div>

                <!-- Registration Details Tab -->
                <div x-show="activeTab === 'registration'">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">Registration Details</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label">Registration Type</label>
                            <select x-model="participant.registration_type" class="form-input">
                                <option value="Full Event Package">Full Event Package</option>
                                <option value="Councilor Package">Councilor Package</option>
                                <option value="Partners Package">Partners Package</option>
                                <option value="N/A">N/A</option>
                            </select>
                        </div>

                        <div>
                            <label class="form-label">Payment Status</label>
                            <select x-model="participant.payment_status" class="form-input">
                                <option value="pending">Pending</option>
                                <option value="processing">Processing</option>
                                <option value="completed">Completed</option>
                                <option value="failed">Failed</option>
                            </select>
                        </div>

                        <div>
                            <label class="form-label">Badge Status</label>
                            <select x-model="participant.badge_status" class="form-input">
                                <option value="Pending">Pending</option>
                                <option value="Picked Up">Picked Up</option>
                            </select>
                        </div>

                        <div>
                            <label class="form-label">Pub Party Number</label>
                            <input type="text" x-model="participant.pub_party_number" class="form-input">
                        </div>

                        <div>
                            <label class="form-label">Gala Dinner Table</label>
                            <input type="text" x-model="participant.gala_dinner_table" class="form-input">
                        </div>

                        <div>
                            <label class="form-label">Order Date</label>
                            <input type="text" x-model="participant.order_date" class="form-input" readonly>
                        </div>

                        <div>
                            <label class="form-label">Diet</label>
                            <input type="text" x-model="participant.diet" class="form-input">
                        </div>

                        <div class="md:col-span-2">
                            <label class="form-label">Allergies</label>
                            <textarea x-model="participant.allergies" class="form-input" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Accommodation Tab -->
                <div x-show="activeTab === 'accommodation'">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">Accommodation</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label">Room Share</label>
                            <select x-model="participant.room_share" class="form-input">
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>
                        </div>

                        <div x-show="participant.room_share === 'yes'">
                            <label class="form-label">Share With</label>
                            <input type="text" x-model="participant.share_with" class="form-input">
                        </div>

                        <div class="md:col-span-2">
                            <label class="form-label">Accommodation Details</label>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <template x-if="!participant.accommodation || participant.accommodation.trim() === ''">
                                    <p class="text-gray-500 italic">No accommodation details available</p>
                                </template>
                                <ul class="list-disc pl-5 space-y-1" x-show="participant.accommodation && participant.accommodation.trim() !== ''">
                                    <template x-for="(item, index) in parseAccommodation(participant.accommodation)" :key="index">
                                        <li x-text="item" class="text-gray-700"></li>
                                    </template>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tours Tab -->
                <div x-show="activeTab === 'tours'">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">Tours</h3>

                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label class="form-label">Tour Details</label>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <template x-if="!participant.tours || participant.tours.trim() === ''">
                                    <p class="text-gray-500 italic">No tour details available</p>
                                </template>
                                <ul class="list-disc pl-5 space-y-1" x-show="participant.tours && participant.tours.trim() !== ''">
                                    <template x-for="(item, index) in parseTours(participant.tours)" :key="index">
                                        <li x-text="item" class="text-gray-700"></li>
                                    </template>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Diet & Allergies Tab -->
                <div x-show="activeTab === 'diet'">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">Diet & Allergies</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label">Diet</label>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <template x-if="!participant.diet || participant.diet.trim() === ''">
                                    <p class="text-gray-500 italic">No diet information available</p>
                                </template>
                                <p x-show="participant.diet && participant.diet.trim() !== ''" x-text="participant.diet" class="text-gray-700"></p>
                            </div>
                        </div>

                        <div>
                            <label class="form-label">Allergies</label>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <template x-if="!participant.allergies || participant.allergies.trim() === ''">
                                    <p class="text-gray-500 italic">No allergies information available</p>
                                </template>
                                <p x-show="participant.allergies && participant.allergies.trim() !== ''" x-text="participant.allergies" class="text-gray-700"></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Admin Notes Tab -->
                <div x-show="activeTab === 'notes'">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">Admin Notes</h3>

                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label class="form-label">Notes</label>
                            <textarea x-model="participant.admin_notes" class="form-input" rows="6"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Save button -->
                <div class="mt-6 flex justify-end">
                    <button
                        @click="saveParticipant"
                        class="btn btn-primary"
                        :class="{'opacity-50 cursor-not-allowed': isSaving}"
                    >
                        <template x-if="!isSaving">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </template>
                        <template x-if="isSaving">
                            <svg class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </template>
                        <span x-text="isSaving ? 'Saving...' : 'Save Changes'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
