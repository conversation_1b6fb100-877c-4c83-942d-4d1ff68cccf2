<?php
/**
 * Direct Payment Template
 * 
 * This template displays the direct payment form.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get available payment gateways
$available_gateways = [];

if (function_exists('WC') && isset(WC()->payment_gateways)) {
    $gateways = WC()->payment_gateways->payment_gateways();
    
    // Filter for only the gateways we want
    if (isset($gateways['revolut_cc']) && $gateways['revolut_cc']->enabled === 'yes') {
        $available_gateways['revolut_cc'] = [
            'id' => 'revolut_cc',
            'title' => __('Credit Card', 'lci-2025-dashboard'),
            'description' => __('Pay securely with your credit card.', 'lci-2025-dashboard'),
            'gateway' => $gateways['revolut_cc'],
        ];
    }
    
    if (isset($gateways['bacs']) && $gateways['bacs']->enabled === 'yes') {
        $available_gateways['bacs'] = [
            'id' => 'bacs',
            'title' => __('Bank Transfer', 'lci-2025-dashboard'),
            'description' => __('Make your payment directly into our bank account.', 'lci-2025-dashboard'),
            'gateway' => $gateways['bacs'],
        ];
    }
}
?>

<div class="lci-dashboard-content">
    <h2 class="lci-dashboard-title"><?php _e('Payment', 'lci-2025-dashboard'); ?></h2>
    
    <?php if (empty($available_gateways)) : ?>
        <div class="lci-notice lci-notice-error">
            <p><?php _e('No payment methods available.', 'lci-2025-dashboard'); ?></p>
        </div>
    <?php else : ?>
        <div class="lci-direct-payment-container">
            <div class="lci-payment-messages"></div>
            
            <form class="lci-payment-form" method="post">
                <div class="lci-payment-methods">
                    <h3><?php _e('Select Payment Method', 'lci-2025-dashboard'); ?></h3>
                    
                    <?php foreach ($available_gateways as $gateway_id => $gateway_data) : ?>
                        <div class="lci-payment-method">
                            <div class="lci-payment-method-header">
                                <input type="radio" name="payment_method" id="payment_method_<?php echo esc_attr($gateway_id); ?>" value="<?php echo esc_attr($gateway_id); ?>" class="lci-payment-method-input" <?php checked($gateway_id, array_key_first($available_gateways)); ?>>
                                <label for="payment_method_<?php echo esc_attr($gateway_id); ?>" class="lci-payment-method-label">
                                    <span class="lci-payment-method-title"><?php echo esc_html($gateway_data['title']); ?></span>
                                </label>
                            </div>
                            <div id="lci-payment-method-description-<?php echo esc_attr($gateway_id); ?>" class="lci-payment-method-description">
                                <?php echo wp_kses_post($gateway_data['description']); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php
                // Check if we're processing a single product or the cart
                if (isset($_GET['product_id']) && !empty($_GET['product_id'])) {
                    $product_id = intval($_GET['product_id']);
                    $quantity = isset($_GET['quantity']) ? intval($_GET['quantity']) : 1;
                    
                    $product = wc_get_product($product_id);
                    if ($product) {
                        $amount = $product->get_price() * $quantity;
                        $description = $product->get_name();
                        
                        echo '<input type="hidden" name="product_id" value="' . esc_attr($product_id) . '">';
                        echo '<input type="hidden" name="quantity" value="' . esc_attr($quantity) . '">';
                    }
                } else {
                    // Use cart total
                    if (function_exists('WC') && !WC()->cart->is_empty()) {
                        $amount = WC()->cart->get_total('');
                        $description = __('Cart Payment', 'lci-2025-dashboard');
                    } else {
                        // Default values
                        $amount = 0;
                        $description = __('Custom Payment', 'lci-2025-dashboard');
                    }
                }
                ?>
                
                <div class="lci-payment-details">
                    <h3><?php _e('Payment Details', 'lci-2025-dashboard'); ?></h3>
                    <p><strong><?php _e('Description:', 'lci-2025-dashboard'); ?></strong> <?php echo esc_html($description); ?></p>
                    <p><strong><?php _e('Amount:', 'lci-2025-dashboard'); ?></strong> <?php echo wc_price($amount); ?></p>
                </div>
                
                <input type="hidden" name="amount" value="<?php echo esc_attr($amount); ?>">
                <input type="hidden" name="description" value="<?php echo esc_attr($description); ?>">
                
                <div class="lci-payment-submit">
                    <button type="submit" class="lci-payment-button"><?php _e('Proceed to Payment', 'lci-2025-dashboard'); ?></button>
                </div>
            </form>
            
            <div class="lci-payment-instructions"></div>
            <div class="lci-order-details"></div>
        </div>
    <?php endif; ?>
</div>
