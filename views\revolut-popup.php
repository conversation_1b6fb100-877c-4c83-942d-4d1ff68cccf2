<?php
/**
 * Revolut Popup Payment Template
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get cart total if available
$amount = 0;
$description = 'Payment';

if (function_exists('WC') && !WC()->cart->is_empty()) {
    $amount = WC()->cart->get_total('');
    $description = 'Cart Payment';
}

// Get product ID and quantity from URL if available
$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : '';
$quantity = isset($_GET['quantity']) ? intval($_GET['quantity']) : 1;

// If product ID is provided, get product details
if (!empty($product_id)) {
    $product = wc_get_product($product_id);
    if ($product) {
        $amount = $product->get_price() * $quantity;
        $description = $product->get_name();
    }
}
?>

<div class="lci-dashboard-content">
    <h2 class="lci-dashboard-title"><?php _e('Secure Card Payment', 'lci-2025-dashboard'); ?></h2>

    <div class="lci-revolut-popup-container">
        <div class="lci-payment-messages"></div>

        <div class="lci-payment-details">
            <h3><?php _e('Payment Details', 'lci-2025-dashboard'); ?></h3>
            <p><strong><?php _e('Description:', 'lci-2025-dashboard'); ?></strong> <?php echo esc_html($description); ?></p>
            <p><strong><?php _e('Amount:', 'lci-2025-dashboard'); ?></strong> <?php echo wc_price($amount); ?></p>

            <div class="lci-payment-info">
                <p><?php _e('You will be redirected to a secure payment page to enter your card details.', 'lci-2025-dashboard'); ?></p>
                <p><?php _e('Your payment will be processed securely by Revolut.', 'lci-2025-dashboard'); ?></p>
                <p><?php _e('After completing the payment, please close the payment window to return to this page.', 'lci-2025-dashboard'); ?></p>
            </div>
        </div>

        <div class="lci-payment-actions">
            <button type="button" class="lci-revolut-popup-button"
                    data-amount="<?php echo esc_attr($amount); ?>"
                    data-description="<?php echo esc_attr($description); ?>"
                    data-product-id="<?php echo esc_attr($product_id); ?>"
                    data-quantity="<?php echo esc_attr($quantity); ?>">
                <i class="fas fa-lock"></i> <?php _e('Pay Securely Now', 'lci-2025-dashboard'); ?>
            </button>
        </div>

        <div class="lci-payment-security">
            <p><i class="fas fa-shield-alt"></i> <?php _e('Your payment is secure and encrypted', 'lci-2025-dashboard'); ?></p>
        </div>
    </div>
</div>

<style>
.lci-revolut-popup-container {
    margin-bottom: 30px;
    max-width: 800px;
}

.lci-payment-details {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.lci-payment-details h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.lci-payment-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #fff;
    border-left: 4px solid #36b1dc;
    border-radius: 4px;
}

.lci-payment-info p {
    margin-bottom: 10px;
    color: #555;
}

.lci-payment-actions {
    margin-bottom: 20px;
    text-align: center;
}

.lci-revolut-popup-button {
    background-color: #36b1dc;
    color: #fff;
    border: none;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 250px;
}

.lci-revolut-popup-button:hover {
    background-color: #2c99c2;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.lci-revolut-popup-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.lci-revolut-popup-button i {
    margin-right: 8px;
}

.lci-payment-security {
    text-align: center;
    margin-top: 20px;
    color: #666;
}

.lci-payment-security i {
    color: #36b1dc;
    margin-right: 5px;
}

.lci-payment-messages {
    margin-bottom: 20px;
}

.lci-notice {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.lci-notice p {
    margin: 0;
    font-size: 15px;
}

.lci-notice-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.lci-notice-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.lci-notice-info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* Responsive styles */
@media (max-width: 768px) {
    .lci-revolut-popup-button {
        width: 100%;
        min-width: auto;
    }

    .lci-payment-details {
        padding: 15px;
    }
}
</style>
