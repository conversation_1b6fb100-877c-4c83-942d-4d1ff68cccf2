/**
 * Mini Cart Initialization
 *
 * This script ensures the mini cart is properly initialized across all pages.
 */
(function() {
    'use strict';

    // Flag to track initialization
    let initialized = false;

    // Function to initialize the mini cart
    function initMiniCart() {
        if (initialized) {
            return;
        }

        // Get the mini cart container
        const miniCartContainer = document.getElementById('mini-cart-container');
        if (!miniCartContainer) {
            return;
        }

        // If Alpine.js is initialized
        if (miniCartContainer.__x) {
            try {
                // Get Alpine.js data
                const data = miniCartContainer.__x.getUnobservedData();

                // Set autoOpenCart to true to prevent auto-opening
                data.autoOpenCart = true;

                // Set initialized flag
                initialized = true;

                // Dispatch event to notify that mini cart is initialized
                document.dispatchEvent(new CustomEvent('mini-cart:initialized'));
            } catch (error) {
                // Silent fail
            }
        }
    }

    // Function to check Alpine.js initialization
    function checkAlpineInit() {
        // Try to initialize
        initMiniCart();

        // If not initialized yet, try again after a short delay
        if (!initialized) {
            setTimeout(checkAlpineInit, 100);
        }
    }

    // Start checking when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkAlpineInit);
    } else {
        checkAlpineInit();
    }

    // Also check when Alpine.js is loaded
    document.addEventListener('alpine:initialized', initMiniCart);
})();
