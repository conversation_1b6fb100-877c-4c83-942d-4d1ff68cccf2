<?php
/**
 * Admin template for Tours management (unified approach)
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Get tours table status
$table_status = LCI_Tours_Unified::check_tours_table();
?>

<!-- Add Tailwind CSS via CDN -->
<script src="https://cdn.tailwindcss.com"></script>
<script>
  tailwind.config = {
    theme: {
      extend: {
        colors: {
          primary: '#0073aa', // WordPress primary blue
          secondary: '#46b450', // WordPress success green
          danger: '#dc3232', // WordPress error red
          warning: '#ffb900', // WordPress warning yellow
        }
      }
    }
  }
</script>

<style>
    /* Hide elements with x-cloak until Alpine.js is loaded */
    [x-cloak] { display: none !important; }
</style>

<div class="wrap lci-admin-wrap">
    <h1 class="wp-heading-inline"><?php _e('Tours Participants', 'lci-2025-dashboard'); ?></h1>

    <div id="tours-manager">
        <!-- Sync Tool -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-medium text-gray-800 mb-4">Sync Tours Participants</h2>

            <div class="mb-4">
                <p class="mb-2">This tool will sync tour participants from WooCommerce orders with the following product IDs:</p>
                <ul class="list-disc pl-5 mb-4">
                    <li>Main Pretour (ID: 743)</li>
                    <li>Legends and Wildlife Escape (ID: 744)</li>
                    <li>Royal Elegance & Sparkling Delights (ID: 745)</li>
                    <li>Brasov Highlights (ID: 746)</li>
                </ul>
                <p class="mb-4">Note: One user can have multiple tours.</p>
            </div>

            <div class="flex items-center">
                <button
                    id="sync-button"
                    onclick="syncTours()"
                    class="px-4 py-2 bg-primary text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="sync-text">Sync Tours Participants</span>
                    <div id="sync-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Syncing...</span>
                    </div>
                </button>

                <div class="ml-4" id="sync-message-container">
                    <div id="sync-success-message" class="text-secondary" style="display: none;">
                        <span id="sync-success-text"></span>
                    </div>
                    <div id="sync-error-message" class="text-danger" style="display: none;">
                        <span id="sync-error-text"></span>
                    </div>
                </div>
            </div>

            <div class="mt-4 flex space-x-4">
                <button
                    id="clear-table-button"
                    onclick="clearTable()"
                    class="px-4 py-2 bg-warning text-white rounded hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-warning focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="clear-table-text">Clear Tours Table</span>
                    <div id="clear-table-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Clearing...</span>
                    </div>
                </button>

                <button
                    id="recreate-table-button"
                    onclick="recreateTable()"
                    class="px-4 py-2 bg-danger text-white rounded hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-danger focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="recreate-table-text">Recreate Tours Table</span>
                    <div id="recreate-table-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Recreating...</span>
                    </div>
                </button>
            </div>
        </div>

        <!-- Participants List -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-medium text-gray-800">Tours Participants</h2>
            </div>

            <!-- Search and Filter Controls -->
            <div class="flex flex-wrap items-center justify-between mb-4">
                <div class="w-full md:w-auto mb-4 md:mb-0">
                    <div class="flex items-center">
                        <input
                            type="text"
                            id="search-query-input"
                            oninput="setSearchQuery(this.value)"
                            placeholder="Search by name, email..."
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-64"
                        >
                    </div>
                </div>

                <div class="w-full md:w-auto">
                    <div class="flex flex-wrap items-center space-x-0 md:space-x-4 space-y-2 md:space-y-0">
                        <select
                            id="tour-filter-select"
                            onchange="setTourFilter(this.value)"
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                        >
                            <option value="">All Tours</option>
                            <option value="main_pretour">Main Pretour</option>
                            <option value="legends_wildlife">Legends and Wildlife Escape</option>
                            <option value="royal_elegance">Royal Elegance & Sparkling Delights</option>
                            <option value="brasov_highlights">Brasov Highlights</option>
                        </select>

                        <select
                            id="status-filter-select"
                            onchange="setStatusFilter(this.value)"
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                        >
                            <option value="">All Statuses</option>
                            <option value="completed">Completed</option>
                            <option value="processing">Processing</option>
                            <option value="on-hold">On Hold</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="pending">Pending</option>
                        </select>

                        <div class="flex items-center">
                            <select
                                id="country-filter-select"
                                onchange="setCountryFilter(this.value)"
                                class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                            >
                                <option value="">All Countries</option>
                                <!-- Country options will be populated by JavaScript -->
                            </select>
                        </div>

                        <div class="flex space-x-2">
                            <button
                                onclick="resetFilters()"
                                class="mt-2 md:mt-0 px-3 py-2 bg-secondary text-white rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-opacity-50 transition-colors duration-200"
                            >
                                Reset Filters
                            </button>

                            <button
                                id="refresh-button"
                                onclick="refreshParticipants()"
                                class="mt-2 md:mt-0 px-3 py-2 bg-primary text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                            >
                                <span id="refresh-text">Refresh</span>
                                <span id="loading-text" style="display: none;">Loading...</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading indicator -->
            <div id="loading-indicator" class="flex justify-center items-center py-8" style="display: none;">
                <svg class="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="ml-2 text-gray-600">Loading participants...</span>
            </div>

            <!-- Participants table -->
            <div id="participants-table-container" class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th id="sort-name" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('last_name')">
                                Name
                                <span id="sort-name-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-name-asc" style="display: none;">▲</span>
                                    <span id="sort-name-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-email" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('email')">
                                Email
                                <span id="sort-email-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-email-asc" style="display: none;">▲</span>
                                    <span id="sort-email-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-country" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('country')">
                                Country
                                <span id="sort-country-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-country-asc" style="display: none;">▲</span>
                                    <span id="sort-country-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-order-date" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('order_date')">
                                Order Date
                                <span id="sort-order-date-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-order-date-asc" style="display: none;">▲</span>
                                    <span id="sort-order-date-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-status" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('payment_status')">
                                Status
                                <span id="sort-status-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-status-asc" style="display: none;">▲</span>
                                    <span id="sort-status-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                Tours
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr id="no-participants-row" style="display: none;">
                            <td colspan="7" class="px-4 py-4 text-center text-gray-500">
                                No participants found. Try adjusting your filters or sync the tours data.
                            </td>
                        </tr>
                        <!-- Participant rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div> <!-- End of tours-manager div -->

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
            <p class="mb-4">Are you sure you want to delete the participant <span id="delete-participant-name" class="font-semibold"></span>?</p>
            <div class="flex justify-end space-x-4">
                <button
                    type="button"
                    onclick="document.getElementById('delete-modal').style.display = 'none';"
                    class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors duration-200"
                >
                    Cancel
                </button>
                <button
                    type="button"
                    id="confirm-delete-button"
                    onclick="deleteParticipant()"
                    class="px-4 py-2 bg-danger text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-danger focus:ring-opacity-50 transition-colors duration-200"
                >
                    Delete
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Participant Modal -->
    <div id="edit-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Edit Participant</h3>
                <button
                    type="button"
                    onclick="document.getElementById('edit-modal').style.display = 'none';"
                    class="text-gray-400 hover:text-gray-500 focus:outline-none"
                >
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <form id="edit-form">
                <input type="hidden" id="edit-id" name="id">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="edit-first-name" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                        <input type="text" id="edit-first-name" name="first_name" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="edit-last-name" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                        <input type="text" id="edit-last-name" name="last_name" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="edit-email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" id="edit-email" name="email" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="edit-phone" class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <input type="text" id="edit-phone" name="phone" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="edit-country" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                        <select id="edit-country" name="country" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select Country</option>
                            <!-- Country options will be populated by JavaScript -->
                        </select>
                    </div>
                    <div>
                        <label for="edit-payment-status" class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                        <select id="edit-payment-status" name="payment_status" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="completed">Completed</option>
                            <option value="processing">Processing</option>
                            <option value="on-hold">On Hold</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                    <div>
                        <label for="edit-user-id" class="block text-sm font-medium text-gray-700 mb-1">WordPress User ID</label>
                        <input type="number" id="edit-user-id" name="user_id" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Tours</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="edit-main-pretour" name="main_pretour" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="edit-main-pretour" class="ml-2 block text-sm text-gray-900">Main Pretour</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="edit-legends-wildlife" name="legends_wildlife" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="edit-legends-wildlife" class="ml-2 block text-sm text-gray-900">Legends and Wildlife Escape</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="edit-royal-elegance" name="royal_elegance" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="edit-royal-elegance" class="ml-2 block text-sm text-gray-900">Royal Elegance & Sparkling Delights</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="edit-brasov-highlights" name="brasov_highlights" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="edit-brasov-highlights" class="ml-2 block text-sm text-gray-900">Brasov Highlights</label>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button
                        type="button"
                        onclick="document.getElementById('edit-modal').style.display = 'none';"
                        class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors duration-200 mr-4"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        class="px-4 py-2 bg-primary text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                    >
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>

<script>
// Initialize when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing tours manager');

    // Initialize the global data object
    window.toursManagerData = {
        searchQuery: '',
        tourFilter: '',
        countryFilter: '',
        statusFilter: '',
        sortField: 'order_date',
        sortAsc: false,
        participants: [],
        filteredParticipants: [],
        countries: []
    };

    // Fetch countries
    fetchCountriesDirectly();

    // Fetch participants
    fetchParticipantsDirectly();

    // Set up event listeners for the edit form
    document.getElementById('edit-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveParticipant();
    });
});

// Helper functions
function setSearchQuery(value) {
    window.toursManagerData.searchQuery = value;
    applyFilters();
}

function setTourFilter(value) {
    window.toursManagerData.tourFilter = value;
    applyFilters();
}

function setStatusFilter(value) {
    window.toursManagerData.statusFilter = value;
    applyFilters();
}

function setCountryFilter(value) {
    window.toursManagerData.countryFilter = value;
    applyFilters();
}

function resetFilters() {
    // Reset filter variables
    window.toursManagerData.searchQuery = '';
    window.toursManagerData.tourFilter = '';
    window.toursManagerData.countryFilter = '';
    window.toursManagerData.statusFilter = '';

    // Reset DOM elements
    document.getElementById('search-query-input').value = '';
    document.getElementById('tour-filter-select').value = '';
    document.getElementById('status-filter-select').value = '';
    document.getElementById('country-filter-select').value = '';

    // Apply filters
    applyFilters();
}

function setSortField(field) {
    if (window.toursManagerData.sortField === field) {
        // Toggle sort direction if the same field is clicked
        window.toursManagerData.sortAsc = !window.toursManagerData.sortAsc;
    } else {
        // Set new sort field and default to ascending
        window.toursManagerData.sortField = field;
        window.toursManagerData.sortAsc = true;
    }

    // Update sort indicators
    updateSortIndicators();

    // Sort the participants
    sortParticipants();

    // Render the table
    renderParticipantsTable();
}

function updateSortIndicators() {
    // Hide all indicators
    const fields = ['name', 'email', 'country', 'order-date', 'status'];
    fields.forEach(function(field) {
        document.getElementById(`sort-${field}-indicator`).style.display = 'none';
        document.getElementById(`sort-${field}-asc`).style.display = 'none';
        document.getElementById(`sort-${field}-desc`).style.display = 'none';
    });

    // Show the active indicator
    let activeField;
    switch (window.toursManagerData.sortField) {
        case 'last_name':
            activeField = 'name';
            break;
        case 'email':
            activeField = 'email';
            break;
        case 'country':
            activeField = 'country';
            break;
        case 'order_date':
            activeField = 'order-date';
            break;
        case 'payment_status':
            activeField = 'status';
            break;
        default:
            return;
    }

    document.getElementById(`sort-${activeField}-indicator`).style.display = 'inline';
    if (window.toursManagerData.sortAsc) {
        document.getElementById(`sort-${activeField}-asc`).style.display = 'inline';
    } else {
        document.getElementById(`sort-${activeField}-desc`).style.display = 'inline';
    }
}

function sortParticipants() {
    if (!Array.isArray(window.toursManagerData.filteredParticipants)) {
        window.toursManagerData.filteredParticipants = [];
        return;
    }

    try {
        // Sort filtered participants
        window.toursManagerData.filteredParticipants.sort((a, b) => {
            try {
                // Check if objects are valid
                if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {
                    return 0;
                }

                let aValue = a[window.toursManagerData.sortField];
                let bValue = b[window.toursManagerData.sortField];

                // Handle date fields
                if (window.toursManagerData.sortField === 'order_date' || window.toursManagerData.sortField === 'created_at' || window.toursManagerData.sortField === 'updated_at') {
                    aValue = new Date(aValue || '1970-01-01').getTime();
                    bValue = new Date(bValue || '1970-01-01').getTime();
                }

                // Handle string fields
                if (typeof aValue === 'string') {
                    aValue = aValue.toLowerCase();
                }
                if (typeof bValue === 'string') {
                    bValue = bValue.toLowerCase();
                }

                // Compare values
                if (aValue < bValue) return window.toursManagerData.sortAsc ? -1 : 1;
                if (aValue > bValue) return window.toursManagerData.sortAsc ? 1 : -1;
                return 0;
            } catch (error) {
                return 0;
            }
        });
    } catch (error) {
        // Silently handle errors
    }
}

function applyFilters() {
    if (!Array.isArray(window.toursManagerData.participants)) {
        window.toursManagerData.filteredParticipants = [];
        return;
    }

    window.toursManagerData.filteredParticipants = window.toursManagerData.participants.filter(p => {
        // Search query filter
        const searchMatch = !window.toursManagerData.searchQuery ||
            (p.first_name && p.first_name.toLowerCase().includes(window.toursManagerData.searchQuery.toLowerCase())) ||
            (p.last_name && p.last_name.toLowerCase().includes(window.toursManagerData.searchQuery.toLowerCase())) ||
            (p.email && p.email.toLowerCase().includes(window.toursManagerData.searchQuery.toLowerCase()));

        // Tour filter
        let tourMatch = true;
        if (window.toursManagerData.tourFilter) {
            switch (window.toursManagerData.tourFilter) {
                case 'main_pretour':
                    tourMatch = p.main_pretour;
                    break;
                case 'legends_wildlife':
                    tourMatch = p.legends_wildlife;
                    break;
                case 'royal_elegance':
                    tourMatch = p.royal_elegance;
                    break;
                case 'brasov_highlights':
                    tourMatch = p.brasov_highlights;
                    break;
            }
        }

        // Country filter
        const countryMatch = !window.toursManagerData.countryFilter ||
            (p.country && p.country === window.toursManagerData.countryFilter);

        // Status filter
        const statusMatch = !window.toursManagerData.statusFilter ||
            (p.payment_status && p.payment_status === window.toursManagerData.statusFilter);

        return searchMatch && tourMatch && countryMatch && statusMatch;
    });

    // Sort the filtered participants
    sortParticipants();

    // Render the participants table
    renderParticipantsTable();
}

function fetchCountriesDirectly() {
    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();
    const url = `${lciAdmin.ajaxUrl}?action=lci_get_tours_countries&nonce=${lciAdmin.nonce}&_=${timestamp}`;

    fetch(url)
        .then(response => response.json())
        .then(function(data) {
            if (data.success && Array.isArray(data.data.countries)) {
                // Store countries in the global data object for later use
                window.toursManagerData.countries = data.data.countries;

                // Update filter dropdown
                updateCountryFilterDropdown(data.data.countries);

                // Update edit form dropdown
                updateEditCountryDropdown(data.data.countries);
            } else {
                console.error('Error fetching countries:', data);
            }
        })
        .catch(function(error) {
            console.error('Error fetching countries:', error);
        });
}

function updateCountryFilterDropdown(countries) {
    // Get the country select element
    const countrySelect = document.getElementById('country-filter-select');

    // Clear existing options (except the first one)
    while (countrySelect.options.length > 1) {
        countrySelect.remove(1);
    }

    // Add new options
    countries.forEach(function(country) {
        const option = document.createElement('option');
        option.value = country;
        option.textContent = country;
        countrySelect.appendChild(option);
    });
}

function updateEditCountryDropdown(countries) {
    // Get the country select element
    const countrySelect = document.getElementById('edit-country');

    // Clear existing options (except the first one)
    while (countrySelect.options.length > 1) {
        countrySelect.remove(1);
    }

    // If no countries are provided, add some common ones as a fallback
    if (!countries || countries.length === 0) {
        const commonCountries = [
            'United States', 'Canada', 'United Kingdom', 'Australia',
            'Germany', 'France', 'Italy', 'Spain', 'Romania', 'Bulgaria',
            'Netherlands', 'Belgium', 'Switzerland', 'Austria', 'Sweden',
            'Norway', 'Denmark', 'Finland', 'Ireland', 'Portugal', 'Greece',
            'Poland', 'Czech Republic', 'Hungary', 'Slovakia', 'Croatia',
            'Serbia', 'Slovenia', 'Bosnia and Herzegovina', 'Montenegro',
            'North Macedonia', 'Albania', 'Moldova', 'Ukraine', 'Belarus',
            'Russia', 'Turkey', 'Israel', 'Egypt', 'South Africa', 'Brazil',
            'Argentina', 'Mexico', 'Japan', 'China', 'India', 'South Korea',
            'Singapore', 'Malaysia', 'Indonesia', 'Thailand', 'Vietnam',
            'Philippines', 'New Zealand'
        ];

        countries = commonCountries;
    }

    // Add new options
    countries.forEach(function(country) {
        const option = document.createElement('option');
        option.value = country;
        option.textContent = country;
        countrySelect.appendChild(option);
    });
}

function fetchParticipantsDirectly() {
    // Show loading state
    document.getElementById('loading-indicator').style.display = 'flex';
    document.getElementById('participants-table-container').style.display = 'none';
    document.getElementById('refresh-text').style.display = 'none';
    document.getElementById('loading-text').style.display = 'inline';
    document.getElementById('refresh-button').disabled = true;

    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();
    let url = `${lciAdmin.ajaxUrl}?action=lci_get_tours_unified&nonce=${lciAdmin.nonce}&_=${timestamp}`;

    // Add filters if they exist
    if (window.toursManagerData.searchQuery) {
        url += `&search=${encodeURIComponent(window.toursManagerData.searchQuery)}`;
    }

    if (window.toursManagerData.tourFilter) {
        url += `&tour_type=${encodeURIComponent(window.toursManagerData.tourFilter)}`;
    }

    if (window.toursManagerData.countryFilter) {
        url += `&country=${encodeURIComponent(window.toursManagerData.countryFilter)}`;
    }

    if (window.toursManagerData.statusFilter) {
        url += `&payment_status=${encodeURIComponent(window.toursManagerData.statusFilter)}`;
    }

    fetch(url)
        .then(response => response.json())
        .then(function(data) {
            if (data.success && Array.isArray(data.data.participants)) {
                window.toursManagerData.participants = data.data.participants;
                window.toursManagerData.filteredParticipants = [...window.toursManagerData.participants];
                sortParticipants();

                // Render the participants table
                renderParticipantsTable();
            } else {
                showToast('Error fetching participants: ' + (data.data?.message || 'Unknown error'), 'error');
                window.toursManagerData.participants = [];
                window.toursManagerData.filteredParticipants = [];

                // Show the no participants message
                document.getElementById('no-participants-row').style.display = 'table-row';
            }
        })
        .catch(function(error) {
            showToast('Error fetching participants: ' + error.message, 'error');
            window.toursManagerData.participants = [];
            window.toursManagerData.filteredParticipants = [];

            // Show the no participants message
            document.getElementById('no-participants-row').style.display = 'table-row';
        })
        .finally(function() {
            // Hide loading state
            document.getElementById('loading-indicator').style.display = 'none';
            document.getElementById('participants-table-container').style.display = 'block';
            document.getElementById('refresh-text').style.display = 'inline';
            document.getElementById('loading-text').style.display = 'none';
            document.getElementById('refresh-button').disabled = false;
        });
}

function refreshParticipants() {
    fetchParticipantsDirectly();
}

function renderParticipantsTable() {
    const tableBody = document.querySelector('#participants-table-container table tbody');
    const noParticipantsRow = document.getElementById('no-participants-row');

    // Clear existing rows except the no-participants-row
    const rows = tableBody.querySelectorAll('tr:not(#no-participants-row)');
    rows.forEach(row => row.remove());

    // Check if there are any participants
    if (window.toursManagerData.filteredParticipants.length === 0) {
        noParticipantsRow.style.display = 'table-row';
        return;
    }

    // Hide the no participants message
    noParticipantsRow.style.display = 'none';

    // Add rows for each participant
    window.toursManagerData.filteredParticipants.forEach((participant, index) => {
        const row = document.createElement('tr');
        row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';

        // Name cell
        const nameCell = document.createElement('td');
        nameCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        nameCell.textContent = participant.first_name + ' ' + participant.last_name;
        row.appendChild(nameCell);

        // Email cell
        const emailCell = document.createElement('td');
        emailCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        emailCell.textContent = participant.email;
        row.appendChild(emailCell);

        // Country cell
        const countryCell = document.createElement('td');
        countryCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        countryCell.textContent = participant.country || 'N/A';
        row.appendChild(countryCell);

        // Order date cell
        const orderDateCell = document.createElement('td');
        orderDateCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        orderDateCell.textContent = formatDate(participant.order_date);
        row.appendChild(orderDateCell);

        // Status cell
        const statusCell = document.createElement('td');
        statusCell.className = 'px-4 py-2 whitespace-nowrap text-sm border-b';

        const statusSpan = document.createElement('span');
        statusSpan.className = 'px-2 inline-flex text-xs leading-5 font-semibold rounded-full';

        // Add status-specific classes
        if (participant.payment_status === 'completed') {
            statusSpan.classList.add('bg-green-100', 'text-green-800');
        } else if (participant.payment_status === 'processing' || participant.payment_status === 'on-hold') {
            statusSpan.classList.add('bg-yellow-100', 'text-yellow-800');
        } else if (participant.payment_status === 'failed' || participant.payment_status === 'cancelled') {
            statusSpan.classList.add('bg-red-100', 'text-red-800');
        } else {
            statusSpan.classList.add('bg-gray-100', 'text-gray-800');
        }

        statusSpan.textContent = participant.payment_status;
        statusCell.appendChild(statusSpan);
        row.appendChild(statusCell);

        // Tours cell
        const toursCell = document.createElement('td');
        toursCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';

        const toursDiv = document.createElement('div');
        toursDiv.className = 'flex flex-wrap gap-1';

        if (participant.main_pretour) {
            const tourSpan = document.createElement('span');
            tourSpan.className = 'px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs';
            tourSpan.textContent = 'Main Pretour';
            toursDiv.appendChild(tourSpan);
        }

        if (participant.legends_wildlife) {
            const tourSpan = document.createElement('span');
            tourSpan.className = 'px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs';
            tourSpan.textContent = 'Legends & Wildlife';
            toursDiv.appendChild(tourSpan);
        }

        if (participant.royal_elegance) {
            const tourSpan = document.createElement('span');
            tourSpan.className = 'px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs';
            tourSpan.textContent = 'Royal Elegance';
            toursDiv.appendChild(tourSpan);
        }

        if (participant.brasov_highlights) {
            const tourSpan = document.createElement('span');
            tourSpan.className = 'px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs';
            tourSpan.textContent = 'Brasov';
            toursDiv.appendChild(tourSpan);
        }

        toursCell.appendChild(toursDiv);
        row.appendChild(toursCell);

        // Actions cell
        const actionsCell = document.createElement('td');
        actionsCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';

        const editButton = document.createElement('button');
        editButton.className = 'px-2 py-1 bg-primary text-white rounded hover:bg-blue-600 mr-2';
        editButton.textContent = 'Edit';
        editButton.setAttribute('data-participant', JSON.stringify(participant));
        editButton.onclick = function() {
            editParticipant(this.getAttribute('data-participant'));
        };
        actionsCell.appendChild(editButton);

        const deleteButton = document.createElement('button');
        deleteButton.className = 'px-2 py-1 bg-danger text-white rounded hover:bg-red-600';
        deleteButton.textContent = 'Delete';
        deleteButton.setAttribute('data-participant-id', participant.id);
        deleteButton.onclick = function() {
            confirmDeleteParticipant(this.getAttribute('data-participant-id'));
        };
        actionsCell.appendChild(deleteButton);

        row.appendChild(actionsCell);

        // Add the row to the table
        tableBody.appendChild(row);
    });
}

// Helper function to format dates
function formatDate(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (e) {
        return dateString;
    }
}

function showToast(message, type) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded shadow-lg z-50';

    // Set background color based on type
    if (type === 'success') {
        toast.classList.add('bg-green-500', 'text-white');
    } else if (type === 'error') {
        toast.classList.add('bg-red-500', 'text-white');
    } else {
        toast.classList.add('bg-blue-500', 'text-white');
    }

    // Set message
    toast.textContent = message;

    // Add to document
    document.body.appendChild(toast);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 500);
    }, 3000);
}

function confirmDeleteParticipant(participantId) {
    // Find the participant by ID
    const participant = window.toursManagerData.filteredParticipants.find(p => p.id == participantId);
    if (participant) {
        // Show the delete confirmation modal
        document.getElementById('delete-modal').style.display = 'flex';
        document.getElementById('delete-participant-name').textContent = participant.first_name + ' ' + participant.last_name;
        document.getElementById('confirm-delete-button').setAttribute('data-participant-id', participant.id);
    } else {
        console.error('Participant not found with ID:', participantId);
    }
}

function deleteParticipant() {
    const participantId = document.getElementById('confirm-delete-button').getAttribute('data-participant-id');
    if (!participantId) {
        console.error('No participant ID found for deletion');
        return;
    }

    // Hide the modal
    document.getElementById('delete-modal').style.display = 'none';

    // Show loading state
    const deleteButton = document.getElementById('confirm-delete-button');
    const originalText = deleteButton.textContent;
    deleteButton.textContent = 'Deleting...';
    deleteButton.disabled = true;

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_delete_tour_participant');
    formData.append('nonce', lciAdmin.nonce);
    formData.append('participant_id', participantId);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            showToast('Participant deleted successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error deleting participant';
            showToast('Error: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
        // Reset button state
        deleteButton.textContent = originalText;
        deleteButton.disabled = false;
    });
}

function editParticipant(participantJson) {
    let participant;
    try {
        participant = JSON.parse(participantJson);
    } catch (e) {
        console.error('Error parsing participant JSON:', e);
        return;
    }

    // Populate the edit form
    document.getElementById('edit-id').value = participant.id;
    document.getElementById('edit-first-name').value = participant.first_name || '';
    document.getElementById('edit-last-name').value = participant.last_name || '';
    document.getElementById('edit-email').value = participant.email || '';
    document.getElementById('edit-phone').value = participant.phone || '';

    // Set the country dropdown value
    const countrySelect = document.getElementById('edit-country');
    if (participant.country) {
        // Check if the country exists in the dropdown
        let countryExists = false;
        for (let i = 0; i < countrySelect.options.length; i++) {
            if (countrySelect.options[i].value === participant.country) {
                countrySelect.value = participant.country;
                countryExists = true;
                break;
            }
        }

        // If the country doesn't exist in the dropdown, add it
        if (!countryExists && participant.country) {
            const option = document.createElement('option');
            option.value = participant.country;
            option.textContent = participant.country;
            countrySelect.appendChild(option);
            countrySelect.value = participant.country;
        }
    } else {
        countrySelect.value = '';
    }

    document.getElementById('edit-payment-status').value = participant.payment_status || '';
    document.getElementById('edit-user-id').value = participant.user_id || '0';

    // Set checkboxes
    document.getElementById('edit-main-pretour').checked = !!participant.main_pretour;
    document.getElementById('edit-legends-wildlife').checked = !!participant.legends_wildlife;
    document.getElementById('edit-royal-elegance').checked = !!participant.royal_elegance;
    document.getElementById('edit-brasov-highlights').checked = !!participant.brasov_highlights;

    // Show the modal
    document.getElementById('edit-modal').style.display = 'flex';
}

function saveParticipant() {
    // Get form data
    const form = document.getElementById('edit-form');
    const formData = new FormData(form);

    // Add action and nonce
    formData.append('action', 'lci_update_tour_participant');
    formData.append('nonce', lciAdmin.nonce);

    // Convert checkboxes to 0/1
    formData.set('main_pretour', document.getElementById('edit-main-pretour').checked ? 1 : 0);
    formData.set('legends_wildlife', document.getElementById('edit-legends-wildlife').checked ? 1 : 0);
    formData.set('royal_elegance', document.getElementById('edit-royal-elegance').checked ? 1 : 0);
    formData.set('brasov_highlights', document.getElementById('edit-brasov-highlights').checked ? 1 : 0);

    // Show loading state
    const saveButton = document.querySelector('#edit-form button[type="submit"]');
    const originalText = saveButton.textContent;
    saveButton.textContent = 'Saving...';
    saveButton.disabled = true;

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            showToast('Participant updated successfully', 'success');

            // Hide the modal
            document.getElementById('edit-modal').style.display = 'none';

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error updating participant';
            showToast('Error: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
        // Reset button state
        saveButton.textContent = originalText;
        saveButton.disabled = false;
    });
}

function syncTours() {
    // Show loading state
    document.getElementById('sync-text').style.display = 'none';
    document.getElementById('sync-loading').style.display = 'flex';
    document.getElementById('sync-button').disabled = true;

    // Hide any existing messages
    document.getElementById('sync-success-message').style.display = 'none';
    document.getElementById('sync-error-message').style.display = 'none';

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_sync_tours_unified');
    formData.append('nonce', lciAdmin.nonce);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            const message = data.data?.message || 'Tours synced successfully';

            // Update DOM directly
            document.getElementById('sync-success-text').textContent = message;
            document.getElementById('sync-success-message').style.display = 'block';

            showToast('Tours synced successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error syncing tours';

            // Update DOM directly
            document.getElementById('sync-error-text').textContent = message;
            document.getElementById('sync-error-message').style.display = 'block';

            showToast('Error syncing tours: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        const message = 'Error syncing tours: ' + error.message;

        // Update DOM directly
        document.getElementById('sync-error-text').textContent = message;
        document.getElementById('sync-error-message').style.display = 'block';

        showToast(message, 'error');
    })
    .finally(function() {
        // Reset the button state
        document.getElementById('sync-text').style.display = 'inline';
        document.getElementById('sync-loading').style.display = 'none';
        document.getElementById('sync-button').disabled = false;
    });
}

function clearTable() {
    // Confirm before proceeding
    if (!confirm('Are you sure you want to clear the tours table? This will delete all tour participant data.')) {
        return;
    }

    // Show loading state
    document.getElementById('clear-table-text').style.display = 'none';
    document.getElementById('clear-table-loading').style.display = 'flex';
    document.getElementById('clear-table-button').disabled = true;

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_clear_tours_table_unified');
    formData.append('nonce', lciAdmin.nonce);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            showToast('Tours table cleared successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error clearing tours table';
            showToast('Error: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
        // Reset button state
        document.getElementById('clear-table-text').style.display = 'inline';
        document.getElementById('clear-table-loading').style.display = 'none';
        document.getElementById('clear-table-button').disabled = false;
    });
}

function recreateTable() {
    // Confirm before proceeding
    if (!confirm('Are you sure you want to recreate the tours table? This will delete all tour participant data and recreate the table structure.')) {
        return;
    }

    // Show loading state
    document.getElementById('recreate-table-text').style.display = 'none';
    document.getElementById('recreate-table-loading').style.display = 'flex';
    document.getElementById('recreate-table-button').disabled = true;

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_recreate_tours_table_unified');
    formData.append('nonce', lciAdmin.nonce);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            showToast('Tours table recreated successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error recreating tours table';
            showToast('Error: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
        // Reset button state
        document.getElementById('recreate-table-text').style.display = 'inline';
        document.getElementById('recreate-table-loading').style.display = 'none';
        document.getElementById('recreate-table-button').disabled = false;
    });
}
</script>

<?php
// End of file
?>
