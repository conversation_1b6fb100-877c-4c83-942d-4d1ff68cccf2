<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Merchandise Shop Shortcode</title>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #36b1dc;
            text-align: center;
            margin-bottom: 30px;
        }
        .instructions {
            background-color: #f8f9fa;
            border-left: 4px solid #36b1dc;
            padding: 15px;
            margin-bottom: 30px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Test Merchandise Shop Shortcode</h1>
    
    <div class="instructions">
        <h2>How to Use the Shortcode</h2>
        <p>The <code>[lci_merchandise_shop]</code> shortcode allows you to display merchandise products from a WooCommerce category on any page, making them available for purchase by both authenticated and unauthenticated users.</p>
        
        <h3>Basic Usage</h3>
        <pre><code>[lci_merchandise_shop]</code></pre>
        
        <h3>With Parameters</h3>
        <pre><code>[lci_merchandise_shop category_id="22" title="LCI 2025 Merchandise" columns="3"]</code></pre>
        
        <h3>Available Parameters</h3>
        <ul>
            <li><strong>category_id</strong> - The WooCommerce category ID to display products from (default: 22)</li>
            <li><strong>title</strong> - The title to display above the products (default: "LCI 2025 Merchandise Shop")</li>
            <li><strong>columns</strong> - The number of columns to display products in (1-4, default: 3)</li>
        </ul>
    </div>
    
    <h2>Implementation Steps</h2>
    <ol>
        <li>Add the shortcode to any WordPress page or post</li>
        <li>Make sure the category ID contains products</li>
        <li>Test adding products to cart as both logged-in and logged-out users</li>
        <li>Verify that the mini-cart functionality works correctly</li>
        <li>Test the checkout process to ensure orders are processed correctly</li>
    </ol>
    
    <h2>Example Page Template</h2>
    <pre><code>&lt;?php
/**
 * Template Name: Merchandise Shop
 */

get_header();
?&gt;

&lt;div class="container mt-5 mb-5"&gt;
    &lt;div class="row"&gt;
        &lt;div class="col-12"&gt;
            &lt;h1 class="mb-4"&gt;LCI 2025 Merchandise&lt;/h1&gt;
            
            &lt;?php echo do_shortcode('[lci_merchandise_shop category_id="22" title="LCI 2025 Merchandise" columns="3"]'); ?&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;?php
get_footer();
?&gt;</code></pre>
</body>
</html>
