# LCI 2025 Support System - Admin Guide

This guide explains how to use the LCI 2025 Support System from an administrator's perspective to manage and respond to user support tickets.

## Table of Contents

1. [Accessing the Admin Support Dashboard](#accessing-the-admin-support-dashboard)
2. [Viewing and Filtering Tickets](#viewing-and-filtering-tickets)
3. [Responding to Tickets](#responding-to-tickets)
4. [Updating Ticket Status](#updating-ticket-status)
5. [Understanding the Notification System](#understanding-the-notification-system)
6. [Best Practices for Support](#best-practices-for-support)

## Accessing the Admin Support Dashboard

To access the Admin Support Dashboard:

1. Log in to the WordPress admin area
2. In the left sidebar, navigate to "LCI Dashboard" > "Support Tickets"
3. The Support Tickets admin page will load, showing all tickets in the system

## Viewing and Filtering Tickets

The admin dashboard provides several ways to filter and sort tickets:

### Filtering Options

- **Status Filter**: Filter tickets by their status (Open, In Progress, Resolved, Closed)
- **Category Filter**: Filter tickets by category (Registration, Payment, Accommodation, etc.)
- **Search**: Search for tickets by ticket ID or subject

### Using Filters

1. Select the desired filters from the dropdown menus at the top of the page
2. Enter search terms in the search box if needed
3. Click the "Apply Filters" button to update the list
4. To clear all filters, click the "Reset" button

### Pagination

- Use the pagination controls at the bottom of the page to navigate through multiple pages of tickets
- You can change the number of tickets displayed per page in the "Screen Options" tab at the top of the page

## Responding to Tickets

To view and respond to a ticket:

1. Click the "View" button next to the ticket in the list
2. The ticket details page will open, showing:
   - Ticket information (ID, subject, category, status)
   - The original message from the user
   - Any attachments
   - The conversation history (all replies)

### Adding a Reply

1. Scroll down to the "Reply to Ticket" section at the bottom of the page
2. Type your response in the message field
3. Attach a file if needed (optional)
4. Click the "Send Reply" button
5. Your reply will be added to the conversation and the user will be notified by email

## Updating Ticket Status

You can update the status of a ticket to reflect its current state:

1. From the ticket details page, locate the "Admin Actions" section
2. Click one of the status buttons:
   - **Mark as Open**: Sets the ticket status to "Open"
   - **Mark as In Progress**: Sets the ticket status to "In Progress"
   - **Mark as Resolved**: Sets the ticket status to "Resolved"
   - **Close Ticket**: Sets the ticket status to "Closed"
3. The status will be updated immediately and the user will be notified by email

### When to Use Each Status

- **Open**: Use when a ticket needs attention but hasn't been addressed yet
- **In Progress**: Use when you're actively working on resolving the issue
- **Resolved**: Use when you believe the issue has been resolved
- **Closed**: Use when the ticket is complete and no further action is needed

## Understanding the Notification System

The support system automatically sends email notifications to keep both users and administrators informed:

### Admin Notifications

Administrators receive email notifications when:
- A new ticket is submitted
- A user replies to an existing ticket

### User Notifications

Users receive email notifications when:
- Their ticket is submitted (confirmation)
- An admin replies to their ticket
- The status of their ticket changes

All notification emails include relevant ticket information and, in the case of admin notifications, a direct link to the ticket in the admin dashboard.

## Best Practices for Support

### Response Time

- Aim to respond to new tickets within 24-48 hours
- For urgent issues, try to provide at least an initial response within 24 hours

### Writing Effective Responses

- Be professional and courteous
- Address the user by name
- Answer all questions asked in the ticket
- Provide clear, step-by-step instructions when needed
- Use simple language and avoid technical jargon
- End with a clear call to action or next steps

### Managing Ticket Flow

- Start each day by checking for new tickets
- Prioritize tickets based on urgency and age
- Update ticket statuses promptly to reflect their current state
- Follow up on "In Progress" tickets that haven't been updated recently

### Handling Difficult Situations

- Remain calm and professional, even when users are frustrated
- Focus on solutions rather than problems
- If you're unsure how to handle a ticket, consult with other team members
- For complex issues, it's better to take time to find the right solution than to respond quickly with incomplete information

---

If you have any questions about administering the Support System, please contact the development <NAME_EMAIL>.
