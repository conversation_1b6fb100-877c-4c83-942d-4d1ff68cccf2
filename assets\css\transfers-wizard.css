/**
 * Transfers Wizard CSS
 * Styles for the multi-step wizard for booking transfers
 */

/* Wizard Container */
.transfers-wizard-container {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02);
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
    margin-bottom: 30px;
}

/* Progress Bar */
.transfers-wizard-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
}

.transfers-wizard-progress::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e9ecef;
    transform: translateY(-50%);
    z-index: 1;
}

.transfers-wizard-progress-bar {
    position: absolute;
    top: 50%;
    left: 0;
    height: 2px;
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    transform: translateY(-50%);
    z-index: 2;
    transition: width 0.3s ease;
}

.transfers-wizard-step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #6c757d;
    position: relative;
    z-index: 3;
    transition: all 0.3s ease;
}

.transfers-wizard-step.active {
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    box-shadow: 0 4px 10px rgba(54, 177, 220, 0.3);
}

.transfers-wizard-step.completed {
    background: #28a745;
    color: white;
}

.transfers-wizard-step-label {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 8px;
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    white-space: nowrap;
}

/* Step Content */
.transfers-wizard-content {
    min-height: 400px;
}

.transfers-wizard-step-content {
    display: none;
}

.transfers-wizard-step-content.active {
    display: block;
    animation: fadeIn 0.5s ease forwards;
}

/* Transfer Type Cards */
.transfers-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.transfers-type-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 20px;
}

.transfers-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.transfers-type-card.selected {
    border: 2px solid #36b1dc;
    box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2);
}

.transfers-type-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.transfers-type-icon i {
    color: white;
    font-size: 24px;
}

.transfers-type-title {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 10px;
}

.transfers-type-description {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
}

/* Form Controls */
.transfers-form-group {
    margin-bottom: 20px;
}

.transfers-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

.transfers-form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 10px;
    font-size: 16px;
    transition: border-color 0.3s;
    background-color: rgba(255, 255, 255, 0.8);
}

.transfers-form-control:focus {
    border-color: #36b1dc;
    outline: none;
    box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.1);
}

.transfers-form-control.is-invalid {
    border-color: #dc3545;
}

.transfers-invalid-feedback {
    display: none;
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
}

.transfers-form-control.is-invalid + .transfers-invalid-feedback {
    display: block;
}

/* Buttons */
.transfers-wizard-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.transfers-btn {
    padding: 12px 25px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.transfers-btn-primary {
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3);
}

.transfers-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(54, 177, 220, 0.4);
}

.transfers-btn-secondary {
    background-color: #e9ecef;
    color: #495057;
}

.transfers-btn-secondary:hover {
    background-color: #dee2e6;
}

.transfers-btn-success {
    background-color: #28a745;
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.transfers-btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.transfers-btn-danger {
    background-color: #dc3545;
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.transfers-btn-danger:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

.transfers-btn i {
    margin-right: 8px;
}

/* Product Cards */
.transfers-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.transfers-product-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    height: 100%;
}

.transfers-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.transfers-product-card.selected {
    border: 2px solid #28a745;
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.2);
}

/* Responsive Styles */
@media (max-width: 767px) {
    .transfers-wizard-container {
        padding: 20px;
    }
    
    .transfers-wizard-step-label {
        display: none;
    }
    
    .transfers-type-grid,
    .transfers-products-grid {
        grid-template-columns: 1fr;
    }
    
    .transfers-wizard-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .transfers-btn {
        width: 100%;
    }
}
