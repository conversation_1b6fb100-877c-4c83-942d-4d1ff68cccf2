<?php
/**
 * Travel Widgets Shortcodes
 * Provides shortcodes for displaying travel booking widgets
 */

if (!defined('ABSPATH')) {
    exit;
}

// Dashboard integration is now handled directly in views/flights.php
// Keeping only individual widget shortcode for external use



/**
 * Simple Flight Widget Shortcode
 */
function lci_flight_widget_shortcode($atts) {
    $atts = shortcode_atts([
        'origin' => 'Your City',
        'destination' => 'Brasov, Romania'
    ], $atts);

    return LCI_Travelpayouts::get_flight_widget($atts['origin']);
}
add_shortcode('lci_flight_widget', 'lci_flight_widget_shortcode');


