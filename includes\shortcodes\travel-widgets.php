<?php
/**
 * Travel Widgets Shortcodes
 * Provides shortcodes for displaying travel booking widgets
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Travel Dashboard Shortcode
 * Displays travel booking widgets for LCI participants
 */
function lci_travel_dashboard_shortcode($atts) {
    $atts = shortcode_atts([
        'show_flights' => 'true',
        'show_hotels' => 'true',
        'show_cars' => 'false',
        'show_insurance' => 'false',
        'title' => 'Plan Your Trip to Romania'
    ], $atts);
    
    // Check if Travelpayouts is configured
    $api_token = get_option('lci_travelpayouts_api_token', '');
    $marker = get_option('lci_travelpayouts_marker', '');
    
    if (empty($api_token) || empty($marker)) {
        return ''; // Don't show widgets if not configured
    }
    
    // Get user's country for better flight suggestions
    $user_country = '';
    $user_city = '';
    
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        
        // Try to get country from participant data
        global $wpdb;
        $participant = $wpdb->get_row($wpdb->prepare(
            "SELECT country FROM {$wpdb->prefix}lci2025_participants WHERE user_id = %d LIMIT 1",
            $user_id
        ));
        
        if ($participant && $participant->country) {
            $user_country = $participant->country;
            $user_city = $user_country; // Fallback to country name
        }
    }
    
    ob_start();
    ?>
    <div class="lci-travel-dashboard">
        <div class="travel-header">
            <h2 class="travel-title"><?php echo esc_html($atts['title']); ?></h2>
            <p class="travel-subtitle">
                Book your travel to Brasov, Romania for the LCI International Convention 2025
                <br><small>August 16-25, 2025 • Earn us commission when you book through these widgets</small>
            </p>
        </div>
        
        <div class="travel-widgets-grid">
            <?php if ($atts['show_flights'] === 'true'): ?>
            <div class="travel-widget-container">
                <?php echo LCI_Travelpayouts::get_flight_widget($user_city ?: 'Your City'); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($atts['show_hotels'] === 'true'): ?>
            <div class="travel-widget-container">
                <?php echo LCI_Travelpayouts::get_hotel_widget(); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($atts['show_cars'] === 'true'): ?>
            <div class="travel-widget-container">
                <?php echo lci_get_car_rental_widget(); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($atts['show_insurance'] === 'true'): ?>
            <div class="travel-widget-container">
                <?php echo lci_get_travel_insurance_widget(); ?>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Travel Tips for Romania -->
        <div class="travel-tips">
            <h3>Travel Tips for Romania</h3>
            <div class="tips-grid">
                <div class="tip-item">
                    <h4>🛂 Visa Requirements</h4>
                    <p>EU citizens need only ID. Others may need visa - check requirements for your country.</p>
                </div>
                <div class="tip-item">
                    <h4>💱 Currency</h4>
                    <p>Romanian Leu (RON). Credit cards widely accepted. €1 ≈ 5 RON</p>
                </div>
                <div class="tip-item">
                    <h4>🚗 Transportation</h4>
                    <p>Rent a car for flexibility or use buses/trains. Brasov is well-connected.</p>
                </div>
                <div class="tip-item">
                    <h4>🌡️ Weather</h4>
                    <p>August: 15-25°C (59-77°F). Pack light layers and comfortable shoes.</p>
                </div>
            </div>
        </div>
        
        <!-- Recommended Itinerary -->
        <div class="travel-itinerary">
            <h3>Suggested Itinerary</h3>
            <div class="itinerary-timeline">
                <div class="itinerary-item">
                    <div class="date">Aug 15</div>
                    <div class="content">
                        <h4>Arrival Day</h4>
                        <p>Arrive in Bucharest or Brasov. Check into hotel, explore city center.</p>
                    </div>
                </div>
                <div class="itinerary-item">
                    <div class="date">Aug 16-18</div>
                    <div class="content">
                        <h4>Pre-Convention</h4>
                        <p>Optional tours, networking events, Brasov sightseeing.</p>
                    </div>
                </div>
                <div class="itinerary-item">
                    <div class="date">Aug 19-23</div>
                    <div class="content">
                        <h4>Main Convention</h4>
                        <p>LCI International Convention sessions and activities.</p>
                    </div>
                </div>
                <div class="itinerary-item">
                    <div class="date">Aug 24-25</div>
                    <div class="content">
                        <h4>Post-Convention</h4>
                        <p>Optional tours, additional sightseeing, departure.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    .lci-travel-dashboard {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }
    
    .travel-header {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .travel-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1rem;
    }
    
    .travel-subtitle {
        font-size: 1.125rem;
        color: #6b7280;
        line-height: 1.6;
    }
    
    .travel-widgets-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .travel-widget-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .travel-tips {
        background: #f8fafc;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 3rem;
    }
    
    .travel-tips h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    .tips-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .tip-item {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    
    .tip-item h4 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }
    
    .tip-item p {
        color: #6b7280;
        line-height: 1.5;
    }
    
    .travel-itinerary {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .travel-itinerary h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .itinerary-timeline {
        position: relative;
        padding-left: 2rem;
    }
    
    .itinerary-timeline::before {
        content: '';
        position: absolute;
        left: 0.75rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e5e7eb;
    }
    
    .itinerary-item {
        position: relative;
        display: flex;
        margin-bottom: 2rem;
    }
    
    .itinerary-item::before {
        content: '';
        position: absolute;
        left: -2.25rem;
        top: 0.5rem;
        width: 12px;
        height: 12px;
        background: #3b82f6;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #3b82f6;
    }
    
    .itinerary-item .date {
        font-weight: 600;
        color: #3b82f6;
        min-width: 80px;
        margin-right: 1rem;
    }
    
    .itinerary-item .content h4 {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }
    
    .itinerary-item .content p {
        color: #6b7280;
        line-height: 1.5;
    }
    
    @media (max-width: 768px) {
        .travel-widgets-grid {
            grid-template-columns: 1fr;
        }
        
        .tips-grid {
            grid-template-columns: 1fr;
        }
        
        .travel-title {
            font-size: 2rem;
        }
        
        .itinerary-item {
            flex-direction: column;
        }
        
        .itinerary-item .date {
            margin-bottom: 0.5rem;
        }
    }
    </style>
    <?php
    
    return ob_get_clean();
}
add_shortcode('lci_travel_dashboard', 'lci_travel_dashboard_shortcode');

/**
 * Car Rental Widget
 */
function lci_get_car_rental_widget() {
    $marker = get_option('lci_travelpayouts_marker', '');
    
    if (empty($marker)) {
        return '';
    }
    
    ob_start();
    ?>
    <div class="lci-travel-widget car-widget">
        <h3 class="widget-title">Rent a Car in Romania</h3>
        <div class="car-rental-info">
            <p>Explore Romania at your own pace with a rental car.</p>
            <div class="car-features">
                <div class="feature">✓ Free cancellation</div>
                <div class="feature">✓ No hidden fees</div>
                <div class="feature">✓ 24/7 support</div>
            </div>
            <a href="<?php echo esc_url(LCI_Travelpayouts::get_affiliate_url('https://www.rentalcars.com/', [
                'affiliateCode' => $marker,
                'prefilledPickup' => 'Brasov',
                'prefilledDropoff' => 'Brasov',
                'pickupDate' => '2025-08-16',
                'dropoffDate' => '2025-08-25'
            ])); ?>" target="_blank" class="btn btn-primary" rel="nofollow">
                Find Rental Cars
            </a>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Travel Insurance Widget
 */
function lci_get_travel_insurance_widget() {
    $marker = get_option('lci_travelpayouts_marker', '');
    
    if (empty($marker)) {
        return '';
    }
    
    ob_start();
    ?>
    <div class="lci-travel-widget insurance-widget">
        <h3 class="widget-title">Travel Insurance</h3>
        <div class="insurance-info">
            <p>Protect your trip with comprehensive travel insurance.</p>
            <div class="insurance-benefits">
                <div class="benefit">🏥 Medical coverage</div>
                <div class="benefit">✈️ Trip cancellation</div>
                <div class="benefit">🧳 Baggage protection</div>
                <div class="benefit">⏰ 24/7 assistance</div>
            </div>
            <a href="<?php echo esc_url(LCI_Travelpayouts::get_affiliate_url('https://www.worldnomads.com/', [
                'affiliate' => $marker,
                'destination' => 'Romania',
                'startDate' => '2025-08-16',
                'endDate' => '2025-08-25'
            ])); ?>" target="_blank" class="btn btn-primary" rel="nofollow">
                Get Quote
            </a>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Simple Flight Widget Shortcode
 */
function lci_flight_widget_shortcode($atts) {
    $atts = shortcode_atts([
        'origin' => 'Your City',
        'destination' => 'Brasov, Romania'
    ], $atts);
    
    return LCI_Travelpayouts::get_flight_widget($atts['origin']);
}
add_shortcode('lci_flight_widget', 'lci_flight_widget_shortcode');

/**
 * Simple Hotel Widget Shortcode
 */
function lci_hotel_widget_shortcode($atts) {
    return LCI_Travelpayouts::get_hotel_widget();
}
add_shortcode('lci_hotel_widget', 'lci_hotel_widget_shortcode');
