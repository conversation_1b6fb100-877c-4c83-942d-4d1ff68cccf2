<div class="wrap lci-admin-wrap">
    <h1 class="text-3xl font-bold mb-6">Debug Logs</h1>

    <div class="bg-white rounded-xl shadow-neumorph p-6 mb-6">
        <h2 class="text-xl font-medium text-gray-800 mb-4">Sync Summary</h2>

        <div class="mb-4">
            <p class="text-gray-600">
                This log shows a summary of all sync operations, including success and error counts.
            </p>
        </div>

        <?php
        $summary_log_file = LCI2025_PATH . 'logs/sync_summary.log';
        if (file_exists($summary_log_file)) {
            $summary_logs = file_get_contents($summary_log_file);
            if (!empty($summary_logs)) {
                echo '<div class="overflow-x-auto">';
                echo '<pre class="bg-gray-50 p-4 rounded-lg text-gray-800 text-sm max-h-96 overflow-y-auto">' . esc_html($summary_logs) . '</pre>';
                echo '</div>';

                echo '<div class="mt-4">';
                echo '<a href="' . admin_url('admin.php?page=lci-debug-logs&action=clear-summary-logs&nonce=' . wp_create_nonce('lci_clear_logs')) . '" class="btn btn-secondary" onclick="return confirm(\'Are you sure you want to clear the summary logs?\');">';
                echo 'Clear Summary Logs';
                echo '</a>';
                echo '</div>';
            } else {
                echo '<div class="bg-gray-50 p-4 rounded-lg text-gray-800">';
                echo 'No summary logs found. Run a sync operation to generate logs.';
                echo '</div>';
            }
        } else {
            echo '<div class="bg-blue-50 p-4 rounded-lg text-blue-800">';
            echo 'No summary log file exists yet. Run a sync operation to generate logs.';
            echo '</div>';
        }
        ?>
    </div>

    <div class="bg-white rounded-xl shadow-neumorph p-6 mb-6">
        <h2 class="text-xl font-medium text-gray-800 mb-4">Sync Error Logs</h2>

        <div class="mb-4">
            <p class="text-gray-600">
                These logs show errors that occurred during the participant synchronization process.
                Use this information to troubleshoot sync issues.
            </p>
        </div>

        <?php
        $error_log_file = LCI2025_PATH . 'logs/sync_errors.log';
        if (file_exists($error_log_file)) {
            $error_logs = file_get_contents($error_log_file);
            if (!empty($error_logs)) {
                echo '<div class="overflow-x-auto">';
                echo '<pre class="bg-red-50 p-4 rounded-lg text-red-800 text-sm max-h-96 overflow-y-auto">' . esc_html($error_logs) . '</pre>';
                echo '</div>';

                echo '<div class="mt-4">';
                echo '<a href="' . admin_url('admin.php?page=lci-debug-logs&action=clear-error-logs&nonce=' . wp_create_nonce('lci_clear_logs')) . '" class="btn btn-danger" onclick="return confirm(\'Are you sure you want to clear the error logs?\');">';
                echo 'Clear Error Logs';
                echo '</a>';
                echo '</div>';
            } else {
                echo '<div class="bg-green-50 p-4 rounded-lg text-green-800">';
                echo 'No error logs found. This is good!';
                echo '</div>';
            }
        } else {
            echo '<div class="bg-blue-50 p-4 rounded-lg text-blue-800">';
            echo 'No error log file exists yet. It will be created when errors occur during sync.';
            echo '</div>';
        }
        ?>
    </div>

    <div class="bg-white rounded-xl shadow-neumorph p-6">
        <h2 class="text-xl font-medium text-gray-800 mb-4">Debug Logs</h2>

        <div class="mb-4">
            <p class="text-gray-600">
                These logs show detailed information about the sync process when debug mode is enabled.
                Enable debug mode in the settings to see more information here.
            </p>
        </div>

        <?php
        $debug_log_file = LCI2025_PATH . 'logs/sync_debug.log';
        if (file_exists($debug_log_file)) {
            $debug_logs = file_get_contents($debug_log_file);
            if (!empty($debug_logs)) {
                echo '<div class="overflow-x-auto">';
                echo '<pre class="bg-blue-50 p-4 rounded-lg text-blue-800 text-sm max-h-96 overflow-y-auto">' . esc_html($debug_logs) . '</pre>';
                echo '</div>';

                echo '<div class="mt-4">';
                echo '<a href="' . admin_url('admin.php?page=lci-debug-logs&action=clear-debug-logs&nonce=' . wp_create_nonce('lci_clear_logs')) . '" class="btn btn-secondary" onclick="return confirm(\'Are you sure you want to clear the debug logs?\');">';
                echo 'Clear Debug Logs';
                echo '</a>';
                echo '</div>';
            } else {
                echo '<div class="bg-gray-50 p-4 rounded-lg text-gray-800">';
                echo 'No debug logs found. Enable debug mode in settings to see detailed logs.';
                echo '</div>';
            }
        } else {
            echo '<div class="bg-blue-50 p-4 rounded-lg text-blue-800">';
            echo 'No debug log file exists yet. Enable debug mode in settings and perform a sync to generate logs.';
            echo '</div>';
        }
        ?>

        <div class="mt-6">
            <h3 class="text-lg font-medium text-gray-800 mb-2">Debug Mode</h3>

            <form method="post" action="options.php">
                <?php settings_fields('lci2025_settings'); ?>

                <div class="flex items-center space-x-4">
                    <label class="inline-flex items-center">
                        <input type="checkbox" name="lci2025_debug_mode" value="1" <?php checked(get_option('lci2025_debug_mode', '0'), '1'); ?> class="rounded text-primary focus:ring-primary">
                        <span class="ml-2">Enable debug logging</span>
                    </label>

                    <?php submit_button('Save Setting', 'btn btn-primary', 'submit', false); ?>
                </div>
            </form>
        </div>
    </div>
</div>
