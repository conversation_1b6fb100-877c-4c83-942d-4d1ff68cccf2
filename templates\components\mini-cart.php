<?php
/**
 * Mini Cart Template
 */
?>
<!-- Ensure lci_ajax_object is defined -->
<script>
    if (typeof lci_ajax_object === 'undefined') {
        window.lci_ajax_object = {
            ajax_url: '<?php echo esc_url(admin_url('admin-ajax.php')); ?>',
            nonce: '<?php echo wp_create_nonce('lci_mini_cart_nonce'); ?>'
        };
    }

    // No special handling - using the standard mini-cart

    // Ensure Alpine.js is loaded
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof Alpine === 'undefined') {
            // Console warning removed
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js';
            script.defer = true;
            document.head.appendChild(script);

            // Add a listener to initialize Alpine.js components when loaded
            script.onload = function() {
                if (window.Alpine) {
                    // Initialize mini-cart component
                    const miniCartContainer = document.getElementById('mini-cart-container');
                    if (miniCartContainer && !miniCartContainer.__x) {
                        try {
                            window.Alpine.initTree(miniCartContainer);
                            // Console log removed
                        } catch (error) {
                            // Console error removed
                        }
                    }
                }
            };
        }
    });
</script>

<div x-data="{
    open: false,
    _items: [],
    get items() {
        // Deduplicate items by key
        const uniqueItems = [];
        const keys = new Set();

        // Only add items with unique keys
        (this._items || []).forEach(item => {
            if (item && item.key && !keys.has(item.key)) {
                keys.add(item.key);
                uniqueItems.push(item);
            }
        });

        return uniqueItems;
    },
    set items(newItems) {
        // Deduplicate items by key
        const uniqueItems = [];
        const keys = new Set();

        // Only add items with unique keys
        (newItems || []).forEach(item => {
            if (item && item.key && !keys.has(item.key)) {
                keys.add(item.key);
                uniqueItems.push(item);
            }
        });

        this._items = uniqueItems;
    },
    loading: false,
    total: '<?php echo esc_attr($cart_total); ?>',
    count: <?php echo intval($cart_count); ?>,
    scrollPosition: 0,
    autoOpenCart: true, // Default to true to prevent auto-opening
    isInitialized: false, // Flag to track initialization

    init() {
        // IMPORTANT: Set initialization flag to false initially
        this.isInitialized = false;

        // Set autoOpenCart to true to prevent auto-opening
        this.autoOpenCart = true;

        // Fetch items without opening the cart
        this.fetchItems();

        // Create a global function to open the cart
        window.openMiniCart = () => {
            // When explicitly called, we want to open the cart
            this.autoOpenCart = false;
            // Set initialization flag to true
            this.isInitialized = true;
            this.openCart();
        };

        // Listen for window events
        window.addEventListener('open-mini-cart', () => {
            // When explicitly triggered, we want to open the cart
            this.autoOpenCart = false;
            // Set initialization flag to true
            this.isInitialized = true;
            this.openCart();
        });

        // Notify that mini cart is ready for initialization
        document.dispatchEvent(new CustomEvent('mini-cart:ready'));

        // Add a small delay to ensure Alpine.js is fully initialized
        setTimeout(() => {
            // Set initialization flag to true after a delay
            this.isInitialized = true;
            // Dispatch event to notify that mini cart is fully initialized with Alpine.js
            document.dispatchEvent(new CustomEvent('mini-cart:alpine-ready'));
        }, 500);

        // Listen for cart updated events
        window.addEventListener('lci:cartUpdated', (event) => {
            // Check if the event has cart data
            if (event.detail && event.detail.cart_count !== undefined && event.detail.cart_total) {
                // Process cart update data
                // Update directly with the provided data
                this.count = event.detail.cart_count;
                this.total = event.detail.cart_total;

                // If we have items data, update that too
                if (event.detail.items) {
                    this.items = event.detail.items;
                    this.loading = false;
                } else {
                    // Otherwise fetch the items
                    this.fetchItems();
                }
            } else {
                // No data provided, fetch everything
                this.fetchItems();
            }
        });
    },

    openCart() {
        // IMPORTANT: Never auto-open on page load
        // Check if this is a direct user action (not page load)
        if (this.isInitialized && !this.autoOpenCart) {
            this.open = true;
            this.fetchItems();

            // Add modal-open class to body
            document.body.classList.add('modal-open');

            // Store the current scroll position
            this.scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

            // Add our own scroll management
            document.body.style.overflow = 'hidden';
            document.body.style.position = 'fixed';
            document.body.style.top = `-${this.scrollPosition}px`;
            document.body.style.width = '100%';
        } else {
            // Prevent auto-opening
            this.autoOpenCart = false;
        }
    },

    closeCart() {
        this.open = false;

        // Remove modal-open class from body
        document.body.classList.remove('modal-open');

        // Restore scroll position
        document.body.style.removeProperty('overflow');
        document.body.style.removeProperty('position');
        document.body.style.removeProperty('top');
        document.body.style.removeProperty('width');
        window.scrollTo(0, this.scrollPosition);
    },

    fetchItems() {
        // Prevent duplicate fetches
        if (this.loading) {
            // Console log removed
            return;
        }

        this.loading = true;
        // Fetch cart items

        fetch(lci_ajax_object.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=lci_get_mini_cart_items&nonce=' + lci_ajax_object.nonce
        })
        .then(response => response.json())
        .then(data => {
            // Process cart items response
            if (data.success) {
                // Clear existing items first to prevent duplicates
                this.items = [];

                // Set timeout to ensure DOM updates before adding new items
                setTimeout(() => {
                    // EMERGENCY FIX: Group items by product ID only
                    const uniqueItems = [];
                    const productIds = new Set();

                    // Only add items with unique product IDs
                    (data.data.items || []).forEach(item => {
                        // Use only product ID for grouping
                        const productId = item.product_id;

                        // Skip if we've already processed this product ID
                        if (!productIds.has(productId)) {
                            productIds.add(productId);
                            uniqueItems.push(item);
                        }
                    });

                    // Log how many items were removed
                    // Console log removed

                    // Add a message to the console for debugging
                    // Console log removed
                    // Console log removed
                    // Console log removed

                    this.items = uniqueItems;
                    this.total = data.data.total || '0.00 €';
                    this.count = uniqueItems.length;

                    // Cart data updated

                    // Check if cart is empty or total is 0, hide fundraising message
                    const fundraisingMessage = document.getElementById('lci-fundraising-message');
                    if (fundraisingMessage) {
                        if (this.count === 0 || this.total === '0.00 €') {
                            // Hide fundraising message for empty cart
                            fundraisingMessage.style.display = 'none';
                        } else {
                            fundraisingMessage.style.display = '';
                        }
                    }

                    // Update mini cart button
                    const miniCartButton = document.getElementById('mini-cart-button');
                    if (miniCartButton) {
                        const countBadge = miniCartButton.querySelector('.badge');
                        const totalText = miniCartButton.querySelector('.mini-cart-button-text');

                        if (countBadge) {
                            countBadge.textContent = this.count;
                            countBadge.style.display = this.count > 0 ? '' : 'none';
                        }

                        if (totalText) {
                            totalText.innerHTML = this.total;
                        }
                    }

                    this.loading = false;
                }, 50);
            } else {
                this.items = [];
                this.loading = false;
            }
        })
        .catch(error => {
            // Console error removed
            this.items = [];
            this.loading = false;
        });
    },

    removeItem(key) {
        // Find the item to remove
        const itemToRemove = this.items.find(item => item.key === key);
        if (!itemToRemove) return;

        // Remove the item from the array
        const itemIndex = this.items.findIndex(item => item.key === key);
        if (itemIndex !== -1) {
            this.items.splice(itemIndex, 1);
        }

        // Update count
        this.count = this.items.reduce((total, item) => total + parseInt(item.quantity), 0);

        // If all items are removed, set total to 0, otherwise calculate the new total
        if (this.items.length === 0) {
            this.total = '0.00 €';

            // Hide fundraising message when cart is empty
            const fundraisingMessage = document.getElementById('lci-fundraising-message');
            if (fundraisingMessage) {
                // Hide fundraising message for empty cart
                fundraisingMessage.style.display = 'none';
            }
        } else if (window.lciCartCalculations && typeof window.lciCartCalculations.calculateNewTotal === 'function') {
            // Use the helper function to calculate the new total
            const newTotal = window.lciCartCalculations.calculateNewTotal(this.total, itemToRemove.total);
            if (newTotal) {
                this.total = newTotal;
                // Total updated after item removal

                // Check if the new total is 0, hide fundraising message
                if (this.total === '0.00 €') {
                    const fundraisingMessage = document.getElementById('lci-fundraising-message');
                    if (fundraisingMessage) {
                        // Hide fundraising message for zero total
                        fundraisingMessage.style.display = 'none';
                    }
                }
            }
        }

        // Update mini cart button
        const miniCartButton = document.getElementById('mini-cart-button');
        if (miniCartButton) {
            const countBadge = miniCartButton.querySelector('.badge');
            if (countBadge) {
                countBadge.textContent = this.count;
                countBadge.style.display = this.count > 0 ? '' : 'none';
            }

            const totalText = miniCartButton.querySelector('.mini-cart-button-text');
            if (totalText) {
                totalText.innerHTML = this.total;
            }
        }

        // Make the AJAX call to remove the item from the server
        fetch(lci_ajax_object.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=lci_remove_cart_item&nonce=' + lci_ajax_object.nonce + '&cart_item_key=' + key
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the total with the accurate value from the server
                this.total = data.cart_total || '0.00 €';

                // Update mini cart button total
                const miniCartButton = document.getElementById('mini-cart-button');
                if (miniCartButton) {
                    const totalText = miniCartButton.querySelector('.mini-cart-button-text');
                    if (totalText) {
                        totalText.innerHTML = this.total;
                    }
                }

                // Only fetch the latest cart data if we're not already loading
                if (!this.loading) {
                    this.fetchItems();
                }

                // Trigger events - use a debounced approach
                setTimeout(() => {
                    document.dispatchEvent(new CustomEvent('lci:cart-item-removed', {
                        detail: { key: key }
                    }));

                    // Use a flag to prevent multiple event triggers
                    if (!window.isUpdatingMiniCart) {
                        window.isUpdatingMiniCart = true;
                        jQuery(document.body).trigger('removed_from_cart');
                        jQuery(document.body).trigger('updated_cart_totals');

                        // Reset the flag after a delay
                        setTimeout(() => {
                            window.isUpdatingMiniCart = false;
                        }, 500);
                    }
                }, 100);
            } else {
                this.fetchItems();
            }
        })
        .catch(error => {
            // Error handling
            this.fetchItems();
        });
    }
}" x-cloak id="mini-cart-container" class="lci-mini-cart-container">
    <!-- Mini Cart Button -->
    <button
        id="mini-cart-button"
        @click="openCart"
        onclick="if(typeof window.openMiniCart === 'function') { window.openMiniCart(); return false; }"
        class="btn btn-primary d-flex align-items-center"
        style="color: #fff !important;"
    >
        <i class="fas fa-shopping-bag me-2" style="color: #fff !important;"></i>
        <span class="mini-cart-button-text" x-html="total"></span>
        <template x-if="count > 0">
            <span class="badge rounded-pill ms-2" style="background-color: #fab33a;" x-text="count"></span>
        </template>
    </button>

    <!-- Mini Cart Modal -->
    <div
        x-show="open && isInitialized"
        x-transition:enter="fade-in"
        x-transition:leave="fade-out"
        class="lci-modal-backdrop"
        @click.self="closeCart"
        style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center; overflow-y: auto; padding: 20px 0;"
    >
        <div
            class="lci-modal lci-mini-cart-modal"
            x-transition:enter="slide-in"
            x-transition:leave="slide-out"
            style="background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.12); max-width: 700px; width: 95%;"
        >
            <div class="lci-modal-header" style="background-color: white; box-shadow: 0 4px 6px rgba(0,0,0,0.05); position: relative;">
                <h2 class="lci-modal-title" style="color: #36b1dc; font-weight: 600; margin: 0 auto; padding: 1rem 0;">
                    <i class="fas fa-shopping-bag me-2"></i> LCI AGM 2025
                </h2>
                <button @click="closeCart" class="lci-modal-close" style="position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; font-size: 1.25rem; color: #6c757d; transition: all 0.2s ease;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="lci-modal-body p-0">
                <div class="mini-cart-items-container">
                    <!-- Loading state -->
                    <template x-if="loading">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading your items...</p>
                        </div>
                    </template>

                    <!-- Empty cart -->
                    <template x-if="!loading && items.length === 0">
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-bag text-muted" style="font-size: 3rem;"></i>
                            <p class="mt-3">Your goodies bag is empty</p>
                        </div>
                    </template>

                    <!-- Cart items -->
                    <template x-if="!loading && items.length > 0">
                        <div class="cart-items-list" style="max-height: 300px; overflow-y: auto;">
                            <!-- EMERGENCY FIX: Use only product ID as key -->
                            <template x-for="(item, index) in items" :key="item.product_id">
                                <div class="mini-cart-item d-flex align-items-center p-3 border-bottom">
                                    <!-- Product Image -->
                                    <div class="mini-cart-item-image me-2">
                                        <img :src="item.image" :alt="item.name" class="img-fluid rounded" style="width: 50px; height: 50px; object-fit: contain;">
                                    </div>

                                    <!-- Quantity Badge -->
                                    <div class="mini-cart-item-quantity-container me-2">
                                        <span class="mini-cart-item-quantity badge bg-light text-dark" x-text="item.quantity + ' ×'"></span>
                                    </div>

                                    <!-- Product Details -->
                                    <div class="mini-cart-item-details flex-grow-1 d-flex align-items-center justify-content-between">
                                        <div class="mini-cart-item-name-container">
                                            <span class="mini-cart-item-name" x-text="item.name"></span>
                                        </div>
                                        <div class="mini-cart-item-price-container">
                                            <span class="mini-cart-item-price text-primary fw-bold" x-html="item.price"></span>
                                        </div>
                                    </div>

                                    <!-- Remove Button -->
                                    <div class="mini-cart-item-remove ms-2">
                                        <a href="#" class="remove-cart-item" @click.prevent="removeItem(item.key)" style="color: #36b1dc; display: inline-flex; align-items: center; justify-content: center; width: 24px; height: 24px; border-radius: 50%; transition: all 0.2s ease;">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Fundraising Message -->
            <?php
            // Only display the fundraising message if there are category 22 products in the cart
            // The PHP function already has the necessary checks
            lci_display_fundraising_message();
            ?>

            <div class="lci-modal-footer">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div class="mini-cart-total-container">
                        Total: <span class="mini-cart-modal-total" x-html="total"></span>
                    </div>
                    <div>
                        <button @click="closeCart" class="lci-btn lci-btn-secondary me-2">Continue Shopping</button>
                        <a href="<?php echo esc_url(wc_get_checkout_url()); ?>" class="lci-btn lci-btn-primary">
                            <i class="fas fa-credit-card me-1"></i> Checkout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
