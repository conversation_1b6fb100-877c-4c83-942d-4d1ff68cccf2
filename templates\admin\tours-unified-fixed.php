<?php
/**
 * Admin template for Tours management (unified approach)
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Get tours table status
$table_status = LCI_Tours_Unified::check_tours_table();
?>

<!-- Add Tailwind CSS via CDN -->
<script src="https://cdn.tailwindcss.com"></script>
<script>
  tailwind.config = {
    theme: {
      extend: {
        colors: {
          primary: '#0073aa', // WordPress primary blue
          secondary: '#46b450', // WordPress success green
          danger: '#dc3232', // WordPress error red
          warning: '#ffb900', // WordPress warning yellow
        }
      }
    }
  }
</script>

<!-- Fix for Alpine.js module error -->
<script>
  // Create a global window.module if it doesn't exist to prevent module.cjs.js error
  if (typeof window.module === 'undefined') {
    window.module = {};
  }
</script>

<!-- Initialize the global data object when the page loads -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize the global data object
    console.log('Initializing global data object');
    
    // Fetch countries
    fetchCountriesDirectly();
    
    // Fetch participants
    fetchParticipantsDirectly();
    
    // Set up event listeners for the edit form
    document.getElementById('edit-form').addEventListener('submit', function(e) {
      e.preventDefault();
      saveParticipant();
    });
  });
  
  function fetchCountriesDirectly() {
    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();
    const url = `${lciAdmin.ajaxUrl}?action=lci_get_tours_countries&nonce=${lciAdmin.nonce}&_=${timestamp}`;
    
    fetch(url)
      .then(response => response.json())
      .then(function(data) {
        if (data.success && Array.isArray(data.data.countries)) {
          // Get the country select element
          const countrySelect = document.getElementById('country-filter-select');
          
          // Clear existing options (except the first one)
          while (countrySelect.options.length > 1) {
            countrySelect.remove(1);
          }
          
          // Add new options
          data.data.countries.forEach(function(country) {
            const option = document.createElement('option');
            option.value = country;
            option.textContent = country;
            countrySelect.appendChild(option);
          });
        } else {
          console.error('Error fetching countries:', data);
        }
      })
      .catch(function(error) {
        console.error('Error fetching countries:', error);
      });
  }
  
  function saveParticipant() {
    // Get form data
    const form = document.getElementById('edit-form');
    const formData = new FormData(form);
    
    // Add action and nonce
    formData.append('action', 'lci_update_tour_participant');
    formData.append('nonce', lciAdmin.nonce);
    
    // Convert checkboxes to 0/1
    formData.set('main_pretour', document.getElementById('edit-main-pretour').checked ? 1 : 0);
    formData.set('legends_wildlife', document.getElementById('edit-legends-wildlife').checked ? 1 : 0);
    formData.set('royal_elegance', document.getElementById('edit-royal-elegance').checked ? 1 : 0);
    formData.set('brasov_highlights', document.getElementById('edit-brasov-highlights').checked ? 1 : 0);
    
    // Show loading state
    const saveButton = document.querySelector('#edit-form button[type="submit"]');
    const originalText = saveButton.textContent;
    saveButton.textContent = 'Saving...';
    saveButton.disabled = true;
    
    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(function(data) {
      if (data.success) {
        // Success case
        showToast('Participant updated successfully', 'success');
        
        // Hide the modal
        document.getElementById('edit-modal').style.display = 'none';
        
        // Refresh the participants list
        refreshParticipants();
      } else {
        // Error case
        const message = data.data?.message || 'Error updating participant';
        showToast('Error: ' + message, 'error');
      }
    })
    .catch(function(error) {
      // Handle network or other errors
      showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
      // Reset button state
      saveButton.textContent = originalText;
      saveButton.disabled = false;
    });
  }
</script>

<style>
    /* Hide elements with x-cloak until Alpine.js is loaded */
    [x-cloak] { display: none !important; }
</style>

<div class="wrap lci-admin-wrap">
    <h1 class="wp-heading-inline"><?php _e('Tours Participants', 'lci-2025-dashboard'); ?></h1>

    <div id="tours-manager">
        <!-- Sync Tool -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-medium text-gray-800 mb-4">Sync Tours Participants</h2>

            <div class="mb-4">
                <p class="mb-2">This tool will sync tour participants from WooCommerce orders with the following product IDs:</p>
                <ul class="list-disc pl-5 mb-4">
                    <li>Main Pretour (ID: 743)</li>
                    <li>Legends and Wildlife Escape (ID: 744)</li>
                    <li>Royal Elegance & Sparkling Delights (ID: 745)</li>
                    <li>Brasov Highlights (ID: 746)</li>
                </ul>
                <p class="mb-4">Note: One user can have multiple tours.</p>
            </div>

            <div class="flex items-center">
                <button
                    id="sync-button"
                    onclick="syncTours()"
                    class="px-4 py-2 bg-primary text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="sync-text">Sync Tours Participants</span>
                    <div id="sync-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Syncing...</span>
                    </div>
                </button>

                <div class="ml-4" id="sync-message-container">
                    <div id="sync-success-message" class="text-secondary" style="display: none;">
                        <span id="sync-success-text"></span>
                    </div>
                    <div id="sync-error-message" class="text-danger" style="display: none;">
                        <span id="sync-error-text"></span>
                    </div>
                </div>
            </div>

            <div class="mt-4 flex space-x-4">
                <button
                    id="clear-table-button"
                    onclick="clearTable()"
                    class="px-4 py-2 bg-warning text-white rounded hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-warning focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="clear-table-text">Clear Tours Table</span>
                    <div id="clear-table-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Clearing...</span>
                    </div>
                </button>

                <button
                    id="recreate-table-button"
                    onclick="recreateTable()"
                    class="px-4 py-2 bg-danger text-white rounded hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-danger focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="recreate-table-text">Recreate Tours Table</span>
                    <div id="recreate-table-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Recreating...</span>
                    </div>
                </button>
            </div>
        </div>
