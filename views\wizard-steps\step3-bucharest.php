<?php
// COMPLETELY REWRITTEN STEP 3 FOR BUCHAREST
// This file shows the Bucharest accommodation product (ID 2151)

// No debug information in production

// Get the actual Bucharest accommodation product from WooCommerce
$product_id = 2151; // Bucharest accommodation product ID
$wc_product = wc_get_product($product_id);

if ($wc_product) {
    // Get product image
    $image_id = $wc_product->get_image_id();
    $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'full') : '';

    // Get hotel stars from product meta
    $hotel_stars = get_post_meta($product_id, '_hotel_stars', true);
    $hotel_stars = !empty($hotel_stars) ? intval($hotel_stars) : 4; // Default to 4 if not set

    // Get hotel website from product meta
    $hotel_website = get_post_meta($product_id, '_hotel_website', true);

    // Get features from product meta
    $features_meta = get_post_meta($product_id, '_hotel_features', true);
    $features = !empty($features_meta) ? explode(',', $features_meta) : ['Gala Dinner Venue', 'Breakfast included'];

    // Get room options from product variations
    $rooms = [];
    if ($wc_product->is_type('variable')) {
        $variations = $wc_product->get_available_variations();
        foreach ($variations as $variation) {
            $variation_obj = wc_get_product($variation['variation_id']);
            $rooms[] = [
                'variation_id' => $variation['variation_id'],
                'name' => $variation_obj->get_name(),
                'price' => $variation_obj->get_price(),
                'price_html' => $variation_obj->get_price_html()
            ];
        }
    } else {
        // If it's a simple product, create a single room option
        $rooms[] = [
            'variation_id' => 0,
            'name' => 'Standard Room',
            'price' => $wc_product->get_price(),
            'price_html' => $wc_product->get_price_html()
        ];
    }

    // Build the product array
    $product = [
        'id' => $product_id,
        'name' => $wc_product->get_name(),
        'description' => $wc_product->get_short_description(),
        'image' => $image_url,
        'hotel_stars' => $hotel_stars,
        'features' => $features,
        'hotel_website' => $hotel_website,
        'rooms' => $rooms
    ];
} else {
    // Fallback to default if product not found
    $product = [
        'id' => 2151,
        'name' => 'HOTEL KRONWELL',
        'description' => 'Luxury accommodation in Bucharest before the Main Pretour.',
        'image' => 'https://www.kronwell.com/wp-content/uploads/2022/03/Kronwell-Brasov-Hotel-Exterior-1.jpg',
        'hotel_stars' => 4,
        'features' => ['Gala Dinner Venue', 'Breakfast included'],
        'hotel_website' => 'https://www.kronwell.com/',
        'rooms' => [
            [
                'name' => 'Single room',
                'price' => '360',
                'price_html' => '<span class="amount">360€</span>'
            ],
            [
                'name' => 'Double room (sharing)',
                'price' => '210',
                'price_html' => '<span class="amount">210€</span>'
            ]
        ]
    ];
}

// Check if product or any of its variations is in cart
$in_cart = false;
$selected_variation_id = 0;

if (WC()->cart) {
    foreach (WC()->cart->get_cart() as $cart_item) {
        if ($cart_item['product_id'] == $product_id ||
            (isset($cart_item['variation_id']) && $cart_item['variation_id'] > 0 && $cart_item['product_id'] == $product_id)) {
            $in_cart = true;
            $selected_variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;
            break;
        }
    }
}

$product_found = true;
?>

<h3 class="text-center mb-4">Bucharest Accommodation Options</h3>
<div class="info-box">
    <i class="fas fa-info-circle me-2"></i>
    <span>Select your accommodation in Bucharest for <?php echo $wizard['bucharest']['nights']; ?> night<?php echo $wizard['bucharest']['nights'] > 1 ? 's' : ''; ?></span>
</div>

<?php if (!$product_found) : ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        No accommodation options found for Bucharest. Please try different criteria or contact us for assistance.
    </div>
<?php else : ?>
    <!-- Two-column card layout: image on left, info on right -->
    <div style="max-width: 800px; margin: 0 auto; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; background: white; display: flex; flex-direction: row;">
        <!-- Left Column - Hotel Image -->
        <div style="width: 40%; min-width: 300px;">
            <img src="<?php echo $product['image']; ?>" alt="<?php echo esc_attr($product['name']); ?>" style="width: 100%; height: 100%; object-fit: cover; display: block;">
        </div>

        <!-- Right Column - Hotel Info -->
        <div style="width: 60%; padding: 20px; display: flex; flex-direction: column;">
            <!-- Hotel Name -->
            <h3 style="color: #00b2e3; font-size: 22px; font-weight: bold; margin: 0 0 5px 0;"><?php echo $product['name']; ?></h3>

            <!-- Hotel Stars -->
            <div style="color: #00b2e3; margin-bottom: 15px;">
                <?php for ($i = 0; $i < $product['hotel_stars']; $i++) : ?>
                    <i class="fas fa-star"></i>
                <?php endfor; ?>
            </div>

            <!-- Accommodation Price Info -->
            <div style="background-color: #00b2e3; color: white; text-align: center; padding: 10px; font-weight: bold; font-size: 14px; margin-bottom: 15px;">
                ACCOMMODATION PRICE FOR <?php echo $wizard['bucharest']['nights']; ?> NIGHT<?php echo $wizard['bucharest']['nights'] > 1 ? 'S' : ''; ?>
            </div>

            <!-- Hotel Features -->
            <div style="margin-bottom: 15px;">
                <?php
                // Filter out 'Gala Dinner Venue' from features
                $filtered_features = [];
                if (is_array($product['features'])) {
                    foreach ($product['features'] as $feature) {
                        if ($feature !== 'Gala Dinner Venue') {
                            $filtered_features[] = $feature;
                        }
                    }
                }

                // Display filtered features
                foreach ($filtered_features as $feature) : ?>
                    <div style="margin-bottom: 8px;">
                        <i class="fas fa-check" style="color: #28a745; margin-right: 5px;"></i> <?php echo $feature; ?>
                    </div>
                <?php endforeach; ?>

                <?php if ($product['hotel_website']) : ?>
                    <div style="margin-top: 10px;">
                        <a href="<?php echo esc_url($product['hotel_website']); ?>" target="_blank" style="color: #00b2e3;">
                            <i class="fas fa-globe"></i> View Hotel website
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Room Options as Choices -->
            <div style="margin-bottom: 15px;">
                <div style="margin-bottom: 10px; font-weight: bold;">Choose Room Type:</div>
                <?php foreach ($product['rooms'] as $index => $room) : ?>
                    <div style="display: flex; align-items: center; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 8px; cursor: pointer;"
                         onclick="document.getElementById('room_variation_<?php echo $index; ?>').checked = true;">
                        <input type="radio" id="room_variation_<?php echo $index; ?>" name="room_variation_id"
                               value="<?php echo isset($room['variation_id']) ? $room['variation_id'] : ''; ?>"
                               style="margin-right: 10px;" required>
                        <label for="room_variation_<?php echo $index; ?>" style="cursor: pointer; flex-grow: 1;">
                            <?php echo $room['name']; ?> - <?php echo $room['price']; ?>€
                        </label>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Action Button with AJAX functionality -->
            <button type="button" id="add_bucharest_to_cart_btn"
                    data-product-id="<?php echo $product['id']; ?>"
                    style="background-color: #00b2e3; color: white; border: none; width: 100%; padding: 12px; font-weight: bold; cursor: pointer; text-transform: uppercase; margin-top: auto;">
                <i class="fas fa-check-circle me-2" style="color: white;"></i> <span style="color: white;">SELECT</span>
            </button>
            <input type="hidden" name="bucharest_product_id" value="<?php echo $product['id']; ?>">

            <!-- Add JavaScript for AJAX add to cart -->
            <script>
            document.addEventListener('DOMContentLoaded', function() {
                const addToCartBtn = document.getElementById('add_bucharest_to_cart_btn');

                if (addToCartBtn) {
                    addToCartBtn.addEventListener('click', function(e) {
                        e.preventDefault();

                        // Get selected variation
                        const selectedVariation = document.querySelector('input[name="room_variation_id"]:checked');
                        if (!selectedVariation) {
                            alert('Please select a room type');
                            return;
                        }

                        const productId = this.getAttribute('data-product-id');
                        const variationId = selectedVariation.value;
                        const originalText = this.innerHTML;

                        // Show loading state
                        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2" style="color: white;"></i> <span style="color: white;">Adding...</span>';
                        this.disabled = true;

                        // Define the AJAX URL and nonce if not already defined
                        if (typeof lci_ajax === 'undefined') {
                            window.lci_ajax = {
                                ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
                                nonce: '<?php echo wp_create_nonce('lci-dashboard-add-to-cart-nonce'); ?>'
                            };
                        }

                        // Get the number of nights from the page
                        const nights = <?php echo isset($wizard['bucharest']['nights']) ? intval($wizard['bucharest']['nights']) : 1; ?>;
                        // Console log removed

                        // Add to cart via AJAX
                        const data = {
                            action: 'lci-dashboard-add-to-cart',
                            product_id: productId,
                            quantity: 1,
                            security: lci_ajax.nonce,
                            nights: nights // Add nights parameter
                        };

                        // If we have a variation ID, add it directly
                        if (variationId) {
                            data.variation_id = variationId;

                            // Get all variation attributes from the form
                            const variationForm = document.querySelector('form');
                            if (variationForm) {
                                const formData = new FormData(variationForm);
                                for (const [key, value] of formData.entries()) {
                                    if (key.startsWith('attribute_')) {
                                        data[key] = value;
                                    }
                                }
                            }
                        }

                        // Log the data we're sending
                        // Console log removed

                        // Use jQuery AJAX for better compatibility
                        jQuery.ajax({
                            url: lci_ajax.ajax_url,
                            type: 'POST',
                            data: data,
                            success: function(response) {
                                // Log the response
                                // Console log removed
                                if (response.success) {
                                    // Show success state
                                    addToCartBtn.innerHTML = '<i class="fas fa-check me-2" style="color: white;"></i> <span style="color: white;">Added to cart!</span>';

                                    // Update mini cart
                                    if (response.data && response.data.cart_count !== undefined) {
                                        // Dispatch event to update mini cart
                                        window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
                                            detail: {
                                                cart_count: response.data.cart_count,
                                                cart_total: response.data.cart_total
                                            }
                                        }));

                                        // Also trigger standard WooCommerce events
                                        jQuery(document.body).trigger('added_to_cart', [null, null, null]);
                                        jQuery(document.body).trigger('updated_cart_totals');
                                    }

                                    // Show confirmation modal
                                    try {
                                        // Create product name with nights information
                                        const productName = nights > 1
                                            ? '<?php echo esc_js($product['name']); ?> (' + nights + ' nights)'
                                            : '<?php echo esc_js($product['name']); ?>';

                                        // Try multiple methods to show confirmation
                                        if (typeof window.showAddToCartConfirmation === 'function') {
                                            window.showAddToCartConfirmation(
                                                productName,
                                                '<?php echo esc_js($product['image']); ?>'
                                            );
                                        } else if (typeof window.showAddToCartConfirmationDirect === 'function') {
                                            window.showAddToCartConfirmationDirect(
                                                productName,
                                                '<?php echo esc_js($product['image']); ?>'
                                            );
                                        } else {
                                            // Dispatch event as fallback
                                            window.dispatchEvent(new CustomEvent('show-modal', {
                                                detail: {
                                                    productName: productName,
                                                    productImage: '<?php echo esc_js($product['image']); ?>'
                                                }
                                            }));
                                        }
                                    } catch (error) {
                                        // Console error removed
                                    }

                                    // Reset button after 2 seconds
                                    setTimeout(function() {
                                        addToCartBtn.innerHTML = originalText;
                                        addToCartBtn.disabled = false;

                                        // Try a fallback method if cart count is still 0
                                        if (response.data.cart_count === 0) {
                                            // Console log removed

                                            // Create a hidden form and submit it
                                            const fallbackForm = document.createElement('form');
                                            fallbackForm.method = 'POST';
                                            fallbackForm.style.display = 'none';

                                            // Add product ID
                                            const productIdInput = document.createElement('input');
                                            productIdInput.type = 'hidden';
                                            productIdInput.name = 'bucharest_product_id';
                                            productIdInput.value = productId;
                                            fallbackForm.appendChild(productIdInput);

                                            // Add variation ID
                                            const variationIdInput = document.createElement('input');
                                            variationIdInput.type = 'hidden';
                                            variationIdInput.name = 'room_variation_id';
                                            variationIdInput.value = variationId;
                                            fallbackForm.appendChild(variationIdInput);

                                            // Get the number of nights from the page
                                            const nights = <?php echo isset($wizard['bucharest']['nights']) ? intval($wizard['bucharest']['nights']) : 1; ?>;
                                            // Console log removed

                                            // Add nights information
                                            const nightsInput = document.createElement('input');
                                            nightsInput.type = 'hidden';
                                            nightsInput.name = 'bucharest_nights';
                                            nightsInput.value = nights;
                                            fallbackForm.appendChild(nightsInput);

                                            // Add add to cart action
                                            const actionInput = document.createElement('input');
                                            actionInput.type = 'hidden';
                                            actionInput.name = 'add_bucharest_to_cart';
                                            actionInput.value = '1';
                                            fallbackForm.appendChild(actionInput);

                                            // Add to document and submit
                                            document.body.appendChild(fallbackForm);
                                            fallbackForm.submit();
                                            return;
                                        }

                                        // Advance to next step
                                        document.querySelector('button[name="wizard_action"][value="next"]').click();
                                    }, 2000);
                                } else {
                                    // Show error
                                    addToCartBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2" style="color: white;"></i> <span style="color: white;">Error</span>';

                                    // Show error message
                                    alert(response.data && response.data.message ? response.data.message : 'Could not add product to cart');

                                    // Reset button after 2 seconds
                                    setTimeout(function() {
                                        addToCartBtn.innerHTML = originalText;
                                        addToCartBtn.disabled = false;
                                    }, 2000);
                                }
                            },
                            error: function(xhr, status, error) {
                                // Console error removed

                                // Show error
                                addToCartBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2" style="color: white;"></i> <span style="color: white;">Error</span>';

                                // Show error message
                                alert('Could not add product to cart. Please try again.');

                                // Reset button after 2 seconds
                                setTimeout(function() {
                                    addToCartBtn.innerHTML = originalText;
                                    addToCartBtn.disabled = false;
                                }, 2000);
                            }
                        });
                    });
                }
            });
            </script>
        </div>
    </div>
    <!-- Continue Button -->
    <div class="text-center mt-4">
        <button type="submit" name="wizard_action" value="next" class="wizard-btn wizard-btn-primary" style="background-color: #00b2e3; color: white; border: none; padding: 10px 20px; border-radius: 4px; font-weight: bold; cursor: pointer;">
            Continue to Brasov Accommodation <i class="fas fa-arrow-right ms-2"></i>
        </button>
    </div>
<?php endif; ?>
