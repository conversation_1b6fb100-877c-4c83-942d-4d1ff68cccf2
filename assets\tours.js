/**
 * Tours JavaScript
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        // Mini cart is now handled by tours-mini-cart.js
        // Dispatch a custom event to notify that tours.js is loaded
        document.dispatchEvent(new CustomEvent('tours:loaded'));
        // Directly add to cart when clicking the Order button - ONLY for tours page
        $('.tours-product-card .show-variations-btn, .tours-product-card .show-quantity-btn').on('click', function() {
            var productId = $(this).data('product-id');
            // Add to cart
            addToCart(productId);
        });

        // Quantity plus
        $('.quantity-plus').on('click', function() {
            var input = $(this).siblings('.quantity-input-field');
            var value = parseInt(input.val());
            input.val(value + 1);
        });

        // Quantity minus
        $('.quantity-minus').on('click', function() {
            var input = $(this).siblings('.quantity-input-field');
            var value = parseInt(input.val());
            if (value > 1) {
                input.val(value - 1);
            }
        });

        // We're now handling add to cart directly from the Order button click

        // Function to add product to cart
        function addToCart(productId) {
            // Get product name and image for confirmation modal
            var productCard = $('.tours-product-card').has('[data-product-id="' + productId + '"]');
            var productName = productCard.find('.tours-product-title').text();
            var productImage = productCard.find('.tours-product-image img').attr('src');

            // Show loading state - ONLY for tours page buttons
            var addButton = $('.tours-product-card [data-product-id="' + productId + '"]');
            var originalText = addButton.html();
            addButton.html('<i class="fas fa-spinner fa-spin"></i> Adding...');
            addButton.prop('disabled', true);

            // Prepare data - simplified to always use quantity=1 and no variations
            var data = {
                action: 'lci-dashboard-add-to-cart',
                security: lci_ajax.nonce,
                nonce: lci_ajax.nonce, // Add nonce parameter as fallback
                product_id: productId,
                quantity: 1 // Always use quantity 1
            };

            // Make AJAX request
            $.ajax({
                url: lci_ajax.ajax_url,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Reset button state
                    addButton.html(originalText);
                    addButton.prop('disabled', false);

                    // Close cards
                    $('.tours-variation-card, .tours-quantity-card').removeClass('active');

                    if (response.success) {
                        // Show tours confirmation modal - use multiple approaches to ensure it works

                        // Try the global function first
                        if (typeof window.showToursAddToCartConfirmation === 'function') {
                            window.showToursAddToCartConfirmation(productName, productImage);
                        } else if (typeof window.showAddToCartConfirmation === 'function') {
                            window.showAddToCartConfirmation(productName, productImage);
                        } else {
                            // Try dispatching events as fallback
                            window.dispatchEvent(new CustomEvent('tours-show-confirmation-pending', {
                                detail: {
                                    productName: productName,
                                    productImage: productImage
                                }
                            }));

                            // Also try the element event
                            const confirmEl = document.getElementById('tours-add-to-cart-confirm');
                            if (confirmEl) {
                                confirmEl.dispatchEvent(new CustomEvent('tours-show-confirmation', {
                                    detail: {
                                        productName: productName,
                                        productImage: productImage
                                    }
                                }));
                            }
                        }

                        // Update mini cart
                        updateMiniCart(response.data.cart_count, response.data.cart_total);
                    } else {
                        alert(response.data.message || 'Error adding product to cart');
                    }
                },
                error: function() {
                    // Reset button state
                    addButton.html(originalText);
                    addButton.prop('disabled', false);

                    alert('Error adding product to cart. Please try again.');
                }
            });
        }

        // Function to update mini cart
        function updateMiniCart(count, total) {
            // Dispatch event to update the tours mini cart
            window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
                detail: {
                    cart_count: count,
                    cart_total: total
                }
            }));
        }

        // No longer need the loadMiniCartItems function
    });
})(jQuery);
