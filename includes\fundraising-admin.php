<?php
/**
 * Fundraising Admin Settings
 *
 * Adds admin settings to manage the regalia fundraising progress
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Add settings link on plugin page
add_filter('plugin_action_links_lci-2025-dashboard/lci-2025-dashboard.php', 'lci_fundraising_settings_link');

function lci_fundraising_settings_link($links) {
    $settings_link = '<a href="' . admin_url('admin.php?page=lci-fundraising') . '">Fundraising Settings</a>';
    array_unshift($links, $settings_link);
    return $links;
}

// Initialize default values
add_action('admin_init', 'lci_fundraising_init_defaults');

function lci_fundraising_init_defaults() {
    // Set default values if not already set
    if (get_option('lci_regalia_fundraising_goal') === false) {
        update_option('lci_regalia_fundraising_goal', 1500);
    }

    if (get_option('lci_regalia_fundraising_current') === false) {
        update_option('lci_regalia_fundraising_current', 0);
    }
}

// Add admin menu item
add_action('admin_menu', 'lci_fundraising_admin_menu', 20); // Higher priority to ensure it loads after the main menu

function lci_fundraising_admin_menu() {
    // Add as a submenu under LCI 2025 Dashboard
    add_submenu_page(
        'lci-dashboard',        // Parent slug (LCI 2025 Dashboard)
        'Fundraising',          // Page title
        'Fundraising',          // Menu title
        'manage_options',       // Capability
        'lci-fundraising',      // Menu slug
        'lci_regalia_fundraising_page' // Function
    );

    // Add a notice to help users find the page
    add_action('admin_notices', 'lci_fundraising_admin_notice');
}

function lci_fundraising_admin_notice() {
    // Only show on dashboard
    $screen = get_current_screen();
    if ($screen->id !== 'dashboard') {
        return;
    }

    // Only show if we haven't dismissed it
    if (get_option('lci_fundraising_notice_dismissed')) {
        return;
    }

    ?>
    <div class="notice notice-info is-dismissible" id="lci-fundraising-notice">
        <p><strong>LCI 2025 Dashboard:</strong> You can now manage the Regalia Fundraising progress from the <a href="<?php echo admin_url('admin.php?page=lci-fundraising'); ?>">Fundraising</a> page under the LCI 2025 menu.</p>
    </div>
    <script>
        jQuery(document).ready(function($) {
            $(document).on('click', '#lci-fundraising-notice .notice-dismiss', function() {
                $.ajax({
                    url: ajaxurl,
                    data: {
                        action: 'dismiss_fundraising_notice'
                    }
                });
            });
        });
    </script>
    <?php
}

// AJAX handler to dismiss the notice
add_action('wp_ajax_dismiss_fundraising_notice', 'lci_dismiss_fundraising_notice');

function lci_dismiss_fundraising_notice() {
    update_option('lci_fundraising_notice_dismissed', true);
    wp_die();
}

// Add a link in the admin bar for quick access
add_action('admin_bar_menu', 'lci_fundraising_admin_bar_link', 999);

function lci_fundraising_admin_bar_link($admin_bar) {
    if (!current_user_can('manage_options')) {
        return;
    }

    $admin_bar->add_node([
        'id'    => 'lci-fundraising',
        'title' => 'Fundraising Settings',
        'href'  => admin_url('admin.php?page=lci-fundraising'),
        'meta'  => [
            'title' => 'Manage Regalia Fundraising Settings',
        ],
    ]);
}

// Register settings
add_action('admin_init', 'lci_fundraising_register_settings');

function lci_fundraising_register_settings() {
    register_setting('lci_regalia_fundraising', 'lci_regalia_fundraising_goal');
    register_setting('lci_regalia_fundraising', 'lci_regalia_fundraising_current');
    register_setting('lci_regalia_fundraising', 'lci_regalia_contributor_count');
    register_setting('lci_regalia_fundraising', 'lci_regalia_category_id');
}

// Admin page content
function lci_regalia_fundraising_page() {
    // Get current values
    $fundraising_goal = get_option('lci_regalia_fundraising_goal', 1500);
    $fundraising_current = get_option('lci_regalia_fundraising_current', 0);
    $fundraising_percentage = ($fundraising_goal > 0) ? min(100, ($fundraising_current / $fundraising_goal) * 100) : 0;
    $participants_funded = ($fundraising_goal > 0) ? floor($fundraising_current / $fundraising_goal) : 0;
    $additional_progress = ($fundraising_goal > 0) ? ($fundraising_current % $fundraising_goal) / $fundraising_goal * 100 : 0;

    // Get stored contributor count and category ID
    $stored_contributor_count = get_option('lci_regalia_contributor_count', 0);
    $category_id = get_option('lci_regalia_category_id', 22); // Default to 22 if not set

    // Calculate actual contributor count from WooCommerce
    $actual_contributor_count = 0;

    if (class_exists('WooCommerce')) {
        global $wpdb;

        // Query to count orders that contain products from this category
        $query = $wpdb->prepare(
            "SELECT COUNT(DISTINCT o.ID)
            FROM {$wpdb->prefix}posts o
            JOIN {$wpdb->prefix}woocommerce_order_items oi ON o.ID = oi.order_id
            JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
            JOIN {$wpdb->prefix}term_relationships tr ON oim.meta_value = tr.object_id
            JOIN {$wpdb->prefix}term_taxonomy tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
            WHERE o.post_type = 'shop_order'
            AND o.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
            AND oim.meta_key = '_product_id'
            AND tt.taxonomy = 'product_cat'
            AND tt.term_id = %d",
            $category_id
        );

        $actual_contributor_count = $wpdb->get_var($query);

        // Ensure we have a number
        $actual_contributor_count = absint($actual_contributor_count);
    }

    // Handle form submission
    if (isset($_POST['submit']) && check_admin_referer('lci_regalia_fundraising_update', 'lci_regalia_fundraising_nonce')) {
        // Update with new values
        if (isset($_POST['lci_regalia_fundraising_goal'])) {
            $goal = max(1, floatval($_POST['lci_regalia_fundraising_goal'])); // Ensure minimum of 1
            update_option('lci_regalia_fundraising_goal', $goal);
        }

        if (isset($_POST['lci_regalia_fundraising_current'])) {
            $current = max(0, floatval($_POST['lci_regalia_fundraising_current'])); // Ensure minimum of 0
            update_option('lci_regalia_fundraising_current', $current);
        }

        if (isset($_POST['lci_regalia_contributor_count'])) {
            $count = max(0, intval($_POST['lci_regalia_contributor_count'])); // Ensure minimum of 0
            update_option('lci_regalia_contributor_count', $count);
        }

        if (isset($_POST['lci_regalia_category_id'])) {
            $cat_id = max(1, intval($_POST['lci_regalia_category_id'])); // Ensure minimum of 1
            update_option('lci_regalia_category_id', $cat_id);
        }

        // Clear any transients to ensure fresh data
        delete_transient('lci_regalia_contributor_count');

        // Refresh values after update
        $fundraising_goal = get_option('lci_regalia_fundraising_goal', 1500);
        $fundraising_current = get_option('lci_regalia_fundraising_current', 0);
        $fundraising_percentage = ($fundraising_goal > 0) ? min(100, ($fundraising_current / $fundraising_goal) * 100) : 0;
        $participants_funded = ($fundraising_goal > 0) ? floor($fundraising_current / $fundraising_goal) : 0;
        $additional_progress = ($fundraising_goal > 0) ? ($fundraising_current % $fundraising_goal) / $fundraising_goal * 100 : 0;
        $stored_contributor_count = get_option('lci_regalia_contributor_count', 0);
        $category_id = get_option('lci_regalia_category_id', 22);

        echo '<div class="notice notice-success is-dismissible"><p>Fundraising settings updated successfully!</p></div>';
    }

    ?>
    <div class="wrap">
        <h1>Regalia Fundraising Settings</h1>

        <div class="card" style="max-width: 800px; margin-top: 20px; padding: 20px;">
            <h2>Current Progress</h2>

            <div style="background-color: #f0f0f0; border-radius: 10px; height: 20px; margin: 20px 0; position: relative; overflow: hidden;">
                <div style="background-color: #36b1dc; height: 100%; width: <?php echo $fundraising_percentage; ?>%; transition: width 1s ease;"></div>
            </div>

            <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                <div>
                    <strong style="font-size: 18px; color: #36b1dc;">€<?php echo number_format($fundraising_current, 0, '.', ','); ?></strong>
                    <span style="display: block; font-size: 14px; color: #666;">raised so far</span>
                </div>
                <div style="text-align: right;">
                    <strong style="font-size: 18px; color: #666;">€<?php echo number_format($fundraising_goal, 0, '.', ','); ?></strong>
                    <span style="display: block; font-size: 14px; color: #666;">goal per participant</span>
                </div>
            </div>

            <div style="text-align: center; margin-bottom: 20px; font-style: italic; color: #666;">
                <?php if ($participants_funded > 0): ?>
                    <p><strong><?php echo $participants_funded; ?> young <?php echo ($participants_funded == 1) ? 'Circler' : 'Circlers'; ?></strong> will attend thanks to your fundraising!
                    <?php if ($additional_progress > 0): ?>
                        You're <?php echo round($additional_progress); ?>% of the way to sponsoring another!
                    <?php endif; ?>
                    </p>
                <?php else: ?>
                    <?php if ($contributor_count > 0): ?>
                        <p>No young Circlers funded yet, but <strong><?php echo $contributor_count; ?> <?php echo ($contributor_count == 1) ? 'Circler has' : 'Circlers have'; ?></strong> already contributed! Update the amount raised to see progress.</p>
                    <?php else: ?>
                        <p>No young Circlers funded yet. Update the amount raised to see progress!</p>
                    <?php endif; ?>
                <?php endif; ?>

                <?php if ($participants_funded > 0 && $contributor_count > 0): ?>
                    <div style="margin-top: 15px; padding: 10px; background-color: #f0f7fb; border-radius: 5px; border-left: 4px solid #36b1dc;">
                        <p style="margin: 0;"><strong style="color: #36b1dc;"><?php echo $contributor_count; ?> <?php echo ($contributor_count == 1) ? 'Circler has' : 'Circlers have'; ?></strong> contributed by purchasing regalia products!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <form method="post" action="">
            <?php wp_nonce_field('lci_regalia_fundraising_update', 'lci_regalia_fundraising_nonce'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><label for="lci_regalia_fundraising_goal">Fundraising Goal per Participant (€)</label></th>
                    <td>
                        <input type="number" name="lci_regalia_fundraising_goal" id="lci_regalia_fundraising_goal" value="<?php echo esc_attr($fundraising_goal); ?>" min="1" step="1" class="regular-text">
                        <p class="description">The amount needed to fund one young Circler's participation.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="lci_regalia_fundraising_current">Current Amount Raised (€)</label></th>
                    <td>
                        <input type="number" name="lci_regalia_fundraising_current" id="lci_regalia_fundraising_current" value="<?php echo esc_attr($fundraising_current); ?>" min="0" step="1" class="regular-text">
                        <p class="description">The total amount raised so far from regalia sales.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="lci_regalia_contributor_count">Number of Contributors</label></th>
                    <td>
                        <input type="number" name="lci_regalia_contributor_count" id="lci_regalia_contributor_count" value="<?php echo esc_attr($stored_contributor_count); ?>" min="0" step="1" class="regular-text">
                        <p class="description">The number of Circlers who have contributed by ordering regalia products.</p>
                        <?php if ($actual_contributor_count > 0): ?>
                            <p><strong>Actual count from WooCommerce:</strong> <?php echo $actual_contributor_count; ?> orders contain products from category ID <?php echo $category_id; ?>.</p>
                            <button type="button" class="button" onclick="document.getElementById('lci_regalia_contributor_count').value='<?php echo $actual_contributor_count; ?>';">Use Actual Count</button>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="lci_regalia_category_id">Regalia Category ID</label></th>
                    <td>
                        <input type="number" name="lci_regalia_category_id" id="lci_regalia_category_id" value="<?php echo esc_attr($category_id); ?>" min="1" step="1" class="regular-text">
                        <p class="description">The WooCommerce category ID for regalia products (default: 22).</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Update Fundraising Progress'); ?>
        </form>
    </div>
    <?php
}
