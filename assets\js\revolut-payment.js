/**
 * Revolut Payment Integration
 */
(function($) {
    'use strict';
    
    // Revolut instance
    let revolutInstance = null;
    
    // Payment data
    let paymentData = {
        publicId: null,
        orderId: null
    };
    
    // Initialize Revolut payment form
    function initRevolutPayment() {
        console.log('Initializing Revolut payment form');
        
        // Check if the Revolut payment method is selected
        if (!$('#payment_method_revolut_cc').is(':checked')) {
            return;
        }
        
        // Check if the Revolut container exists
        const revolutContainer = $('#revolut-payment-container');
        if (revolutContainer.length === 0) {
            console.error('Revolut payment container not found');
            return;
        }
        
        // Create payment intent
        createPaymentIntent();
    }
    
    // Create payment intent
    function createPaymentIntent() {
        console.log('Creating payment intent');
        
        // Show loading state
        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-info"><p><i class="fas fa-spinner fa-spin me-2"></i> ' + lciRevolutData.i18n.processingMessage + '</p></div>');
        
        // Get form data
        const formData = new FormData($('#lci-payment-form')[0]);
        formData.append('action', 'lci_revolut_create_payment');
        formData.append('security', lciRevolutData.nonce);
        
        // Send AJAX request
        $.ajax({
            url: lciRevolutData.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    console.log('Payment intent created', response.data);
                    
                    // Store payment data
                    paymentData.publicId = response.data.public_id;
                    paymentData.orderId = response.data.order_id;
                    
                    // Initialize Revolut form
                    initRevolutForm(response.data.public_id);
                } else {
                    console.error('Error creating payment intent', response.data);
                    $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + (response.data.message || lciRevolutData.i18n.errorMessage) + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error', error);
                $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + lciRevolutData.i18n.errorMessage + '</p></div>');
            }
        });
    }
    
    // Initialize Revolut form
    function initRevolutForm(publicId) {
        console.log('Initializing Revolut form with public ID', publicId);
        
        // Clear previous instance
        if (revolutInstance) {
            revolutInstance.destroy();
        }
        
        // Clear container
        $('#revolut-payment-container').empty();
        
        // Initialize Revolut
        revolutInstance = RevolutCheckout(publicId);
        
        // Create card form
        revolutInstance.createCardField({
            target: document.getElementById('revolut-payment-container'),
            locale: lciRevolutData.locale,
            labels: {
                cardNumberLabel: lciRevolutData.i18n.cardNumberLabel,
                cardHolderLabel: lciRevolutData.i18n.cardHolderLabel,
                expiryDateLabel: lciRevolutData.i18n.expiryDateLabel,
                cvvLabel: lciRevolutData.i18n.cvvLabel,
            },
            styles: {
                default: {
                    color: '#333',
                    fontSize: '16px',
                    lineHeight: '1.5',
                    fontFamily: 'Open Sans, sans-serif',
                },
                focus: {
                    color: '#36b1dc',
                    borderColor: '#36b1dc',
                },
                error: {
                    color: '#dc3545',
                    borderColor: '#dc3545',
                },
            },
            onSuccess: handlePaymentSuccess,
            onError: handlePaymentError,
        });
        
        // Show the form
        $('#revolut-payment-form').show();
        
        // Hide loading message
        $('#lci-payment-messages').empty();
    }
    
    // Handle payment success
    function handlePaymentSuccess(event) {
        console.log('Payment successful', event);
        
        // Show success message
        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-success"><p><i class="fas fa-check-circle me-2"></i> ' + lciRevolutData.i18n.successMessage + '</p></div>');
        
        // Confirm payment
        confirmPayment();
    }
    
    // Handle payment error
    function handlePaymentError(event) {
        console.error('Payment error', event);
        
        // Show error message
        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + (event.message || lciRevolutData.i18n.errorMessage) + '</p></div>');
    }
    
    // Confirm payment
    function confirmPayment() {
        console.log('Confirming payment');
        
        // Send AJAX request
        $.ajax({
            url: lciRevolutData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lci_revolut_confirm_payment',
                security: lciRevolutData.nonce,
                public_id: paymentData.publicId,
                order_id: paymentData.orderId
            },
            success: function(response) {
                if (response.success) {
                    console.log('Payment confirmed', response.data);
                    
                    // Show success message
                    $('#lci-payment-messages').html('<div class="lci-notice lci-notice-success"><p><i class="fas fa-check-circle me-2"></i> ' + lciRevolutData.i18n.successMessage + ' ' + response.data.message + '</p></div>');
                    
                    // Redirect to success page
                    setTimeout(function() {
                        window.location.href = response.data.redirect;
                    }, 1500);
                } else {
                    console.error('Error confirming payment', response.data);
                    $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + (response.data.message || lciRevolutData.i18n.errorMessage) + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error', error);
                $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + lciRevolutData.i18n.errorMessage + '</p></div>');
            }
        });
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        // Handle payment method selection
        $('input[name="payment_method"]').on('change', function() {
            if ($(this).val() === 'revolut_cc') {
                initRevolutPayment();
            } else {
                // Hide Revolut form
                $('#revolut-payment-form').hide();
            }
        });
        
        // Handle form submission
        $('#lci-payment-form').on('submit', function(e) {
            const selectedMethod = $('input[name="payment_method"]:checked').val();
            
            // Only intercept for Revolut CC
            if (selectedMethod === 'revolut_cc') {
                e.preventDefault();
                
                // If we already have a payment intent, submit the form
                if (revolutInstance && paymentData.publicId) {
                    revolutInstance.submit();
                } else {
                    // Otherwise, create a payment intent
                    initRevolutPayment();
                }
            }
        });
        
        // Initialize if Revolut CC is selected
        if ($('#payment_method_revolut_cc').is(':checked')) {
            initRevolutPayment();
        }
    });
})(jQuery);