/**
 * LCI 2025 Dashboard Admin CSS
 */

/* Override WordPress admin styles */
.lci-admin-wrap {
    margin: 20px 20px 0 2px;
}

/* Custom Tailwind extensions */
.shadow-neumorph {
    box-shadow: 5px 5px 10px #d1d9e6, -5px -5px 10px #ffffff;
}

.shadow-neumorph-inset {
    box-shadow: inset 5px 5px 10px #d1d9e6, inset -5px -5px 10px #ffffff;
}

.shadow-glass {
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.07);
}

.backdrop-blur {
    backdrop-filter: blur(4px);
}

.bg-glass {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* Custom color classes */
.bg-primary {
    background-color: #36b1dc !important;
}

.bg-primary-light {
    background-color: #5ebbf5 !important;
}

.bg-primary-dark {
    background-color: #1a91bc !important;
}

.text-primary {
    color: #36b1dc !important;
}

.text-primary-light {
    color: #5ebbf5 !important;
}

.text-primary-dark {
    color: #1a91bc !important;
}

.border-primary {
    border-color: #36b1dc !important;
}

.hover\:bg-primary:hover {
    background-color: #36b1dc !important;
}

.hover\:bg-primary-dark:hover {
    background-color: #1a91bc !important;
}

.hover\:text-primary:hover {
    color: #36b1dc !important;
}

.hover\:border-primary:hover {
    border-color: #36b1dc !important;
}

/* Chart containers */
.h-64 {
    height: 16rem;
    position: relative;
    width: 100%;
}

.chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Animation classes */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
}

.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
}

.hover\:scale-105:hover {
    transform: scale(1.05);
}

/* Custom components */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.completed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-badge.processing {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-badge.pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-badge.failed {
    background-color: #fee2e2;
    color: #b91c1c;
}

/* Card styles */
.card {
    background-color: white;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Form styles */
.form-input {
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;
    padding: 0.5rem 0.75rem;
    width: 100%;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #36b1dc;
    box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary {
    background-color: #36b1dc;
    color: white;
}

.btn-primary:hover {
    background-color: #1a91bc;
}

.btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
}

.btn-secondary:hover {
    background-color: #e5e7eb;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

/* Table styles */
.table-container {
    overflow-x: auto;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background-color: #f9fafb;
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 500;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.data-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.data-table tr:hover {
    background-color: #f9fafb;
}

/* Loading spinner */
.spinner {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 3px solid #36b1dc;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 0.75rem;
    transition: opacity 0.3s ease-in-out;
}

.loading-overlay .spinner {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

.loading-overlay .loading-text {
    margin-top: 1rem;
    font-size: 1rem;
    color: #4b5563;
    font-weight: 500;
}

.loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

/* Toast notifications */
.toast {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 50;
    max-width: 24rem;
    transform: translateY(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast-success {
    border-left: 4px solid #10b981;
}

.toast-error {
    border-left: 4px solid #ef4444;
}

.toast-info {
    border-left: 4px solid #3b82f6;
}

/* Loading overlay */
.loading-overlay {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    padding: 2rem;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 0.5rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1rem;
    color: #4b5563;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .lci-admin-wrap {
        margin: 10px 10px 0 10px;
    }

    .card {
        border-radius: 0.75rem;
    }

    .form-input, .btn {
        padding: 0.375rem 0.75rem;
    }

    .data-table th, .data-table td {
        padding: 0.5rem 0.75rem;
    }
}
