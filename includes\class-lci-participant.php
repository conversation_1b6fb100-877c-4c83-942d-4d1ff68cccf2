<?php
/**
 * LCI 2025 Dashboard Participant Class
 *
 * Handles participant data operations
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class LCI_Participant {
    /**
     * Get a participant by ID
     *
     * @param int $id Participant ID
     * @return object|null Participant object or null if not found
     */
    public static function get_participant($id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $id
        ));
    }

    /**
     * Get a participant by unique registration ID
     *
     * @param string $unique_reg_id Unique registration ID
     * @return object|null Participant object or null if not found
     */
    public static function get_participant_by_unique_id($unique_reg_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE unique_reg_id = %s",
            $unique_reg_id
        ));
    }

    /**
     * Get a participant by order ID
     *
     * @param int $order_id WooCommerce order ID
     * @return object|null Participant object or null if not found
     */
    public static function get_participant_by_order_id($order_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE order_id = %d",
            $order_id
        ));
    }

    /**
     * Get a participant by user ID
     *
     * @param int $user_id WordPress user ID
     * @return object|null Participant object or null if not found
     */
    public static function get_participant_by_user_id($user_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE user_id = %d",
            $user_id
        ));
    }

    /**
     * Get all participants
     *
     * @param array $args Query arguments
     * @return array Array of participant objects
     */
    public static function get_participants($args = []) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        $defaults = [
            'orderby' => 'order_date',
            'order' => 'DESC',
            'limit' => -1,
            'offset' => 0,
            'search' => '',
            'where' => []
        ];

        $args = wp_parse_args($args, $defaults);

        // Build the query
        $query = "SELECT * FROM $table_name";

        // Add WHERE clauses
        if (!empty($args['where'])) {
            $query .= " WHERE ";
            $where_clauses = [];

            foreach ($args['where'] as $column => $value) {
                if (is_array($value)) {
                    $placeholders = array_fill(0, count($value), '%s');
                    $placeholders = implode(', ', $placeholders);
                    $where_clauses[] = $wpdb->prepare("$column IN ($placeholders)", $value);
                } else {
                    $where_clauses[] = $wpdb->prepare("$column = %s", $value);
                }
            }

            $query .= implode(' AND ', $where_clauses);
        }

        // Add search
        if (!empty($args['search'])) {
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';

            $search_query = $wpdb->prepare(
                "first_name LIKE %s OR last_name LIKE %s OR email LIKE %s OR unique_reg_id LIKE %s OR original_reg_id LIKE %s",
                $search_term, $search_term, $search_term, $search_term, $search_term
            );

            if (strpos($query, 'WHERE') !== false) {
                $query .= " AND ($search_query)";
            } else {
                $query .= " WHERE $search_query";
            }
        }

        // Add order
        $query .= $wpdb->prepare(" ORDER BY %s %s", $args['orderby'], $args['order']);

        // Add limit
        if ($args['limit'] > 0) {
            $query .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['limit']);
        }

        return $wpdb->get_results($query);
    }

    /**
     * Count participants
     *
     * @param array $args Query arguments
     * @return int Number of participants
     */
    public static function count_participants($args = []) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        $defaults = [
            'search' => '',
            'where' => []
        ];

        $args = wp_parse_args($args, $defaults);

        // Build the query
        $query = "SELECT COUNT(*) FROM $table_name";

        // Add WHERE clauses
        if (!empty($args['where'])) {
            $query .= " WHERE ";
            $where_clauses = [];

            foreach ($args['where'] as $column => $value) {
                if (is_array($value)) {
                    $placeholders = array_fill(0, count($value), '%s');
                    $placeholders = implode(', ', $placeholders);
                    $where_clauses[] = $wpdb->prepare("$column IN ($placeholders)", $value);
                } else {
                    $where_clauses[] = $wpdb->prepare("$column = %s", $value);
                }
            }

            $query .= implode(' AND ', $where_clauses);
        }

        // Add search
        if (!empty($args['search'])) {
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';

            $search_query = $wpdb->prepare(
                "first_name LIKE %s OR last_name LIKE %s OR email LIKE %s OR unique_reg_id LIKE %s OR original_reg_id LIKE %s",
                $search_term, $search_term, $search_term, $search_term, $search_term
            );

            if (strpos($query, 'WHERE') !== false) {
                $query .= " AND ($search_query)";
            } else {
                $query .= " WHERE $search_query";
            }
        }

        return (int) $wpdb->get_var($query);
    }

    /**
     * Sync participant data from WooCommerce order
     *
     * @param int $order_id WooCommerce order ID
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public static function sync_from_order($order_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        // Enable debug logging if debug mode is enabled
        $debug_mode = get_option('lci2025_debug_mode', '0');
        $debug_log = [];

        if ($debug_mode) {
            $debug_log[] = "Starting sync for order ID: {$order_id}";
        }

        // Get order data
        $order = wc_get_order($order_id);
        if (!$order) {
            $error_message = "Order not found: {$order_id}";
            if ($debug_mode) {
                self::log_error($error_message, $order_id);
            }
            return new WP_Error('invalid_order', $error_message);
        }

        $user_id = $order->get_user_id();

        if ($debug_mode) {
            $debug_log[] = "User ID: {$user_id}";
        }

        // Get original registration ID from order meta
        $original_reg_id = $order->get_meta('_alg_wc_full_custom_order_number') ?: $order_id;

        if ($debug_mode) {
            $debug_log[] = "Original Reg ID: {$original_reg_id}";
        }

        // Check if this order is already in our table
        $existing = self::get_participant_by_order_id($order_id);

        if ($debug_mode) {
            $debug_log[] = "Existing record: " . ($existing ? "Yes (ID: {$existing->id})" : "No");
        }

        try {
            // Get country code and convert to full name
            $country_code = $order->get_billing_country();
            $country_name = self::get_country_name($country_code);

            // Extract all required data from order
            $participant_data = [
                'user_id' => $user_id,
                'order_id' => $order_id,
                'original_reg_id' => $original_reg_id,
                'first_name' => $order->get_billing_first_name(),
                'last_name' => $order->get_billing_last_name(),
                'email' => $order->get_billing_email(),
                'phone' => $order->get_billing_phone(),
                'country' => $country_name,
                'order_date' => $order->get_date_created() ? $order->get_date_created()->date('Y-m-d H:i:s') : current_time('mysql'),
                // Extract other fields from order meta
                'association' => self::get_association_from_order($order),
                'club_no' => $order->get_meta('club_no'),
                'club_position' => $order->get_meta('position'),
                'registration_type' => self::get_registration_type_from_order($order),
                'payment_status' => $order->get_status(),
                'diet' => $order->get_meta('specific_diet'),
                'allergies' => $order->get_meta('allergies'),
                'room_share' => $order->get_meta('room_share_options'),
                'share_with' => $order->get_meta('share_room_with'),
                'accommodation' => self::get_accommodation_from_order($order),
                'tours' => self::get_tours_from_order($order),
                'updated_at' => current_time('mysql'),
                'last_sync' => current_time('mysql')
            ];

            if ($debug_mode) {
                $debug_log[] = "Participant data prepared";
                // Log specific fields that might be problematic
                $debug_log[] = "Email: {$participant_data['email']}";
                $debug_log[] = "Registration Type: {$participant_data['registration_type']}";
            }

            // Validate required fields
            $required_fields = ['first_name', 'last_name', 'email', 'registration_type'];
            foreach ($required_fields as $field) {
                if (empty($participant_data[$field])) {
                    $error_message = "Required field '{$field}' is empty for order {$order_id}";
                    if ($debug_mode) {
                        self::log_error($error_message, $order_id, $participant_data);
                    }
                    return new WP_Error('missing_required_field', $error_message);
                }
            }

            if ($existing) {
                // Update existing record
                $result = $wpdb->update(
                    $table_name,
                    $participant_data,
                    ['id' => $existing->id]
                );

                $success = ($result !== false);

                if ($debug_mode) {
                    $debug_log[] = "Update result: " . ($success ? "Success" : "Failed: {$wpdb->last_error}");
                }
            } else {
                // Insert new record with a unique registration ID
                $participant_data['unique_reg_id'] = LCI_Database::generate_unique_reg_id();
                $participant_data['created_at'] = current_time('mysql');

                $result = $wpdb->insert($table_name, $participant_data);
                $success = ($result !== false);

                if ($debug_mode) {
                    $debug_log[] = "Insert result: " . ($success ? "Success (ID: {$wpdb->insert_id})" : "Failed: {$wpdb->last_error}");
                }
            }

            if (!$success) {
                $error_message = "Failed to sync participant data: {$wpdb->last_error}";
                if ($debug_mode) {
                    self::log_error($error_message, $order_id, $participant_data);
                }
                return new WP_Error('db_error', $error_message);
            }

            if ($debug_mode) {
                $debug_log[] = "Sync completed successfully";
                self::log_debug("Sync success for order {$order_id}", $debug_log);
            }

            return true;

        } catch (Exception $e) {
            $error_message = "Exception during sync: {$e->getMessage()}";
            if ($debug_mode) {
                self::log_error($error_message, $order_id);
            }
            return new WP_Error('sync_exception', $error_message);
        }
    }

    /**
     * Log error message
     *
     * @param string $message Error message
     * @param int $order_id Order ID
     * @param array $data Additional data
     */
    private static function log_error($message, $order_id, $data = []) {
        $log_file = LCI2025_PATH . 'logs/sync_errors.log';
        $log_dir = dirname($log_file);

        // Create logs directory if it doesn't exist
        if (!file_exists($log_dir)) {
            mkdir($log_dir, 0755, true);
        }

        $timestamp = current_time('mysql');
        $log_entry = "[{$timestamp}] [Order: {$order_id}] ERROR: {$message}\n";

        if (!empty($data)) {
            $log_entry .= "Data: " . print_r($data, true) . "\n";
        }

        file_put_contents($log_file, $log_entry, FILE_APPEND);
    }

    /**
     * Log debug message
     *
     * @param string $message Debug message
     * @param array $data Additional data
     */
    private static function log_debug($message, $data = []) {
        $log_file = LCI2025_PATH . 'logs/sync_debug.log';
        $log_dir = dirname($log_file);

        // Create logs directory if it doesn't exist
        if (!file_exists($log_dir)) {
            mkdir($log_dir, 0755, true);
        }

        $timestamp = current_time('mysql');
        $log_entry = "[{$timestamp}] DEBUG: {$message}\n";

        if (!empty($data)) {
            $log_entry .= "Data: " . print_r($data, true) . "\n";
        }

        file_put_contents($log_file, $log_entry, FILE_APPEND);
    }

    /**
     * Get association from order
     *
     * @param WC_Order $order WooCommerce order
     * @return string Association name
     */
    private static function get_association_from_order($order) {
        $your_association = $order->get_meta('your_association');
        $assoc_map = [
            'RT' => 'rt_association',
            'LC' => 'lc_association',
            'TANGENT' => 'tangent_association',
            'AGORA' => 'agora_association',
            'C41' => 'c41_club',
        ];

        $assoc_key = isset($assoc_map[$your_association]) ? $assoc_map[$your_association] : '';
        $association_raw = $assoc_key ? $order->get_meta($assoc_key) : '';
        $association_clean = str_replace('_', ' ', $association_raw);

        return $association_clean;
    }

    /**
     * Get registration type from order
     *
     * @param WC_Order $order WooCommerce order
     * @return string Registration type
     */
    private static function get_registration_type_from_order($order) {
        $registration_type = 'N/A';

        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            if ($product_id == 41) {
                $registration_type = 'Full Event Package';
                break;
            } elseif ($product_id == 40) {
                $registration_type = 'Councilor Package';
                break;
            } elseif ($product_id == 42) {
                $registration_type = 'Partners Package';
                break;
            }
        }

        return $registration_type;
    }

    /**
     * Get accommodation from order
     *
     * @param WC_Order $order WooCommerce order
     * @return string Accommodation details
     */
    private static function get_accommodation_from_order($order) {
        $accommodation_items = [];

        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            if ($product) {
                $product_id = $product->is_type('variation') ? $product->get_parent_id() : $product->get_id();

                if (in_array($product_id, [1679, 137])) {
                    continue;
                }

                $term_ids = wp_get_post_terms($product_id, 'product_cat', ['fields' => 'ids']);

                if (!empty($term_ids)) {
                    if (in_array(18, $term_ids)) {
                        $accommodation_items[] = $product->get_name() . ' (3 nights)';
                    } elseif (in_array(20, $term_ids)) {
                        $accommodation_items[] = $product->get_name() . ' (4 nights)';
                    }
                }
            }
        }

        return !empty($accommodation_items) ? implode(', ', $accommodation_items) : '';
    }

    /**
     * Get tours from order
     *
     * @param WC_Order $order WooCommerce order
     * @return string Tour details
     */
    private static function get_tours_from_order($order) {
        $tour_items = [];

        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            if ($product) {
                $product_id = $product->is_type('variation') ? $product->get_parent_id() : $product->get_id();
                $term_ids = wp_get_post_terms($product_id, 'product_cat', ['fields' => 'ids']);

                if (!empty($term_ids) && in_array(21, $term_ids)) {
                    $tour_items[] = $product->get_name();
                }
            }
        }

        return !empty($tour_items) ? implode(', ', $tour_items) : '';
    }

    /**
     * Update participant data
     *
     * @param int $id Participant ID
     * @param array $data Participant data
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public static function update_participant($id, $data) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        // Get existing participant
        $participant = self::get_participant($id);
        if (!$participant) {
            return new WP_Error('invalid_participant', 'Participant not found');
        }

        // Add updated timestamp
        $data['updated_at'] = current_time('mysql');

        // Update the record
        $result = $wpdb->update(
            $table_name,
            $data,
            ['id' => $id]
        );

        if ($result === false) {
            return new WP_Error('db_error', 'Failed to update participant data: ' . $wpdb->last_error);
        }

        return true;
    }

    /**
     * Delete participant
     *
     * @param int $id Participant ID
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public static function delete_participant($id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        // Get existing participant
        $participant = self::get_participant($id);
        if (!$participant) {
            return new WP_Error('invalid_participant', 'Participant not found');
        }

        // Delete the record
        $result = $wpdb->delete(
            $table_name,
            ['id' => $id]
        );

        if ($result === false) {
            return new WP_Error('db_error', 'Failed to delete participant: ' . $wpdb->last_error);
        }

        return true;
    }

    /**
     * Get country name from country code
     *
     * @param string $country_code Country code
     * @return string Country name
     */
    public static function get_country_name($country_code) {
        // Get WooCommerce countries if available
        if (function_exists('WC')) {
            $countries = WC()->countries->get_countries();
            if (isset($countries[$country_code])) {
                return $countries[$country_code];
            }
        }

        // Fallback to our own country list if WooCommerce is not available
        $countries = self::get_countries_list();
        if (isset($countries[$country_code])) {
            return $countries[$country_code];
        }

        return $country_code;
    }

    /**
     * Get list of countries
     *
     * @return array List of countries
     */
    public static function get_countries_list() {
        return [
            'AF' => 'Afghanistan',
            'AX' => 'Aland Islands',
            'AL' => 'Albania',
            'DZ' => 'Algeria',
            'AS' => 'American Samoa',
            'AD' => 'Andorra',
            'AO' => 'Angola',
            'AI' => 'Anguilla',
            'AQ' => 'Antarctica',
            'AG' => 'Antigua and Barbuda',
            'AR' => 'Argentina',
            'AM' => 'Armenia',
            'AW' => 'Aruba',
            'AU' => 'Australia',
            'AT' => 'Austria',
            'AZ' => 'Azerbaijan',
            'BS' => 'Bahamas',
            'BH' => 'Bahrain',
            'BD' => 'Bangladesh',
            'BB' => 'Barbados',
            'BY' => 'Belarus',
            'BE' => 'Belgium',
            'BZ' => 'Belize',
            'BJ' => 'Benin',
            'BM' => 'Bermuda',
            'BT' => 'Bhutan',
            'BO' => 'Bolivia',
            'BQ' => 'Bonaire, Saint Eustatius and Saba',
            'BA' => 'Bosnia and Herzegovina',
            'BW' => 'Botswana',
            'BV' => 'Bouvet Island',
            'BR' => 'Brazil',
            'IO' => 'British Indian Ocean Territory',
            'BN' => 'Brunei Darussalam',
            'BG' => 'Bulgaria',
            'BF' => 'Burkina Faso',
            'BI' => 'Burundi',
            'KH' => 'Cambodia',
            'CM' => 'Cameroon',
            'CA' => 'Canada',
            'CV' => 'Cape Verde',
            'KY' => 'Cayman Islands',
            'CF' => 'Central African Republic',
            'TD' => 'Chad',
            'CL' => 'Chile',
            'CN' => 'China',
            'CX' => 'Christmas Island',
            'CC' => 'Cocos (Keeling) Islands',
            'CO' => 'Colombia',
            'KM' => 'Comoros',
            'CG' => 'Congo',
            'CD' => 'Congo, Democratic Republic of the',
            'CK' => 'Cook Islands',
            'CR' => 'Costa Rica',
            'CI' => 'Cote D\'Ivoire',
            'HR' => 'Croatia',
            'CU' => 'Cuba',
            'CW' => 'Curacao',
            'CY' => 'Cyprus',
            'CZ' => 'Czech Republic',
            'DK' => 'Denmark',
            'DJ' => 'Djibouti',
            'DM' => 'Dominica',
            'DO' => 'Dominican Republic',
            'EC' => 'Ecuador',
            'EG' => 'Egypt',
            'SV' => 'El Salvador',
            'GQ' => 'Equatorial Guinea',
            'ER' => 'Eritrea',
            'EE' => 'Estonia',
            'ET' => 'Ethiopia',
            'FK' => 'Falkland Islands (Malvinas)',
            'FO' => 'Faroe Islands',
            'FJ' => 'Fiji',
            'FI' => 'Finland',
            'FR' => 'France',
            'GF' => 'French Guiana',
            'PF' => 'French Polynesia',
            'TF' => 'French Southern Territories',
            'GA' => 'Gabon',
            'GM' => 'Gambia',
            'GE' => 'Georgia',
            'DE' => 'Germany',
            'GH' => 'Ghana',
            'GI' => 'Gibraltar',
            'GR' => 'Greece',
            'GL' => 'Greenland',
            'GD' => 'Grenada',
            'GP' => 'Guadeloupe',
            'GU' => 'Guam',
            'GT' => 'Guatemala',
            'GG' => 'Guernsey',
            'GN' => 'Guinea',
            'GW' => 'Guinea-Bissau',
            'GY' => 'Guyana',
            'HT' => 'Haiti',
            'HM' => 'Heard Island and Mcdonald Islands',
            'VA' => 'Holy See (Vatican City State)',
            'HN' => 'Honduras',
            'HK' => 'Hong Kong',
            'HU' => 'Hungary',
            'IS' => 'Iceland',
            'IN' => 'India',
            'ID' => 'Indonesia',
            'IR' => 'Iran, Islamic Republic of',
            'IQ' => 'Iraq',
            'IE' => 'Ireland',
            'IM' => 'Isle of Man',
            'IL' => 'Israel',
            'IT' => 'Italy',
            'JM' => 'Jamaica',
            'JP' => 'Japan',
            'JE' => 'Jersey',
            'JO' => 'Jordan',
            'KZ' => 'Kazakhstan',
            'KE' => 'Kenya',
            'KI' => 'Kiribati',
            'KP' => 'Korea, Democratic People\'s Republic of',
            'KR' => 'Korea, Republic of',
            'XK' => 'Kosovo',
            'KW' => 'Kuwait',
            'KG' => 'Kyrgyzstan',
            'LA' => 'Lao People\'s Democratic Republic',
            'LV' => 'Latvia',
            'LB' => 'Lebanon',
            'LS' => 'Lesotho',
            'LR' => 'Liberia',
            'LY' => 'Libyan Arab Jamahiriya',
            'LI' => 'Liechtenstein',
            'LT' => 'Lithuania',
            'LU' => 'Luxembourg',
            'MO' => 'Macao',
            'MK' => 'Macedonia, the Former Yugoslav Republic of',
            'MG' => 'Madagascar',
            'MW' => 'Malawi',
            'MY' => 'Malaysia',
            'MV' => 'Maldives',
            'ML' => 'Mali',
            'MT' => 'Malta',
            'MH' => 'Marshall Islands',
            'MQ' => 'Martinique',
            'MR' => 'Mauritania',
            'MU' => 'Mauritius',
            'YT' => 'Mayotte',
            'MX' => 'Mexico',
            'FM' => 'Micronesia, Federated States of',
            'MD' => 'Moldova, Republic of',
            'MC' => 'Monaco',
            'MN' => 'Mongolia',
            'ME' => 'Montenegro',
            'MS' => 'Montserrat',
            'MA' => 'Morocco',
            'MZ' => 'Mozambique',
            'MM' => 'Myanmar',
            'NA' => 'Namibia',
            'NR' => 'Nauru',
            'NP' => 'Nepal',
            'NL' => 'Netherlands',
            'NC' => 'New Caledonia',
            'NZ' => 'New Zealand',
            'NI' => 'Nicaragua',
            'NE' => 'Niger',
            'NG' => 'Nigeria',
            'NU' => 'Niue',
            'NF' => 'Norfolk Island',
            'MP' => 'Northern Mariana Islands',
            'NO' => 'Norway',
            'OM' => 'Oman',
            'PK' => 'Pakistan',
            'PW' => 'Palau',
            'PS' => 'Palestinian Territory, Occupied',
            'PA' => 'Panama',
            'PG' => 'Papua New Guinea',
            'PY' => 'Paraguay',
            'PE' => 'Peru',
            'PH' => 'Philippines',
            'PN' => 'Pitcairn',
            'PL' => 'Poland',
            'PT' => 'Portugal',
            'PR' => 'Puerto Rico',
            'QA' => 'Qatar',
            'RE' => 'Reunion',
            'RO' => 'Romania',
            'RU' => 'Russian Federation',
            'RW' => 'Rwanda',
            'BL' => 'Saint Barthelemy',
            'SH' => 'Saint Helena',
            'KN' => 'Saint Kitts and Nevis',
            'LC' => 'Saint Lucia',
            'MF' => 'Saint Martin',
            'PM' => 'Saint Pierre and Miquelon',
            'VC' => 'Saint Vincent and the Grenadines',
            'WS' => 'Samoa',
            'SM' => 'San Marino',
            'ST' => 'Sao Tome and Principe',
            'SA' => 'Saudi Arabia',
            'SN' => 'Senegal',
            'RS' => 'Serbia',
            'SC' => 'Seychelles',
            'SL' => 'Sierra Leone',
            'SG' => 'Singapore',
            'SX' => 'Sint Maarten',
            'SK' => 'Slovakia',
            'SI' => 'Slovenia',
            'SB' => 'Solomon Islands',
            'SO' => 'Somalia',
            'ZA' => 'South Africa',
            'GS' => 'South Georgia and the South Sandwich Islands',
            'SS' => 'South Sudan',
            'ES' => 'Spain',
            'LK' => 'Sri Lanka',
            'SD' => 'Sudan',
            'SR' => 'Suriname',
            'SJ' => 'Svalbard and Jan Mayen',
            'SZ' => 'Swaziland',
            'SE' => 'Sweden',
            'CH' => 'Switzerland',
            'SY' => 'Syrian Arab Republic',
            'TW' => 'Taiwan, Province of China',
            'TJ' => 'Tajikistan',
            'TZ' => 'Tanzania, United Republic of',
            'TH' => 'Thailand',
            'TL' => 'Timor-Leste',
            'TG' => 'Togo',
            'TK' => 'Tokelau',
            'TO' => 'Tonga',
            'TT' => 'Trinidad and Tobago',
            'TN' => 'Tunisia',
            'TR' => 'Turkey',
            'TM' => 'Turkmenistan',
            'TC' => 'Turks and Caicos Islands',
            'TV' => 'Tuvalu',
            'UG' => 'Uganda',
            'UA' => 'Ukraine',
            'AE' => 'United Arab Emirates',
            'GB' => 'United Kingdom',
            'US' => 'United States',
            'UM' => 'United States Minor Outlying Islands',
            'UY' => 'Uruguay',
            'UZ' => 'Uzbekistan',
            'VU' => 'Vanuatu',
            'VE' => 'Venezuela',
            'VN' => 'Viet Nam',
            'VG' => 'Virgin Islands, British',
            'VI' => 'Virgin Islands, U.s.',
            'WF' => 'Wallis and Futuna',
            'EH' => 'Western Sahara',
            'YE' => 'Yemen',
            'ZM' => 'Zambia',
            'ZW' => 'Zimbabwe'
        ];
    }

    /**
     * Get participant statistics
     *
     * @return array Statistics data
     */
    public static function get_statistics() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        $stats = [
            'total' => 0,
            'registration_types' => [],
            'payment_status' => [],
            'badge_status' => [],
        ];

        // Total participants
        $stats['total'] = self::count_participants();

        // Registration types
        $reg_types = $wpdb->get_results(
            "SELECT registration_type, COUNT(*) as count
             FROM $table_name
             GROUP BY registration_type
             ORDER BY count DESC"
        );

        foreach ($reg_types as $type) {
            $stats['registration_types'][$type->registration_type] = (int) $type->count;
        }

        // Payment status
        $payment_statuses = $wpdb->get_results(
            "SELECT payment_status, COUNT(*) as count
             FROM $table_name
             GROUP BY payment_status
             ORDER BY count DESC"
        );

        foreach ($payment_statuses as $status) {
            $stats['payment_status'][$status->payment_status] = (int) $status->count;
        }

        // Badge status
        $badge_statuses = $wpdb->get_results(
            "SELECT badge_status, COUNT(*) as count
             FROM $table_name
             GROUP BY badge_status
             ORDER BY count DESC"
        );

        foreach ($badge_statuses as $status) {
            $stats['badge_status'][$status->badge_status] = (int) $status->count;
        }

        return $stats;
    }
}
