<?php
/**
 * LCI Tours Unified Class
 *
 * Handles tour participants management for the LCI 2025 Dashboard using a unified table approach.
 */

class LCI_Tours_Unified {
    // Product IDs for tours
    const MAIN_PRETOUR_ID = 743;
    const LEGENDS_WILDLIFE_ID = 744;
    const ROYAL_ELEGANCE_ID = 745;
    const BRASOV_HIGHLIGHTS_ID = 746;

    /**
     * Initialize the class
     */
    public static function init() {
        // Register AJAX handlers
        add_action('wp_ajax_lci_sync_tours_unified', [__CLASS__, 'ajax_sync_tours']);
        add_action('wp_ajax_lci_get_tours_unified', [__CLASS__, 'ajax_get_tours']);
        add_action('wp_ajax_lci_update_tour_unified', [__CLASS__, 'ajax_update_tour']);
        add_action('wp_ajax_lci_delete_tour_unified', [__CLASS__, 'ajax_delete_tour']);
        add_action('wp_ajax_lci_clear_tours_table_unified', [__CLASS__, 'ajax_clear_tours_table']);
        add_action('wp_ajax_lci_recreate_tours_table_unified', [__CLASS__, 'ajax_recreate_tours_table']);
        add_action('wp_ajax_lci_get_tours_countries', [__CLASS__, 'ajax_get_tours_countries']);
    }

    /**
     * Log messages to a custom log file
     *
     * @param string $message Message to log
     * @param string $level Log level (info, warning, error)
     */
    public static function log($message, $level = 'info') {
        if (!is_string($message)) {
            $message = print_r($message, true);
        }

        $log_dir = WP_CONTENT_DIR . '/logs';
        if (!file_exists($log_dir)) {
            mkdir($log_dir, 0755, true);
        }

        $log_file = $log_dir . '/lci-tours-unified.log';
        $timestamp = current_time('mysql');
        $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;

        error_log($log_entry, 3, $log_file);
    }

    /**
     * Create the tours table
     *
     * @param bool $force Whether to force table recreation
     */
    public static function create_tables($force = false) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_tours_unified';
        $charset_collate = $wpdb->get_charset_collate();

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

        if ($table_exists && !$force) {
            // Check if table structure needs to be updated
            $current_columns = $wpdb->get_results("DESCRIBE $table_name", ARRAY_A);
            $current_column_names = array_column($current_columns, 'Field');

            // List of required columns
            $required_columns = [
                'id', 'user_id', 'order_id', 'first_name', 'last_name', 'email',
                'phone', 'country', 'country_code', 'order_date', 'payment_status',
                'main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights',
                'created_at', 'updated_at'
            ];

            $needs_recreation = false;
            foreach ($required_columns as $column) {
                if (!in_array($column, $current_column_names)) {
                    self::log("Column '$column' missing from tours table, needs recreation", "warning");
                    $needs_recreation = true;
                    break;
                }
            }

            if ($needs_recreation) {
                self::log("Dropping table to recreate with updated structure", "info");
                $wpdb->query("DROP TABLE IF EXISTS $table_name");
                self::log("Table dropped", "info");
            } else {
                self::log("Table structure is up to date, no need to recreate", "info");
                return; // Skip recreation if the table structure is already correct
            }
        } else {
            self::log("Table does not exist, will create it", "info");
        }

        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            order_id BIGINT(20) UNSIGNED NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(50),
            country VARCHAR(100),
            country_code VARCHAR(10),
            order_date DATETIME,
            payment_status VARCHAR(50) NOT NULL,
            main_pretour TINYINT(1) NOT NULL DEFAULT 0,
            legends_wildlife TINYINT(1) NOT NULL DEFAULT 0,
            royal_elegance TINYINT(1) NOT NULL DEFAULT 0,
            brasov_highlights TINYINT(1) NOT NULL DEFAULT 0,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY order_id (order_id),
            KEY country_code (country_code),
            KEY main_pretour (main_pretour),
            KEY legends_wildlife (legends_wildlife),
            KEY royal_elegance (royal_elegance),
            KEY brasov_highlights (brasov_highlights)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result = dbDelta($sql);

        // Log the result of the table creation
        self::log("Table creation result: " . print_r($result, true), "info");

        // Verify table was created
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        self::log("Table exists after creation: " . ($table_exists ? "Yes" : "No"), "info");

        if ($table_exists) {
            $table_structure = $wpdb->get_results("DESCRIBE $table_name");
            self::log("Created table structure: " . print_r($table_structure, true), "info");
        }
    }

    /**
     * Check if tours table exists
     *
     * @return array Table existence and data status
     */
    public static function check_tours_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_tours_unified';

        // Check if table exists
        $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        $count = 0;

        if ($exists) {
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        }

        return [
            'exists' => $exists,
            'has_data' => $count > 0,
            'count' => $count
        ];
    }

    /**
     * Get tours participants
     *
     * @param array $args Query arguments
     * @return array Tours participants
     */
    public static function get_tours_participants($args = []) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_tours_unified';

        self::log("Getting tours participants with args: " . print_r($args, true), "info");

        // Default arguments
        $defaults = [
            'orderby' => 'order_date',
            'order' => 'DESC',
            'limit' => -1,
            'offset' => 0,
            'search' => '',
            'tour_type' => '',
            'country' => '',
            'payment_status' => ''
        ];

        $args = wp_parse_args($args, $defaults);

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

        if (!$table_exists) {
            // Create the table if it doesn't exist
            self::create_tables();

            // Check again if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if (!$table_exists) {
                // If still doesn't exist, return empty result
                return [
                    'participants' => [],
                    'total' => 0,
                    'source' => 'none'
                ];
            }
        }

        // Build query
        $query = "SELECT * FROM $table_name";
        $count_query = "SELECT COUNT(*) FROM $table_name";
        $where = [];

        // Add search condition
        if (!empty($args['search'])) {
            $search = '%' . $wpdb->esc_like($args['search']) . '%';
            $where[] = $wpdb->prepare("(first_name LIKE %s OR last_name LIKE %s OR email LIKE %s)",
                $search, $search, $search);
        }

        // Add tour type filter
        if (!empty($args['tour_type'])) {
            switch ($args['tour_type']) {
                case 'main_pretour':
                    $where[] = "main_pretour = 1";
                    break;
                case 'legends_wildlife':
                    $where[] = "legends_wildlife = 1";
                    break;
                case 'royal_elegance':
                    $where[] = "royal_elegance = 1";
                    break;
                case 'brasov_highlights':
                    $where[] = "brasov_highlights = 1";
                    break;
            }
            self::log("Added tour filter: " . $args['tour_type'], "info");
        }

        // Add country filter
        if (!empty($args['country'])) {
            $where[] = $wpdb->prepare("country = %s", $args['country']);
            self::log("Added country filter: " . $args['country'], "info");
        }

        // Add payment status filter
        if (!empty($args['payment_status'])) {
            $where[] = $wpdb->prepare("payment_status = %s", $args['payment_status']);
            self::log("Added payment status filter: " . $args['payment_status'], "info");
        }

        // Combine WHERE clauses
        if (!empty($where)) {
            $query .= " WHERE " . implode(" AND ", $where);
            $count_query .= " WHERE " . implode(" AND ", $where);
        }

        // Add ORDER BY
        if (!empty($args['orderby'])) {
            $query .= " ORDER BY {$args['orderby']} {$args['order']}";
        }

        // Add LIMIT
        if ($args['limit'] > 0) {
            $query .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['limit']);
        }

        // Get total count
        $total = $wpdb->get_var($count_query);

        // Get participants
        $participants = $wpdb->get_results($query, ARRAY_A);

        // Log the results
        self::log("Found " . count($participants) . " participants", "info");
        if (count($participants) > 0) {
            self::log("First participant: " . print_r($participants[0], true), "info");
        }

        // Log the SQL query
        self::log("SQL Query: " . $query, "info");

        // Check for SQL errors
        if ($wpdb->last_error) {
            self::log("SQL Error: " . $wpdb->last_error, "error");
        }

        // Ensure all fields are properly formatted for JSON
        foreach ($participants as &$participant) {
            // Convert numeric strings to numbers
            foreach (['id', 'user_id', 'order_id'] as $field) {
                if (isset($participant[$field])) {
                    $participant[$field] = intval($participant[$field]);
                }
            }

            // Convert boolean fields
            foreach (['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'] as $field) {
                if (isset($participant[$field])) {
                    $participant[$field] = (bool)$participant[$field];
                }
            }

            // Ensure date fields are properly formatted
            foreach (['order_date', 'created_at', 'updated_at'] as $field) {
                if (isset($participant[$field])) {
                    // Ensure date is in ISO format
                    $date = strtotime($participant[$field]);
                    if ($date) {
                        $participant[$field] = date('c', $date);
                    }
                }
            }
        }

        return [
            'participants' => $participants,
            'total' => $total,
            'source' => 'database'
        ];
    }

    /**
     * Sync tours participants from WooCommerce orders
     *
     * @param bool $debug Whether to include debug information in the result
     * @return array Result of sync operation
     */
    public static function sync_tours_participants($debug = false) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_tours_unified';

        // Log table structure for debugging
        if ($debug) {
            self::log("Starting tours sync process", "info");

            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
            self::log("Table exists check: " . ($table_exists ? "Yes" : "No"), "info");

            if ($table_exists) {
                $table_structure = $wpdb->get_results("DESCRIBE $table_name");
                self::log("Tours Table Structure: " . print_r($table_structure, true), "info");
            }
        }

        // Always ensure the table exists before syncing
        self::create_tables();

        // Get all orders
        $orders = wc_get_orders([
            'limit' => -1,
            'status' => ['processing', 'completed', 'on-hold'],
            'return' => 'ids',
        ]);

        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'total_count' => count($orders),
            'errors' => [],
            'main_pretour_count' => 0,
            'legends_wildlife_count' => 0,
            'royal_elegance_count' => 0,
            'brasov_highlights_count' => 0
        ];

        // Process each order
        foreach ($orders as $order_id) {
            $order = wc_get_order($order_id);

            if (!$order) {
                $result['error_count']++;
                $result['errors'][] = "Order #$order_id not found";
                continue;
            }

            // Check for tour product IDs in this order
            $has_main_pretour = false;
            $has_legends_wildlife = false;
            $has_royal_elegance = false;
            $has_brasov_highlights = false;

            foreach ($order->get_items() as $item) {
                $product_id = $item->get_product_id();

                if ($product_id == self::MAIN_PRETOUR_ID) {
                    $has_main_pretour = true;
                    $result['main_pretour_count']++;
                } elseif ($product_id == self::LEGENDS_WILDLIFE_ID) {
                    $has_legends_wildlife = true;
                    $result['legends_wildlife_count']++;
                } elseif ($product_id == self::ROYAL_ELEGANCE_ID) {
                    $has_royal_elegance = true;
                    $result['royal_elegance_count']++;
                } elseif ($product_id == self::BRASOV_HIGHLIGHTS_ID) {
                    $has_brasov_highlights = true;
                    $result['brasov_highlights_count']++;
                }
            }

            // Skip orders without any tour products
            if (!$has_main_pretour && !$has_legends_wildlife && !$has_royal_elegance && !$has_brasov_highlights) {
                continue;
            }

            // Get user ID from order
            $user_id = $order->get_user_id();

            // Get country information
            $country_code = $order->get_billing_country();
            $country_name = WC()->countries->get_countries()[$country_code] ?? $country_code;

            // Prepare data for insertion/update
            $now = current_time('mysql');
            $data = [
                'user_id' => $user_id,
                'order_id' => $order_id,
                'first_name' => $order->get_billing_first_name(),
                'last_name' => $order->get_billing_last_name(),
                'email' => $order->get_billing_email(),
                'phone' => $order->get_billing_phone(),
                'country' => $country_name,
                'country_code' => $country_code,
                'order_date' => $order->get_date_created() ? $order->get_date_created()->date('Y-m-d H:i:s') : $now,
                'payment_status' => $order->get_status(),
                'main_pretour' => $has_main_pretour ? 1 : 0,
                'legends_wildlife' => $has_legends_wildlife ? 1 : 0,
                'royal_elegance' => $has_royal_elegance ? 1 : 0,
                'brasov_highlights' => $has_brasov_highlights ? 1 : 0,
                'updated_at' => $now
            ];

            // Make sure we have required fields
            if (empty($data['email'])) {
                $result['error_count']++;
                $error_msg = "Error processing order #$order_id: Missing email address";
                $result['errors'][] = $error_msg;
                self::log($error_msg, "error");
                continue;
            }

            // Log the data we're about to insert/update
            if ($debug) {
                self::log("Processing order #$order_id with data: " . print_r($data, true), "info");
            }

            try {
                // Check if this order is already in the tours table
                $existing = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $table_name WHERE order_id = %d",
                    $order_id
                ));

                if ($existing) {
                    // Update existing record
                    $data['updated_at'] = $now;

                    self::log("Updating existing record for order #$order_id with data: " . print_r($data, true), "info");

                    // Log the SQL query that will be executed
                    $wpdb->update(
                        $table_name,
                        $data,
                        ['id' => $existing->id]
                    );

                    self::log("Update SQL: " . $wpdb->last_query, "info");

                    if ($wpdb->last_error) {
                        throw new Exception($wpdb->last_error);
                    }

                    // Verify the update
                    $updated_record = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $table_name WHERE id = %d",
                        $existing->id
                    ));

                    if ($updated_record) {
                        self::log("Record updated successfully for order #$order_id", "info");
                    } else {
                        throw new Exception("Failed to verify updated record");
                    }
                } else {
                    // Insert new record
                    $data['created_at'] = $now;

                    self::log("Inserting new record for order #$order_id with data: " . print_r($data, true), "info");

                    $insert_result = $wpdb->insert($table_name, $data);

                    self::log("Insert SQL: " . $wpdb->last_query, "info");

                    if ($insert_result === false) {
                        throw new Exception($wpdb->last_error ?: "Unknown database error during insert");
                    }

                    // Get the inserted ID
                    $insert_id = $wpdb->insert_id;
                    self::log("Inserted record with ID: $insert_id", "info");

                    // Verify the insert
                    $inserted_record = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $table_name WHERE id = %d",
                        $insert_id
                    ));

                    if ($inserted_record) {
                        self::log("Record inserted successfully for order #$order_id", "info");
                    } else {
                        throw new Exception("Failed to verify inserted record");
                    }
                }

                $result['success_count']++;
            } catch (Exception $e) {
                $result['error_count']++;
                $error_message = "Error processing order #$order_id: " . $e->getMessage();
                $result['errors'][] = $error_message;
                self::log($error_message, "error");

                // Log additional details for debugging
                if ($debug) {
                    $trace = $e->getTraceAsString();
                    self::log("Exception trace for order #$order_id: " . $trace, "error");

                    // Log the SQL query if available
                    if (!empty($wpdb->last_query)) {
                        self::log("Last SQL query: " . $wpdb->last_query, "error");
                    }
                }
            }
        }

        return $result;
    }

    /**
     * AJAX handler for syncing tours participants
     */
    public static function ajax_sync_tours() {
        self::log("AJAX sync request received", "info");

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            self::log("Invalid nonce in sync request", "error");
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            self::log("Permission denied for user: " . get_current_user_id(), "error");
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        try {
            self::log("Starting sync process", "info");

            // Ensure the table exists
            self::create_tables();

            // Sync participants with debug mode enabled
            self::log("Calling sync_tours_participants with debug mode", "info");
            $result = self::sync_tours_participants(true);

            // Get the first few errors to display
            $error_sample = array_slice($result['errors'], 0, 5);
            $error_message = '';

            if (!empty($error_sample)) {
                $error_message = ' Sample errors: ' . implode('; ', $error_sample);
                if (count($result['errors']) > 5) {
                    $error_message .= ' (and ' . (count($result['errors']) - 5) . ' more)';
                }
            }

            $response_message = sprintf(
                'Sync completed. Processed %d orders: %d successful, %d errors. Found %d Main Pretour, %d Legends & Wildlife, %d Royal Elegance, %d Brasov Highlights.%s',
                $result['total_count'],
                $result['success_count'],
                $result['error_count'],
                $result['main_pretour_count'],
                $result['legends_wildlife_count'],
                $result['royal_elegance_count'],
                $result['brasov_highlights_count'],
                $error_message
            );

            self::log("Sync completed with results: " . $response_message, "info");

            wp_send_json_success([
                'message' => $response_message,
                'result' => $result
            ]);
        } catch (Exception $e) {
            $error_msg = 'Error syncing participants: ' . $e->getMessage();
            self::log($error_msg, "error");
            self::log("Exception trace: " . $e->getTraceAsString(), "error");

            wp_send_json_error([
                'message' => $error_msg
            ]);
        }
    }

    /**
     * AJAX handler for getting tours participants
     */
    public static function ajax_get_tours() {
        self::log("AJAX get tours request received", "info");

        // Check nonce
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'lci_admin_nonce')) {
            self::log("Invalid nonce in get tours request", "error");
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            self::log("Permission denied for user: " . get_current_user_id(), "error");
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        try {
            self::log("Starting get tours process", "info");

            // Ensure the table exists
            self::create_tables();

            // Direct check to verify data exists in the table
            global $wpdb;
            $table_name = $wpdb->prefix . 'lci2025_tours_unified';
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            self::log("Direct database check: Table has $count records", "info");

            if ($count > 0) {
                $sample = $wpdb->get_row("SELECT * FROM $table_name LIMIT 1", ARRAY_A);
                self::log("Sample record from direct query: " . print_r($sample, true), "info");
            }

            // Get query parameters
            $search = isset($_REQUEST['search']) ? sanitize_text_field($_REQUEST['search']) : '';
            $tour_type = isset($_REQUEST['tour_type']) ? sanitize_text_field($_REQUEST['tour_type']) : '';
            $country = isset($_REQUEST['country']) ? sanitize_text_field($_REQUEST['country']) : '';
            $payment_status = isset($_REQUEST['payment_status']) ? sanitize_text_field($_REQUEST['payment_status']) : '';
            $per_page = isset($_REQUEST['per_page']) ? intval($_REQUEST['per_page']) : -1; // -1 means no pagination

            // Log the filter parameters
            self::log("Filter parameters - Search: '$search', Tour: '$tour_type', Country: '$country', Status: '$payment_status', No pagination (all records)", "info");

            // No offset needed for no pagination
            $offset = 0;

            // Get participants
            $result = self::get_tours_participants([
                'search' => $search,
                'tour_type' => $tour_type,
                'country' => $country,
                'payment_status' => $payment_status,
                'limit' => $per_page,
                'offset' => $offset
            ]);

            // If no participants found but we know they exist, add a manual check
            if (empty($result['participants']) && $count > 0) {
                self::log("No participants returned from get_tours_participants but direct query shows $count records", "warning");

                // Try a direct query as a fallback
                global $wpdb;
                $table_name = $wpdb->prefix . 'lci2025_tours_unified';
                $direct_participants = $wpdb->get_results("SELECT * FROM $table_name", ARRAY_A);

                self::log("Direct query fallback found " . count($direct_participants) . " participants", "info");

                if (!empty($direct_participants)) {
                    $result['participants'] = $direct_participants;
                    $result['total'] = $count;
                    $result['source'] = 'direct_query';
                }
            }

            $response_data = [
                'participants' => $result['participants'],
                'total' => $result['total'],
                'source' => $result['source'],
                'direct_count' => $count // Add the direct count for debugging
            ];

            self::log("Returning " . count($result['participants']) . " participants, total: " . $result['total'], "info");

            // Log the first participant if available
            if (!empty($result['participants'])) {
                self::log("First participant in response: " . print_r($result['participants'][0], true), "info");
            } else {
                self::log("No participants found in the database", "warning");
            }

            wp_send_json_success($response_data);
        } catch (Exception $e) {
            $error_msg = 'Error getting participants: ' . $e->getMessage();
            self::log($error_msg, "error");
            self::log("Exception trace: " . $e->getTraceAsString(), "error");

            wp_send_json_error([
                'message' => $error_msg
            ]);
        }
    }

    /**
     * AJAX handler for getting all countries from the tours table
     */
    public static function ajax_get_tours_countries() {
        // Check nonce
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        try {
            self::log("Getting all countries from tours table", "info");

            global $wpdb;
            $table_name = $wpdb->prefix . 'lci2025_tours_unified';

            // Check if table exists
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
                self::log("Table does not exist, cannot get countries", "error");
                wp_send_json_error(['message' => 'Table does not exist']);
                return;
            }

            // Get all unique countries
            $query = "SELECT DISTINCT country FROM $table_name WHERE country IS NOT NULL AND country != '' ORDER BY country ASC";
            $countries = $wpdb->get_col($query);

            self::log("Found " . count($countries) . " unique countries", "info");

            wp_send_json_success([
                'countries' => $countries
            ]);
        } catch (Exception $e) {
            $error_msg = 'Error getting countries: ' . $e->getMessage();
            self::log($error_msg, "error");

            wp_send_json_error([
                'message' => $error_msg
            ]);
        }
    }

    /**
     * AJAX handler for clearing the tours table
     */
    public static function ajax_clear_tours_table() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_tours_unified';

        self::log("Clearing tours table", "info");

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            self::log("Table does not exist, cannot clear", "error");
            wp_send_json_error(['message' => 'Table does not exist']);
            return;
        }

        // Truncate the table
        $result = $wpdb->query("TRUNCATE TABLE $table_name");
        self::log("Truncate query executed", "info");

        if ($result === false) {
            $error = $wpdb->last_error ?: "Unknown error";
            self::log("Error clearing table: " . $error, "error");
            wp_send_json_error([
                'message' => 'Error clearing table: ' . $error
            ]);
            return;
        }

        self::log("Table cleared successfully", "info");

        wp_send_json_success([
            'message' => 'Tours table cleared successfully'
        ]);
    }

    /**
     * AJAX handler for recreating the tours table
     */
    public static function ajax_recreate_tours_table() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        try {
            self::log("Recreating tours table", "info");

            global $wpdb;
            $table_name = $wpdb->prefix . 'lci2025_tours_unified';

            // Force drop the table
            $wpdb->query("DROP TABLE IF EXISTS $table_name");
            self::log("Table dropped", "info");

            // Create the table with the latest structure
            self::create_tables(true);

            // Verify the table was created
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if ($table_exists) {
                self::log("Table recreated successfully", "info");

                wp_send_json_success([
                    'message' => 'Tours table recreated successfully'
                ]);
            } else {
                throw new Exception("Failed to recreate table");
            }
        } catch (Exception $e) {
            self::log("Error recreating table: " . $e->getMessage(), "error");

            wp_send_json_error([
                'message' => 'Error recreating table: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for updating a tour participant
     */
    public static function ajax_update_tour() {
        self::log("AJAX update tour request received", "info");

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            self::log("Invalid nonce in update request", "error");
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            self::log("Permission denied for user: " . get_current_user_id(), "error");
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        // Get participant ID
        $participant_id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        self::log("Updating participant ID: $participant_id", "info");

        if (!$participant_id) {
            self::log("Invalid participant ID", "error");
            wp_send_json_error(['message' => 'Invalid participant ID']);
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_tours_unified';

        // Check if participant exists
        $participant = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $participant_id
        ));

        if (!$participant) {
            self::log("Participant not found with ID: $participant_id", "error");
            wp_send_json_error(['message' => 'Participant not found']);
            return;
        }

        self::log("Found participant to update: " . print_r($participant, true), "info");

        // Get fields to update
        $fields = [
            'first_name', 'last_name', 'email', 'phone', 'country',
            'payment_status', 'main_pretour', 'legends_wildlife',
            'royal_elegance', 'brasov_highlights', 'user_id'
        ];

        $data = [];
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                if ($field === 'user_id') {
                    // Ensure user_id is an integer
                    $data[$field] = intval($_POST[$field]);
                } elseif (in_array($field, ['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'])) {
                    // Ensure boolean fields are 0 or 1
                    $data[$field] = !empty($_POST[$field]) ? 1 : 0;
                } else {
                    $data[$field] = sanitize_text_field($_POST[$field]);
                }
            }
        }

        self::log("Update data: " . print_r($data, true), "info");

        // Add updated_at timestamp
        $data['updated_at'] = current_time('mysql');

        // Update participant
        $result = $wpdb->update(
            $table_name,
            $data,
            ['id' => $participant_id]
        );

        self::log("Update SQL: " . $wpdb->last_query, "info");

        if ($result === false) {
            $error = $wpdb->last_error ?: "Unknown error";
            self::log("Error updating participant: " . $error, "error");
            wp_send_json_error([
                'message' => 'Error updating participant: ' . $error
            ]);
            return;
        }

        // Get the updated participant
        $updated_participant = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $participant_id
        ), ARRAY_A);

        self::log("Participant updated successfully: " . print_r($updated_participant, true), "info");

        wp_send_json_success([
            'message' => 'Participant updated successfully',
            'participant' => $updated_participant
        ]);
    }

    /**
     * AJAX handler for deleting a tour participant
     */
    public static function ajax_delete_tour() {
        self::log("AJAX delete tour request received", "info");

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            self::log("Invalid nonce in delete request", "error");
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            self::log("Permission denied for user: " . get_current_user_id(), "error");
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        // Get participant ID
        $participant_id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        self::log("Deleting participant ID: $participant_id", "info");

        if (!$participant_id) {
            self::log("Invalid participant ID", "error");
            wp_send_json_error(['message' => 'Invalid participant ID']);
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_tours_unified';

        // Check if participant exists before deleting
        $participant = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $participant_id
        ));

        if (!$participant) {
            self::log("Participant not found with ID: $participant_id", "error");
            wp_send_json_error(['message' => 'Participant not found']);
            return;
        }

        self::log("Found participant to delete: " . print_r($participant, true), "info");

        // Delete participant
        $result = $wpdb->delete(
            $table_name,
            ['id' => $participant_id],
            ['%d']
        );

        self::log("Delete SQL: " . $wpdb->last_query, "info");

        if ($result === false) {
            $error = $wpdb->last_error ?: "Unknown error";
            self::log("Error deleting participant: " . $error, "error");
            wp_send_json_error([
                'message' => 'Error deleting participant: ' . $error
            ]);
            return;
        }

        self::log("Participant deleted successfully", "info");

        wp_send_json_success([
            'message' => 'Participant deleted successfully'
        ]);
    }
}
