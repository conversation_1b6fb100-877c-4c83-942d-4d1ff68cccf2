/**
 * Tours Mini Cart
 *
 * Complete replacement for the mini cart on the tours page
 */
(function($) {
    'use strict';

    // Check if we're on the tours page
    function isToursPage() {
        // Check if the URL contains 'tab=tours'
        if (window.location.href.indexOf('tab=tours') > -1) {
            return true;
        }

        // Check if there's a tours container on the page
        if (document.querySelector('.tours-container')) {
            return true;
        }

        return false;
    }

    // If we're not on the tours page, don't do anything
    if (!isToursPage()) {
        // Not on tours page, skip initialization
        return;
    }

    // On tours page, initialize tours mini cart

    // Global variables
    let cartCount = 0;
    let cartTotal = '0.00 €';
    let cartItems = [];
    let isCartOpen = false;
    let isLoading = false;

    // Get the AJAX object - try both lci_ajax_object and lci_ajax
    let ajaxObject = window.lci_ajax_object || window.lci_ajax || {};

    // Make sure we have the required properties
    if (!ajaxObject.ajax_url) {
        // AJAX URL not found, try fallbacks
        if (window.lci_ajax) {
            ajaxObject.ajax_url = window.lci_ajax.ajax_url;
        } else {
            ajaxObject.ajax_url = window.ajaxurl || '/wp-admin/admin-ajax.php';
        }
    }

    if (!ajaxObject.nonce) {
        // Nonce not found, try fallbacks
        if (window.lci_ajax) {
            ajaxObject.nonce = window.lci_ajax.nonce;
        } else {
            // Create a random nonce as a last resort
            ajaxObject.nonce = Math.random().toString(36).substring(2, 15);
        }
    }

    // Use the determined AJAX object

    // Function to initialize the tours mini cart
    function initToursMiniCart() {
        // Initialize tours mini cart

        // Find and remove the Alpine.js mini cart
        const alpineCart = document.getElementById('mini-cart-container');
        if (alpineCart) {
            // Get the cart data before removing
            try {
                if (alpineCart.__x) {
                    const data = alpineCart.__x.getUnobservedData();
                    cartCount = data.count || 0;
                    cartTotal = data.total || '0.00 €';
                }
            } catch (error) {
                // Error handling
            }

            // Remove the Alpine.js mini cart
            alpineCart.remove();
        }

        // Create our own mini cart
        createToursMiniCart();

        // Fetch cart items
        fetchCartItems();

        // Listen for cart updates
        window.addEventListener('lci:cartUpdated', handleCartUpdate);
    }

    // Function to create the tours mini cart
    function createToursMiniCart() {
        // Create the mini cart button
        const miniCartButton = document.createElement('button');
        miniCartButton.id = 'tours-mini-cart-button';
        miniCartButton.className = 'btn d-flex align-items-center';
        miniCartButton.style.backgroundColor = '#36b1dc';
        miniCartButton.style.borderColor = '#36b1dc';
        miniCartButton.style.color = '#fff !important';
        miniCartButton.innerHTML = `
            <i class="fas fa-shopping-bag me-2" style="color: #fff !important;"></i>
            <span class="tours-mini-cart-total">${cartTotal}</span>
            <span class="badge rounded-pill ms-2" style="background-color: #fab33a; display: ${cartCount > 0 ? '' : 'none'};">${cartCount}</span>
        `;
        miniCartButton.addEventListener('click', openMiniCart);

        // Create the mini cart container
        const miniCartContainer = document.createElement('div');
        miniCartContainer.id = 'tours-mini-cart-container';
        miniCartContainer.className = 'tours-mini-cart-container';
        miniCartContainer.appendChild(miniCartButton);

        // Create the mini cart modal
        const miniCartModal = document.createElement('div');
        miniCartModal.id = 'tours-mini-cart-modal';
        miniCartModal.className = 'lci-modal-backdrop';
        miniCartModal.style.position = 'fixed';
        miniCartModal.style.top = '0';
        miniCartModal.style.left = '0';
        miniCartModal.style.right = '0';
        miniCartModal.style.bottom = '0';
        miniCartModal.style.backgroundColor = 'rgba(0,0,0,0.5)';
        miniCartModal.style.zIndex = '9999';
        miniCartModal.style.display = 'none';
        miniCartModal.style.alignItems = 'center';
        miniCartModal.style.justifyContent = 'center';
        miniCartModal.style.overflowY = 'auto';
        miniCartModal.style.padding = '20px 0';
        miniCartModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeMiniCart();
            }
        });

        // Create the mini cart modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'lci-modal lci-mini-cart-modal';
        modalContent.style.backgroundColor = 'white';
        modalContent.style.borderRadius = '12px';
        modalContent.style.overflow = 'hidden';
        modalContent.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.12)';
        modalContent.style.maxWidth = '700px';
        modalContent.style.width = '95%';

        // Create the mini cart modal header
        const modalHeader = document.createElement('div');
        modalHeader.className = 'lci-modal-header';
        modalHeader.style.backgroundColor = 'white';
        modalHeader.style.boxShadow = '0 4px 6px rgba(0,0,0,0.05)';
        modalHeader.style.position = 'relative';
        modalHeader.innerHTML = `
            <h2 class="lci-modal-title" style="color: #36b1dc; font-weight: 600; margin: 0 auto; padding: 1rem 0;">
                <i class="fas fa-shopping-bag me-2"></i> LCI AGM 2025
            </h2>
            <button id="tours-mini-cart-close" class="lci-modal-close" style="position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; font-size: 1.25rem; color: #6c757d; transition: all 0.2s ease;">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Create the mini cart modal body
        const modalBody = document.createElement('div');
        modalBody.className = 'lci-modal-body p-0';
        modalBody.innerHTML = `
            <div id="tours-mini-cart-items-container" class="mini-cart-items-container">
                <div id="tours-mini-cart-loading" class="text-center py-5" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading your items...</p>
                </div>
                <div id="tours-mini-cart-empty" class="text-center py-5" style="display: none;">
                    <i class="fas fa-shopping-bag text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-3">Your goodies bag is empty</p>
                </div>
                <div id="tours-mini-cart-items" class="cart-items-list"></div>
            </div>
        `;

        // Create the fundraising message container
        const fundraisingContainer = document.createElement('div');
        fundraisingContainer.id = 'tours-fundraising-container';

        // Create the mini cart modal footer
        const modalFooter = document.createElement('div');
        modalFooter.className = 'lci-modal-footer';
        modalFooter.innerHTML = `
            <div class="d-flex justify-content-between align-items-center w-100">
                <div class="mini-cart-total-container">
                    Total: <span id="tours-mini-cart-modal-total" class="mini-cart-modal-total">${cartTotal}</span>
                </div>
                <div>
                    <button id="tours-mini-cart-continue" class="lci-btn lci-btn-secondary me-2">Continue Shopping</button>
                    <a href="${window.location.pathname}?tab=payment" class="lci-btn lci-btn-primary">
                        <i class="fas fa-credit-card me-1"></i> Checkout
                    </a>
                </div>
            </div>
        `;

        // Assemble the mini cart modal
        modalContent.appendChild(modalHeader);
        modalContent.appendChild(modalBody);
        modalContent.appendChild(fundraisingContainer);
        modalContent.appendChild(modalFooter);
        miniCartModal.appendChild(modalContent);
        miniCartContainer.appendChild(miniCartModal);

        // Find the mini cart container in the DOM
        const headerContainer = document.querySelector('.tours-container .d-flex.justify-content-between.align-items-center.mb-4');
        if (headerContainer) {
            // Find the existing mini cart container
            const existingMiniCart = headerContainer.querySelector('.mini-cart-container, #mini-cart-container');
            if (existingMiniCart) {
                // Replace the existing mini cart with our own
                existingMiniCart.replaceWith(miniCartContainer);
            } else {
                // Add our mini cart to the header
                headerContainer.appendChild(miniCartContainer);
            }

            // Add event listeners only if we successfully added the mini cart
            const closeButton = document.getElementById('tours-mini-cart-close');
            if (closeButton) {
                closeButton.addEventListener('click', closeMiniCart);
            }

            const continueButton = document.getElementById('tours-mini-cart-continue');
            if (continueButton) {
                continueButton.addEventListener('click', closeMiniCart);
            }
        } else {
            // Header container not found - don't try to add event listeners
            return false;
        }

        // Tours mini cart created successfully
    }

    // Function to open the mini cart
    function openMiniCart() {
        // Opening tours mini cart
        isCartOpen = true;
        document.getElementById('tours-mini-cart-modal').style.display = 'flex';
        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';
        fetchCartItems();
        updateFundraisingMessage();
    }

    // Function to close the mini cart
    function closeMiniCart() {
        // Closing tours mini cart
        isCartOpen = false;
        document.getElementById('tours-mini-cart-modal').style.display = 'none';
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('overflow');
    }

    // Function to update the fundraising message
    function updateFundraisingMessage() {
        const fundraisingContainer = document.getElementById('tours-fundraising-container');
        if (!fundraisingContainer) return;

        // Only show fundraising message if cart has items
        if (cartCount === 0) {
            fundraisingContainer.innerHTML = '';
            return;
        }

        // Make AJAX request to get fundraising message
        $.ajax({
            url: ajaxObject.ajax_url,
            type: 'POST',
            data: {
                action: 'lci_get_fundraising_message',
                nonce: ajaxObject.nonce,
                security: ajaxObject.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    // If response data is empty, clear the container
                    if (!response.data || response.data.trim() === '') {
                        fundraisingContainer.innerHTML = '';
                    } else {
                        // Add the fundraising message
                        fundraisingContainer.innerHTML = response.data;
                    }
                }
            },
            error: function(error) {
                // Error handling
                fundraisingContainer.innerHTML = '';
            }
        });
    }

    // Function to fetch cart items
    function fetchCartItems() {
        if (isLoading) return;
        isLoading = true;

        // Show loading state
        document.getElementById('tours-mini-cart-loading').style.display = 'block';
        document.getElementById('tours-mini-cart-empty').style.display = 'none';
        document.getElementById('tours-mini-cart-items').style.display = 'none';

        // Fetching cart items

        // Make AJAX request to get cart items
        $.ajax({
            url: ajaxObject.ajax_url,
            type: 'POST',
            data: {
                action: 'lci_get_mini_cart_items',
                nonce: ajaxObject.nonce,
                security: ajaxObject.nonce // Add security parameter as fallback
            },
            success: function(response) {
                // Process cart items response
                isLoading = false;

                if (response.success) {
                    cartItems = response.data.items || [];
                    cartTotal = response.data.total || '0.00 €';
                    cartCount = response.data.count || 0;

                    // Update the cart button
                    updateCartButton();

                    // Update the cart items
                    updateCartItems();

                    // Update the fundraising message
                    updateFundraisingMessage();
                } else {
                    // Show empty cart
                    document.getElementById('tours-mini-cart-loading').style.display = 'none';
                    document.getElementById('tours-mini-cart-empty').style.display = 'block';
                    document.getElementById('tours-mini-cart-items').style.display = 'none';
                }
            },
            error: function(error) {
                // Error handling
                isLoading = false;

                // Show empty cart
                document.getElementById('tours-mini-cart-loading').style.display = 'none';
                document.getElementById('tours-mini-cart-empty').style.display = 'block';
                document.getElementById('tours-mini-cart-items').style.display = 'none';
            }
        });
    }

    // Function to update the cart button
    function updateCartButton() {
        const totalElement = document.querySelector('.tours-mini-cart-total');
        if (totalElement) {
            totalElement.innerHTML = cartTotal;
        }

        const badgeElement = document.querySelector('#tours-mini-cart-button .badge');
        if (badgeElement) {
            badgeElement.textContent = cartCount;
            badgeElement.style.display = cartCount > 0 ? '' : 'none';
        }

        const modalTotalElement = document.getElementById('tours-mini-cart-modal-total');
        if (modalTotalElement) {
            modalTotalElement.innerHTML = cartTotal;
        }
    }

    // Function to update the cart items
    function updateCartItems() {
        const itemsContainer = document.getElementById('tours-mini-cart-items');
        if (!itemsContainer) return;

        // Clear the items container
        itemsContainer.innerHTML = '';

        // Check if cart is empty
        if (cartItems.length === 0) {
            document.getElementById('tours-mini-cart-loading').style.display = 'none';
            document.getElementById('tours-mini-cart-empty').style.display = 'block';
            document.getElementById('tours-mini-cart-items').style.display = 'none';
            return;
        }

        // Add items to the container
        cartItems.forEach(function(item) {
            const itemElement = document.createElement('div');
            itemElement.className = 'mini-cart-item d-flex align-items-center p-3 border-bottom';
            itemElement.innerHTML = `
                <div class="mini-cart-item-image me-2">
                    <img src="${item.image}" alt="${item.name}" class="img-fluid rounded" style="width: 50px; height: 50px; object-fit: contain;">
                </div>
                <div class="mini-cart-item-quantity-container me-2">
                    <span class="mini-cart-item-quantity badge bg-light text-dark">${item.quantity} ×</span>
                </div>
                <div class="mini-cart-item-details flex-grow-1 d-flex align-items-center justify-content-between">
                    <div class="mini-cart-item-name-container">
                        <span class="mini-cart-item-name">${item.name}</span>
                    </div>
                    <div class="mini-cart-item-price-container">
                        <span class="mini-cart-item-price text-primary fw-bold">${item.price}</span>
                    </div>
                </div>
                <div class="mini-cart-item-remove ms-2">
                    <a href="#" class="remove-cart-item" data-key="${item.key}" style="color: #36b1dc; display: inline-flex; align-items: center; justify-content: center; width: 24px; height: 24px; border-radius: 50%; transition: all 0.2s ease;">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            `;
            itemsContainer.appendChild(itemElement);

            // Add event listener to remove button
            const removeButton = itemElement.querySelector('.remove-cart-item');
            if (removeButton) {
                removeButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    removeCartItem(this.dataset.key);
                });
            }
        });

        // Show the items container
        document.getElementById('tours-mini-cart-loading').style.display = 'none';
        document.getElementById('tours-mini-cart-empty').style.display = 'none';
        document.getElementById('tours-mini-cart-items').style.display = 'block';
    }

    // Function to remove a cart item
    function removeCartItem(key) {
        if (isLoading) return;
        isLoading = true;

        // Removing cart item

        // Make AJAX request to remove cart item
        $.ajax({
            url: ajaxObject.ajax_url,
            type: 'POST',
            data: {
                action: 'lci_remove_cart_item',
                nonce: ajaxObject.nonce,
                security: ajaxObject.nonce, // Add security parameter as fallback
                cart_item_key: key
            },
            success: function(response) {
                // Process remove cart item response
                isLoading = false;

                if (response.success) {
                    // Update cart data
                    cartTotal = response.cart_total || '0.00 €';
                    cartCount = response.cart_count || 0;

                    // Update the cart button
                    updateCartButton();

                    // Fetch cart items
                    fetchCartItems();

                    // Trigger events
                    $(document.body).trigger('removed_from_cart');
                    $(document.body).trigger('updated_cart_totals');
                    window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
                        detail: {
                            cart_count: cartCount,
                            cart_total: cartTotal
                        }
                    }));
                } else {
                    // Fetch cart items
                    fetchCartItems();
                }
            },
            error: function(error) {
                // Error handling
                isLoading = false;

                // Fetch cart items
                fetchCartItems();
            }
        });
    }

    // Function to handle cart updates
    function handleCartUpdate(event) {
        // Cart updated, process event

        // Update cart data
        if (event.detail) {
            if (event.detail.cart_count !== undefined) {
                cartCount = event.detail.cart_count;
            }
            if (event.detail.cart_total) {
                cartTotal = event.detail.cart_total;
            }
            if (event.detail.items) {
                cartItems = event.detail.items;
            }
        }

        // Update the cart button
        updateCartButton();

        // Update the cart items if the cart is open
        if (isCartOpen) {
            updateCartItems();
        }
    }

    // Initialize the tours mini cart when the DOM is ready
    $(document).ready(function() {
        // Initialize after DOM is ready

        // Wait a bit to ensure Alpine.js has initialized
        setTimeout(initToursMiniCart, 500);
    });
})(jQuery);
