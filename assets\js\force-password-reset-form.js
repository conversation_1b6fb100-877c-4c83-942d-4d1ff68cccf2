/**
 * Force Password Reset Form Display
 *
 * This script forces the password reset form to be displayed when the page loads
 * with the appropriate URL parameters.
 *
 * Note: Most of this functionality has been moved to auth.js for better integration.
 * This script now serves as a backup to ensure the form is displayed correctly.
 */
(function() {
    console.log('Force password reset form script loaded (backup)');

    // Check if this is a password reset URL
    const urlParams = new URLSearchParams(window.location.search);
    const key = urlParams.get('key');
    const login = urlParams.get('login');
    const action = urlParams.get('action');
    const showResetForm = urlParams.get('show-reset-form');

    // Log all URL parameters for debugging
    console.log('Backup script - URL params:', {
        key,
        login,
        action,
        showResetForm,
        fullUrl: window.location.href,
        search: window.location.search
    });

    // Only proceed if this is a password reset URL
    // For the lost-password page, we don't need key and login
    const isLostPasswordPage = window.location.href.includes('lost-password');
    const isResetUrl = (isLostPasswordPage && (action === 'reset_password' || showResetForm === 'true')) ||
                      (key && login && (action === 'reset_password' || showResetForm === 'true'));

    if (!isResetUrl) {
        console.log('Not a password reset URL, backup script will not modify forms');
        return; // Exit the script if not a reset URL
    }

    console.log('Password reset URL detected in backup script');

    // Function to force display of the change password form
    function forceDisplayChangeForm() {
        console.log('Backup script attempting to force display of change password form');

        // Get the forms
        const changeForm = document.getElementById('lci-change-password-form');
        const loginForm = document.getElementById('lci-login-form');

        if (changeForm && loginForm) {
            console.log('Forms found in backup script');

            // Check if the change form is already visible
            const isChangeFormVisible =
                !changeForm.classList.contains('d-none') &&
                changeForm.style.display !== 'none' &&
                changeForm.style.opacity !== '0';

            if (isChangeFormVisible) {
                console.log('Change form is already visible, no action needed');
                return;
            }

            console.log('Forcing display of change password form from backup script');

            // Hide login form
            loginForm.classList.add('d-none');

            // Show change password form
            changeForm.classList.remove('d-none');
            changeForm.style.display = 'block';
            changeForm.style.opacity = '1';
            changeForm.style.transform = 'translateY(0)';

            // Set the email field
            const emailField = document.getElementById('lci-change-email');

            // Check if we're on the lost-password page
            const isOnLostPasswordPage = window.location.href.includes('lost-password');

            // ONLY prompt if we're on the lost-password page AND don't have a login AND don't have a key in the URL
            const urlParams = new URLSearchParams(window.location.search);
            const hasKeyInUrl = urlParams.get('key');
            const hasLoginInUrl = urlParams.get('login');

            // Only prompt if we're missing both key and login parameters
            if (!login && isOnLostPasswordPage && !hasKeyInUrl && !hasLoginInUrl) {
                // Prompt the user for their username or email
                login = prompt("Please enter your username or email address to reset your password:");
                console.log('User provided login:', login);
            }

            if (emailField && login) {
                emailField.value = login;
                console.log('Email field set to:', login);
            }

            // Also store the login in a hidden field for form submission
            let loginField = document.getElementById('lci-reset-login');
            if (!loginField) {
                loginField = document.createElement('input');
                loginField.type = 'hidden';
                loginField.id = 'lci-reset-login';
                loginField.name = 'login';
                changeForm.appendChild(loginField);
            }

            if (loginField && login) {
                loginField.value = login;
                console.log('Login field set to:', login);
            }

            // Set the key field - IMPORTANT: This must work correctly
            let keyField = document.getElementById('lci-reset-key');

            // Always create a new key field to ensure it's properly set
            if (keyField) {
                // Remove existing field to avoid duplicates
                keyField.remove();
            }

            // Create a new key field
            keyField = document.createElement('input');
            keyField.type = 'hidden';
            keyField.id = 'lci-reset-key';
            keyField.name = 'key';
            changeForm.appendChild(keyField);

            // We already have isOnLostPasswordPage defined above

            // Set the key value
            if (key) {
                keyField.value = key;
                console.log('Reset key set to:', key);
            } else if (isOnLostPasswordPage) {
                // On lost-password page, we'll generate a key via AJAX when needed
                console.log('On lost-password page - key will be generated when form is submitted');

                // If we have a login, we can pre-fetch a key
                if (login) {
                    console.log('Pre-fetching key for login:', login);

                    // Make an AJAX request to get a key
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', lci_ajax.ajax_url, true);
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                    xhr.onload = function() {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.success && response.data && response.data.key) {
                                    keyField.value = response.data.key;
                                    console.log('Generated key via AJAX:', response.data.key);
                                }
                            } catch (e) {
                                console.error('Error parsing key response:', e);
                            }
                        }
                    };

                    xhr.send(new URLSearchParams({
                        action: 'lci_generate_reset_key',
                        login: login
                    }).toString());
                }
            } else {
                console.warn('No key value available from URL parameters');
            }
        }
    }

    // Try after a delay to ensure the main script has had a chance to run
    setTimeout(forceDisplayChangeForm, 1000);

    // Try again after a longer delay as a fallback
    setTimeout(forceDisplayChangeForm, 2000);
})();
