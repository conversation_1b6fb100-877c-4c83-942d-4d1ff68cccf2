<?php
// Step 2: Nights Selection or Accommodation Type
// This step asks users about nights or accommodation type based on previous selections

// Debug information for administrators
if (current_user_can('administrator')) {
    echo '<div class="alert alert-warning mb-4">';
    echo '<h5><i class="fas fa-bug me-2"></i> Step 2 Debug Information</h5>';
    echo '<p>has_main_pretour: ' . ($wizard['has_main_pretour'] ? 'Yes' : 'No') . '</p>';
    echo '<p>bucharest[selected]: ' . $wizard['bucharest']['selected'] . '</p>';
    echo '<p>brasov[selected]: ' . $wizard['brasov']['selected'] . '</p>';
    echo '<p>in_bucharest_flow: ' . (isset($wizard['in_bucharest_flow']) ? 'Yes' : 'No') . '</p>';
    echo '</div>';
}

// CRITICAL FIX: Force Bucharest flow if we're in it
if (isset($wizard['in_bucharest_flow']) && $wizard['in_bucharest_flow']) {
    // Force bucharest_selected to yes
    $wizard['bucharest']['selected'] = 'yes';
}

// For users with Main Pretour who selected Bucharest
if ($wizard['has_main_pretour'] && $wizard['bucharest']['selected'] === 'yes') {
    ?>
    <h3 class="text-center mb-4">How many nights will you stay in Bucharest?</h3>
    <div class="info-box">
        <i class="fas fa-info-circle me-2"></i>
        <span>Select the number of nights you need accommodation in Bucharest before the Main Pretour</span>
    </div>

    <div class="nights-selector">
        <div class="nights-selector-container">
            <button type="button" class="nights-btn nights-btn-minus" id="bucharest-nights-minus" <?php echo $wizard['bucharest']['nights'] <= 1 ? 'disabled' : ''; ?>>
                <i class="fas fa-minus"></i>
            </button>
            <div class="nights-display">
                <span class="nights-count" id="bucharest-nights-count"><?php echo $wizard['bucharest']['nights']; ?></span>
                <span class="nights-label" id="bucharest-nights-label"><?php echo $wizard['bucharest']['nights'] === 1 ? 'Night' : 'Nights'; ?></span>
            </div>
            <button type="button" class="nights-btn nights-btn-plus" id="bucharest-nights-plus">
                <i class="fas fa-plus"></i>
            </button>
        </div>
    </div>

    <input type="hidden" name="bucharest_nights" id="bucharest-nights-input" value="<?php echo $wizard['bucharest']['nights']; ?>">

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const minusBtn = document.getElementById('bucharest-nights-minus');
        const plusBtn = document.getElementById('bucharest-nights-plus');
        const countDisplay = document.getElementById('bucharest-nights-count');
        const labelDisplay = document.getElementById('bucharest-nights-label');
        const input = document.getElementById('bucharest-nights-input');

        let nights = <?php echo $wizard['bucharest']['nights']; ?>;

        minusBtn.addEventListener('click', function() {
            if (nights > 1) {
                nights--;
                updateDisplay();
            }
        });

        plusBtn.addEventListener('click', function() {
            nights++;
            updateDisplay();
        });

        function updateDisplay() {
            countDisplay.textContent = nights;
            labelDisplay.textContent = nights === 1 ? 'Night' : 'Nights';
            input.value = nights;

            minusBtn.disabled = nights <= 1;
        }
    });
    </script>
    <?php
}
// For users with Main Pretour who selected No for Bucharest, or users without Main Pretour
else if (($wizard['has_main_pretour'] && $wizard['bucharest']['selected'] === 'no') ||
         (!$wizard['has_main_pretour'] && $wizard['brasov']['selected'] === 'yes')) {
    ?>
    <h3 class="text-center mb-4">When do you need accommodation in Brasov?</h3>
    <div class="info-box" style="background-color: #e3f2fd; border-color: #90caf9; margin-bottom: 20px;">
        <i class="fas fa-calendar-alt me-2" style="color: #1976d2;"></i>
        <span>Please select when you need accommodation in Brasov. You can select multiple options by completing the wizard multiple times.</span>
    </div>

    <div class="option-cards" style="gap: 20px;">
        <label class="option-card <?php echo $wizard['brasov']['type'] === 'pre' ? 'selected' : ''; ?>" style="box-shadow: 0 4px 8px rgba(0,0,0,0.1); transition: all 0.3s ease;">
            <input type="radio" name="brasov_type" value="pre" style="display: none;" <?php checked($wizard['brasov']['type'], 'pre'); ?>>
            <div class="option-card-image" style="position: relative;">
                <div style="position: absolute; top: 10px; left: 10px; background-color: rgba(0,0,0,0.6); color: white; padding: 5px 10px; border-radius: 20px; font-size: 12px; font-weight: bold; z-index: 2;">
                    <i class="fas fa-calendar-minus me-1"></i> PRE-EVENT
                </div>
                <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121214/pre-event.jpg" alt="Pre-event Accommodation" style="width: 100%; height: 200px; object-fit: cover;">
            </div>
            <div class="option-card-content" style="padding: 15px;">
                <h4 class="option-card-title" style="color: #1976d2; font-size: 18px; margin-bottom: 8px;">Pre-event Accommodation</h4>
                <p class="option-card-description" style="color: #555; margin-bottom: 5px;">Stay in Brasov before the event</p>
                <p style="font-size: 13px; color: #666; margin-top: 10px;"><i class="fas fa-info-circle me-1"></i> You'll be able to select the number of nights on the next screen</p>
            </div>
        </label>

        <label class="option-card <?php echo $wizard['brasov']['type'] === 'main' ? 'selected' : ''; ?>" style="box-shadow: 0 4px 8px rgba(0,0,0,0.1); transition: all 0.3s ease;">
            <input type="radio" name="brasov_type" value="main" style="display: none;" <?php checked($wizard['brasov']['type'], 'main'); ?>>
            <div class="option-card-image" style="position: relative;">
                <div style="position: absolute; top: 10px; left: 10px; background-color: rgba(0,178,227,0.8); color: white; padding: 5px 10px; border-radius: 20px; font-size: 12px; font-weight: bold; z-index: 2;">
                    <i class="fas fa-calendar-day me-1"></i> DURING EVENT
                </div>
                <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121213/brasov-1.jpg" alt="Main Event Accommodation" style="width: 100%; height: 200px; object-fit: cover;">
            </div>
            <div class="option-card-content" style="padding: 15px;">
                <h4 class="option-card-title" style="color: #00b2e3; font-size: 18px; margin-bottom: 8px;">Main Event Accommodation</h4>
                <p class="option-card-description" style="color: #555; margin-bottom: 5px;">Stay in Brasov during the event (August 21-24, 2025)</p>
                <p style="font-size: 13px; color: #666; margin-top: 10px;"><i class="fas fa-info-circle me-1"></i> Standard 3-night package for the main event</p>
            </div>
        </label>

        <label class="option-card <?php echo $wizard['brasov']['type'] === 'post' ? 'selected' : ''; ?>" style="box-shadow: 0 4px 8px rgba(0,0,0,0.1); transition: all 0.3s ease;">
            <input type="radio" name="brasov_type" value="post" style="display: none;" <?php checked($wizard['brasov']['type'], 'post'); ?>>
            <div class="option-card-image" style="position: relative;">
                <div style="position: absolute; top: 10px; left: 10px; background-color: rgba(0,0,0,0.6); color: white; padding: 5px 10px; border-radius: 20px; font-size: 12px; font-weight: bold; z-index: 2;">
                    <i class="fas fa-calendar-plus me-1"></i> POST-EVENT
                </div>
                <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121215/post-event.jpg" alt="Post-event Accommodation" style="width: 100%; height: 200px; object-fit: cover;">
            </div>
            <div class="option-card-content" style="padding: 15px;">
                <h4 class="option-card-title" style="color: #1976d2; font-size: 18px; margin-bottom: 8px;">Post-event Accommodation</h4>
                <p class="option-card-description" style="color: #555; margin-bottom: 5px;">Stay in Brasov after the event</p>
                <p style="font-size: 13px; color: #666; margin-top: 10px;"><i class="fas fa-info-circle me-1"></i> You'll be able to select the number of nights on the next screen</p>
            </div>
        </label>
    </div>

    <script>
    // Simple script to handle option card selection
    document.addEventListener('DOMContentLoaded', function() {
        const optionCards = document.querySelectorAll('.option-card');
        optionCards.forEach(card => {
            card.addEventListener('click', function() {
                // Find the radio input inside this card and check it
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;

                    // Remove selected class from all cards
                    optionCards.forEach(c => c.classList.remove('selected'));

                    // Add selected class to this card
                    this.classList.add('selected');

                    // If they select "main", go directly to next step
                    if (radio.value === 'main') {
                        setTimeout(() => {
                            document.querySelector('button[name="wizard_action"][value="next"]').click();
                        }, 500);
                    } else {
                        // For pre/post, show nights selection
                        const nightsSelector = document.getElementById('brasov-nights-selector');
                        if (nightsSelector) {
                            nightsSelector.style.display = 'block';
                        }
                    }
                }
            });
        });
    });
    </script>

    <!-- Nights selection for pre/post event accommodation -->
    <div id="brasov-nights-selector" style="display: <?php echo ($wizard['brasov']['type'] === 'pre' || $wizard['brasov']['type'] === 'post') ? 'block' : 'none'; ?>; margin-top: 30px; background-color: #f8f9fa; border-radius: 10px; padding: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
        <h3 class="text-center mb-3" style="color: #00b2e3; font-size: 22px;">
            <i class="fas fa-moon me-2"></i> How many nights will you stay in Brasov
            <?php echo $wizard['brasov']['type'] === 'pre' ? 'before' : 'after'; ?> the event?
        </h3>

        <div class="info-box" style="background-color: #fff3cd; border-color: #ffeeba; margin-bottom: 20px;">
            <i class="fas fa-info-circle me-2" style="color: #856404;"></i>
            <span>Select the number of nights you need accommodation in Brasov <?php echo $wizard['brasov']['type'] === 'pre' ? 'before the event starts' : 'after the event ends'; ?>.</span>
        </div>

        <div class="nights-selector" style="max-width: 300px; margin: 0 auto;">
            <div class="nights-selector-container" style="background: white; border: 2px solid #00b2e3; border-radius: 50px; padding: 5px; display: flex; align-items: center; justify-content: space-between;">
                <button type="button" class="nights-btn nights-btn-minus" id="brasov-nights-minus" <?php echo $wizard['brasov']['nights'] <= 1 ? 'disabled' : ''; ?>
                        style="width: 40px; height: 40px; border-radius: 50%; background: <?php echo $wizard['brasov']['nights'] <= 1 ? '#e0e0e0' : '#00b2e3'; ?>; color: white; border: none; font-size: 18px; cursor: pointer; transition: all 0.2s ease;">
                    <i class="fas fa-minus"></i>
                </button>
                <div class="nights-display" style="flex-grow: 1; text-align: center;">
                    <span class="nights-count" id="brasov-nights-count" style="font-size: 24px; font-weight: bold; color: #00b2e3;"><?php echo $wizard['brasov']['nights']; ?></span>
                    <span class="nights-label" id="brasov-nights-label" style="font-size: 16px; color: #555; margin-left: 5px;"><?php echo $wizard['brasov']['nights'] === 1 ? 'Night' : 'Nights'; ?></span>
                </div>
                <button type="button" class="nights-btn nights-btn-plus" id="brasov-nights-plus"
                        style="width: 40px; height: 40px; border-radius: 50%; background: #00b2e3; color: white; border: none; font-size: 18px; cursor: pointer; transition: all 0.2s ease;">
                    <i class="fas fa-plus"></i>
                </button>
            </div>

            <div style="text-align: center; margin-top: 15px; font-size: 14px; color: #666;">
                <i class="fas fa-calendar-alt me-1"></i>
                <?php if ($wizard['brasov']['type'] === 'pre'): ?>
                    Arrive before August 21, 2025
                <?php else: ?>
                    Stay after August 24, 2025
                <?php endif; ?>
            </div>
        </div>

        <input type="hidden" name="brasov_nights" id="brasov-nights-input" value="<?php echo $wizard['brasov']['nights']; ?>">

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const minusBtn = document.getElementById('brasov-nights-minus');
            const plusBtn = document.getElementById('brasov-nights-plus');
            const countDisplay = document.getElementById('brasov-nights-count');
            const labelDisplay = document.getElementById('brasov-nights-label');
            const input = document.getElementById('brasov-nights-input');

            let nights = <?php echo $wizard['brasov']['nights']; ?>;

            minusBtn.addEventListener('click', function() {
                if (nights > 1) {
                    nights--;
                    updateDisplay();
                }
            });

            plusBtn.addEventListener('click', function() {
                nights++;
                updateDisplay();
            });

            function updateDisplay() {
                countDisplay.textContent = nights;
                labelDisplay.textContent = nights === 1 ? 'Night' : 'Nights';
                input.value = nights;

                minusBtn.disabled = nights <= 1;

                // Update the minus button color based on disabled state
                if (nights <= 1) {
                    minusBtn.style.background = '#e0e0e0';
                } else {
                    minusBtn.style.background = '#00b2e3';
                }
            }

            // Show/hide nights selector based on selected accommodation type
            const radioButtons = document.querySelectorAll('input[name="brasov_type"]');
            radioButtons.forEach(radio => {
                radio.addEventListener('change', function() {
                    const nightsSelector = document.getElementById('brasov-nights-selector');
                    if (this.value === 'pre' || this.value === 'post') {
                        nightsSelector.style.display = 'block';
                    } else {
                        nightsSelector.style.display = 'none';
                    }
                });
            });
        });
        </script>
    </div>
    <?php
}
?>
