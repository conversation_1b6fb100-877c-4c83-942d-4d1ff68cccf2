/**
 * Password Reset Handler
 *
 * This script handles password reset links and ensures the reset form is displayed
 * when a user clicks on a password reset link from their email.
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Password reset handler loaded');

    // Check URL parameters for password reset
    const urlParams = new URLSearchParams(window.location.search);
    const key = urlParams.get('key');
    const login = urlParams.get('login');
    const action = urlParams.get('action');
    const showResetForm = urlParams.get('show-reset-form');

    // Only proceed if this is a password reset URL
    // For the lost-password page, we don't need key and login
    const isLostPasswordPage = window.location.href.includes('lost-password');
    const isResetUrl = (isLostPasswordPage && (action === 'reset_password' || showResetForm === 'true')) ||
                      (key && login && (action === 'reset_password' || showResetForm === 'true'));

    if (!isResetUrl) {
        console.log('Not a password reset URL, handler will not modify forms');
        return; // Exit the handler if not a reset URL
    }

    console.log('Password reset handler - URL params:', { key, login, action, showResetForm });
    console.log('Password reset parameters detected - showing reset form');

        // Find the change password form
        const changeForm = document.getElementById('lci-change-password-form');
        const loginForm = document.getElementById('lci-login-form');
        const forgotForm = document.getElementById('lci-forgot-form');
        const magicForm = document.getElementById('lci-magic-form');

        if (changeForm && loginForm) {
            console.log('Forms found, showing change password form');

            // Hide all other forms
            loginForm.classList.add('d-none');
            if (forgotForm) forgotForm.classList.add('d-none');
            if (magicForm) magicForm.classList.add('d-none');

            // Show change password form
            changeForm.classList.remove('d-none');

            // Force display properties to ensure visibility
            changeForm.style.display = 'block';
            changeForm.style.opacity = '1';
            changeForm.style.transform = 'translateY(0)';

            console.log('Change password form display style:', changeForm.style.display);

            // Set the email field
            const emailField = document.getElementById('lci-change-email');
            if (emailField) {
                emailField.value = login;
                console.log('Email field set to:', login);
            }

            // Add a message to inform the user
            const messageEl = document.createElement('div');
            messageEl.className = 'alert alert-info text-center mb-4';
            messageEl.innerHTML = 'Please create a new password for your account.';

            const heading = document.querySelector('#lci-change-password-form h2');
            if (heading && heading.parentNode) {
                heading.parentNode.insertBefore(messageEl, heading.nextSibling);
                console.log('Info message added');
            }

            // Add the key as a hidden field
            let keyField = document.getElementById('lci-reset-key');
            if (!keyField) {
                keyField = document.createElement('input');
                keyField.type = 'hidden';
                keyField.id = 'lci-reset-key';
                keyField.name = 'key';
                changeForm.appendChild(keyField);
            }
            keyField.value = key;
            console.log('Key field set to:', key);

            // Make sure the form is visible
            changeForm.style.opacity = '1';
            changeForm.style.transform = 'translateY(0)';
        } else {
            console.error('Required forms not found');
        }
});
