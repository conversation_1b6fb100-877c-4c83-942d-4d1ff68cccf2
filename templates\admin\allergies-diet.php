<div class="wrap lci-admin-wrap">
    <div x-data="allergiesDietTable">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">Allergies & Diet</h1>
        </div>

        <!-- Loading state -->
        <div x-show="isLoading" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
            <div class="spinner"></div>
            <span class="loading-text">Loading participants...</span>
        </div>

        <!-- Filters -->
        <div x-show="!isLoading" class="bg-white rounded-xl shadow-neumorph p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-end space-y-4 md:space-y-0 md:space-x-4">
                <div class="flex-1">
                    <label class="form-label">Search</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <input
                            type="text"
                            x-model="searchQuery"
                            @input="debounceSearch()"
                            placeholder="Search by name, email, diet or allergies..."
                            class="form-input pl-10"
                        >
                    </div>
                </div>

                <div>
                    <label class="form-label">Diet Filter</label>
                    <select x-model="dietFilter" @change="filterParticipants()" class="form-input">
                        <option value="">All Diets</option>
                        <option value="vegetarian">Vegetarian</option>
                        <option value="vegan">Vegan</option>
                        <option value="gluten">Gluten Free</option>
                        <option value="lactose">Lactose Free</option>
                        <option value="kosher">Kosher</option>
                        <option value="halal">Halal</option>
                    </select>
                </div>

                <div>
                    <label class="form-label">Has Allergies</label>
                    <select x-model="hasAllergies" @change="filterParticipants()" class="form-input">
                        <option value="">All</option>
                        <option value="yes">Yes</option>
                        <option value="no">No</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Participants table -->
        <div x-show="!isLoading" class="bg-white rounded-xl shadow-neumorph overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="text-left bg-gray-50">
                            <th @click="sort('id')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    ID
                                    <svg x-show="sortField === 'id'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('unique_reg_id')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Unique ID
                                    <svg x-show="sortField === 'unique_reg_id'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('first_name')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Name
                                    <svg x-show="sortField === 'first_name'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('email')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Email
                                    <svg x-show="sortField === 'email'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('diet')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Diet
                                    <svg x-show="sortField === 'diet'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('allergies')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Allergies
                                    <svg x-show="sortField === 'allergies'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th class="p-4 font-medium text-gray-600">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr x-show="isLoading">
                            <td colspan="7" class="p-4 text-center text-gray-500">
                                <div class="flex justify-center items-center space-x-2">
                                    <svg class="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>Loading participants...</span>
                                </div>
                            </td>
                        </tr>

                        <template x-if="!isLoading && Array.isArray(paginatedParticipants) && paginatedParticipants.length > 0">
                            <template x-for="(participant, index) in paginatedParticipants" :key="index">
                                <tr class="border-t border-gray-100 hover:bg-gray-50">
                                    <td class="p-4">
                                        <div class="font-medium" x-text="participant.id || '-'"></div>
                                    </td>
                                    <td class="p-4">
                                        <div class="font-medium text-primary" x-text="participant.unique_reg_id || '-'"></div>
                                    </td>
                                    <td class="p-4">
                                        <div x-text="(participant.first_name || '') + ' ' + (participant.last_name || '')"></div>
                                    </td>
                                    <td class="p-4">
                                        <div x-text="participant.email || '-'"></div>
                                    </td>
                                    <td class="p-4">
                                        <div x-text="participant.diet || '-'" :class="{'italic text-gray-500': !participant.diet}"></div>
                                    </td>
                                    <td class="p-4">
                                        <div x-text="participant.allergies || '-'" :class="{'italic text-gray-500': !participant.allergies}"></div>
                                    </td>
                                    <td class="p-4">
                                        <a :href="'admin.php?page=lci-participant-detail&id=' + participant.id + '&tab=diet'" class="btn btn-sm btn-primary">
                                            View
                                        </a>
                                    </td>
                                </tr>
                            </template>
                        </template>

                        <tr x-show="!isLoading && (!Array.isArray(paginatedParticipants) || paginatedParticipants.length === 0)">
                            <td colspan="7" class="p-4 text-center text-gray-500">
                                No participants found matching your criteria.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div x-show="!isLoading && Array.isArray(filteredParticipants) && filteredParticipants.length > 0" class="p-4 border-t border-gray-100 flex flex-col md:flex-row md:justify-between md:items-center">
                <div class="mb-4 md:mb-0 text-sm text-gray-600">
                    Showing <span x-text="Math.min((currentPage - 1) * perPage + 1, filteredParticipants.length)"></span>
                    to <span x-text="Math.min(currentPage * perPage, filteredParticipants.length)"></span>
                    of <span x-text="filteredParticipants.length"></span> participants
                </div>

                <div class="flex justify-center space-x-2">
                    <button
                        @click="currentPage--"
                        :disabled="currentPage === 1"
                        class="btn btn-sm btn-secondary"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                    >
                        Previous
                    </button>
                    <button
                        @click="currentPage++"
                        :disabled="currentPage === totalPages"
                        class="btn btn-sm btn-secondary"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
                    >
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
