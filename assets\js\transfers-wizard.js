/**
 * Transfers Wizard JavaScript
 * Handles the multi-step wizard for booking transfers
 */

// Define the transferWizardData function globally
window.transferWizardData = function() {
    return {
            // Current step (1-6)
            currentStep: 1,

            // Step 1: Airport Selection
            selectedAirport: '',

            // Check if user has Main Pretour
            hasMainPretour: typeof hasMainPretour !== 'undefined' ? hasMainPretour : false,

            // Step 2: Transfer Type
            transferType: '',
            isRoundTrip: false, // This will be set to true for round trip options
            isArrivalOnly: false, // This will be set to true for arrival-only transfers
            isDepartureOnly: false, // This will be set to true for departure-only transfers

            // Step 2: Arrival Details
            arrivalDate: '',
            arrivalTime: '',
            arrivalFlight: '',

            // Step 3: Departure Details (for round trips)
            departureDate: '',
            departureTime: '',
            departureFlight: '',

            // Step 4: Additional Details
            specialRequests: '',

            // Step 5: Cart Summary
            products: [],
            loading: false,
            errors: {},

            // Validation functions
            validateStep1() {
                this.errors = {};

                if (!this.selectedAirport) {
                    this.errors.selectedAirport = 'Please select an airport';
                    return false;
                }

                return true;
            },

            validateStep2() {
                this.errors = {};

                if (!this.transferType) {
                    this.errors.transferType = 'Please select a transfer type';
                    return false;
                }

                // Reset all flags first
                this.isRoundTrip = false;
                this.isArrivalOnly = false;
                this.isDepartureOnly = false;

                // Set flags based on the selected transfer type
                if (this.transferType === 'bucharest_to_brasov_round') {
                    this.isRoundTrip = true;
                } else if (this.transferType === 'brasov_to_bucharest') {
                    this.isDepartureOnly = true;
                } else {
                    // bucharest_to_brasov, brasov_airport_to_venue, bucharest_to_pretour_hotel
                    this.isArrivalOnly = true;
                }

                console.log('Transfer type:', this.transferType);
                console.log('isRoundTrip:', this.isRoundTrip);
                console.log('isArrivalOnly:', this.isArrivalOnly);
                console.log('isDepartureOnly:', this.isDepartureOnly);

                return true;
            },

            // Update flight number manually
            updateArrivalFlight(value) {
                console.log('Manual update of arrival flight:', value);
                this.arrivalFlight = value;
            },

            validateStep3() {
                this.errors = {};

                // Try to get the flight number directly from the input field as a fallback
                const flightInput = document.getElementById('arrival-flight');
                if (flightInput && (!this.arrivalFlight || this.arrivalFlight.trim() === '')) {
                    this.arrivalFlight = flightInput.value;
                    console.log('Got flight number directly from input:', this.arrivalFlight);
                }

                console.log('Validating step 3...');
                console.log('isDepartureOnly:', this.isDepartureOnly);
                console.log('arrivalDate:', this.arrivalDate);
                console.log('arrivalTime:', this.arrivalTime);
                console.log('arrivalFlight:', this.arrivalFlight);
                console.log('arrivalFlight type:', typeof this.arrivalFlight);
                console.log('arrivalFlight length:', this.arrivalFlight ? this.arrivalFlight.length : 0);

                // Skip validation for departure-only transfers
                if (this.isDepartureOnly) {
                    console.log('Skipping validation for departure-only transfer');
                    return true;
                }

                if (!this.arrivalDate) {
                    this.errors.arrivalDate = 'Please select an arrival date';
                    console.log('Validation failed: No arrival date');
                    return false;
                }

                if (!this.arrivalTime) {
                    this.errors.arrivalTime = 'Please select an arrival time';
                    console.log('Validation failed: No arrival time');
                    return false;
                }

                // Flight number is optional
                if (this.arrivalFlight && this.arrivalFlight.trim() !== '') {
                    console.log('Flight number provided:', this.arrivalFlight);
                } else {
                    console.log('No flight number provided, but it\'s optional');
                }

                console.log('Step 3 validation passed');
                return true;
            },

            // Update departure flight manually
            updateDepartureFlight(value) {
                console.log('Manual update of departure flight:', value);
                this.departureFlight = value;
            },

            validateStep4() {
                this.errors = {};

                // Try to get the flight number directly from the input field as a fallback
                const flightInput = document.getElementById('departure-flight');
                if (flightInput && (!this.departureFlight || this.departureFlight.trim() === '')) {
                    this.departureFlight = flightInput.value;
                    console.log('Got departure flight number directly from input:', this.departureFlight);
                }

                console.log('Validating step 4...');
                console.log('isArrivalOnly:', this.isArrivalOnly);
                console.log('departureDate:', this.departureDate);
                console.log('departureTime:', this.departureTime);
                console.log('departureFlight:', this.departureFlight);
                console.log('departureFlight type:', typeof this.departureFlight);
                console.log('departureFlight length:', this.departureFlight ? this.departureFlight.length : 0);

                // Skip validation for arrival-only transfers
                if (this.isArrivalOnly) {
                    console.log('Skipping validation for arrival-only transfer');
                    return true;
                }

                if (!this.departureDate) {
                    this.errors.departureDate = 'Please select a departure date';
                    console.log('Validation failed: No departure date');
                    return false;
                }

                if (!this.departureTime) {
                    this.errors.departureTime = 'Please select a departure time';
                    console.log('Validation failed: No departure time');
                    return false;
                }

                // Flight number is optional
                if (this.departureFlight && this.departureFlight.trim() !== '') {
                    console.log('Departure flight number provided:', this.departureFlight);
                } else {
                    console.log('No departure flight number provided, but it\'s optional');
                }

                console.log('Step 4 validation passed');
                return true;
            },

            validateStep5() {
                this.errors = {};
                return true;
            },

            // Navigation functions
            nextStep() {
                console.log('Next step clicked. Current step:', this.currentStep);

                // Capture input values directly from DOM as a fallback
                if (this.currentStep === 3) {
                    const flightInput = document.getElementById('arrival-flight');
                    if (flightInput && flightInput.value) {
                        this.arrivalFlight = flightInput.value;
                        console.log('Captured flight number before validation:', this.arrivalFlight);
                    }
                }

                // Validate current step
                let isValid = false;

                switch (this.currentStep) {
                    case 1:
                        console.log('Validating step 1...');
                        isValid = this.validateStep1();
                        console.log('Step 1 validation result:', isValid);
                        break;
                    case 2:
                        console.log('Validating step 2...');
                        isValid = this.validateStep2();
                        console.log('Step 2 validation result:', isValid);
                        // isRoundTrip, isArrivalOnly, and isDepartureOnly are now set in validateStep2()
                        break;
                    case 3:
                        console.log('Validating step 3...');
                        // Double-check flight number before validation
                        const flightInput = document.getElementById('arrival-flight');
                        if (flightInput && flightInput.value) {
                            this.arrivalFlight = flightInput.value;
                            console.log('Double-checked flight number before validation:', this.arrivalFlight);
                        }
                        isValid = this.validateStep3();
                        console.log('Step 3 validation result:', isValid);
                        break;
                    case 4:
                        console.log('Validating step 4...');
                        // Double-check flight number before validation
                        const departureFlightInput = document.getElementById('departure-flight');
                        if (departureFlightInput && departureFlightInput.value) {
                            this.departureFlight = departureFlightInput.value;
                            console.log('Double-checked departure flight number before validation:', this.departureFlight);
                        }
                        isValid = this.validateStep4();
                        console.log('Step 4 validation result:', isValid);
                        break;
                    case 5:
                        console.log('Validating step 5...');
                        isValid = this.validateStep5();
                        console.log('Step 5 validation result:', isValid);
                        if (isValid) {
                            console.log('Fetching products...');
                            this.fetchProducts();
                        }
                        break;
                    default:
                        console.log('Unknown step:', this.currentStep);
                        break;
                }

                if (isValid) {
                    console.log('Validation passed, moving to next step...');

                    // Always proceed to the next step in sequence
                    // No more skipping steps - we'll follow the wizard flow step by step
                    console.log('BEFORE STEP CHANGE: Current step =', this.currentStep);
                    const oldStep = this.currentStep;
                    this.currentStep++;
                    console.log('AFTER STEP CHANGE: Old step =', oldStep, 'New step =', this.currentStep);

                    // Force a redraw by triggering a small timeout
                    setTimeout(() => {
                        console.log('AFTER TIMEOUT: Current step =', this.currentStep);
                    }, 10);

                    console.log('New current step:', this.currentStep);

                    // Scroll to top
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }
            },

            prevStep() {
                console.log('Previous step clicked. Current step:', this.currentStep);

                // Always go back one step at a time - no skipping
                this.currentStep--;

                console.log('Moving to previous step in sequence:', this.currentStep);

                // Scroll to top
                window.scrollTo({ top: 0, behavior: 'smooth' });
            },


            // Data persistence removed - no longer saving state to localStorage
            saveState() {
                // Function kept as a stub to avoid breaking references, but does nothing
                console.log('State persistence disabled - not saving to localStorage');
            },

            loadState() {
                // Function kept as a stub to avoid breaking references, but does nothing
                console.log('State persistence disabled - not loading from localStorage');

                // Always start fresh with step 1
                this.currentStep = 1;
            },

            resetWizard() {
                // Reset all fields (localStorage reference removed)
                this.currentStep = 1;
                this.selectedAirport = '';
                this.transferType = '';
                this.isRoundTrip = false;
                this.isArrivalOnly = false;
                this.isDepartureOnly = false;

                // Log the reset state
                console.log('Reset wizard state:');
                console.log('isRoundTrip:', this.isRoundTrip);
                console.log('isArrivalOnly:', this.isArrivalOnly);
                console.log('isDepartureOnly:', this.isDepartureOnly);
                this.arrivalDate = '';
                this.arrivalTime = '';
                this.arrivalFlight = '';
                this.departureDate = '';
                this.departureTime = '';
                this.departureFlight = '';
                this.specialRequests = '';
                this.products = [];
                this.errors = {};

                // Scroll to top
                window.scrollTo({ top: 0, behavior: 'smooth' });
            },

            // Product fetching
            fetchProducts() {
                this.loading = true;

                // Prepare data for AJAX request
                const data = {
                    action: 'lci_get_transfer_products',
                    nonce: (typeof lci_transfers_ajax !== 'undefined' ? lci_transfers_ajax.nonce : lci_ajax.nonce),
                    selected_airport: this.selectedAirport,
                    transfer_type: this.transferType,
                    arrival_date: this.arrivalDate,
                    arrival_time: this.arrivalTime,
                    departure_date: this.isRoundTrip ? this.departureDate : '',
                    departure_time: this.isRoundTrip ? this.departureTime : ''
                };

                // Fetch products via AJAX
                jQuery.ajax({
                    url: (typeof lci_transfers_ajax !== 'undefined' ? lci_transfers_ajax.ajax_url : lci_ajax.ajax_url),
                    type: 'POST',
                    data: data,
                    success: (response) => {
                        this.loading = false;

                        if (response.success) {
                            this.products = response.data.products;
                        } else {
                            this.errors.general = response.data.message || 'Failed to fetch products';
                        }
                    },
                    error: () => {
                        this.loading = false;
                        this.errors.general = 'Network error. Please try again.';
                    }
                });
            },

            // Add products to cart
            addToCart() {
                // Prevent multiple submissions
                if (this.loading) {
                    console.log('TRANSFER DEBUG: Request already in progress, preventing duplicate submission');
                    return;
                }

                console.log('TRANSFER DEBUG: Starting addToCart process');
                this.loading = true;

                // Prepare data for AJAX request
                const data = {
                    action: 'lci_book_transfer',
                    nonce: (typeof lci_transfers_ajax !== 'undefined' ? lci_transfers_ajax.nonce : lci_ajax.nonce),
                    selected_airport: this.selectedAirport,
                    transfer_type: this.transferType,
                    arrival_date: this.arrivalDate,
                    arrival_time: this.arrivalTime,
                    arrival_flight: this.arrivalFlight,
                    departure_date: this.isRoundTrip ? this.departureDate : '',
                    departure_time: this.isRoundTrip ? this.departureTime : '',
                    departure_flight: this.isRoundTrip ? this.departureFlight : '',
                    special_requests: this.specialRequests,
                    products: this.products.filter(p => p.selected).map(p => p.id)
                };

                console.log('TRANSFER DEBUG: Sending AJAX request with data:', data);
                console.log('TRANSFER DEBUG: Using nonce:', data.nonce);
                console.log('TRANSFER DEBUG: Using URL:', (typeof lci_transfers_ajax !== 'undefined' ? lci_transfers_ajax.ajax_url : lci_ajax.ajax_url));

                // Book transfer via AJAX
                jQuery.ajax({
                    url: (typeof lci_transfers_ajax !== 'undefined' ? lci_transfers_ajax.ajax_url : lci_ajax.ajax_url),
                    type: 'POST',
                    data: data,
                    success: (response) => {
                        console.log('TRANSFER DEBUG: Received response:', response);
                        this.loading = false;

                        if (response.success) {
                            console.log('TRANSFER DEBUG: Success! Redirecting to:', response.data.redirect_url);
                            // Redirect to checkout
                            window.location.href = response.data.redirect_url;
                        } else {
                            console.log('TRANSFER DEBUG: Error:', response.data.message);
                            this.errors.general = response.data.message || 'Failed to book transfer';
                        }
                    },
                    error: (xhr, status, error) => {
                        console.log('TRANSFER DEBUG: AJAX error:', status, error);
                        console.log('TRANSFER DEBUG: Response text:', xhr.responseText);
                        this.loading = false;
                        this.errors.general = 'Network error. Please try again.';
                    }
                });
            },

            // Initialize
            init() {
                // Always start from step 1 on page load/refresh
                this.currentStep = 1;
                console.log('Initializing wizard - setting currentStep to 1');

                // Ensure properties are initialized
                if (typeof this.isArrivalOnly === 'undefined') {
                    this.isArrivalOnly = false;
                }

                if (typeof this.isDepartureOnly === 'undefined') {
                    this.isDepartureOnly = false;
                }

                console.log('Initial state:');
                console.log('currentStep:', this.currentStep);
                console.log('isRoundTrip:', this.isRoundTrip);
                console.log('isArrivalOnly:', this.isArrivalOnly);
                console.log('isDepartureOnly:', this.isDepartureOnly);

                // Initialize date pickers after a short delay to ensure DOM is ready
                setTimeout(() => {
                    this.initDatePickers();
                }, 100);

                // Keep WordPress session alive
                this.keepSessionAlive();
            },

            // Function to keep WordPress session alive
            keepSessionAlive() {
                // Send a heartbeat request every 5 minutes to keep the session alive
                setInterval(() => {
                    jQuery.ajax({
                        url: (typeof lci_transfers_ajax !== 'undefined' ? lci_transfers_ajax.ajax_url : lci_ajax.ajax_url),
                        type: 'POST',
                        data: {
                            action: 'heartbeat',
                            _nonce: (typeof lci_transfers_ajax !== 'undefined' ? lci_transfers_ajax.nonce : lci_ajax.nonce),
                            screen_id: 'lci-transfers'
                        },
                        success: () => {
                            console.log('Session kept alive');
                        }
                    });
                }, 300000); // 5 minutes
            },

            // Initialize Flatpickr date and time pickers
            initDatePickers() {
                // Base date picker configuration
                const baseDateConfig = {
                    dateFormat: "Y-m-d",
                    disableMobile: true,
                    static: false,
                    inline: false,
                    allowInput: true,
                    clickOpens: true,
                    wrap: false,
                    altInput: true,
                    altFormat: "F j, Y",
                };

                // Arrival date picker configuration (August 12-20)
                const arrivalDateConfig = {
                    ...baseDateConfig,
                    minDate: "2025-08-12",
                    maxDate: "2025-08-20",
                    onChange: (_, dateStr) => {
                        this.arrivalDate = dateStr;
                        console.log('Arrival date set to:', dateStr);
                    }
                };

                // Departure date picker configuration (August 24-27)
                const departureDateConfig = {
                    ...baseDateConfig,
                    minDate: "2025-08-24",
                    maxDate: "2025-08-27",
                    onChange: (_, dateStr) => {
                        this.departureDate = dateStr;
                        console.log('Departure date set to:', dateStr);
                    }
                };

                // Time picker configuration
                const timeConfig = {
                    enableTime: true,
                    noCalendar: true,
                    dateFormat: "H:i",
                    time_24hr: true,
                    minuteIncrement: 15,
                    onChange: (_, timeStr, instance) => {
                        if (instance.element.id === 'arrival-time') {
                            this.arrivalTime = timeStr;
                        } else if (instance.element.id === 'departure-time') {
                            this.departureTime = timeStr;
                        }
                    }
                };

                // Initialize date pickers
                if (document.getElementById('arrival-date')) {
                    try {
                        flatpickr('#arrival-date', {
                            ...arrivalDateConfig,
                            // No default date
                            defaultDate: undefined,
                            onChange: (_, dateStr) => {
                                console.log('Arrival date changed to:', dateStr);
                                this.arrivalDate = dateStr;

                                // Force update the input value
                                const input = document.getElementById('arrival-date');
                                if (input) {
                                    input.value = dateStr;
                                    // Trigger Alpine.js to update
                                    input.dispatchEvent(new Event('input'));
                                }
                            },
                            onReady: (_, __, instance) => {
                                // Force clear on initialization
                                instance.clear();
                                this.arrivalDate = '';
                                console.log('Arrival date picker ready and cleared');
                            }
                        });

                        // Clear the input value
                        const arrivalDateInput = document.getElementById('arrival-date');
                        if (arrivalDateInput) {
                            arrivalDateInput.value = '';
                        }
                        console.log('Arrival date picker initialized with range: Aug 12-20');
                    } catch (error) {
                        console.error('Error initializing arrival date picker:', error);
                    }
                } else {
                    console.warn('Arrival date input not found in the DOM');
                }

                if (document.getElementById('departure-date')) {
                    try {
                        flatpickr('#departure-date', {
                            ...departureDateConfig,
                            // No default date
                            defaultDate: undefined,
                            onChange: (_, dateStr) => {
                                console.log('Departure date changed to:', dateStr);
                                this.departureDate = dateStr;

                                // Force update the input value
                                const input = document.getElementById('departure-date');
                                if (input) {
                                    input.value = dateStr;
                                    // Trigger Alpine.js to update
                                    input.dispatchEvent(new Event('input'));
                                }
                            },
                            onReady: (_, __, instance) => {
                                // Force clear on initialization
                                instance.clear();
                                this.departureDate = '';
                                console.log('Departure date picker ready and cleared');
                            }
                        });

                        // Clear the input value
                        const departureDateInput = document.getElementById('departure-date');
                        if (departureDateInput) {
                            departureDateInput.value = '';
                        }
                        console.log('Departure date picker initialized with range: Aug 24-27');
                    } catch (error) {
                        console.error('Error initializing departure date picker:', error);
                    }
                } else {
                    console.warn('Departure date input not found in the DOM');
                }

                // Initialize time pickers
                if (document.getElementById('arrival-time')) {
                    try {
                        flatpickr('#arrival-time', {
                            ...timeConfig,
                            // No default time
                            defaultDate: undefined,
                            onChange: (_, timeStr) => {
                                console.log('Arrival time changed to:', timeStr);
                                this.arrivalTime = timeStr;
                            },
                            onReady: (_, __, instance) => {
                                // Force clear on initialization
                                instance.clear();
                                this.arrivalTime = '';
                                console.log('Arrival time picker ready and cleared');
                            }
                        });

                        // Clear the input value
                        const arrivalTimeInput = document.getElementById('arrival-time');
                        if (arrivalTimeInput) {
                            arrivalTimeInput.value = '';
                        }
                        console.log('Arrival time picker initialized');
                    } catch (error) {
                        console.error('Error initializing arrival time picker:', error);
                    }
                } else {
                    console.warn('Arrival time input not found in the DOM');
                }

                if (document.getElementById('departure-time')) {
                    try {
                        flatpickr('#departure-time', {
                            ...timeConfig,
                            // No default time
                            defaultDate: undefined,
                            onChange: (_, timeStr) => {
                                console.log('Departure time changed to:', timeStr);
                                this.departureTime = timeStr;
                            },
                            onReady: (_, __, instance) => {
                                // Force clear on initialization
                                instance.clear();
                                this.departureTime = '';
                                console.log('Departure time picker ready and cleared');
                            }
                        });

                        // Clear the input value
                        const departureTimeInput = document.getElementById('departure-time');
                        if (departureTimeInput) {
                            departureTimeInput.value = '';
                        }
                        console.log('Departure time picker initialized');
                    } catch (error) {
                        console.error('Error initializing departure time picker:', error);
                    }
                } else {
                    console.warn('Departure time input not found in the DOM');
                }
            }
        };
};
