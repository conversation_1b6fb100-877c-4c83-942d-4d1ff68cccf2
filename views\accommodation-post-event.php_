<?php
/**
 * Post-Event Brasov Accommodation View
 *
 * Displays Post-Event accommodation options in Brasov
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Output script to ensure functions are globally accessible
?>
<script>
// Define global function stubs to ensure they are available
window.addToCartPostEvent = function() {
    console.log("addToCartPostEvent called");
    // The real implementation will be loaded later
};

window.closePostEventConfirmModal = function() {
    console.log("closePostEventConfirmModal called");
    // Direct DOM manipulation to close the modal
    const confirmModal = document.getElementById('postEventCartConfirmModal');
    if (confirmModal) {
        confirmModal.style.display = 'none';
    }
};

window.showPostEventConfirmModal = function(productId) {
    console.log("showPostEventConfirmModal called with", productId);
    // The real implementation will be loaded later
};

window.showPostEventModal = function(event) {
    console.log("showPostEventModal called");
    // Direct DOM manipulation to open the modal
    const modalElement = document.querySelector('.post-event-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'flex';
    }
};

window.closePostEventModal = function() {
    console.log("closePostEventModal called");
    // Direct DOM manipulation to close the modal
    const modalElement = document.querySelector('.post-event-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'none';
    }
};

window.openCustomMiniCart = function() {
    console.log("openCustomMiniCart called");
    // Direct DOM manipulation to open the mini cart
    const modal = document.getElementById('custom-mini-cart-modal');
    if (modal) {
        modal.style.display = 'block';
        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';

        // Try to fetch cart items if the function exists
        if (typeof fetchCustomMiniCartItems === 'function') {
            fetchCustomMiniCartItems();
        } else if (typeof window.fetchCustomMiniCartItems === 'function') {
            window.fetchCustomMiniCartItems();
        } else {
            console.log("fetchCustomMiniCartItems function not available yet");
            // Show loading state
            const loadingElement = document.getElementById('custom-mini-cart-loading');
            if (loadingElement) loadingElement.style.display = 'flex';

            // Hide other elements
            const emptyElement = document.getElementById('custom-mini-cart-empty');
            if (emptyElement) emptyElement.style.display = 'none';

            const itemsElement = document.getElementById('custom-mini-cart-items');
            if (itemsElement) itemsElement.style.display = 'none';

            const fundraisingElement = document.getElementById('custom-mini-cart-fundraising');
            if (fundraisingElement) fundraisingElement.style.display = 'none';
        }
    }
};

window.closePostEventModal = function() {
    console.log("closePostEventModal called");
    // Direct DOM manipulation to close the modal
    const modalElement = document.querySelector('.post-event-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'none';
    }
};

window.closeCustomMiniCart = function() {
    console.log("closeCustomMiniCart called");
    // Direct DOM manipulation to close the mini cart
    const modal = document.getElementById('custom-mini-cart-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('overflow');
    }
};

window.fetchCustomMiniCartItems = function() {
    console.log("fetchCustomMiniCartItems called");

    // Show loading state
    document.getElementById('custom-mini-cart-loading').style.display = 'flex';
    document.getElementById('custom-mini-cart-empty').style.display = 'none';
    document.getElementById('custom-mini-cart-items').style.display = 'none';
    document.getElementById('custom-mini-cart-fundraising').style.display = 'none';

    // Create a nonce for security (use the one from lci_ajax_object if available)
    const nonce = typeof lci_ajax !== 'undefined' ? lci_ajax.nonce : '';
    const ajaxUrl = typeof lci_ajax !== 'undefined' ? lci_ajax.ajax_url : '/wp-admin/admin-ajax.php';

    console.log('Using AJAX URL:', ajaxUrl);
    console.log('Using nonce:', nonce);

    // Create a URLSearchParams object for the request
    const params = new URLSearchParams();
    params.append('action', 'lci_get_mini_cart_items');
    params.append('nonce', nonce);
    params.append('_', new Date().getTime()); // Add timestamp to prevent caching

    console.log('Sending mini cart AJAX request with data:', Object.fromEntries(params));

    // Fetch cart items via AJAX
    fetch(ajaxUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params,
        credentials: 'same-origin' // Include cookies for session handling
    })
    .then(response => {
        console.log('Mini cart AJAX response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Mini cart items response:', data);

        // Hide loading state
        document.getElementById('custom-mini-cart-loading').style.display = 'none';

        if (data.success && data.data) {
            // Update cart items
            const cartItems = data.data.items || [];
            const cartTotal = data.data.total || '€0.00';
            const fundraisingAmount = data.data.fundraising_amount || '€0.00';

            console.log('Cart items count:', cartItems.length);
            console.log('Cart total:', cartTotal);

            // Update total - format the total to just show the amount with proper formatting
            const formattedTotal = typeof cartTotal === 'string' ?
                cartTotal.replace(/<\/?[^>]+(>|$)/g, "").trim().replace(/[^\d,]/g, "") :
                cartTotal;
            document.getElementById('custom-mini-cart-total').textContent = formattedTotal + ' €';
            console.log('Formatted total:', formattedTotal + ' €');

            // Update fundraising amount if available
            if (fundraisingAmount && parseFloat(fundraisingAmount.replace(/[^0-9.-]+/g, '')) > 0) {
                // Format the fundraising amount to just show the amount with proper formatting
                const formattedFundraising = typeof fundraisingAmount === 'string' ?
                    fundraisingAmount.replace(/<\/?[^>]+(>|$)/g, "").trim().replace(/[^\d,]/g, "") :
                    fundraisingAmount;
                document.getElementById('custom-mini-cart-fundraising-amount').textContent = formattedFundraising + ' €';
                document.getElementById('custom-mini-cart-fundraising').style.display = 'block';
                console.log('Showing fundraising amount:', formattedFundraising + ' €');
            } else {
                document.getElementById('custom-mini-cart-fundraising').style.display = 'none';
                console.log('Hiding fundraising amount');
            }

            if (cartItems.length > 0) {
                // Show items container
                document.getElementById('custom-mini-cart-items').style.display = 'block';
                console.log('Showing cart items');

                // Generate HTML for cart items
                let itemsHtml = '';
                cartItems.forEach(item => {
                    console.log('Processing cart item:', item);
                    itemsHtml += `
                        <div class="custom-mini-cart-item">
                            <div class="custom-mini-cart-item-image">
                                <img src="${item.image || 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'}" alt="${item.name}">
                            </div>
                            <div class="custom-mini-cart-item-details">
                                <div class="custom-mini-cart-item-name">${item.name}</div>
                                ${item.meta ? `<div class="custom-mini-cart-item-meta">${item.meta}</div>` : ''}
                                <div class="custom-mini-cart-item-price">
                                    <span class="custom-mini-cart-item-quantity">${item.quantity} ×</span>
                                    ${item.price}
                                </div>
                            </div>
                            <button type="button" class="custom-mini-cart-item-remove" onclick="removeCustomMiniCartItem('${item.key}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                });

                document.getElementById('custom-mini-cart-items').innerHTML = itemsHtml;
            } else {
                // Show empty state
                document.getElementById('custom-mini-cart-empty').style.display = 'flex';
                console.log('Showing empty cart state');
            }

            // Force WooCommerce to update the cart
            if (typeof jQuery !== 'undefined') {
                console.log('Triggering WooCommerce cart update');
                jQuery(document.body).trigger('wc_update_cart');
                jQuery(document.body).trigger('wc_fragment_refresh');
            }
        } else {
            // Show error state
            document.getElementById('custom-mini-cart-empty').style.display = 'flex';
            document.getElementById('custom-mini-cart-empty-icon').className = 'fas fa-exclamation-triangle';
            document.getElementById('custom-mini-cart-empty-title').textContent = 'Error loading cart';
            document.getElementById('custom-mini-cart-empty-subtitle').textContent = 'Please try again later';
            console.log('Error loading cart:', data);
        }
    })
    .catch(error => {
        console.error('Error fetching mini cart items:', error);

        // Hide loading state
        document.getElementById('custom-mini-cart-loading').style.display = 'none';

        // Show error state
        document.getElementById('custom-mini-cart-empty').style.display = 'flex';
        document.getElementById('custom-mini-cart-empty-icon').className = 'fas fa-exclamation-triangle';
        document.getElementById('custom-mini-cart-empty-title').textContent = 'Error loading cart';
        document.getElementById('custom-mini-cart-empty-subtitle').textContent = 'Please try again later';
    });
};

// Function to force WooCommerce cart update
window.forceWooCommerceCartUpdate = function() {
    console.log('Forcing WooCommerce cart update');

    if (typeof jQuery === 'undefined' || typeof wc_add_to_cart_params === 'undefined') {
        console.log('jQuery or WooCommerce params not available');
        return;
    }

    jQuery.ajax({
        url: wc_add_to_cart_params.wc_ajax_url.toString().replace('%%endpoint%%', 'get_refreshed_fragments'),
        type: 'POST',
        data: {
            time: new Date().getTime()
        },
        success: function(data) {
            console.log('Cart fragments refreshed:', data);

            // Trigger standard WooCommerce events
            jQuery(document.body).trigger('wc_fragment_refresh');
            jQuery(document.body).trigger('added_to_cart');

            // Update the mini cart count directly
            if (data.fragments && data.fragments['.custom-mini-cart-count']) {
                jQuery('.custom-mini-cart-count').replaceWith(data.fragments['.custom-mini-cart-count']);
            }

            // Refresh our custom mini cart
            fetchCustomMiniCartItems();
        },
        error: function(error) {
            console.error('Error refreshing cart fragments:', error);
        }
    });
};

window.removeCustomMiniCartItem = function(itemKey) {
    console.log("removeCustomMiniCartItem called with", itemKey);

    // Show loading state
    document.getElementById('custom-mini-cart-loading').style.display = 'flex';
    document.getElementById('custom-mini-cart-items').style.display = 'none';
    document.getElementById('custom-mini-cart-empty').style.display = 'none';

    // Create a nonce for security (use the one from lci_ajax_object if available)
    const nonce = typeof lci_ajax !== 'undefined' ? lci_ajax.nonce : '';
    const ajaxUrl = typeof lci_ajax !== 'undefined' ? lci_ajax.ajax_url : '/wp-admin/admin-ajax.php';

    console.log('Using AJAX URL:', ajaxUrl);
    console.log('Using nonce:', nonce);

    // Create a URLSearchParams object for the request
    const params = new URLSearchParams();
    params.append('action', 'lci_remove_cart_item');
    params.append('cart_item_key', itemKey);
    params.append('nonce', nonce);
    params.append('_', new Date().getTime()); // Add timestamp to prevent caching

    console.log('Sending remove cart item AJAX request with data:', Object.fromEntries(params));

    // Remove item via AJAX
    fetch(ajaxUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params,
        credentials: 'same-origin' // Include cookies for session handling
    })
    .then(response => {
        console.log('Remove cart item AJAX response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Remove cart item response:', data);

        // Force WooCommerce to update the cart
        if (typeof jQuery !== 'undefined') {
            console.log('Triggering WooCommerce cart update');
            jQuery(document.body).trigger('wc_update_cart');
            jQuery(document.body).trigger('wc_fragment_refresh');
        }

        // Refresh cart items
        console.log('Refreshing cart items');
        fetchCustomMiniCartItems();

        // Update mini cart count on the button with animation
        if (data.success && data.data && data.data.count !== undefined) {
            console.log('Updating cart count to:', data.data.count);
            const cartCountElement = document.querySelector('.custom-mini-cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = data.data.count;

                // Add pulse animation
                cartCountElement.classList.remove('count-pulse');
                void cartCountElement.offsetWidth; // Trigger reflow to restart animation
                cartCountElement.classList.add('count-pulse');
            } else {
                console.log('Cart count element not found');
            }
        } else {
            console.log('Could not update cart count:', {
                success: data.success,
                hasData: !!data.data,
                count: data.data ? data.data.count : 'undefined'
            });
        }
    })
    .catch(error => {
        console.error('Error removing cart item:', error);

        // Refresh cart items anyway
        console.log('Refreshing cart items despite error');
        fetchCustomMiniCartItems();
    });
};
</script>
<?php

// Enqueue the accommodation CSS
wp_enqueue_style('accommodation-css', LCI2025_URL . 'assets/css/accommodation.css', array(), LCI2025_VERSION);

// Localize script with AJAX URL and nonce
wp_localize_script('jquery', 'lci_ajax', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lci_ajax_nonce')
));

// Add inline CSS for the accommodation products
$accommodation_products_css = "
.accommodation-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.accommodation-product-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.accommodation-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accommodation-product-image {
    height: 350px;
    overflow: hidden;
    position: relative;
}

.accommodation-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.accommodation-product-card:hover .accommodation-product-image img {
    transform: scale(1.05);
}

.accommodation-product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.accommodation-product-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.accommodation-product-title {
    color: #00b2e3;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.accommodation-product-description {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
    flex-grow: 1;
}

.accommodation-product-price {
    font-weight: bold;
    font-size: 18px;
    color: #343a40;
    margin-bottom: 15px;
}

.accommodation-product-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #36b1dc;
    color: white !important;
    border: none;
    width: 100%;
    box-shadow: 0 4px 6px rgba(54, 177, 220, 0.2);
}

.accommodation-product-button i {
    color: white;
}

.accommodation-product-button:hover {
    background-color: #36b1dc;
    color: white !important;
    box-shadow: 0 6px 12px rgba(54, 177, 220, 0.3);
    transform: translateY(-2px);
}

.accommodation-product-variations {
    margin-top: 15px;
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.accommodation-product-variation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.accommodation-product-variation-name {
    font-size: 14px;
    color: #495057;
}

.accommodation-product-variation-price {
    font-weight: bold;
    color: #00b2e3;
}

.accommodation-product-stars {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: #ffc107;
}

/* Features List with Checkmarks */
.accommodation-product-features {
    margin-bottom: 15px;
}

.accommodation-features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.accommodation-features-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    font-size: 14px;
    color: #495057;
    line-height: 1.4;
}

.accommodation-features-list li i {
    color: #36b1dc;
    margin-right: 8px;
    font-size: 16px;
    flex-shrink: 0;
    margin-top: 2px;
}

.accommodation-product-soldout {
    opacity: 0.7;
    position: relative;
}

.accommodation-product-soldout::after {
    content: 'SOLD OUT';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-15deg);
    background-color: rgba(220, 53, 69, 0.8);
    color: white;
    padding: 10px 20px;
    font-weight: bold;
    font-size: 24px;
    border-radius: 5px;
    z-index: 10;
}

.accommodation-product-variation.soldout {
    opacity: 0.5;
    text-decoration: line-through;
}

/* Modern Nights Selector - 2025UX Trends */
.nights-label-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 500;
    color: #333;
}

.nights-icon {
    color: #36b1dc;
    font-size: 16px;
}

.nights-selector {
    max-width: 383px;
    margin: 0px auto;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.nights-selector:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nights-selector-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    padding: 8px 12px;
}

.nights-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: white;
    color: #495057;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nights-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.nights-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nights-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.nights-btn-minus {
    background-color: #f8d7da;
    color: #dc3545;
}

.nights-btn-plus {
    background-color: #d1e7dd;
    color: #198754;
}

.nights-display {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.nights-display span:first-child {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.nights-value-container {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 5px;
}

.nights-count {
    font-size: 24px;
    font-weight: 700;
    color: #36b1dc;
    display: block;
    text-align: center;
}

.nights-label {
    font-size: 14px;
    color: #6c757d;
    display: block;
    text-align: center;
}

.nights-progress-container {
    height: 6px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
    overflow: hidden;
}

.nights-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #36b1dc, #90caf9);
    border-radius: 3px;
    transition: width 0.3s ease;
}

@media (min-width: 768px) {
    .nights-btn {
        width: 48px;
        height: 48px;
    }

    .nights-count {
        font-size: 28px;
    }
}

@media (max-width: 768px) {
    .accommodation-products-grid {
        grid-template-columns: 1fr;
    }
}
";

wp_add_inline_style('accommodation-css', $accommodation_products_css);

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Get Post-Event accommodation products (category ID 37)
$post_event_products = [];
$args = [
    'post_type' => 'product',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'tax_query' => array(
        array(
            'taxonomy' => 'product_cat',
            'field' => 'id',
            'terms' => 37, // Pre/Post Event accommodation category ID
        ),
    ),
];

$posts = get_posts($args);
foreach ($posts as $post) {
    $product = wc_get_product($post->ID);
    if ($product) {
        $post_event_products[] = $product;
    }
}

// Check if any products are in the cart
$products_in_cart = [];
if (WC()->cart) {
    foreach (WC()->cart->get_cart() as $cart_item) {
        $products_in_cart[] = $cart_item['product_id'];
    }
}

// Initialize nights value
$nights = isset($_POST['nights']) ? intval($_POST['nights']) : 1;
?>

<div class="accommodation-container">
    <!-- Header Section - 2025 UX/UI Style -->
    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 40px 20px; position: relative; overflow: hidden; border-radius: 0 0 30px 30px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2); margin-bottom: 40px;">
        <!-- Animated background elements -->
        <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%); border-radius: 50%; animation: pulse 8s infinite alternate;"></div>
        <div class="position-absolute" style="bottom: -30px; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%); border-radius: 50%; animation: float 12s infinite alternate;"></div>
        <div class="position-absolute" style="top: 20%; right: 20%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%); border-radius: 50%; animation: float 7s infinite alternate-reverse;"></div>

        <!-- Glassmorphism overlay -->
        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>

        <div class="container position-relative" style="z-index: 2;">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <!-- Modern 3D icon with shadow and glow -->
                    <div style="background: rgba(255, 255, 255, 0.2); width: 60px; height: 60px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); transform: perspective(500px) rotateX(10deg); border: 1px solid rgba(255, 255, 255, 0.3);">
                        <div style="position: relative;">
                            <i class="fas fa-hotel" style="color: white; font-size: 28px; filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.8), transparent 70%); filter: blur(8px); opacity: 0.5; animation: glow 3s infinite alternate;"></div>
                        </div>
                    </div>

                    <!-- Typography with modern styling -->
                    <div>
                        <!-- Title -->
                        <h1 style="color: white; font-size: 28px; font-weight: 800; margin: 0; letter-spacing: -0.5px; text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">Post-Event Accommodation</h1>

                        <!-- Simple subtitle text -->
                        <p style="color: white; font-size: 16px; margin: 10px 0 0 0; font-weight: 400; letter-spacing: 0.5px; text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);">Book your stay in Brasov after the main event</p>

                        <!-- Back button positioned below the subtitle -->
                        <div style="margin-top: 12px;">
                            <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation', remove_query_arg('category_id'))); ?>" class="d-inline-flex align-items-center back-to-accommodations-btn" style="background: rgba(255, 255, 255, 0.2); color: white; border: none; padding: 6px 14px; border-radius: 8px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; font-size: 12px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); transition: all 0.3s ease; position: relative; overflow: hidden; text-decoration: none;">
                                <i class="fas fa-arrow-left me-1" style="color: white; position: relative; z-index: 1; font-size: 10px;"></i> <span style="color: white; position: relative; z-index: 1;">Back to All Accommodations</span>
                            </a>
                        </div>


                    </div>
                </div>

                <!-- Modern Trendy Mini Cart Button -->
                <div style="position: relative; z-index: 10;">
                    <button id="custom-mini-cart-button" class="custom-mini-cart-btn" onclick="openCustomMiniCart()">
                        <div class="custom-mini-cart-btn-inner">
                            <div class="custom-mini-cart-icon-wrapper">
                                <div class="custom-mini-cart-icon-bg">
                                    <i class="fas fa-shopping-bag custom-mini-cart-icon"></i>
                                </div>
                                <span class="custom-mini-cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                            </div>
                            <div class="custom-mini-cart-info">
                                <span class="custom-mini-cart-label">Your Cart</span>
                                <span class="custom-mini-cart-total"><?php echo WC()->cart->get_cart_total(); ?></span>
                            </div>
                        </div>
                    </button>
                </div>

                <style>
                /* Modern Trendy Mini Cart Button Styles */
                .custom-mini-cart-btn {
                    position: relative;
                    background: rgba(255, 255, 255, 0.15);
                    backdrop-filter: blur(10px);
                    -webkit-backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 16px;
                    padding: 0;
                    overflow: hidden;
                    cursor: pointer;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
                }

                .custom-mini-cart-btn-inner {
                    position: relative;
                    z-index: 2;
                    display: flex;
                    align-items: center;
                    padding: 10px 16px;
                }

                .custom-mini-cart-icon-wrapper {
                    position: relative;
                    margin-right: 14px;
                }

                .custom-mini-cart-icon-bg {
                    width: 42px;
                    height: 42px;
                    background: linear-gradient(135deg, #36b1dc, #2980b9);
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 4px 10px rgba(54, 177, 220, 0.3);
                    transition: all 0.3s ease;
                }

                .custom-mini-cart-icon {
                    color: white;
                    font-size: 18px;
                    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
                }

                .custom-mini-cart-count {
                    position: absolute;
                    top: -6px;
                    right: -6px;
                    background: #ff6b6b;
                    color: white;
                    font-size: 11px;
                    font-weight: 700;
                    min-width: 20px;
                    height: 20px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                    padding: 0 6px;
                    border: 2px solid white;
                    transition: all 0.3s ease;
                }

                .custom-mini-cart-info {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                }

                .custom-mini-cart-label {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: 500;
                    margin-bottom: 2px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .custom-mini-cart-total {
                    color: white;
                    font-weight: 700;
                    font-size: 16px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                }

                /* Hover effects */
                .custom-mini-cart-btn:hover {
                    background: rgba(255, 255, 255, 0.25);
                    transform: translateY(-2px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                }

                .custom-mini-cart-btn:hover .custom-mini-cart-icon-bg {
                    transform: scale(1.05);
                    box-shadow: 0 6px 15px rgba(54, 177, 220, 0.4);
                }

                .custom-mini-cart-btn:hover .custom-mini-cart-count {
                    transform: scale(1.1);
                    background: #ff5252;
                }

                .custom-mini-cart-btn:active {
                    transform: translateY(1px);
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                }

                /* Pulse animation for the count badge when items are added */
                @keyframes countPulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.3); }
                    100% { transform: scale(1); }
                }

                .count-pulse {
                    animation: countPulse 0.5s ease-out;
                }
                </style>
            </div>
        </div>
    </div>

    <style>
    .back-to-accommodations-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(54, 177, 220, 0.3);
        background: rgba(255, 255, 255, 0.3) !important;
    }

    .back-to-accommodations-btn:hover i,
    .back-to-accommodations-btn:hover span {
        color: white !important;
    }

    /* Add ripple effect on button click */
    .back-to-accommodations-btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%, -50%);
        transform-origin: 50% 50%;
    }

    .back-to-accommodations-btn:active::after {
        animation: backBtnRipple 0.6s ease-out;
    }

    @keyframes backBtnRipple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(20, 20);
            opacity: 0;
        }
    }
    </style>

    <!-- Introduction Section - 2025 UX/UI Style -->
    <div class="mb-5">
        <div class="section-container" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden; margin-bottom: 30px;">
            <!-- Decorative elements -->
            <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
            <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

            <!-- Section header with 3D effect -->
            <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
                <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                    <i class="fas fa-calendar-alt" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                </div>
                <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Staying Longer in Brasov?</h3>
            </div>

            <p style="margin-bottom: 20px; color: #37474f; font-size: 15px; line-height: 1.5; position: relative; z-index: 1;">
                Book your accommodation in Brasov after the main event ends (after August 24, 2025).
                Select the number of nights you need and choose from our recommended hotels.
            </p>

                <div id="current-settings-display" style="background-color: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); margin-top: 10px;">
                    <div style="margin-bottom: 12px;">
                        <div style="font-weight: 600; color: #36b1dc; font-size: 16px;">YOUR STAY PREFERENCES</div>
                    </div>

                    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px;">
                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">DURATION</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-moon me-1" style="color: #36b1dc;"></i>
                                <span id="current-nights"><?php echo $nights; ?></span> <span id="current-nights-label">night</span>
                            </div>
                        </div>

                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">ROOM TYPE</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-bed me-1" style="color: #36b1dc;"></i>
                                <span id="current-room-type">Single Room</span>
                            </div>
                        </div>
                    </div>

                    <div style="height: 1px; background-color: #e0e0e0; margin: 15px 0;"></div>

                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">CHECK-IN</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-calendar-check me-1" style="color: #36b1dc;"></i>
                                24/08/2025
                            </div>
                        </div>

                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">CHECK-OUT</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-calendar-times me-1" style="color: #36b1dc;"></i>
                                <span id="current-checkout-date">25/08/2025</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (empty($post_event_products)): ?>
        <!-- No products available message -->
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span>No accommodation options are currently available for post-event. Please check back later.</span>
        </div>
        <?php else: ?>
        <!-- Products Grid -->
        <div class="accommodation-products-grid">
            <?php foreach ($post_event_products as $product):
                $product_id = $product->get_id();
                $in_cart = in_array($product_id, $products_in_cart);
                $image_url = wp_get_attachment_url($product->get_image_id());
                $price_html = $product->get_price_html();
                $description = $product->get_short_description();
                $is_in_stock = $product->is_in_stock();

                // Get hotel stars from product meta
                $hotel_stars = get_post_meta($product_id, '_number_of_stars', true);
                if (!$hotel_stars) $hotel_stars = 3; // Default to 3 stars

                // Get hotel features from product meta
                $hotel_features = get_post_meta($product_id, '_hotel_features', true);
                if ($hotel_features) {
                    $features = explode("\n", $hotel_features);
                } else {
                    $features = [];
                }

                // Get hotel website from product meta
                $hotel_website = get_post_meta($product_id, '_hotel_website', true);

                // Add soldout class if product is out of stock
                $soldout_class = !$is_in_stock ? 'accommodation-product-soldout' : '';
            ?>
            <div class="accommodation-product-card">
                <div class="accommodation-product-image">
                    <?php if ($in_cart): ?>
                    <div class="accommodation-product-badge">In Cart</div>
                    <?php endif; ?>
                    <img src="<?php echo esc_url($image_url ? $image_url : 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121215/post-event.jpg'); ?>" alt="<?php echo esc_attr($product->get_name()); ?>">
                </div>
                <div class="accommodation-product-content">
                    <h4 class="accommodation-product-title"><?php echo esc_html($product->get_name()); ?></h4>

                    <!-- Hotel Stars -->
                    <div class="accommodation-product-stars">
                        <?php for ($i = 0; $i < $hotel_stars; $i++): ?>
                            <i class="fas fa-star"></i>
                        <?php endfor; ?>
                        <span class="accommodation-product-star-description">
                            <?php echo $hotel_stars; ?>-star hotel
                        </span>
                    </div>

                    <!-- Description List with Checkmarks -->
                    <div class="accommodation-product-features">
                        <ul class="accommodation-features-list">
                            <?php
                            // Get description fields from product meta
                            $descriere = get_post_meta($product_id, '_descriere', true);
                            $descriere1 = get_post_meta($product_id, '_descriere1', true);
                            $descriere2 = get_post_meta($product_id, '_descriere2', true);
                            $descriere3 = get_post_meta($product_id, '_descriere3', true);

                            // Display description fields if they exist
                            if ($descriere) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere) . '</li>';
                            if ($descriere1) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere1) . '</li>';
                            if ($descriere2) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere2) . '</li>';
                            if ($descriere3) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere3) . '</li>';
                            ?>
                        </ul>
                    </div>

                    <div class="accommodation-product-description">
                        <?php if (!empty($features)): ?>
                        <ul class="mt-2 mb-0" style="padding-left: 20px;">
                            <?php foreach ($features as $feature): ?>
                            <li><?php echo esc_html(trim($feature)); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>

                        <?php if ($hotel_website): ?>
                        <div class="mt-2">
                            <a href="<?php echo esc_url($hotel_website); ?>" target="_blank" style="color: #00b2e3; text-decoration: none; display: inline-flex; align-items: center;">
                                <i class="fas fa-globe me-1"></i> Visit hotel website
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($product->is_type('variable')):
                        $variations = $product->get_available_variations();
                    ?>
                    <div class="accommodation-product-variations" data-product-id="<?php echo esc_attr($product_id); ?>">
                        <h5 class="accommodation-variations-title">Available Room Types:</h5>
                        <?php
                        $first_variation = true;
                        foreach ($variations as $variation):
                            $variation_obj = wc_get_product($variation['variation_id']);
                            if (!$variation_obj || !$variation_obj->is_in_stock()) continue;

                            $variation_id = $variation['variation_id'];

                            // Get attribute names instead of values
                            $attribute_names = [];
                            foreach ($variation['attributes'] as $key => $value) {
                                $taxonomy = str_replace('attribute_', '', $key);
                                $term = get_term_by('slug', $value, $taxonomy);
                                if ($term) {
                                    $attribute_names[] = $term->name;
                                } else {
                                    // For custom product attributes
                                    $attribute_names[] = $value;
                                }
                            }

                            $variation_name = implode(' - ', $attribute_names);
                            $variation_price_html = $variation_obj->get_price_html();

                            // Check if this is a double room variation
                            $is_double_room = stripos($variation_name, 'double') !== false;

                            // Get variation price
                            $variation_price = $variation_obj->get_price();
                        ?>
                        <div class="accommodation-product-variation">
                            <label class="accommodation-variation-radio-label">
                                <input type="radio" name="variation_<?php echo esc_attr($product_id); ?>"
                                       value="<?php echo esc_attr($variation_id); ?>"
                                       data-variation-name="<?php echo esc_attr($variation_name); ?>"
                                       data-is-double="<?php echo $is_double_room ? 'true' : 'false'; ?>"
                                       data-price="<?php echo esc_attr($variation_price); ?>"
                                       <?php echo $first_variation ? 'checked' : ''; ?>>
                                <div class="accommodation-product-variation-name"><?php echo esc_html($variation_name); ?></div>
                                <div class="accommodation-product-variation-price"><?php echo $variation_price_html; ?></div>
                            </label>
                        </div>
                        <?php
                        $first_variation = false;
                        endforeach;
                        ?>
                    </div>
                    <?php else: ?>
                    <div class="accommodation-product-single-price">
                        <?php echo $price_html; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ($in_cart): ?>
                    <button class="accommodation-product-button" disabled style="background: #28a745;">
                        <i class="fas fa-check me-2"></i> Added to Cart
                    </button>
                    <?php elseif (!$is_in_stock): ?>
                    <button class="accommodation-product-button" disabled style="background: #6c757d;">
                        <i class="fas fa-times me-2"></i> Sold Out
                    </button>
                    <?php else: ?>
                    <button class="accommodation-product-button add-to-cart-btn"
                            data-product-id="<?php echo esc_attr($product_id); ?>"
                            data-product-name="<?php echo esc_attr($product->get_name()); ?>"
                            data-product-type="<?php echo esc_attr($product->get_type()); ?>"
                            data-price="<?php echo esc_attr($product->get_price()); ?>">
                        <i class="fas fa-shopping-cart me-2"></i> Add to Cart
                    </button>
                    <button onclick="showPostEventModal(event)" class="accommodation-settings-button">
                        <i class="fas fa-cog me-2"></i> Change Stay Settings
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add to Cart Confirmation Modal -->
<div class="accommodation-cart-confirm-modal" id="postEventCartConfirmModal" style="display: none;">
    <div class="accommodation-cart-confirm-overlay" style="backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>
    <div class="accommodation-cart-confirm-content">
        <div class="accommodation-cart-confirm-header">
            <h3 id="confirmModalTitle"><i class="fas fa-check-circle me-2" style="color: white;"></i> Confirm Your Selection</h3>
            <button type="button" class="accommodation-cart-confirm-close btn-close btn-close-white" onclick="closePostEventConfirmModal()" aria-label="Close" style="position: relative; z-index: 1;"></button>
        </div>
        <div class="accommodation-cart-confirm-body">
            <p id="confirmModalIntro">Please confirm your post-event accommodation selection:</p>

            <div class="accommodation-cart-confirm-details">
                <div class="accommodation-cart-confirm-product">
                    <div class="accommodation-cart-confirm-product-image">
                        <img id="confirmProductImage" src="" alt="Hotel Image">
                        <div class="accommodation-cart-confirm-product-image-overlay">
                            <div class="accommodation-cart-confirm-product-image-stars" id="confirmProductStars"></div>
                        </div>
                    </div>
                    <div class="accommodation-cart-confirm-product-info">
                        <h4 id="postEventConfirmProductName"></h4>
                        <div class="accommodation-cart-confirm-product-meta">
                            <div class="accommodation-cart-confirm-product-meta-item">
                                <span class="accommodation-cart-confirm-product-meta-label"><i class="fas fa-calendar-check"></i> Check-in:</span>
                                <span class="accommodation-cart-confirm-product-meta-value">24/08/2025</span>
                            </div>
                            <div class="accommodation-cart-confirm-product-meta-item">
                                <span class="accommodation-cart-confirm-product-meta-label"><i class="fas fa-calendar-times"></i> Check-out:</span>
                                <span class="accommodation-cart-confirm-product-meta-value" id="postEventConfirmCheckoutDate"></span>
                            </div>
                            <div class="accommodation-cart-confirm-product-meta-item">
                                <span class="accommodation-cart-confirm-product-meta-label"><i class="fas fa-moon"></i> Nights:</span>
                                <span class="accommodation-cart-confirm-product-meta-value" id="postEventConfirmNights"></span>
                            </div>
                            <div class="accommodation-cart-confirm-product-meta-item" id="confirmRoomTypeRow" style="display: none;">
                                <span class="accommodation-cart-confirm-product-meta-label"><i class="fas fa-bed"></i> Room Type:</span>
                                <span class="accommodation-cart-confirm-product-meta-value" id="confirmRoomType"></span>
                            </div>
                            <div class="accommodation-cart-confirm-product-meta-item" id="confirmDoubleRoomRow" style="display: none;">
                                <span class="accommodation-cart-confirm-product-meta-label"><i class="fas fa-users"></i> Double Room:</span>
                                <span class="accommodation-cart-confirm-product-meta-value">Yes (quantity will be doubled)</span>
                            </div>
                            <div class="accommodation-cart-confirm-product-meta-item accommodation-cart-confirm-product-meta-total">
                                <span class="accommodation-cart-confirm-product-meta-label"><i class="fas fa-euro-sign"></i> Total Price:</span>
                                <span class="accommodation-cart-confirm-product-meta-value" id="confirmTotalPrice">Calculating...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="accommodation-cart-confirm-notice" id="confirmDoubleRoomNotice" style="display: none;">
                <i class="fas fa-info-circle"></i>
                <div>
                    <p class="mt-2 mb-0">You've selected a Double Room (sharing). This room type is designed for two people sharing the same room.</p>
                </div>
            </div>
        </div>
        <div class="accommodation-cart-confirm-footer">
            <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-secondary" onclick="closePostEventConfirmModal()" style="text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                <i class="fas fa-times me-2" style="color: white !important;"></i> <span style="color: white !important;">CANCEL</span>
            </button>
            <button type="button" id="confirmBookButton" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-primary" onclick="addToCartPostEvent()" style="text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                <i class="fas fa-bed me-2" style="color: white !important;"></i> <span style="color: white !important;">BOOK</span>
            </button>
        </div>
    </div>
</div>

<!-- Custom UX2025 Mini Cart Modal -->
<div id="custom-mini-cart-modal" class="custom-mini-cart-modal" style="display: none;">
    <div class="custom-mini-cart-overlay" onclick="closeCustomMiniCart()"></div>
    <div class="custom-mini-cart-content">
        <div class="custom-mini-cart-header">
            <h3><i class="fas fa-shopping-cart me-2" style="color: white;"></i> LCI AGM 2025</h3>
            <button type="button" class="custom-mini-cart-close" onclick="closeCustomMiniCart()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="custom-mini-cart-body">
            <!-- Loading state -->
            <div id="custom-mini-cart-loading" class="custom-mini-cart-loading">
                <div class="custom-mini-cart-loading-spinner"></div>
                <p>Loading your cart...</p>
            </div>

            <!-- Empty cart state -->
            <div id="custom-mini-cart-empty" class="custom-mini-cart-empty" style="display: none;">
                <div class="custom-mini-cart-empty-icon-wrapper">
                    <i id="custom-mini-cart-empty-icon" class="fas fa-shopping-cart"></i>
                </div>
                <h4 id="custom-mini-cart-empty-title">Your cart is empty</h4>
                <p id="custom-mini-cart-empty-subtitle">Add items to get started</p>
            </div>

            <!-- Cart items -->
            <div id="custom-mini-cart-items" class="custom-mini-cart-items" style="display: none;"></div>

            <!-- Cart footer -->
            <div class="custom-mini-cart-footer">
                <!-- Fundraising section -->
                <div id="custom-mini-cart-fundraising" class="custom-mini-cart-fundraising" style="display: none;">
                    <h4>Fundraising Contribution</h4>
                    <div class="custom-mini-cart-fundraising-row">
                        <span>Your contribution:</span>
                        <span id="custom-mini-cart-fundraising-amount">0,00 €</span>
                    </div>
                </div>

                <!-- Cart total -->
                <div class="custom-mini-cart-total-row">
                    <span>Total:</span>
                    <span id="custom-mini-cart-total"><?php
                        $total = WC()->cart->get_cart_contents_total();
                        echo number_format($total, 2, ',', '.') . ' €';
                    ?></span>
                </div>

                <!-- Cart buttons -->
                <div class="custom-mini-cart-buttons">
                    <button type="button" class="custom-mini-cart-btn-continue" onclick="closeCustomMiniCart()">
                        Continue Shopping
                    </button>
                    <a href="<?php echo esc_url(wc_get_checkout_url()); ?>" class="custom-mini-cart-btn-checkout">
                        <i class="fas fa-credit-card me-2"></i> Checkout
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom UX2025 Mini Cart Modal Styles */
.custom-mini-cart-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: none;
    animation: customFadeIn 0.3s ease;
}

@keyframes customFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.custom-mini-cart-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
}

.custom-mini-cart-content {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 400px;
    max-width: 100%;
    background-color: white;
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.custom-mini-cart-header {
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.custom-mini-cart-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
}

.custom-mini-cart-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.custom-mini-cart-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.custom-mini-cart-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.custom-mini-cart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
    flex: 1;
}

.custom-mini-cart-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #36b1dc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.custom-mini-cart-loading p {
    color: #78909c;
    font-size: 14px;
    margin: 0;
}

.custom-mini-cart-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
    flex: 1;
}

.custom-mini-cart-empty-icon-wrapper {
    width: 60px;
    height: 60px;
    background-color: #f5f5f5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.custom-mini-cart-empty-icon-wrapper i {
    font-size: 24px;
    color: #bdbdbd;
}

.custom-mini-cart-empty h4 {
    color: #37474f;
    font-size: 18px;
    margin: 0 0 5px 0;
    font-weight: 600;
}

.custom-mini-cart-empty p {
    color: #78909c;
    font-size: 14px;
    margin: 0;
    text-align: center;
}

.custom-mini-cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.custom-mini-cart-item {
    display: flex;
    align-items: flex-start;
    padding-bottom: 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid #f5f5f5;
}

.custom-mini-cart-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.custom-mini-cart-item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 15px;
    flex-shrink: 0;
}

.custom-mini-cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.custom-mini-cart-item-details {
    flex: 1;
}

.custom-mini-cart-item-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 15px;
}

.custom-mini-cart-item-meta {
    color: #5d6d7e;
    font-size: 13px;
    margin-bottom: 5px;
}

.custom-mini-cart-item-price {
    font-weight: 600;
    color: #36b1dc;
    font-size: 14px;
}

.custom-mini-cart-item-quantity {
    background: rgba(54, 177, 220, 0.1);
    color: #36b1dc;
    font-size: 12px;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 12px;
    margin-right: 10px;
}

.custom-mini-cart-item-remove {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.custom-mini-cart-item-remove:hover {
    background: rgba(220, 53, 69, 0.2);
}

.custom-mini-cart-footer {
    padding: 20px;
    border-top: 1px solid #f5f5f5;
    background-color: #f9f9fa;
}

.custom-mini-cart-fundraising {
    background: rgba(54, 177, 220, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.custom-mini-cart-fundraising h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #36b1dc;
}

.custom-mini-cart-fundraising-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.custom-mini-cart-fundraising-row span:first-child {
    color: #5d6d7e;
    font-size: 14px;
}

.custom-mini-cart-fundraising-row span:last-child {
    color: #36b1dc;
    font-weight: 600;
    font-size: 14px;
}

.custom-mini-cart-total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.custom-mini-cart-total-row span:first-child {
    color: #2c3e50;
    font-weight: 600;
    font-size: 16px;
}

.custom-mini-cart-total-row span:last-child {
    color: #2c3e50;
    font-weight: 700;
    font-size: 18px;
}

.custom-mini-cart-buttons {
    display: flex;
    gap: 10px;
}

.custom-mini-cart-btn-continue {
    flex: 1;
    padding: 12px;
    background-color: #f5f5f5;
    color: #5d6d7e;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.custom-mini-cart-btn-continue:hover {
    background-color: #e0e0e0;
}

.custom-mini-cart-btn-checkout {
    flex: 1;
    padding: 12px;
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-mini-cart-btn-checkout:hover {
    background: linear-gradient(135deg, #2980b9, #1c6ea4);
    color: white;
    text-decoration: none;
}

/* Animation for cart count */
@keyframes countPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); background-color: #ff6b6b; }
    100% { transform: scale(1); }
}

.count-pulse {
    animation: countPulse 0.5s ease;
}

/* Accommodation Cart Confirm Modal Styles */
.accommodation-cart-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: none;
}

.accommodation-cart-confirm-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

.accommodation-cart-confirm-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 95%;
    max-width: 700px;
    overflow: hidden;
}

.accommodation-cart-confirm-header {
    background-color: #36b1dc;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.accommodation-cart-confirm-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.accommodation-cart-confirm-header h3 i {
    color: white;
    margin-right: 8px;
}

.accommodation-cart-confirm-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.accommodation-cart-confirm-body {
    padding: 20px;
}

@media (min-width: 768px) {
    .accommodation-cart-confirm-header {
        padding: 20px 25px;
    }

    .accommodation-cart-confirm-header h3 {
        font-size: 22px;
    }

    .accommodation-cart-confirm-body {
        padding: 25px 30px;
    }

    .accommodation-cart-confirm-details {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .accommodation-cart-confirm-row {
        flex-direction: column;
        background-color: #f8f9fa;
        padding: 12px 15px;
        border-radius: 6px;
        border-bottom: none;
        margin-bottom: 0;
    }

    .accommodation-cart-confirm-label {
        margin-bottom: 5px;
        color: #6c757d;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .accommodation-cart-confirm-value {
        font-size: 16px;
        font-weight: 500;
    }

    .accommodation-cart-confirm-total {
        grid-column: 1 / -1;
        margin-top: 15px;
        background-color: #e3f2fd;
        border-top: none;
        padding-top: 0;
    }
}

.accommodation-cart-confirm-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.accommodation-cart-confirm-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.accommodation-cart-confirm-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.accommodation-cart-confirm-total {
    margin-top: 15px;
    border-top: 2px solid #e9ecef;
    padding-top: 15px;
    font-weight: bold;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-label {
    font-size: 16px;
    color: #343a40;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-value {
    font-size: 16px;
    color: #36b1dc;
}

.accommodation-cart-confirm-label {
    font-weight: 600;
    color: #495057;
}

.accommodation-cart-confirm-value {
    color: #343a40;
}

.accommodation-cart-confirm-notice {
    background-color: #e8f4fd;
    color: #0c5460;
    padding: 16px;
    border-radius: 8px;
    margin-top: 15px;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid #36b1dc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.accommodation-cart-confirm-notice i {
    margin-right: 12px;
    font-size: 18px;
    margin-top: 2px;
    color: #36b1dc;
}

.accommodation-cart-confirm-notice p {
    line-height: 1.5;
}

.accommodation-cart-confirm-notice strong {
    color: #0c5460;
    font-weight: 600;
}

.accommodation-cart-confirm-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
}

@media (min-width: 768px) {
    .accommodation-cart-confirm-footer {
        padding: 20px 25px;
    }

    .accommodation-cart-confirm-btn {
        min-width: 150px;
        padding: 12px 20px;
    }
}

.accommodation-cart-confirm-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    width: 48%;
    color: white;
}

.accommodation-cart-confirm-btn i {
    color: white;
    margin-right: 8px;
}

.accommodation-cart-confirm-btn-primary {
    background-color: #36b1dc;
    color: white;
}

.accommodation-cart-confirm-btn-primary:hover {
    background-color: #2d9cc3;
    color: white;
}

.accommodation-cart-confirm-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.accommodation-cart-confirm-btn-secondary:hover {
    background-color: #5a6268;
    color: white;
}

.accommodation-cart-confirm-dates-note {
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
}

.accommodation-cart-confirm-dates-note i {
    color: #36b1dc;
    margin-right: 6px;
    font-size: 14px;
}
</style>

<script>
// Variables to store product information for the confirmation modal
let postEventProductId = 0;
let postEventVariationId = 0;
let postEventNights = 1;
let postEventIsDoubleRoom = false;

// Function to show the confirmation modal
function showPostEventConfirmModal(productId, variationId, isDoubleRoom) {
    postEventProductId = productId;
    postEventVariationId = variationId;
    postEventIsDoubleRoom = isDoubleRoom;

    // Get saved nights from session storage
    postEventNights = parseInt(sessionStorage.getItem('postEventNights')) || 1;

    // Get product name
    const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${productId}"]`).closest('.accommodation-product-card');
    const productName = productCard.querySelector('.accommodation-product-title').textContent;

    // Get variation name if applicable
    let roomType = '';
    if (variationId) {
        const variationRadio = document.querySelector(`input[value="${variationId}"]`);
        if (variationRadio) {
            roomType = variationRadio.getAttribute('data-variation-name');
        }
    }

    // Calculate check-out date
    const checkoutDate = calculatePostEventCheckoutDate(postEventNights);

    // Update modal content
    document.getElementById('postEventConfirmProductName').textContent = productName;

    if (roomType) {
        document.getElementById('confirmRoomTypeRow').style.display = 'flex';
        document.getElementById('confirmRoomType').textContent = roomType;
    } else {
        document.getElementById('confirmRoomTypeRow').style.display = 'none';
    }

    document.getElementById('postEventConfirmCheckoutDate').textContent = checkoutDate;
    document.getElementById('postEventConfirmNights').textContent = `${postEventNights} ${postEventNights === 1 ? 'night' : 'nights'}`;

    // Hide double room notice (no longer needed)
    document.getElementById('confirmDoubleRoomRow').style.display = 'none';
    document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

    // Calculate and display the total price
    let productPrice = 0;

    if (variationId) {
        // Get price from the selected variation
        const selectedVariation = document.querySelector(`input[value="${variationId}"]`);
        if (selectedVariation) {
            productPrice = parseFloat(selectedVariation.getAttribute('data-price') || 0);
        }
    } else {
        // Get price from the button's data attribute
        const addToCartBtn = document.querySelector(`.add-to-cart-btn[data-product-id="${productId}"]`);
        if (addToCartBtn) {
            productPrice = parseFloat(addToCartBtn.getAttribute('data-price') || 0);
        }
    }

    // Calculate total price based on nights
    const totalPrice = productPrice * postEventNights;

    // Format the price with currency symbol
    const formattedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(totalPrice);

    // Update the price display
    document.getElementById('confirmTotalPrice').textContent = formattedPrice;

    // Show modal
    const confirmModal = document.getElementById('postEventCartConfirmModal');
    confirmModal.style.display = 'block';
}

// Function to close the confirmation modal
function closePostEventConfirmModal() {
    const confirmModal = document.getElementById('postEventCartConfirmModal');
    confirmModal.style.display = 'none';
}

// Function to calculate check-out date
function calculatePostEventCheckoutDate(nights) {
    // Base date is 24/08/2025 (check-in date)
    const baseDate = new Date(2025, 7, 24); // Month is 0-indexed, so 7 = August

    // Calculate check-out date by adding nights to base date
    const checkoutDate = new Date(baseDate);
    checkoutDate.setDate(baseDate.getDate() + nights);

    // Format the date as DD/MM/YYYY
    const day = String(checkoutDate.getDate()).padStart(2, '0');
    const month = String(checkoutDate.getMonth() + 1).padStart(2, '0');
    const year = checkoutDate.getFullYear();

    return `${day}/${month}/${year}`;
}

// Function to add product to cart
window.addToCartPostEvent = function() {
    console.log('Real addToCartPostEvent function called');
    const confirmModal = document.getElementById('postEventCartConfirmModal');
    const modalContent = document.querySelector('.accommodation-cart-confirm-content');
    const modalDetails = document.querySelector('.accommodation-cart-confirm-details');
    const modalFooter = document.querySelector('.accommodation-cart-confirm-footer');
    const modalTitle = document.querySelector('.accommodation-cart-confirm-header h3');
    const modalIntro = document.querySelector('.accommodation-cart-confirm-body > p');
    const bookButton = document.querySelector('.accommodation-cart-confirm-btn-primary');

    // Show loading state
    if (modalContent) {
        modalContent.classList.add('processing');
    }

    if (modalDetails) {
        modalDetails.style.display = 'none';
    }

    if (modalFooter) {
        modalFooter.style.display = 'none';
    }

    if (modalTitle) {
        modalTitle.innerHTML = '<i class="fas fa-spinner fa-spin me-2" style="color: white;"></i> Processing...';
    }

    if (modalIntro) {
        modalIntro.innerHTML = 'Please wait while we add your selection to the cart...';
    }

    if (bookButton) {
        bookButton.disabled = true;
        bookButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2" style="color: white !important;"></i> <span style="color: white !important;">PROCESSING</span>';
    }

    // Calculate quantity based on room type
    const quantity = postEventIsDoubleRoom ? postEventNights * 2 : postEventNights;

    // Get the security nonce if available
    const nonce = typeof lci_ajax_object !== 'undefined' ? lci_ajax_object.nonce : '';
    const ajaxUrl = typeof lci_ajax_object !== 'undefined' ? lci_ajax_object.ajax_url : '/wp-admin/admin-ajax.php';

    // Prepare the data for the AJAX request
    const data = {
        action: 'lci-dashboard-add-to-cart',
        product_id: postEventProductId,
        quantity: quantity,
        security: nonce
    };

    // Add variation ID if applicable
    if (postEventVariationId) {
        data.variation_id = postEventVariationId;
    }

    console.log('Sending AJAX request with data:', data);

    // Send AJAX request
    jQuery.ajax({
        type: 'POST',
        url: ajaxUrl,
        data: data,
        success: function(response) {
            console.log('AJAX success response:', response);

            // Show success message
            if (modalTitle) {
                modalTitle.innerHTML = '<i class="fas fa-check-circle me-2" style="color: white;"></i> Added to Cart';
            }

            if (modalIntro) {
                modalIntro.innerHTML = 'Your selection has been added to your cart successfully!';
            }

            // Show success message instead of reloading
            const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${postEventProductId}"]`).closest('.accommodation-product-card');
            const addToCartBtn = productCard.querySelector('.add-to-cart-btn');

            // Replace button with success message
            if (addToCartBtn) {
                addToCartBtn.innerHTML = '<i class="fas fa-check me-2"></i> Added to Cart';
                addToCartBtn.style.backgroundColor = '#28a745';
                addToCartBtn.disabled = true;
            }

            // Hide settings button
            const settingsBtn = productCard.querySelector('.accommodation-settings-button');
            if (settingsBtn) {
                settingsBtn.style.display = 'none';
            }

            // Refresh the mini-cart contents in the background
            setTimeout(() => {
                refreshMiniCart();
            }, 500);

            // Auto-close the modal after 5 seconds
            setTimeout(() => {
                closePostEventConfirmModal(); // This function uses the correct modal ID 'postEventCartConfirmModal'
            }, 3000);
        },
        error: function(error) {
            console.error('Error adding to cart:', error);

            // Reset modal state
            if (modalTitle) {
                modalTitle.innerHTML = '<i class="fas fa-check-circle me-2" style="color: white;"></i> Confirm Your Selection';
            }

            if (modalIntro) {
                modalIntro.innerHTML = 'Please confirm your post-event accommodation selection:';
            }

            if (bookButton) {
                bookButton.disabled = false;
                bookButton.innerHTML = '<i class="fas fa-shopping-cart me-2" style="color: white !important;"></i> <span style="color: white !important;">ADD TO CART</span>';
            }

            if (modalContent) {
                modalContent.classList.remove('processing');
            }

            // Show the details and footer again
            if (modalDetails) {
                modalDetails.style.display = 'block';
            }

            if (modalFooter) {
                modalFooter.style.display = 'flex';
            }

            // Hide confirmation modal
            confirmModal.style.display = 'none';

            // Show error notification
            showErrorNotification('Error: Could not add to cart. Please try again.');
        }
    });
}

// Function to refresh the mini-cart
function refreshMiniCart() {
    // Get the mini-cart container
    const miniCartContainer = document.querySelector('.dashboard-mini-cart');
    if (!miniCartContainer) {
        console.warn('Mini-cart container not found');
        return;
    }

    console.log('Refreshing mini-cart...');

    // Add loading indicator
    miniCartContainer.classList.add('loading');

    // Fetch updated mini-cart HTML via AJAX
    fetch('?wc-ajax=get_refreshed_fragments', {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update fragments (including mini-cart)
        if (data.fragments) {
            for (let id in data.fragments) {
                const element = document.querySelector(id);
                if (element) {
                    element.innerHTML = data.fragments[id];
                }
            }
        }

        // Remove loading indicator
        miniCartContainer.classList.remove('loading');

        console.log('Mini-cart refreshed successfully');
    })
    .catch(error => {
        console.error('Error refreshing mini-cart:', error);
        miniCartContainer.classList.remove('loading');
    });
}

// Function to show a nice UX2025 styled notification when product is added to cart
window.showAddedToCartNotification = function(productName, roomType, nights, totalPrice, productImage) {
    // Create notification container if it doesn't exist
    let notificationContainer = document.getElementById('addedToCartNotification');

    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'addedToCartNotification';
        notificationContainer.style.position = 'fixed';
        notificationContainer.style.top = '20px';
        notificationContainer.style.right = '20px';
        notificationContainer.style.zIndex = '10000';
        notificationContainer.style.maxWidth = '400px';
        notificationContainer.style.width = '100%';
        document.body.appendChild(notificationContainer);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.style.background = 'rgba(255, 255, 255, 0.95)';
    notification.style.borderRadius = '16px';
    notification.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(54, 177, 220, 0.1)';
    notification.style.padding = '20px';
    notification.style.marginBottom = '15px';
    notification.style.animation = 'slideInRight 0.5s forwards';
    notification.style.position = 'relative';
    notification.style.overflow = 'hidden';
    notification.style.backdropFilter = 'blur(10px)';
    notification.style.webkitBackdropFilter = 'blur(10px)';
    notification.style.border = '1px solid rgba(255, 255, 255, 0.8)';

    // Add keyframes for animation if they don't exist
    if (!document.getElementById('notification-animations')) {
        const style = document.createElement('style');
        style.id = 'notification-animations';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    // Add success icon and gradient
    notification.innerHTML = `
        <div style="position: absolute; top: 0; left: 0; right: 0; height: 5px; background: linear-gradient(to right, #36b1dc, #2980b9);"></div>
        <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>

        <div style="display: flex; align-items: flex-start;">
            <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.3);">
                <i class="fas fa-check" style="color: white; font-size: 18px;"></i>
            </div>

            <div style="flex: 1;">
                <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 16px; font-weight: 700;">Added to Your Cart</h4>

                <div style="background: rgba(248, 249, 250, 0.7); border-radius: 10px; padding: 12px; margin-bottom: 15px;">
                    <div style="margin-bottom: 8px;">
                        <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Hotel:</span>
                        <span style="color: #2c3e50; font-weight: 600;">${productName}</span>
                    </div>

                    ${roomType ? `
                    <div style="margin-bottom: 8px;">
                        <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Room Type:</span>
                        <span style="color: #2c3e50; font-weight: 600;">${roomType}</span>
                    </div>
                    ` : ''}

                    <div style="margin-bottom: 8px;">
                        <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Stay Duration:</span>
                        <span style="color: #2c3e50; font-weight: 600;">${nights}</span>
                    </div>

                    <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.1), rgba(54, 177, 220, 0.05)); padding: 8px 12px; border-radius: 8px; margin-top: 10px;">
                        <span style="color: #2c3e50; font-weight: 700; font-size: 14px;">Total: ${totalPrice}</span>
                    </div>
                </div>

                <div style="display: flex; justify-content: flex-end;">
                    <button onclick="this.closest('#addedToCartNotification').removeChild(this.closest('div').parentNode.parentNode)" style="background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; border: none; font-size: 14px; cursor: pointer; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; display: inline-flex; align-items: center;">
                        <i class="fas fa-check" style="margin-right: 8px; color: white;"></i> OK
                    </button>
                </div>
            </div>
        </div>
    `;

    // Add notification to container
    notificationContainer.appendChild(notification);

    // Auto-remove notification after 8 seconds
    setTimeout(() => {
        if (notification.parentNode === notificationContainer) {
            notification.style.animation = 'slideOutRight 0.5s forwards';
            setTimeout(() => {
                if (notification.parentNode === notificationContainer) {
                    notificationContainer.removeChild(notification);
                }
            }, 500);
        }
    }, 8000);
}

// Function to show error notification
window.showErrorNotification = function(message) {
    // Create notification container if it doesn't exist
    let notificationContainer = document.getElementById('addedToCartNotification');

    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'addedToCartNotification';
        notificationContainer.style.position = 'fixed';
        notificationContainer.style.top = '20px';
        notificationContainer.style.right = '20px';
        notificationContainer.style.zIndex = '10000';
        notificationContainer.style.maxWidth = '400px';
        notificationContainer.style.width = '100%';
        document.body.appendChild(notificationContainer);
    }

    // Add keyframes for animation if they don't exist
    if (!document.getElementById('notification-animations')) {
        const style = document.createElement('style');
        style.id = 'notification-animations';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.style.background = 'rgba(255, 255, 255, 0.95)';
    notification.style.borderRadius = '16px';
    notification.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(220, 53, 69, 0.1)';
    notification.style.padding = '20px';
    notification.style.marginBottom = '15px';
    notification.style.animation = 'slideInRight 0.5s forwards';
    notification.style.position = 'relative';
    notification.style.overflow = 'hidden';
    notification.style.backdropFilter = 'blur(10px)';
    notification.style.webkitBackdropFilter = 'blur(10px)';
    notification.style.border = '1px solid rgba(255, 255, 255, 0.8)';

    // Add error icon and gradient
    notification.innerHTML = `
        <div style="position: absolute; top: 0; left: 0; right: 0; height: 5px; background: linear-gradient(to right, #dc3545, #c82333);"></div>
        <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(220, 53, 69, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>

        <div style="display: flex; align-items: flex-start;">
            <div style="background: linear-gradient(135deg, #dc3545, #c82333); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);">
                <i class="fas fa-exclamation-triangle" style="color: white; font-size: 18px;"></i>
            </div>

            <div style="flex: 1;">
                <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 16px; font-weight: 700;">Error</h4>
                <p style="margin: 0 0 15px 0; color: #495057;">${message}</p>

                <button onclick="this.closest('#addedToCartNotification').removeChild(this.closest('div').parentNode.parentNode)" style="background: none; border: none; color: #6c757d; font-size: 14px; cursor: pointer; padding: 8px 12px; border-radius: 6px; transition: all 0.3s ease;">
                    Close
                </button>
            </div>
        </div>
    `;

    // Add notification to container
    notificationContainer.appendChild(notification);

    // Auto-remove notification after 8 seconds
    setTimeout(() => {
        if (notification.parentNode === notificationContainer) {
            notification.style.animation = 'slideOutRight 0.5s forwards';
            setTimeout(() => {
                if (notification.parentNode === notificationContainer) {
                    notificationContainer.removeChild(notification);
                }
            }, 500);
        }
    }, 8000);
}
</script>

<!-- Post-Event Accommodation Modal -->
<div x-data="postEventAccommodation()" x-init="initModal()">
    <div class="post-event-modal-overlay" id="postEventModalOverlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); z-index: 9999; justify-content: center; align-items: center; backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);">
        <div class="post-event-modal" style="background: white; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2); position: relative; animation: modalFadeIn 0.5s ease-out;">
            <style>
            @keyframes modalFadeIn {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            </style>
            <div class="post-event-modal-header">
                <h3><i class="fas fa-hotel"></i> Post-Event Accommodation</h3>
                <button onclick="closePostEventModal()" class="btn-close btn-close-white" aria-label="Close" style="position: relative; z-index: 2;"></button>
            </div>
            <div class="post-event-modal-body">
                <!-- Decorative elements -->
                <div class="position-absolute" style="top: 20px; right: 20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                <div class="position-absolute" style="bottom: 30px; left: 10px; width: 120px; height: 120px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.03), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                <p class="mb-4" style="color: #37474f; font-size: 15px; position: relative; z-index: 1;">Please select your accommodation preferences for your stay after the main event:</p>

                <!-- Nights Selector - Enhanced UX2025 Style -->
                <div class="post-event-modal-form-group">
                    <label for="nights" class="nights-label-header" style="display: flex; align-items: center; margin-bottom: 18px; color: #2c3e50; font-weight: 700;">
                        <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2), inset 0 2px 3px rgba(255, 255, 255, 0.3);">
                            <i class="fas fa-moon" style="color: white; font-size: 18px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));"></i>
                        </div>
                        <span style="position: relative; font-size: 18px;">
                            How many nights will you stay?
                            <div style="position: absolute; bottom: -5px; left: 0; width: 40px; height: 3px; background: linear-gradient(to right, #36b1dc, transparent); border-radius: 3px;"></div>
                        </span>
                    </label>
                    <div class="nights-selector" style="max-width: 100%;">
                        <div class="nights-selector-container" style="background: rgba(255, 255, 255, 0.8); border: 2px solid rgba(54, 177, 220, 0.3); border-radius: 20px; padding: 15px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); position: relative; overflow: hidden; display: flex; align-items: center;">
                            <!-- Decorative elements -->
                            <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                            <div style="position: absolute; bottom: -30px; left: 10%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.08), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                            <button type="button" class="nights-btn nights-btn-minus" @click="decrementNights()" :disabled="nights <= 1" style="width: 50px; height: 50px; border-radius: 15px; background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; border: none; font-size: 20px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2); position: relative; z-index: 1;">
                                <i class="fas fa-minus" style="color: white !important;"></i>
                            </button>
                            <div class="nights-display" style="flex-grow: 1; text-align: center; padding: 0 20px; position: relative; z-index: 1;">
                                <div class="nights-value-container" style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                                    <span class="nights-count" x-text="nights" style="font-size: 36px; font-weight: 800; color: #2c3e50; margin-right: 10px; text-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);"></span>
                                    <span class="nights-label" x-text="nights === 1 ? 'Night' : 'Nights'" style="font-size: 20px; color: #5d6d7e; font-weight: 600;"></span>
                                </div>
                                <!-- Enhanced Progress Bar with Markers -->
                                <div class="nights-progress-container" style="margin-top: 15px; position: relative;">
                                    <!-- Progress Track -->
                                    <div style="height: 12px; background: rgba(54, 177, 220, 0.1); border-radius: 6px; overflow: hidden; box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); position: relative;">
                                        <!-- Progress Fill -->
                                        <div class="nights-progress-bar" :style="'width: ' + (nights * 20) + '%'" style="height: 100%; background: linear-gradient(to right, #36b1dc, #2980b9); border-radius: 6px; transition: width 0.3s ease; box-shadow: 0 1px 3px rgba(54, 177, 220, 0.3);">
                                            <!-- Animated Glow Effect -->
                                            <div style="position: absolute; top: 0; right: 0; bottom: 0; width: 15px; background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent); animation: progressGlow 1.5s infinite; transform: skewX(-20deg);"></div>
                                        </div>
                                    </div>

                                    <!-- Progress Markers -->
                                    <div style="position: relative; height: 30px; margin-top: 5px;">
                                        <!-- Marker 1 -->
                                        <div style="position: absolute; left: 0%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 1 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 1 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 1 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">1 Night</span>
                                        </div>

                                        <!-- Marker 2 -->
                                        <div style="position: absolute; left: 25%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 2 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 2 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 2 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">2 Nights</span>
                                        </div>

                                        <!-- Marker 3 -->
                                        <div style="position: absolute; left: 50%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 3 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 3 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 3 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">3 Nights</span>
                                        </div>

                                        <!-- Marker 4 -->
                                        <div style="position: absolute; left: 75%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 4 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 4 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 4 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">4 Nights</span>
                                        </div>

                                        <!-- Marker 5 -->
                                        <div style="position: absolute; left: 100%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 5 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 5 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 5 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">5 Nights</span>
                                        </div>
                                    </div>

                                    <style>
                                    @keyframes progressGlow {
                                        0% { left: -15px; }
                                        100% { left: 100%; }
                                    }

                                    .active-marker {
                                        transform: scale(1.1);
                                    }
                                    </style>
                                </div>
                            </div>
                            <button type="button" class="nights-btn nights-btn-plus" @click="incrementNights()" style="width: 50px; height: 50px; border-radius: 15px; background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; border: none; font-size: 20px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2); position: relative; z-index: 1;">
                                <i class="fas fa-plus" style="color: white !important;"></i>
                            </button>
                        </div>
                    </div>

                    <style>
                    /* Hover and active states for night selector buttons */
                    .nights-btn:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 6px 15px rgba(54, 177, 220, 0.3);
                        background: linear-gradient(135deg, #2980b9, #1c6ea4);
                    }

                    .nights-btn:active {
                        transform: translateY(1px);
                        box-shadow: 0 2px 5px rgba(54, 177, 220, 0.2);
                    }

                    .nights-btn:hover i,
                    .nights-btn:active i {
                        color: white !important;
                    }

                    /* Disabled state */
                    .nights-btn:disabled {
                        background: linear-gradient(135deg, #a8d1e7, #a8d1e7);
                        cursor: not-allowed;
                        transform: none;
                        opacity: 0.7;
                    }

                    /* Add ripple effect on button click */
                    .nights-btn::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 5px;
                        height: 5px;
                        background: rgba(255, 255, 255, 0.5);
                        opacity: 0;
                        border-radius: 100%;
                        transform: scale(1, 1) translate(-50%, -50%);
                        transform-origin: 50% 50%;
                    }

                    .nights-btn:active::after {
                        animation: nightsBtnRipple 0.6s ease-out;
                    }

                    @keyframes nightsBtnRipple {
                        0% {
                            transform: scale(0, 0);
                            opacity: 0.5;
                        }
                        100% {
                            transform: scale(20, 20);
                            opacity: 0;
                        }
                    }

                    /* Animations for nights change */
                    .nights-change-animation {
                        animation: nightsChange 0.3s ease-out;
                    }

                    @keyframes nightsChange {
                        0% {
                            transform: scale(1);
                        }
                        50% {
                            transform: scale(1.2);
                            color: #36b1dc;
                        }
                        100% {
                            transform: scale(1);
                        }
                    }

                    .progress-change-animation {
                        animation: progressChange 0.3s ease-out;
                    }

                    @keyframes progressChange {
                        0% {
                            background: linear-gradient(to right, #36b1dc, #2980b9);
                        }
                        50% {
                            background: linear-gradient(to right, #4CAF50, #2980b9);
                        }
                        100% {
                            background: linear-gradient(to right, #36b1dc, #2980b9);
                        }
                    }

                    .marker-pulse {
                        animation: markerPulse 0.5s ease-out;
                    }

                    @keyframes markerPulse {
                        0% {
                            transform: scale(1.1);
                            box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);
                        }
                        50% {
                            transform: scale(1.3);
                            box-shadow: 0 0 0 5px rgba(54, 177, 220, 0.4);
                        }
                        100% {
                            transform: scale(1.1);
                            box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);
                        }
                    }
                    </style>
                </div>

                <!-- Room Type Selector - Enhanced UX2025 Style -->
                <div class="post-event-modal-form-group" style="margin-top: 25px;">
                    <label for="roomType" style="display: flex; align-items: center; margin-bottom: 18px; color: #2c3e50; font-weight: 700;">
                        <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2), inset 0 2px 3px rgba(255, 255, 255, 0.3);">
                            <i class="fas fa-bed" style="color: white; font-size: 18px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));"></i>
                        </div>
                        <span style="position: relative; font-size: 18px;">
                            Room Type
                            <div style="position: absolute; bottom: -5px; left: 0; width: 40px; height: 3px; background: linear-gradient(to right, #36b1dc, transparent); border-radius: 3px;"></div>
                        </span>
                    </label>

                    <div style="position: relative;">
                        <select id="roomType" x-model="roomType" class="form-select" style="border-radius: 16px; border: 2px solid rgba(54, 177, 220, 0.3); padding: 15px 20px; background: rgba(255, 255, 255, 0.8); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); font-size: 16px; color: #2c3e50; transition: all 0.3s ease; backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); appearance: none; -webkit-appearance: none; font-weight: 600; width: 100%;">
                            <option value="single">Single Room (1 person)</option>
                            <option value="double">Double Room (sharing)</option>
                        </select>
                        <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                            <i class="fas fa-chevron-down" style="color: #36b1dc; font-size: 16px;"></i>
                        </div>
                    </div>

                    <style>
                    /* Custom styling for the select dropdown */
                    .form-select:focus {
                        outline: none;
                        border-color: #36b1dc;
                        box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);
                    }

                    .form-select:hover {
                        border-color: #36b1dc;
                        transform: translateY(-2px);
                        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.07);
                    }
                    </style>
                </div>

                <!-- Stay Dates Information - UX2025 Style -->
                <!-- Your Stay Dates Header - Now outside the dates container -->
                <div style="display: flex; align-items: center; margin: 25px 0 15px 0;">
                    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2), inset 0 2px 3px rgba(255, 255, 255, 0.3);">
                        <i class="fas fa-calendar-alt" style="color: white; font-size: 18px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));"></i>
                    </div>
                    <span style="color: #2c3e50; font-weight: 700; font-size: 18px; position: relative;">
                        Your Stay Dates
                        <div style="position: absolute; bottom: -5px; left: 0; width: 40px; height: 3px; background: linear-gradient(to right, #36b1dc, transparent); border-radius: 3px;"></div>
                    </span>
                </div>

                <div class="post-event-modal-dates" style="background: rgba(255, 255, 255, 0.8); border-radius: 20px; padding: 20px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); border: 1px solid rgba(54, 177, 220, 0.2); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); position: relative; overflow: hidden;">
                    <!-- Decorative elements -->
                    <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                    <div style="position: absolute; bottom: -30px; left: 10%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.08), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                    <div class="post-event-modal-dates-row" style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border-bottom: 1px solid rgba(0, 0, 0, 0.05); background: rgba(255, 255, 255, 0.5); border-radius: 12px; margin-bottom: 10px; transition: all 0.3s ease;">
                        <span class="post-event-modal-dates-label" style="color: #5d6d7e; font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">Check-in Date:</span>
                        <span class="post-event-modal-dates-value" style="color: #2c3e50; font-weight: 600; font-size: 15px; background: linear-gradient(135deg, rgba(54, 177, 220, 0.15), rgba(54, 177, 220, 0.05)); padding: 8px 15px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);">24/08/2025</span>
                    </div>
                    <div class="post-event-modal-dates-row" style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background: rgba(255, 255, 255, 0.5); border-radius: 12px; transition: all 0.3s ease;">
                        <span class="post-event-modal-dates-label" style="color: #5d6d7e; font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">Check-out Date:</span>
                        <span class="post-event-modal-dates-value" x-text="calculateCheckoutDate()" style="color: #2c3e50; font-weight: 600; font-size: 15px; background: linear-gradient(135deg, rgba(54, 177, 220, 0.15), rgba(54, 177, 220, 0.05)); padding: 8px 15px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);"></span>
                    </div>
                </div>

                <!-- Notice for Double Room - UX2025 Style -->
                <div class="post-event-modal-notice" x-show="roomType === 'double'" style="margin-top: 20px; background: rgba(54, 177, 220, 0.08); border-left: 4px solid #36b1dc; padding: 15px; border-radius: 12px; display: flex; align-items: flex-start; animation: fadeIn 0.5s ease-out; position: relative; overflow: hidden;">
                    <!-- Decorative elements -->
                    <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2); flex-shrink: 0; position: relative; z-index: 1;">
                        <i class="fas fa-info" style="color: white; font-size: 14px;"></i>
                    </div>
                    <div style="position: relative; z-index: 1;">
                        <p style="margin: 0; color: #5d6d7e; font-size: 14px; line-height: 1.5;">
                            You've selected a Double Room (sharing). This room type is designed for two people sharing the same room.
                        </p>
                    </div>

                    <style>
                    @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(10px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                    </style>
                </div>
            </div>
            <div class="post-event-modal-footer" style="display: flex; justify-content: space-between; padding: 20px; border-top: 1px solid rgba(0, 0, 0, 0.05);">
                <button onclick="closePostEventModal()" class="post-event-modal-btn post-event-modal-btn-secondary" style="flex: 1; margin-right: 10px; padding: 15px; border: none; border-radius: 12px; background: #f1f5f9; color: #64748b; font-weight: 600; cursor: pointer; transition: all 0.3s ease; text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    Cancel
                </button>
                <button @click="savePreferences()" class="post-event-modal-btn post-event-modal-btn-primary" style="flex: 1; margin-left: 10px; padding: 15px; border: none; border-radius: 12px; background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; font-weight: 600; cursor: pointer; transition: all 0.3s ease; text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.3);">
                    <i class="fas fa-check me-2" style="color: white !important;"></i> Save Preferences
                </button>

                <style>
                /* Button hover effects */
                .post-event-modal-btn-secondary:hover {
                    background: #e2e8f0;
                    transform: translateY(-2px);
                    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
                }

                .post-event-modal-btn-primary:hover {
                    background: linear-gradient(135deg, #2980b9, #1c6ea4);
                    transform: translateY(-2px);
                    box-shadow: 0 6px 15px rgba(54, 177, 220, 0.4);
                }

                /* Button active effects */
                .post-event-modal-btn-secondary:active {
                    transform: translateY(1px);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                }

                .post-event-modal-btn-primary:active {
                    transform: translateY(1px);
                    box-shadow: 0 2px 5px rgba(54, 177, 220, 0.2);
                }

                /* Media query for mobile */
                @media (max-width: 576px) {
                    .post-event-modal-footer {
                        flex-direction: column;
                    }

                    .post-event-modal-btn-secondary {
                        margin-right: 0;
                        margin-bottom: 10px;
                    }

                    .post-event-modal-btn-primary {
                        margin-left: 0;
                        margin-top: 10px;
                    }
                }
                </style>
            </div>
        </div>
    </div>
</div>

<script>
// Function to update current settings display
window.updateCurrentSettingsDisplay = function() {
    try {
        const savedRoomType = sessionStorage.getItem('postEventRoomType') || 'single';
        const savedNights = parseInt(sessionStorage.getItem('postEventNights')) || 1;

        // Update nights display
        const currentNightsElement = document.getElementById('current-nights');
        if (currentNightsElement) {
            currentNightsElement.textContent = savedNights;
        }

        // Update room type display
        const currentRoomTypeElement = document.getElementById('current-room-type');
        if (currentRoomTypeElement) {
            currentRoomTypeElement.textContent = savedRoomType === 'single' ? 'Single Room' : 'Double Room';
        }

        // Update check-out date display
        const currentCheckoutDateElement = document.getElementById('current-checkout-date');
        if (currentCheckoutDateElement) {
            // Base date is 24/08/2025 (check-in date)
            const baseDate = new Date(2025, 7, 24); // Month is 0-indexed, so 7 = August

            // Calculate check-out date by adding nights to base date
            const checkoutDate = new Date(baseDate);
            checkoutDate.setDate(baseDate.getDate() + savedNights);

            // Format the date as DD/MM/YYYY
            const day = String(checkoutDate.getDate()).padStart(2, '0');
            const month = String(checkoutDate.getMonth() + 1).padStart(2, '0');
            const year = checkoutDate.getFullYear();

            currentCheckoutDateElement.textContent = `${day}/${month}/${year}`;
        }
    } catch (error) {
        console.error('Error updating current settings display:', error);
    }
}

function postEventAccommodation() {
    return {
        showModal: false,
        nights: 1,
        roomType: 'single',

        initModal() {
            console.log('Initializing Post-Event modal');

            // Load saved preferences if available
            const savedNights = sessionStorage.getItem('postEventNights');
            const savedRoomType = sessionStorage.getItem('postEventRoomType');

            if (savedNights) this.nights = parseInt(savedNights);
            if (savedRoomType) this.roomType = savedRoomType;

            // Always apply preferences to buttons
            this.applyPreferencesToButtons();

            // Update current settings display
            if (typeof updateCurrentSettingsDisplay === 'function') {
                updateCurrentSettingsDisplay();
            }

            // Listen for modal visibility changes
            this.$watch('showModal', value => {
                if (value) {
                    console.log('Modal is now visible');
                    document.querySelector('.post-event-modal-overlay').style.display = 'flex';
                }
            });

            // Add animation classes to elements
            this.$nextTick(() => {
                // Add animation delays to progress markers
                const markers = document.querySelectorAll('.nights-progress-container [style*="position: absolute; left:"]');
                markers.forEach((marker, index) => {
                    const elements = marker.querySelectorAll('*');
                    elements.forEach(el => {
                        el.style.transitionDelay = `${index * 0.05}s`;
                    });
                });
            });
        },

        closeModal() {
            this.showModal = false;
            document.querySelector('.post-event-modal-overlay').style.display = 'none';
        },

        incrementNights() {
            console.log('Incrementing nights');
            if (this.nights < 5) {
                this.nights++;
                document.querySelector('.nights-count').textContent = this.nights;
                this.updateNightsDisplay();
                this.animateNightsChange('increase');
            }
        },

        decrementNights() {
            console.log('Decrementing nights');
            if (this.nights > 1) {
                this.nights--;
                document.querySelector('.nights-count').textContent = this.nights;
                this.updateNightsDisplay();
                this.animateNightsChange('decrease');
            }
        },

        animateNightsChange(direction) {
            // Add animation to the nights count
            const nightsCount = document.querySelector('.nights-count');
            if (nightsCount) {
                nightsCount.classList.add('nights-change-animation');
                setTimeout(() => {
                    nightsCount.classList.remove('nights-change-animation');
                }, 300);
            }

            // Add animation to the progress bar
            const progressBar = document.querySelector('.nights-progress-bar');
            if (progressBar) {
                progressBar.classList.add('progress-change-animation');
                setTimeout(() => {
                    progressBar.classList.remove('progress-change-animation');
                }, 300);
            }

            // Add animation to the active markers
            const activeMarkers = document.querySelectorAll('.active-marker');
            activeMarkers.forEach(marker => {
                marker.classList.add('marker-pulse');
                setTimeout(() => {
                    marker.classList.remove('marker-pulse');
                }, 500);
            });
        },

        updateNightsDisplay() {
            console.log('Updating nights display');

            // Update nights label
            const nightsLabel = document.querySelector('.nights-label');
            if (nightsLabel) {
                nightsLabel.textContent = this.nights === 1 ? 'Night' : 'Nights';
            }

            // Update checkout date display
            const checkoutDateElement = document.querySelector('.post-event-modal-dates-value:nth-child(2)');
            if (checkoutDateElement) {
                checkoutDateElement.textContent = this.calculateCheckoutDate();
                console.log('Updated checkout date to:', this.calculateCheckoutDate());
            }

            // Update double room calculation if visible
            if (this.roomType === 'double') {
                const doubleRoomElements = document.querySelectorAll('strong[x-text="nights"]');
                doubleRoomElements.forEach(el => {
                    el.textContent = this.nights;
                });

                const totalNightsElements = document.querySelectorAll('strong[x-text="nights * 2"]');
                totalNightsElements.forEach(el => {
                    el.textContent = this.nights * 2;
                });

                console.log('Updated double room calculations');
            }
        },

        calculateCheckoutDate() {
            // Base date is 24/08/2025 (check-in date)
            const baseDate = new Date(2025, 7, 24); // Month is 0-indexed, so 7 = August

            // Calculate checkout date by adding nights to base date
            // For 1 night: Check-in on 24/08, stay night of 24-25, check out on 25/08
            // For 2 nights: Check-in on 24/08, stay nights of 24-25 and 25-26, check out on 26/08
            const checkoutDate = new Date(baseDate);
            checkoutDate.setDate(baseDate.getDate() + this.nights);

            // Format the date as DD/MM/YYYY
            const day = String(checkoutDate.getDate()).padStart(2, '0');
            const month = String(checkoutDate.getMonth() + 1).padStart(2, '0');
            const year = checkoutDate.getFullYear();

            return `${day}/${month}/${year}`;
        },

        savePreferences() {
            console.log('Alpine savePreferences called');

            try {
                // Save preferences to session storage
                sessionStorage.setItem('postEventNights', this.nights.toString());
                sessionStorage.setItem('postEventRoomType', this.roomType);
                console.log('Saved to session storage:', { nights: this.nights, roomType: this.roomType });

                // Add animation to the save button
                const saveButton = document.querySelector('.post-event-modal-btn-primary');
                if (saveButton) {
                    saveButton.innerHTML = '<i class="fas fa-check me-2" style="color: white !important;"></i> Saved!';
                    saveButton.style.background = 'linear-gradient(135deg, #4CAF50, #388E3C)';
                    saveButton.style.transform = 'translateY(-2px)';
                    saveButton.style.boxShadow = '0 6px 15px rgba(76, 175, 80, 0.4)';

                    setTimeout(() => {
                        // Apply preferences to buttons in Alpine component
                        this.applyPreferencesToButtons();

                        // Update current settings display
                        if (typeof updateCurrentSettingsDisplay === 'function') {
                            updateCurrentSettingsDisplay();
                        }

                        // Call global functions to ensure everything is updated
                        if (typeof updatePostEventAddToCartButtons === 'function') {
                            updatePostEventAddToCartButtons(this.nights, this.roomType);
                        }

                        if (typeof applyPostEventRoomTypePreferences === 'function') {
                            applyPostEventRoomTypePreferences();
                        }

                        // Close the modal
                        this.closeModal();

                        // Reset the button
                        setTimeout(() => {
                            saveButton.innerHTML = '<i class="fas fa-check me-2" style="color: white !important;"></i> Save Preferences';
                            saveButton.style.background = 'linear-gradient(135deg, #36b1dc, #2980b9)';
                            saveButton.style.transform = '';
                            saveButton.style.boxShadow = '';
                        }, 300);
                    }, 800);
                } else {
                    // If button not found, just close the modal
                    this.closeModal();
                }

                // Show success notification
                showNotification('Preferences saved successfully!', 'success');
            } catch (error) {
                console.error('Error saving preferences:', error);
            }
        },

        applyPreferencesToButtons() {
            // Get all add to cart buttons
            const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

            // Update all product variations based on room type preference
            const productVariationContainers = document.querySelectorAll('.accommodation-product-variations');

            productVariationContainers.forEach(container => {
                const productId = container.getAttribute('data-product-id');
                const variations = container.querySelectorAll('.accommodation-product-variation');

                let singleRoomVariation = null;
                let doubleRoomVariation = null;

                // Find single and double room variations
                variations.forEach(variation => {
                    const radioInput = variation.querySelector('input[type=radio]');
                    const variationName = radioInput.getAttribute('data-variation-name');

                    if (variationName.toLowerCase().includes('single')) {
                        singleRoomVariation = variation;
                    } else if (variationName.toLowerCase().includes('double')) {
                        doubleRoomVariation = variation;
                    }
                });

                // Apply room type preference
                if (singleRoomVariation && doubleRoomVariation) {
                    const singleRadio = singleRoomVariation.querySelector('input[type=radio]');
                    const doubleRadio = doubleRoomVariation.querySelector('input[type=radio]');

                    if (this.roomType === 'double') {
                        // Select double room variation
                        doubleRadio.checked = true;

                        // Disable single room variation
                        singleRoomVariation.classList.add('variation-disabled');
                        singleRadio.disabled = true;

                        // Enable double room variation
                        doubleRoomVariation.classList.remove('variation-disabled');
                        doubleRadio.disabled = false;
                    } else {
                        // When single room is selected
                        // Enable single room variation
                        singleRoomVariation.classList.remove('variation-disabled');
                        singleRadio.disabled = false;

                        // Disable double room variation
                        doubleRoomVariation.classList.add('variation-disabled');
                        doubleRadio.disabled = true;

                        // Default to single room
                        singleRadio.checked = true;
                    }
                }
            });

            // Update button text to reflect preferences
            addToCartButtons.forEach(button => {
                const buttonText = this.roomType === 'double'
                    ? `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${this.nights} nights × 2 people)`
                    : `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${this.nights} nights)`;

                button.innerHTML = buttonText;

                // Update quantity attribute
                const quantity = this.roomType === 'double' ? this.nights * 2 : this.nights;
                button.setAttribute('data-quantity', quantity);
            });
        }
    };
}
</script>

<!-- Script for modal functionality -->
<script>
// Function to show the Post-Event modal
function showPostEventModal(event) {
    // Only call preventDefault if event is provided
    if (event && event.preventDefault) {
        event.preventDefault();
    }
    const modalElement = document.querySelector('.post-event-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'flex';
        console.log('Modal displayed via direct function call');
    }
}

// Function to close the Post-Event modal
function closePostEventModal() {
    const modalElement = document.querySelector('.post-event-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'none';
        console.log('Modal hidden via direct function call');
    }
}

// Apply room type preferences - global function
function applyPostEventRoomTypePreferences() {
    try {
        console.log('Applying post-event room type preferences');
        const savedRoomType = sessionStorage.getItem('postEventRoomType') || 'single';
        console.log('Saved room type:', savedRoomType);

        // Get saved nights
        const savedNights = parseInt(sessionStorage.getItem('postEventNights')) || 1;
        console.log('Saved nights:', savedNights);

        // Update Add to Cart buttons first
        updatePostEventAddToCartButtons(savedNights, savedRoomType);

        // Update all product variations based on room type preference
        const productVariationContainers = document.querySelectorAll('.accommodation-product-variations');

        productVariationContainers.forEach(container => {
            const productId = container.getAttribute('data-product-id');
            const variations = container.querySelectorAll('.accommodation-product-variation');

            let singleRoomVariation = null;
            let doubleRoomVariation = null;

            // Find single and double room variations
            variations.forEach(variation => {
                const radioInput = variation.querySelector('input[type=radio]');
                const variationName = radioInput.getAttribute('data-variation-name');

                if (variationName.toLowerCase().includes('single')) {
                    singleRoomVariation = variation;
                } else if (variationName.toLowerCase().includes('double')) {
                    doubleRoomVariation = variation;
                }
            });

            // Apply room type preference
            if (singleRoomVariation && doubleRoomVariation) {
                const singleRadio = singleRoomVariation.querySelector('input[type=radio]');
                const doubleRadio = doubleRoomVariation.querySelector('input[type=radio]');

                if (savedRoomType === 'double') {
                    // Select double room variation
                    doubleRadio.checked = true;

                    // Disable single room variation
                    singleRoomVariation.classList.add('variation-disabled');
                    singleRadio.disabled = true;

                    // Enable double room variation
                    doubleRoomVariation.classList.remove('variation-disabled');
                    doubleRadio.disabled = false;
                } else {
                    // When single room is selected
                    // Enable single room variation
                    singleRoomVariation.classList.remove('variation-disabled');
                    singleRadio.disabled = false;

                    // Disable double room variation
                    doubleRoomVariation.classList.add('variation-disabled');
                    doubleRadio.disabled = true;

                    // Default to single room
                    singleRadio.checked = true;
                }
            }
        });
    } catch (error) {
        console.error('Error in applyPostEventRoomTypePreferences:', error);
    }
}

// Function to update Add to Cart buttons with new nights value
function updatePostEventAddToCartButtons(nights, roomType) {
    console.log('Updating Add to Cart buttons with:', { nights, roomType });
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    console.log('Found buttons:', addToCartButtons.length);

    addToCartButtons.forEach(button => {
        // Skip buttons that are already in "Added to Cart" state
        if (button.disabled) {
            console.log('Skipping disabled button');
            return;
        }

        const buttonText = roomType === 'double'
            ? `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${nights} nights × 2 people)`
            : `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${nights} nights)`;

        console.log('Setting button text to:', buttonText);
        button.innerHTML = buttonText;

        // Update quantity attribute
        const quantity = roomType === 'double' ? nights * 2 : nights;
        button.setAttribute('data-quantity', quantity);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for Alpine.js');

    // Check if user already has preferences saved
    const hasPreferences = sessionStorage.getItem('postEventNights') && sessionStorage.getItem('postEventRoomType');
    console.log('User has saved preferences:', hasPreferences ? 'Yes' : 'No');

    // Check if Alpine.js is loaded
    if (typeof Alpine === 'undefined') {
        console.log('Alpine.js not loaded, loading it manually');

        // Load Alpine.js manually if it's not loaded
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js';
        script.defer = true;
        document.head.appendChild(script);

        script.onload = function() {
            console.log('Alpine.js loaded manually');

            // Initialize Alpine.js
            Alpine.start();

            // Only show modal if user doesn't have preferences saved
            if (!hasPreferences) {
                console.log('Showing modal because no preferences found');
                setTimeout(function() {
                    showPostEventModal();
                }, 500);
            } else {
                console.log('Not showing modal because preferences already exist');
            }
        };
    } else {
        // If Alpine.js is already loaded, only show modal if user doesn't have preferences
        if (!hasPreferences) {
            console.log('Showing modal because no preferences found');
            setTimeout(function() {
                showPostEventModal();
            }, 500);
        } else {
            console.log('Not showing modal because preferences already exist');
        }
    }

    // Update current settings display
    if (typeof updateCurrentSettingsDisplay === 'function') {
        updateCurrentSettingsDisplay();
    }

    // Apply preferences on page load
    applyPostEventRoomTypePreferences();

    // Add to Cart Confirmation Modal Functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    const confirmModal = document.getElementById('postEventCartConfirmModal');

    // Get saved preferences
    const getNights = function() {
        const savedNights = sessionStorage.getItem('postEventNights');
        return savedNights ? parseInt(savedNights) : 1;
    };

    const getRoomType = function() {
        const savedRoomType = sessionStorage.getItem('postEventRoomType');
        return savedRoomType || 'single';
    };

    // Add click event to all Add to Cart buttons
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');
            const productType = this.getAttribute('data-product-type');

            // Set hotel name in confirmation modal
            document.getElementById('postEventConfirmProductName').textContent = productName;

            // Get current nights value from session storage
            const nights = getNights();

            // Set nights in confirmation modal
            document.getElementById('postEventConfirmNights').textContent = nights + (nights === 1 ? ' night' : ' nights');

            // Calculate and display check-out date
            const calculateCheckoutDate = function(nights) {
                // Base date is 24/08/2025 (check-in date)
                const baseDate = new Date(2025, 7, 24); // Month is 0-indexed, so 7 = August

                // Calculate check-out date by adding nights to base date
                const checkoutDate = new Date(baseDate);
                checkoutDate.setDate(baseDate.getDate() + nights);

                // Format the date as DD/MM/YYYY
                const day = String(checkoutDate.getDate()).padStart(2, '0');
                const month = String(checkoutDate.getMonth() + 1).padStart(2, '0');
                const year = checkoutDate.getFullYear();

                return `${day}/${month}/${year}`;
            };

            // Set check-out date
            document.getElementById('postEventConfirmCheckoutDate').textContent = calculateCheckoutDate(nights);

            // Handle variation selection for variable products
            let variationId = null;
            let variationName = null;
            let isDoubleRoom = false;

            // Get user room type preference
            const userRoomType = getRoomType();
            const isUserDoubleRoom = userRoomType === 'double';

            if (productType === 'variable') {
                // Find the appropriate variation based on user preference
                let selectedVariation = null;

                if (isUserDoubleRoom) {
                    // If user prefers double room, find double room variation
                    selectedVariation = document.querySelector(`input[name="variation_${productId}"][data-is-double="true"]`);
                } else {
                    // Otherwise, get the selected variation
                    selectedVariation = document.querySelector(`input[name="variation_${productId}"]:checked`);
                }

                if (selectedVariation) {
                    variationId = selectedVariation.value;
                    variationName = selectedVariation.getAttribute('data-variation-name');
                    isDoubleRoom = selectedVariation.getAttribute('data-is-double') === 'true' || isUserDoubleRoom;

                    // Show room type in confirmation modal
                    document.getElementById('confirmRoomTypeRow').style.display = 'flex';
                    document.getElementById('confirmRoomType').textContent = variationName;
                } else {
                    // Hide room type if no variation selected
                    document.getElementById('confirmRoomTypeRow').style.display = 'none';
                }
            } else {
                // Hide room type for simple products
                document.getElementById('confirmRoomTypeRow').style.display = 'none';
            }

            // Hide double room notice (no longer needed)
            document.getElementById('confirmDoubleRoomRow').style.display = 'none';
            document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

            // Calculate and display the total price
            let productPrice = 0;

            if (productType === 'variable' && variationId) {
                // Get price from the selected variation
                const selectedVariation = document.querySelector(`input[value="${variationId}"]`);
                if (selectedVariation) {
                    productPrice = parseFloat(selectedVariation.getAttribute('data-price') || 0);
                }
            } else {
                // Get price from the button's data attribute
                productPrice = parseFloat(this.getAttribute('data-price') || 0);
            }

            // Calculate total price based on nights
            const totalPrice = productPrice * nights;

            // Format the price with European format (comma as decimal separator)
            const formattedPrice = totalPrice.toFixed(2).replace('.', ',') + ' €';

            // Update the price display
            document.getElementById('confirmTotalPrice').textContent = formattedPrice;

            // Store data for add to cart action
            postEventProductId = productId;
            postEventVariationId = variationId;
            postEventIsDoubleRoom = isDoubleRoom || isUserDoubleRoom;
            postEventNights = nights;

            // Show confirmation modal
            confirmModal.style.display = 'block';
        });
    });
});

// Function to add product to cart
window.addToCartPostEvent = function() {
    console.log('Real addToCartPostEvent function called');
    const confirmModal = document.getElementById('postEventCartConfirmModal');
    const modalContent = document.querySelector('.accommodation-cart-confirm-content');
    const modalDetails = document.querySelector('.accommodation-cart-confirm-details');
    const modalFooter = document.querySelector('.accommodation-cart-confirm-footer');
    const modalTitle = document.querySelector('.accommodation-cart-confirm-header h3');
    const modalIntro = document.querySelector('.accommodation-cart-confirm-body > p');
    const bookButton = document.querySelector('.accommodation-cart-confirm-btn-primary');

    // Show loading state
    if (bookButton) {
        bookButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';
        bookButton.disabled = true;
    }

    // Hide the details and footer
    if (modalDetails) {
        modalDetails.style.display = 'none';
    }

    if (modalFooter) {
        modalFooter.style.display = 'none';
    }

    // Add loading animation to the modal content
    if (modalContent) {
        modalContent.classList.add('processing');
    }

    // Calculate quantity based on room type
    const quantity = postEventIsDoubleRoom ? postEventNights * 2 : postEventNights;

    // Create a URLSearchParams object for the request
    const params = new URLSearchParams();
    params.append('action', 'lci-dashboard-add-to-cart');
    params.append('product_id', postEventProductId);
    params.append('quantity', quantity);
    params.append('security', lci_ajax.nonce);

    // Add variation ID if applicable
    if (postEventVariationId) {
        params.append('variation_id', postEventVariationId);
    }

    // Add a timestamp to prevent caching
    params.append('_', new Date().getTime());

    console.log('Sending AJAX request with data:', Object.fromEntries(params));

    // First, try to directly update the WooCommerce cart using the WC AJAX API
    if (typeof jQuery !== 'undefined' && typeof wc_add_to_cart_params !== 'undefined') {
        console.log('Using direct WooCommerce AJAX API');

        // Create data for WooCommerce AJAX
        const wcData = {
            product_id: postEventProductId,
            quantity: quantity,
            variation_id: postEventVariationId || 0
        };

        // Add to cart using WooCommerce's own AJAX API
        jQuery.ajax({
            type: 'POST',
            url: wc_add_to_cart_params.wc_ajax_url.toString().replace('%%endpoint%%', 'add_to_cart'),
            data: wcData,
            success: function(response) {
                console.log('WooCommerce direct add to cart response:', response);

                // Force cart fragments update
                jQuery(document.body).trigger('wc_fragment_refresh');
                jQuery(document.body).trigger('added_to_cart');
            },
            error: function(error) {
                console.error('WooCommerce direct add to cart error:', error);
            }
        });
    }

    // Also send our custom AJAX request as a backup
    fetch(lci_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params,
        credentials: 'same-origin' // Include cookies for session handling
    })
    .then(response => {
        console.log('AJAX response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('AJAX response:', data);

        // Force WooCommerce to update the cart
        if (typeof forceWooCommerceCartUpdate === 'function') {
            console.log('Calling forceWooCommerceCartUpdate');
            forceWooCommerceCartUpdate();
        } else {
            console.log('forceWooCommerceCartUpdate function not available');

            // Fallback to direct jQuery calls
            if (typeof jQuery !== 'undefined') {
                console.log('Triggering WooCommerce cart update via jQuery');
                jQuery(document.body).trigger('wc_update_cart');
                jQuery(document.body).trigger('wc_fragment_refresh');
                jQuery(document.body).trigger('added_to_cart', [null, null, jQuery('.add-to-cart-btn')]);
            }
        }

        // Add a slight delay to show the animation
        setTimeout(() => {
            // Update modal to show success message
            if (modalTitle) {
                modalTitle.innerHTML = '<i class="fas fa-check-circle me-2" style="color: white;"></i> Added to Cart';
            }

            if (modalContent) {
                modalContent.classList.remove('processing');
            }

            if (modalIntro) {
                modalIntro.innerHTML = 'Your selection has been added to your cart successfully!';
            }

            // Show success message instead of reloading
            const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${postEventProductId}"]`).closest('.accommodation-product-card');
            const addToCartBtn = productCard.querySelector('.add-to-cart-btn');

            // Replace button with success message
            if (addToCartBtn) {
                addToCartBtn.innerHTML = '<i class="fas fa-check me-2"></i> Added to Cart';
                addToCartBtn.style.backgroundColor = '#28a745';
                addToCartBtn.disabled = true;
            }

            // Hide settings button
            const settingsBtn = productCard.querySelector('.accommodation-settings-button');
            if (settingsBtn) {
                settingsBtn.style.display = 'none';
            }

            // Update mini cart count
            const miniCartCount = document.querySelector('.custom-mini-cart-count');
            if (miniCartCount && data.data && data.data.cart_count !== undefined) {
                console.log('Updating mini cart count to:', data.data.cart_count);
                miniCartCount.textContent = data.data.cart_count;

                // Add pulse animation
                miniCartCount.classList.remove('count-pulse');
                void miniCartCount.offsetWidth; // Trigger reflow to restart animation
                miniCartCount.classList.add('count-pulse');
            } else {
                console.log('Could not update mini cart count:', {
                    miniCartCount: !!miniCartCount,
                    dataData: !!data.data,
                    cartCount: data.data ? data.data.cart_count : 'undefined'
                });
            }

            // Update mini cart total
            const miniCartTotal = document.querySelector('#custom-mini-cart-total');
            if (miniCartTotal && data.data && data.data.cart_total !== undefined) {
                console.log('Updating mini cart total to:', data.data.cart_total);
                // Format the total to just show the amount with proper formatting
                const formattedTotal = typeof data.data.cart_total === 'string' ?
                    data.data.cart_total.replace(/<\/?[^>]+(>|$)/g, "").trim().replace(/[^\d,]/g, "") :
                    data.data.cart_total;
                miniCartTotal.textContent = formattedTotal + ' €';
            } else {
                console.log('Could not update mini cart total:', {
                    miniCartTotal: !!miniCartTotal,
                    dataData: !!data.data,
                    cartTotal: data.data ? data.data.cart_total : 'undefined'
                });
            }

            // Refresh the mini-cart contents
            if (typeof fetchCustomMiniCartItems === 'function') {
                console.log('Refreshing mini cart contents');
                fetchCustomMiniCartItems();
            } else {
                console.log('fetchCustomMiniCartItems function not available');
            }

            // Force a direct AJAX call to get the cart contents
            console.log('Forcing direct AJAX call to get cart contents');
            const cartParams = new URLSearchParams();
            cartParams.append('action', 'lci_get_mini_cart_items');
            cartParams.append('nonce', lci_ajax.nonce);

            fetch(lci_ajax.ajax_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: cartParams
            })
            .then(response => response.json())
            .then(cartData => {
                console.log('Direct cart AJAX response:', cartData);
                if (cartData.success && cartData.data) {
                    console.log('Cart items:', cartData.data.items);
                    console.log('Cart total:', cartData.data.total);
                    console.log('Cart count:', cartData.data.count);
                }
            })
            .catch(error => {
                console.error('Error fetching cart contents:', error);
            });

            // Close the modal after a delay
            setTimeout(() => {
                confirmModal.style.display = 'none';
            }, 2000);

        }, 1000);
    })
    .catch(error => {
        console.error('Error adding to cart:', error);

        // Reset the modal
        if (modalTitle) {
            modalTitle.innerHTML = '<i class="fas fa-exclamation-circle me-2" style="color: white;"></i> Error';
        }

        if (modalContent) {
            modalContent.classList.remove('processing');
        }

        if (modalIntro) {
            modalIntro.innerHTML = 'There was an error adding this item to your cart. Please try again.';
        }

        if (bookButton) {
            bookButton.innerHTML = '<i class="fas fa-shopping-cart me-2"></i> Try Again';
            bookButton.disabled = false;
        }

        // Show the footer again
        if (modalFooter) {
            modalFooter.style.display = 'flex';
        }
    });
};
</script>

<style>
/* Post-Event Accommodation Modal Styles */
.post-event-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.post-event-modal {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 95%;
    max-width: 700px;
    overflow: hidden;
}

.post-event-modal-header {
    background-color: #36b1dc;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-event-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.post-event-modal-header h3 i {
    color: white;
    margin-right: 8px;
}

.post-event-modal-body {
    padding: 20px;
}

@media (min-width: 768px) {
    .post-event-modal-header {
        padding: 20px 25px;
    }

    .post-event-modal-header h3 {
        font-size: 22px;
    }

    .post-event-modal-body {
        padding: 25px 30px;
    }
}

.post-event-modal-form-group {
    margin-bottom: 20px;
}

.post-event-modal-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.post-event-modal-dates {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.post-event-modal-dates-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.post-event-modal-dates-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.post-event-modal-dates-label {
    font-weight: 600;
    color: #495057;
}

.post-event-modal-dates-value {
    color: #343a40;
    font-weight: 500;
}

.post-event-modal-dates-note {
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
}

.post-event-modal-dates-note i {
    color: #36b1dc;
    margin-right: 6px;
    font-size: 14px;
}

.post-event-modal-notice {
    background-color: #e8f4fd;
    color: #0c5460;
    padding: 16px;
    border-radius: 8px;
    margin-top: 15px;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid #36b1dc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.post-event-modal-notice i {
    margin-right: 12px;
    font-size: 18px;
    margin-top: 2px;
    color: #36b1dc;
}

.post-event-modal-notice p {
    line-height: 1.5;
}

.post-event-modal-notice strong {
    color: #0c5460;
    font-weight: 600;
}

.post-event-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
}

@media (min-width: 768px) {
    .post-event-modal-footer {
        padding: 20px 25px;
    }

    .post-event-modal-btn {
        min-width: 150px;
        padding: 12px 20px;
    }
}

.post-event-modal-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    width: 48%;
    color: white;
}

.post-event-modal-btn i {
    color: white;
    margin-right: 8px;
}

.post-event-modal-btn-primary {
    background-color: #36b1dc;
    color: white;
}

.post-event-modal-btn-primary:hover {
    background-color: #2d9cc3;
    color: white;
}

.post-event-modal-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.post-event-modal-btn-secondary:hover {
    background-color: #5a6268;
    color: white;
}

.accommodation-settings-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    margin-top: 10px;
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
    transition: all 0.2s ease;
}

.accommodation-settings-button:hover {
    background-color: #e9ecef;
}

/* Variation radio buttons */
.accommodation-variations-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.accommodation-variation-radio-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
    width: 100%;
}

.accommodation-variation-radio-label:hover {
    background-color: #f8f9fa;
}

.accommodation-variation-radio-label input[type="radio"] {
    margin-right: 10px;
}

.accommodation-product-star-description {
    font-size: 14px;
    color: #6c757d;
    margin-left: 8px;
}

.accommodation-product-short-description {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
    max-height: 80px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

/* Disabled variation styles */
.variation-disabled {
    opacity: 0.5;
    pointer-events: none;
}

.variation-disabled input[type="radio"] {
    opacity: 0.5;
}

/* Loading indicator for mini-cart */
.dashboard-mini-cart.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.dashboard-mini-cart.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #36b1dc;
    border-top-color: transparent;
    border-radius: 50%;
    animation: mini-cart-spinner 0.8s linear infinite;
}

@keyframes mini-cart-spinner {
    to {
        transform: rotate(360deg);
    }
}

/* Accommodation Cart Confirm Modal Styles - UX2025 */
.accommodation-cart-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.accommodation-cart-confirm-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.accommodation-cart-confirm-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 700px;
    overflow: hidden;
    animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -45%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.accommodation-cart-confirm-header {
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.accommodation-cart-confirm-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    z-index: 0;
}

.accommodation-cart-confirm-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    color: white;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
}

.accommodation-cart-confirm-header h3 i {
    color: white;
    margin-right: 10px;
    font-size: 22px;
}

.accommodation-cart-confirm-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    opacity: 0.8;
    transition: all 0.2s ease;
}

.accommodation-cart-confirm-close:hover {
    opacity: 1;
    transform: scale(1.1);
}

.accommodation-cart-confirm-body {
    padding: 25px 30px;
}

.accommodation-cart-confirm-body > p {
    margin-top: 0;
    margin-bottom: 25px;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
}

.accommodation-cart-confirm-details {
    margin-bottom: 25px;
}

.accommodation-cart-confirm-product {
    display: flex;
    gap: 20px;
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.accommodation-cart-confirm-product-image {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.accommodation-cart-confirm-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.accommodation-cart-confirm-product:hover .accommodation-cart-confirm-product-image img {
    transform: scale(1.05);
}

.accommodation-cart-confirm-product-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    display: flex;
    justify-content: center;
}

.accommodation-cart-confirm-product-image-stars {
    color: #ffc107;
    font-size: 14px;
}

.accommodation-cart-confirm-product-info {
    flex: 1;
}

.accommodation-cart-confirm-product-info h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 700;
    color: #36b1dc;
}

.accommodation-cart-confirm-product-meta {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.accommodation-cart-confirm-product-meta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.accommodation-cart-confirm-product-meta-item:last-child {
    border-bottom: none;
}

.accommodation-cart-confirm-product-meta-label {
    font-weight: 600;
    color: #5d6d7e;
    display: flex;
    align-items: center;
    gap: 8px;
}

.accommodation-cart-confirm-product-meta-label i {
    color: #36b1dc;
    font-size: 14px;
}

.accommodation-cart-confirm-product-meta-value {
    color: #2c3e50;
    font-weight: 600;
}

.accommodation-cart-confirm-product-meta-total {
    margin-top: 5px;
    padding-top: 10px;
    border-top: 2px solid rgba(54, 177, 220, 0.1);
}

.accommodation-cart-confirm-product-meta-total .accommodation-cart-confirm-product-meta-label {
    font-weight: 700;
    color: #2c3e50;
}

.accommodation-cart-confirm-product-meta-total .accommodation-cart-confirm-product-meta-value {
    font-weight: 700;
    color: #36b1dc;
    font-size: 18px;
}

.accommodation-cart-confirm-notice {
    background: linear-gradient(to right, rgba(54, 177, 220, 0.05), rgba(54, 177, 220, 0.1));
    color: #2c3e50;
    padding: 20px;
    border-radius: 15px;
    margin-top: 20px;
    font-size: 15px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid #36b1dc;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.accommodation-cart-confirm-notice i {
    margin-right: 15px;
    font-size: 20px;
    margin-top: 2px;
    color: #36b1dc;
}

.accommodation-cart-confirm-notice p {
    line-height: 1.6;
    margin: 0;
}

.accommodation-cart-confirm-notice strong {
    color: #36b1dc;
    font-weight: 700;
}

.accommodation-cart-confirm-footer {
    padding: 20px 30px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    gap: 15px;
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
}

.accommodation-cart-confirm-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 20px;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    width: 48%;
    color: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.accommodation-cart-confirm-btn i {
    margin-right: 8px;
}

.accommodation-cart-confirm-btn-primary {
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
}

.accommodation-cart-confirm-btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1c6ea4);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(54, 177, 220, 0.3);
}

.accommodation-cart-confirm-btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.accommodation-cart-confirm-btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #343a40);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(108, 117, 125, 0.3);
}

/* Processing state for the modal */
.accommodation-cart-confirm-content.processing {
    position: relative;
}

.accommodation-cart-confirm-content.processing::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    z-index: 1;
    border-radius: 20px;
}

.accommodation-cart-confirm-content.processing::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 50px;
    height: 50px;
    margin: -25px 0 0 -25px;
    border: 3px solid rgba(54, 177, 220, 0.2);
    border-top: 3px solid #36b1dc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 2;
    box-shadow: 0 0 20px rgba(54, 177, 220, 0.3);
}
</style>

<script>
// Initialize the cart when the page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing cart');

    // Force WooCommerce cart update first
    if (typeof forceWooCommerceCartUpdate === 'function') {
        console.log('Forcing WooCommerce cart update on page load');
        forceWooCommerceCartUpdate();
    } else {
        console.log('forceWooCommerceCartUpdate function not available on page load');
        // Initialize the mini cart directly
        fetchCustomMiniCartItems();
    }
});
</script>
