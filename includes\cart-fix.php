<?php
/**
 * Cart Fix - Clean implementation to prevent duplicates
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize the cart fix
 */
function lci_init_cart_fix() {
    // Filter the cart contents before they're displayed
    add_filter('woocommerce_cart_contents', 'lci_filter_cart_contents');

    // Filter the cart item count
    add_filter('woocommerce_cart_contents_count', 'lci_filter_cart_count');

    // Prevent adding duplicate products to cart
    add_filter('woocommerce_add_to_cart_validation', 'lci_prevent_duplicate_products', 10, 3);

    // Filter the mini cart items
    add_filter('woocommerce_before_mini_cart', 'lci_before_mini_cart');
    add_filter('woocommerce_after_mini_cart', 'lci_after_mini_cart');

    // Add a placeholder function for WOD (WooCommerce Order Dashboard) hooks
    if (!function_exists('wod_get_orders')) {
        function wod_get_orders() {
            // This is a placeholder function to prevent errors
            // It seems there was a WOD (WooCommerce Order Dashboard) plugin that's no longer active
            return [];
        }
        // Log that we've added this placeholder
        error_log('Added placeholder for wod_get_orders function to prevent errors');
    }
}
add_action('init', 'lci_init_cart_fix');

/**
 * Global variable to store the original cart contents
 */
global $lci_original_cart;
$lci_original_cart = null;

/**
 * Before mini cart - save the original cart contents
 */
function lci_before_mini_cart() {
    global $lci_original_cart;

    if (function_exists('WC') && WC()->cart) {
        $lci_original_cart = WC()->cart->get_cart();

        // Apply our filter
        $filtered_cart = lci_get_unique_cart_items($lci_original_cart);

        // Replace the cart contents temporarily
        WC()->cart->set_cart_contents($filtered_cart);
    }
}

/**
 * After mini cart - restore the original cart contents
 */
function lci_after_mini_cart() {
    global $lci_original_cart;

    if (function_exists('WC') && WC()->cart && $lci_original_cart !== null) {
        // Restore the original cart contents
        WC()->cart->set_cart_contents($lci_original_cart);
        $lci_original_cart = null;
    }
}

/**
 * Filter cart contents to show only unique products
 */
function lci_filter_cart_contents($cart_contents) {
    // Only filter on the regalia shop page
    if (!isset($_GET['tab']) || $_GET['tab'] !== 'regalia-shop') {
        return $cart_contents;
    }

    return lci_get_unique_cart_items($cart_contents);
}

/**
 * Get unique cart items by product ID and variation ID
 */
function lci_get_unique_cart_items($cart_contents) {
    if (empty($cart_contents)) {
        return $cart_contents;
    }

    // Return the original cart contents without deduplication
    // This ensures each variation appears as a separate item
    return $cart_contents;
}

/**
 * Filter cart count to show the actual number of items
 */
function lci_filter_cart_count($count) {
    // Return the actual count without filtering
    return $count;
}

/**
 * Handle product variations correctly
 */
function lci_prevent_duplicate_products($passed, $product_id, $quantity) {
    // Only handle this on the regalia shop page
    if (!isset($_GET['tab']) || $_GET['tab'] !== 'regalia-shop') {
        return $passed;
    }

    // Check if this is a variable product by looking for variation attributes in POST data
    $is_variation = false;
    foreach ($_POST as $key => $value) {
        if (strpos($key, 'attribute_') === 0) {
            $is_variation = true;
            break;
        }
    }

    // For variations, always allow adding as a new item
    if ($is_variation) {
        return $passed;
    }

    // For simple products, update quantity if already in cart
    if (function_exists('WC') && WC()->cart) {
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            // Only match simple products (no variation_id)
            if ($cart_item['product_id'] == $product_id && empty($cart_item['variation_id'])) {
                // Product already in cart - update quantity instead of adding a new item
                $new_quantity = $cart_item['quantity'] + $quantity;
                WC()->cart->set_quantity($cart_item_key, $new_quantity);

                // Return false to prevent the default add to cart behavior
                return false;
            }
        }
    }

    return $passed;
}

/**
 * Custom function to get mini cart items
 */
function lci_get_mini_cart_items_ajax() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_mini_cart_nonce')) {
        wp_send_json_error(['message' => 'Invalid security token']);
        return;
    }

    // Get cart contents
    $cart_items = array();
    $cart_total = '0.00 €';

    if (function_exists('WC') && !WC()->cart->is_empty()) {
        // Get unique cart items
        $unique_cart_items = lci_get_unique_cart_items(WC()->cart->get_cart());

        foreach ($unique_cart_items as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];

            if (!$product) {
                continue;
            }

            // Get product image
            $image_id = $product->get_image_id();
            $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : wc_placeholder_img_src();

            // Calculate the correct line total based on the updated quantity
            $unit_price = $product->get_price();
            $line_total = $unit_price * $cart_item['quantity'];

            // Check if product is in category 22
            $is_category_22 = has_term(22, 'product_cat', $cart_item['product_id']);

            // Get product details
            $item = array(
                'key' => $cart_item_key,
                'product_id' => $cart_item['product_id'],
                'name' => $product->get_name(),
                'price' => wc_price($unit_price),
                'quantity' => $cart_item['quantity'],
                'total' => wc_price($line_total),
                'image' => $image_url,
                'remove_url' => wc_get_cart_remove_url($cart_item_key),
                'is_category_22' => $is_category_22
            );

            $cart_items[] = $item;
        }

        $cart_total = WC()->cart->get_cart_total();
    }

    // Check if there are any category 22 products
    $has_category_22 = false;
    foreach ($cart_items as $item) {
        if (isset($item['is_category_22']) && $item['is_category_22']) {
            $has_category_22 = true;
            break;
        }
    }

    // Get the fundraising amount
    $fundraising_amount = 0;
    if ($has_category_22 && function_exists('lci_get_category_22_total_value')) {
        $fundraising_amount = lci_get_category_22_total_value();
    }

    wp_send_json_success([
        'items' => $cart_items,
        'total' => $cart_total,
        'count' => count($cart_items),
        'has_category_22' => $has_category_22,
        'fundraising_amount' => $fundraising_amount,
        'fundraising_message' => $has_category_22 && $fundraising_amount > 0 ? lci_get_fundraising_message() : ''
    ]);
}
add_action('wp_ajax_lci_get_mini_cart_items_ajax', 'lci_get_mini_cart_items_ajax');
add_action('wp_ajax_nopriv_lci_get_mini_cart_items_ajax', 'lci_get_mini_cart_items_ajax');
