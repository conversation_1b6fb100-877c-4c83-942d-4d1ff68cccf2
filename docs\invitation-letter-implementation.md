# Invitation Letter Generator Implementation Guide

This document provides instructions for implementing the invitation letter generator feature for the LCI 2025 AGM website.

## Overview

The invitation letter generator allows users who need a visa to enter Romania to generate an official invitation letter. The system:

1. Determines if the user is a delegate or participant based on purchased products
2. Collects necessary information through a form
3. Generates a PDF with the appropriate template
4. Stores the PDF URL in the user's metadata
5. Provides a verification system via QR codes

## Implementation Steps

### 1. Create WordPress Pages

Create the following WordPress pages:

#### Verify Invitation Page
- Title: "Verify Invitation"
- Slug: `verify-invitation`
- Content: `[lci2025_verify_invitation]`
- Template: Full Width

### 2. Install Required Libraries

The system requires a PDF generation library. You can choose either:

#### Option 1: TCPDF
```bash
# Download TCPDF
mkdir -p lib/tcpdf
cd lib/tcpdf
wget https://github.com/tecnickcom/TCPDF/archive/refs/tags/6.6.2.zip
unzip 6.6.2.zip
mv TCPDF-6.6.2/* .
rm -rf TCPDF-6.6.2 6.6.2.zip
```

#### Option 2: mPDF
```bash
# Install Composer if not already installed
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer

# Install mPDF using Composer
mkdir -p lib/mpdf
cd lib/mpdf
composer require mpdf/mpdf
```

### 3. Install QR Code Library

```bash
# Download phpqrcode
mkdir -p lib/phpqrcode
cd lib/phpqrcode
wget https://sourceforge.net/projects/phpqrcode/files/phpqrcode.zip
unzip phpqrcode.zip
rm phpqrcode.zip
```

### 4. Create Upload Directories

Create directories for storing generated files:

```bash
mkdir -p wp-content/uploads/invitation-letters
mkdir -p wp-content/uploads/invitation-qrcodes
chmod 755 wp-content/uploads/invitation-letters
chmod 755 wp-content/uploads/invitation-qrcodes
```

### 5. Database Setup

The system will automatically create the required database table (`wp_lci_invitation_letters`) when the first invitation letter is generated. The table structure is:

```sql
CREATE TABLE wp_lci_invitation_letters (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    unique_id varchar(100) NOT NULL,
    user_id bigint(20) NOT NULL,
    full_name varchar(100) NOT NULL,
    passport_number varchar(50) NOT NULL,
    nationality varchar(100) NOT NULL,
    user_type varchar(20) NOT NULL,
    generated_date datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
    pdf_url varchar(255) NOT NULL,
    PRIMARY KEY  (id),
    UNIQUE KEY unique_id (unique_id)
);
```

## Testing

### Test Cases

1. **Delegate User**
   - Log in as a user who has purchased product ID 40
   - Navigate to the My Visa page
   - Click "Generate your invitation letter"
   - Fill out the form and submit
   - Verify that the PDF is generated with the delegate template
   - Verify that the PDF URL is stored in user metadata
   - Verify that the "Generate" button changes to "Download"

2. **Participant User**
   - Log in as a user who has purchased product ID 41 or 42
   - Navigate to the My Visa page
   - Click "Generate your invitation letter"
   - Fill out the form and submit
   - Verify that the PDF is generated with the participant template
   - Verify that the PDF URL is stored in user metadata
   - Verify that the "Generate" button changes to "Download"

3. **QR Code Verification**
   - Generate an invitation letter
   - Scan the QR code in the PDF
   - Verify that it redirects to the verification page
   - Verify that the verification page displays the correct information

## Troubleshooting

### Common Issues

1. **PDF Generation Fails**
   - Check that the PDF library (TCPDF or mPDF) is properly installed
   - Check that the upload directories have proper permissions
   - Check PHP error logs for detailed error messages

2. **QR Code Generation Fails**
   - Check that the phpqrcode library is properly installed
   - Check that the GD library is enabled in PHP
   - Check PHP error logs for detailed error messages

3. **Database Issues**
   - Check that the database table was created correctly
   - Check that the WordPress user has proper permissions to create tables
   - Check MySQL error logs for detailed error messages

## Security Considerations

1. **Data Protection**
   - All user inputs are sanitized before being used in database queries
   - PDFs are stored in a secure directory with proper permissions
   - Unique IDs are used for verification to prevent guessing

2. **Access Control**
   - Only logged-in users can generate invitation letters
   - Users can only generate invitation letters for themselves
   - The verification page only displays minimal information

## Future Enhancements

1. **Admin Interface**
   - Add an admin interface to view and manage generated invitation letters
   - Add the ability to manually generate invitation letters for users

2. **Email Notifications**
   - Send email notifications when invitation letters are generated
   - Include the PDF as an attachment

3. **Multiple Languages**
   - Add support for generating invitation letters in additional languages

4. **Customizable Templates**
   - Add the ability to customize invitation letter templates through the admin interface
