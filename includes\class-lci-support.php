<?php
/**
 * LCI Support Ticket System
 *
 * Handles support ticket functionality for LCI 2025 Dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class LCI_Support {
    /**
     * Initialize the support ticket system
     */
    public static function init() {
        // Create database tables on plugin activation
        // Use the main plugin file path from the defined constant
        register_activation_hook(LCI2025_PATH . 'lci-2025-dashboard.php', [__CLASS__, 'create_tables']);

        // Also create tables immediately if they don't exist yet
        self::create_tables();

        // Add AJAX handlers
        add_action('wp_ajax_lci_submit_ticket', [__CLASS__, 'ajax_submit_ticket']);
        add_action('wp_ajax_lci_get_tickets', [__CLASS__, 'ajax_get_tickets']);
        add_action('wp_ajax_lci_get_ticket_details', [__CLASS__, 'ajax_get_ticket_details']);
        add_action('wp_ajax_lci_reply_to_ticket', [__CLASS__, 'ajax_reply_to_ticket']);
        add_action('wp_ajax_lci_update_ticket_status', [__CLASS__, 'ajax_update_ticket_status']);
        add_action('wp_ajax_lci_delete_ticket', [__CLASS__, 'ajax_delete_ticket']);

        // Admin-only AJAX handlers
        add_action('wp_ajax_lci_admin_get_tickets', [__CLASS__, 'ajax_admin_get_tickets']);

        // Add admin menu items
        add_action('admin_menu', [__CLASS__, 'add_admin_menu']);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', [__CLASS__, 'enqueue_scripts']);

        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', [__CLASS__, 'admin_enqueue_scripts']);
    }

    /**
     * Create database tables
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Support tickets table
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';
        $sql = "CREATE TABLE IF NOT EXISTS $tickets_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            ticket_id VARCHAR(20) NOT NULL,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            category VARCHAR(50) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'open',
            priority VARCHAR(20) NOT NULL DEFAULT 'medium',
            email VARCHAR(100) NOT NULL,
            attachment_url VARCHAR(255),
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY ticket_id (ticket_id),
            KEY user_id (user_id),
            KEY status (status),
            KEY category (category)
        ) $charset_collate;";

        // Ticket replies table
        $replies_table = $wpdb->prefix . 'lci2025_ticket_replies';
        $sql2 = "CREATE TABLE IF NOT EXISTS $replies_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            ticket_id BIGINT(20) UNSIGNED NOT NULL,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            is_admin TINYINT(1) NOT NULL DEFAULT 0,
            message TEXT NOT NULL,
            attachment_url VARCHAR(255),
            created_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            KEY ticket_id (ticket_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        dbDelta($sql2);
    }

    /**
     * Add admin menu items
     */
    public static function add_admin_menu() {
        add_submenu_page(
            'lci-dashboard',
            'Support Tickets',
            'Support Tickets',
            'manage_options',
            'lci-support-tickets',
            [__CLASS__, 'render_admin_page']
        );

        add_submenu_page(
            'lci-dashboard',
            'Support Analytics',
            'Support Analytics',
            'manage_options',
            'lci-support-analytics',
            [__CLASS__, 'render_analytics_page']
        );
    }

    /**
     * Render admin page
     */
    public static function render_admin_page() {
        include LCI2025_PATH . 'templates/admin/support-tickets.php';
    }

    /**
     * Render analytics page
     */
    public static function render_analytics_page() {
        include LCI2025_PATH . 'templates/admin/support-analytics.php';
    }

    /**
     * Enqueue scripts and styles for frontend
     */
    public static function enqueue_scripts() {
        // Create the directory if it doesn't exist
        $js_dir = LCI2025_PATH . 'assets/js';
        if (!file_exists($js_dir)) {
            wp_mkdir_p($js_dir);
        }

        // Make sure the JS file exists
        $js_file = $js_dir . '/support.js';

        // First, localize the script data so it's available globally
        wp_register_script(
            'lci-support-data',
            '',  // No actual file
            [],
            LCI2025_VERSION
        );

        wp_localize_script('lci-support-data', 'lciSupport', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lci_support_nonce'),
            'messages' => array(
                'ticketSubmitted' => __('Your ticket has been submitted successfully.', 'lci-2025-dashboard'),
                'ticketError' => __('There was an error submitting your ticket. Please try again.', 'lci-2025-dashboard'),
                'replySubmitted' => __('Your reply has been submitted successfully.', 'lci-2025-dashboard'),
                'replyError' => __('There was an error submitting your reply. Please try again.', 'lci-2025-dashboard'),
            )
        ));

        // Enqueue the empty script to ensure the data is output
        wp_enqueue_script('lci-support-data');

        // Make sure Alpine.js is loaded next
        wp_enqueue_script(
            'alpine',
            'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js',
            ['lci-support-data'],
            null,
            true
        );

        // Add defer attribute to Alpine.js script
        add_filter('script_loader_tag', function($tag, $handle) {
            if ('alpine' === $handle) {
                return str_replace(' src', ' defer src', $tag);
            }
            return $tag;
        }, 10, 2);

        // Then load our support script
        wp_enqueue_script(
            'lci-support',
            LCI2025_URL . 'assets/js/support.js',
            ['jquery', 'alpine', 'lci-support-data'],
            LCI2025_VERSION,
            true
        );

        // lciSupport data is already localized above
    }

    /**
     * Enqueue scripts and styles for admin
     */
    public static function admin_enqueue_scripts($hook) {
        if ($hook != 'lci-dashboard_page_lci-support-tickets' && $hook != 'lci-dashboard_page_lci-support-analytics') {
            return;
        }

        // Create the directory if it doesn't exist
        $js_dir = LCI2025_PATH . 'admin/js';
        if (!file_exists($js_dir)) {
            wp_mkdir_p($js_dir);
        }

        // Make sure the JS file exists
        $js_file = $js_dir . '/support-admin.js';

        // First, register and localize the script data so it's available globally
        wp_register_script(
            'lci-admin-support-data',
            '',  // No actual file
            [],
            LCI2025_VERSION,
            false  // Load in header
        );

        wp_localize_script('lci-admin-support-data', 'lciAdminSupport', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lci_admin_support_nonce'),
            'messages' => array(
                'replySubmitted' => __('Your reply has been submitted successfully.', 'lci-2025-dashboard'),
                'replyError' => __('There was an error submitting your reply. Please try again.', 'lci-2025-dashboard'),
                'statusUpdated' => __('Ticket status updated successfully.', 'lci-2025-dashboard'),
                'statusError' => __('There was an error updating the ticket status. Please try again.', 'lci-2025-dashboard'),
                'ticketDeleted' => __('Ticket deleted successfully.', 'lci-2025-dashboard'),
                'ticketDeleteError' => __('There was an error deleting the ticket. Please try again.', 'lci-2025-dashboard'),
            )
        ));

        // Enqueue the empty script to ensure the data is output in the header
        wp_enqueue_script('lci-admin-support-data');

        // Make sure Alpine.js is loaded next in the header
        wp_enqueue_script(
            'alpine-js',
            'https://cdn.jsdelivr.net/npm/alpinejs@3.12.0/dist/cdn.min.js',
            ['lci-admin-support-data'],
            '3.12.0',
            false  // Load in header
        );

        // Add defer attribute to Alpine.js
        add_filter('script_loader_tag', function($tag, $handle) {
            if ('alpine-js' === $handle) {
                return str_replace(' src', ' defer src', $tag);
            }
            return $tag;
        }, 10, 2);

        // Then load our admin support script in the footer
        wp_enqueue_script(
            'lci-admin-support',
            LCI2025_URL . 'admin/js/support-admin.js',
            ['jquery', 'alpine-js', 'lci-admin-support-data'],
            LCI2025_VERSION,
            true  // Load in footer
        );
    }

    /**
     * Create a new support ticket
     *
     * @param array $ticket_data Ticket data
     * @return int|false Ticket ID or false on failure
     */
    public static function create_ticket($ticket_data) {
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';

        // Generate unique ticket ID
        $ticket_id = 'TKT-' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));

        $result = $wpdb->insert(
            $tickets_table,
            [
                'ticket_id' => $ticket_id,
                'user_id' => $ticket_data['user_id'],
                'subject' => $ticket_data['subject'],
                'message' => $ticket_data['message'],
                'category' => $ticket_data['category'],
                'status' => 'open',
                'priority' => 'medium',
                'email' => $ticket_data['email'],
                'attachment_url' => isset($ticket_data['attachment_url']) ? $ticket_data['attachment_url'] : null,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ]
        );

        if ($result) {
            $ticket_db_id = $wpdb->insert_id;

            // Send notification email
            self::send_ticket_notification_email($ticket_db_id, $ticket_id, $ticket_data);

            return $ticket_db_id;
        }

        return false;
    }

    /**
     * Send notification email for new ticket
     *
     * @param int $ticket_db_id Database ID of the ticket
     * @param string $ticket_id Ticket ID (e.g., TKT-ABCD1234)
     * @param array $ticket_data Ticket data
     * @return bool Whether the email was sent
     */
    public static function send_ticket_notification_email($ticket_db_id, $ticket_id, $ticket_data) {
        // Get admin email
        $admin_email = get_option('admin_email');

        // Get user data
        $user = get_userdata($ticket_data['user_id']);
        $user_name = $user ? $user->display_name : 'Unknown User';

        // Get category name
        $categories = [
            'registration' => 'Registration Issues',
            'payment' => 'Payment & Invoices',
            'accommodation' => 'Accommodation',
            'tours' => 'Tours & Activities',
            'visa' => 'Visa & Travel',
            'other' => 'Other Questions'
        ];
        $category_name = isset($categories[$ticket_data['category']]) ? $categories[$ticket_data['category']] : $ticket_data['category'];

        // Build admin email
        $admin_subject = sprintf('[LCI 2025 Support] New Ticket: %s', $ticket_data['subject']);

        $admin_message = sprintf(
            "A new support ticket has been submitted.\n\n" .
            "Ticket ID: %s\n" .
            "Subject: %s\n" .
            "Category: %s\n" .
            "From: %s (%s)\n\n" .
            "Message:\n%s\n\n" .
            "Please log in to the admin dashboard to respond to this ticket:\n%s",
            $ticket_id,
            $ticket_data['subject'],
            $category_name,
            $user_name,
            $ticket_data['email'],
            $ticket_data['message'],
            admin_url('admin.php?page=lci-support-tickets')
        );

        // Build user confirmation email
        $user_subject = sprintf('[LCI 2025 Support] Your Ticket #%s Has Been Received', $ticket_id);

        $user_message = sprintf(
            "Dear %s,\n\n" .
            "Thank you for contacting LCI 2025 AGM support. Your ticket has been received and will be addressed by our team.\n\n" .
            "Ticket Details:\n" .
            "Ticket ID: %s\n" .
            "Subject: %s\n" .
            "Category: %s\n\n" .
            "Your Message:\n%s\n\n" .
            "We will respond to your inquiry as soon as possible. You can view the status of your ticket and reply to it from your dashboard.\n\n" .
            "Best regards,\n" .
            "LCI 2025 AGM Support Team",
            $user_name,
            $ticket_id,
            $ticket_data['subject'],
            $category_name,
            $ticket_data['message']
        );

        // Send emails
        $admin_mail_sent = wp_mail($admin_email, $admin_subject, $admin_message);
        $user_mail_sent = wp_mail($ticket_data['email'], $user_subject, $user_message);

        return $admin_mail_sent && $user_mail_sent;
    }

    /**
     * Get tickets for a user
     *
     * @param int $user_id User ID
     * @return array Tickets
     */
    public static function get_user_tickets($user_id) {
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';

        $tickets = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $tickets_table WHERE user_id = %d ORDER BY created_at DESC",
                $user_id
            ),
            ARRAY_A
        );

        return $tickets;
    }

    /**
     * Get all tickets (admin function)
     *
     * @param array $args Query arguments
     * @return array Tickets
     */
    public static function get_all_tickets($args = []) {
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';

        $defaults = [
            'status' => '',
            'category' => '',
            'search' => '',
            'orderby' => 'created_at',
            'order' => 'DESC',
            'limit' => 20,
            'offset' => 0
        ];

        $args = wp_parse_args($args, $defaults);

        $where = [];
        $where_values = [];

        if (!empty($args['status'])) {
            $where[] = 'status = %s';
            $where_values[] = $args['status'];
        }

        if (!empty($args['category'])) {
            $where[] = 'category = %s';
            $where_values[] = $args['category'];
        }

        if (!empty($args['search'])) {
            $where[] = '(subject LIKE %s OR ticket_id LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_clause = '';
        if (!empty($where)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where);
        }

        $orderby = sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);
        if (!$orderby) {
            $orderby = 'created_at DESC';
        }

        $limit_clause = '';
        if ($args['limit'] > 0) {
            $limit_clause = $wpdb->prepare('LIMIT %d OFFSET %d', $args['limit'], $args['offset']);
        }

        $query = "SELECT * FROM $tickets_table $where_clause ORDER BY $orderby $limit_clause";

        if (!empty($where_values)) {
            $query = $wpdb->prepare($query, $where_values);
        }

        $tickets = $wpdb->get_results($query, ARRAY_A);

        return $tickets;
    }

    /**
     * Get ticket details
     *
     * @param int $ticket_id Ticket ID
     * @return array|false Ticket details or false if not found
     */
    public static function get_ticket($ticket_id) {
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';

        $ticket = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $tickets_table WHERE id = %d",
                $ticket_id
            ),
            ARRAY_A
        );

        if (!$ticket) {
            return false;
        }

        // Get replies
        $replies_table = $wpdb->prefix . 'lci2025_ticket_replies';
        $replies = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $replies_table WHERE ticket_id = %d ORDER BY created_at ASC",
                $ticket_id
            ),
            ARRAY_A
        );

        $ticket['replies'] = $replies;

        return $ticket;
    }

    /**
     * Get ticket details - Alias for get_ticket() for backward compatibility
     *
     * @param int $ticket_id Ticket ID
     * @return array|false Ticket details or false if not found
     */
    public static function get_ticket_details($ticket_id) {
        return self::get_ticket($ticket_id);
    }

    /**
     * Add a reply to a ticket
     *
     * @param array $reply_data Reply data
     * @return int|false Reply ID or false on failure
     */
    public static function add_reply($reply_data) {
        global $wpdb;
        $replies_table = $wpdb->prefix . 'lci2025_ticket_replies';

        $result = $wpdb->insert(
            $replies_table,
            [
                'ticket_id' => $reply_data['ticket_id'],
                'user_id' => $reply_data['user_id'],
                'is_admin' => isset($reply_data['is_admin']) ? $reply_data['is_admin'] : 0,
                'message' => $reply_data['message'],
                'attachment_url' => isset($reply_data['attachment_url']) ? $reply_data['attachment_url'] : null,
                'created_at' => current_time('mysql')
            ]
        );

        if ($result) {
            $reply_id = $wpdb->insert_id;

            // Update ticket updated_at timestamp
            $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';
            $wpdb->update(
                $tickets_table,
                ['updated_at' => current_time('mysql')],
                ['id' => $reply_data['ticket_id']]
            );

            // Send notification email
            self::send_reply_notification_email($reply_data['ticket_id'], $reply_id, $reply_data);

            return $reply_id;
        }

        return false;
    }

    /**
     * Send notification email for ticket reply
     *
     * @param int $ticket_id Ticket ID
     * @param int $reply_id Reply ID
     * @param array $reply_data Reply data
     * @return bool Whether the email was sent
     */
    public static function send_reply_notification_email($ticket_id, $reply_id, $reply_data) {
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';

        // Get ticket data
        $ticket = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $tickets_table WHERE id = %d",
                $ticket_id
            ),
            ARRAY_A
        );

        if (!$ticket) {
            return false;
        }

        // Get user data
        $user = get_userdata($reply_data['user_id']);
        $user_name = $user ? $user->display_name : 'Unknown User';

        // Determine recipient based on who replied
        $is_admin_reply = isset($reply_data['is_admin']) && $reply_data['is_admin'] == 1;

        if ($is_admin_reply) {
            // Admin replied, notify the user
            $ticket_owner = get_userdata($ticket['user_id']);
            if (!$ticket_owner) {
                return false;
            }

            $recipient_email = $ticket['email'];
            $recipient_name = $ticket_owner->display_name;

            $subject = sprintf('[LCI 2025 Support] New Reply to Your Ticket #%s', $ticket['ticket_id']);

            $message = sprintf(
                "Dear %s,\n\n" .
                "Our support team has replied to your ticket.\n\n" .
                "Ticket Details:\n" .
                "Ticket ID: %s\n" .
                "Subject: %s\n\n" .
                "Reply from Support Team:\n%s\n\n" .
                "You can view the full conversation and reply from your dashboard.\n\n" .
                "Best regards,\n" .
                "LCI 2025 AGM Support Team",
                $recipient_name,
                $ticket['ticket_id'],
                $ticket['subject'],
                $reply_data['message']
            );
        } else {
            // User replied, notify admin
            $admin_email = get_option('admin_email');
            $recipient_email = $admin_email;

            $subject = sprintf('[LCI 2025 Support] New Reply to Ticket #%s', $ticket['ticket_id']);

            $message = sprintf(
                "A user has replied to a support ticket.\n\n" .
                "Ticket ID: %s\n" .
                "Subject: %s\n" .
                "From: %s (%s)\n\n" .
                "Reply:\n%s\n\n" .
                "Please log in to the admin dashboard to respond to this ticket:\n%s",
                $ticket['ticket_id'],
                $ticket['subject'],
                $user_name,
                $ticket['email'],
                $reply_data['message'],
                admin_url('admin.php?page=lci-support-tickets')
            );
        }

        // Send email
        return wp_mail($recipient_email, $subject, $message);
    }

    /**
     * Update ticket status
     *
     * @param int $ticket_id Ticket ID
     * @param string $status New status
     * @return bool Success or failure
     */
    public static function update_ticket_status($ticket_id, $status) {
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';

        // Get current ticket data before update
        $ticket = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $tickets_table WHERE id = %d",
                $ticket_id
            ),
            ARRAY_A
        );

        if (!$ticket) {
            return false;
        }

        // Only proceed if status is actually changing
        if ($ticket['status'] === $status) {
            return true;
        }

        $result = $wpdb->update(
            $tickets_table,
            [
                'status' => $status,
                'updated_at' => current_time('mysql')
            ],
            ['id' => $ticket_id]
        );

        if ($result !== false) {
            // Send status update notification
            self::send_status_update_notification($ticket_id, $ticket, $status);
            return true;
        }

        return false;
    }

    /**
     * Send notification email for ticket status update
     *
     * @param int $ticket_id Ticket ID
     * @param array $ticket Ticket data
     * @param string $new_status New status
     * @return bool Whether the email was sent
     */
    public static function send_status_update_notification($ticket_id, $ticket, $new_status) {
        // Get user data
        $user = get_userdata($ticket['user_id']);
        if (!$user) {
            return false;
        }

        // Get status text
        $status_texts = [
            'open' => 'Open',
            'in-progress' => 'In Progress',
            'resolved' => 'Resolved',
            'closed' => 'Closed'
        ];

        $new_status_text = isset($status_texts[$new_status]) ? $status_texts[$new_status] : ucfirst($new_status);

        // Build email
        $subject = sprintf('[LCI 2025 Support] Ticket #%s Status Updated', $ticket['ticket_id']);

        $message = sprintf(
            "Dear %s,\n\n" .
            "The status of your support ticket has been updated.\n\n" .
            "Ticket Details:\n" .
            "Ticket ID: %s\n" .
            "Subject: %s\n" .
            "New Status: %s\n\n",
            $user->display_name,
            $ticket['ticket_id'],
            $ticket['subject'],
            $new_status_text
        );

        // Add specific message based on status
        switch ($new_status) {
            case 'in-progress':
                $message .= "Your ticket is now being handled by our support team. We'll keep you updated on the progress.\n\n";
                break;
            case 'resolved':
                $message .= "Your ticket has been marked as resolved. If you're satisfied with the resolution, no further action is needed. If you still have questions or concerns, please reply to the ticket and it will be reopened automatically.\n\n";
                break;
            case 'closed':
                $message .= "Your ticket has been closed. If you need further assistance, please create a new support ticket.\n\n";
                break;
        }

        $message .= "You can view the full details of your ticket from your dashboard.\n\n" .
                   "Best regards,\n" .
                   "LCI 2025 AGM Support Team";

        // Send email
        return wp_mail($ticket['email'], $subject, $message);
    }

    /**
     * AJAX: Submit a new ticket
     */
    public static function ajax_submit_ticket() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_support_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => 'You must be logged in to submit a ticket.']);
        }

        // Validate required fields
        $required_fields = ['ticket_subject', 'ticket_category', 'ticket_message', 'ticket_email'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                wp_send_json_error(['message' => 'Please fill in all required fields.']);
            }
        }

        // Handle file upload if present
        $attachment_url = '';
        if (!empty($_FILES['ticket_attachment']) && $_FILES['ticket_attachment']['error'] === UPLOAD_ERR_OK) {
            $upload = wp_handle_upload($_FILES['ticket_attachment'], ['test_form' => false]);
            if (isset($upload['url'])) {
                $attachment_url = $upload['url'];
            }
        }

        // Create ticket
        $ticket_data = [
            'user_id' => get_current_user_id(),
            'subject' => sanitize_text_field($_POST['ticket_subject']),
            'category' => sanitize_text_field($_POST['ticket_category']),
            'message' => sanitize_textarea_field($_POST['ticket_message']),
            'email' => sanitize_email($_POST['ticket_email']),
            'attachment_url' => $attachment_url
        ];

        $ticket_id = self::create_ticket($ticket_data);

        if ($ticket_id) {
            wp_send_json_success([
                'message' => 'Ticket submitted successfully.',
                'ticket_id' => $ticket_id
            ]);
        } else {
            wp_send_json_error(['message' => 'Error creating ticket. Please try again.']);
        }
    }

    /**
     * AJAX: Get user tickets
     */
    public static function ajax_get_tickets() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_support_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => 'You must be logged in to view tickets.']);
        }

        $user_id = get_current_user_id();
        $tickets = self::get_user_tickets($user_id);

        wp_send_json_success(['tickets' => $tickets]);
    }

    /**
     * AJAX: Get ticket details
     */
    public static function ajax_get_ticket_details() {
        // Check nonce - accept both admin and user nonces
        if (!isset($_POST['nonce']) ||
            (!wp_verify_nonce($_POST['nonce'], 'lci_support_nonce') &&
             !wp_verify_nonce($_POST['nonce'], 'lci_admin_support_nonce'))) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => 'You must be logged in to view ticket details.']);
        }

        // Validate ticket ID
        if (empty($_POST['ticket_id'])) {
            wp_send_json_error(['message' => 'Invalid ticket ID.']);
        }

        $ticket_id = intval($_POST['ticket_id']);
        $ticket = self::get_ticket($ticket_id);

        if (!$ticket) {
            wp_send_json_error(['message' => 'Ticket not found.']);
        }

        // Check if user owns the ticket or is an admin
        $user_id = get_current_user_id();
        if ($ticket['user_id'] != $user_id && !current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to view this ticket.']);
        }

        wp_send_json_success(['ticket' => $ticket]);
    }

    /**
     * AJAX: Reply to a ticket
     */
    public static function ajax_reply_to_ticket() {
        // Check nonce - accept both admin and user nonces
        if (!isset($_POST['nonce']) ||
            (!wp_verify_nonce($_POST['nonce'], 'lci_support_nonce') &&
             !wp_verify_nonce($_POST['nonce'], 'lci_admin_support_nonce'))) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => 'You must be logged in to reply to a ticket.']);
        }

        // Validate required fields
        if (empty($_POST['ticket_id']) || empty($_POST['message'])) {
            wp_send_json_error(['message' => 'Please fill in all required fields.']);
        }

        $ticket_id = intval($_POST['ticket_id']);
        $ticket = self::get_ticket($ticket_id);

        if (!$ticket) {
            wp_send_json_error(['message' => 'Ticket not found.']);
        }

        // Check if user owns the ticket or is an admin
        $user_id = get_current_user_id();
        if ($ticket['user_id'] != $user_id && !current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to reply to this ticket.']);
        }

        // Handle file upload if present
        $attachment_url = '';
        if (!empty($_FILES['attachment']) && $_FILES['attachment']['error'] === UPLOAD_ERR_OK) {
            $upload = wp_handle_upload($_FILES['attachment'], ['test_form' => false]);
            if (isset($upload['url'])) {
                $attachment_url = $upload['url'];
            }
        }

        // Add reply
        $reply_data = [
            'ticket_id' => $ticket_id,
            'user_id' => $user_id,
            'is_admin' => current_user_can('manage_options') ? 1 : 0,
            'message' => sanitize_textarea_field($_POST['message']),
            'attachment_url' => $attachment_url
        ];

        $reply_id = self::add_reply($reply_data);

        if ($reply_id) {
            // If admin is replying, update status to 'in-progress' if it's 'open'
            if (current_user_can('manage_options') && $ticket['status'] === 'open') {
                self::update_ticket_status($ticket_id, 'in-progress');
            }
            // If user is replying to a resolved ticket, reopen it
            elseif (!current_user_can('manage_options') && $ticket['status'] === 'resolved') {
                self::update_ticket_status($ticket_id, 'open');

                // Add a system note about reopening
                $system_note = [
                    'ticket_id' => $ticket_id,
                    'user_id' => $user_id,
                    'is_admin' => 0,
                    'message' => '--- This ticket has been automatically reopened due to a new reply from the user ---',
                    'attachment_url' => ''
                ];
                self::add_reply($system_note);
            }

            wp_send_json_success([
                'message' => 'Reply submitted successfully.',
                'reply_id' => $reply_id
            ]);
        } else {
            wp_send_json_error(['message' => 'Error submitting reply. Please try again.']);
        }
    }

    /**
     * AJAX: Update ticket status
     */
    public static function ajax_update_ticket_status() {
        // Check nonce - accept both admin and user nonces
        if (!isset($_POST['nonce']) ||
            (!wp_verify_nonce($_POST['nonce'], 'lci_support_nonce') &&
             !wp_verify_nonce($_POST['nonce'], 'lci_admin_support_nonce'))) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check if user is an admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to update ticket status.']);
        }

        // Validate required fields
        if (empty($_POST['ticket_id']) || empty($_POST['status'])) {
            wp_send_json_error(['message' => 'Please provide ticket ID and status.']);
        }

        $ticket_id = intval($_POST['ticket_id']);
        $status = sanitize_text_field($_POST['status']);

        // Validate status
        $valid_statuses = ['open', 'in-progress', 'resolved', 'closed'];
        if (!in_array($status, $valid_statuses)) {
            wp_send_json_error(['message' => 'Invalid status.']);
        }

        $result = self::update_ticket_status($ticket_id, $status);

        if ($result) {
            wp_send_json_success(['message' => 'Ticket status updated successfully.']);
        } else {
            wp_send_json_error(['message' => 'Error updating ticket status. Please try again.']);
        }
    }

    /**
     * AJAX: Admin get all tickets
     */
    public static function ajax_admin_get_tickets() {
        // Check nonce - accept both admin and user nonces
        if (!isset($_POST['nonce']) ||
            (!wp_verify_nonce($_POST['nonce'], 'lci_support_nonce') &&
             !wp_verify_nonce($_POST['nonce'], 'lci_admin_support_nonce'))) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check if user is an admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to view all tickets.']);
        }

        $args = [
            'status' => isset($_POST['status']) ? sanitize_text_field($_POST['status']) : '',
            'category' => isset($_POST['category']) ? sanitize_text_field($_POST['category']) : '',
            'search' => isset($_POST['search']) ? sanitize_text_field($_POST['search']) : '',
            'orderby' => isset($_POST['orderby']) ? sanitize_text_field($_POST['orderby']) : 'created_at',
            'order' => isset($_POST['order']) ? sanitize_text_field($_POST['order']) : 'DESC',
            'limit' => isset($_POST['limit']) ? intval($_POST['limit']) : 20,
            'offset' => isset($_POST['offset']) ? intval($_POST['offset']) : 0
        ];

        $tickets = self::get_all_tickets($args);

        // Get total count for pagination
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';

        $where = [];
        $where_values = [];

        if (!empty($args['status'])) {
            $where[] = 'status = %s';
            $where_values[] = $args['status'];
        }

        if (!empty($args['category'])) {
            $where[] = 'category = %s';
            $where_values[] = $args['category'];
        }

        if (!empty($args['search'])) {
            $where[] = '(subject LIKE %s OR ticket_id LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_clause = '';
        if (!empty($where)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where);
        }

        $count_query = "SELECT COUNT(*) FROM $tickets_table $where_clause";

        if (!empty($where_values)) {
            $count_query = $wpdb->prepare($count_query, $where_values);
        }

        $total = $wpdb->get_var($count_query);

        wp_send_json_success([
            'tickets' => $tickets,
            'total' => intval($total),
            'pages' => ceil($total / $args['limit'])
        ]);
    }

    /**
     * Delete a ticket and its replies
     *
     * @param int $ticket_id Ticket ID
     * @return bool Whether the ticket was deleted
     */
    public static function delete_ticket($ticket_id) {
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';
        $replies_table = $wpdb->prefix . 'lci2025_ticket_replies';

        // Start transaction
        $wpdb->query('START TRANSACTION');

        // Delete replies first
        $replies_deleted = $wpdb->delete(
            $replies_table,
            ['ticket_id' => $ticket_id],
            ['%d']
        );

        // Delete the ticket
        $ticket_deleted = $wpdb->delete(
            $tickets_table,
            ['id' => $ticket_id],
            ['%d']
        );

        // Commit or rollback transaction
        if ($ticket_deleted !== false) {
            $wpdb->query('COMMIT');
            return true;
        } else {
            $wpdb->query('ROLLBACK');
            return false;
        }
    }

    /**
     * AJAX: Delete ticket
     */
    public static function ajax_delete_ticket() {
        // Check nonce - accept both admin and user nonces
        if (!isset($_POST['nonce']) ||
            (!wp_verify_nonce($_POST['nonce'], 'lci_support_nonce') &&
             !wp_verify_nonce($_POST['nonce'], 'lci_admin_support_nonce'))) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check if user is an admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to delete tickets.']);
        }

        // Validate required fields
        if (empty($_POST['ticket_id'])) {
            wp_send_json_error(['message' => 'Please provide a ticket ID.']);
        }

        $ticket_id = intval($_POST['ticket_id']);

        // Check if ticket exists
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'lci2025_support_tickets';
        $ticket = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $tickets_table WHERE id = %d",
                $ticket_id
            )
        );

        if (!$ticket) {
            wp_send_json_error(['message' => 'Ticket not found.']);
        }

        $result = self::delete_ticket($ticket_id);

        if ($result) {
            wp_send_json_success(['message' => 'Ticket deleted successfully.']);
        } else {
            wp_send_json_error(['message' => 'Error deleting ticket. Please try again.']);
        }
    }
}

// Initialize the support ticket system
add_action('init', ['LCI_Support', 'init']);
