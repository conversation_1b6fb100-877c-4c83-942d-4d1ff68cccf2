/**
 * Emergency Cart Rebuild Script
 * This script completely rebuilds the cart to eliminate all duplicates
 */
(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('EMERGENCY: Cart rebuild script loaded');
        
        // Function to completely rebuild the cart
        function rebuildCart() {
            console.log('EMERGENCY: Rebuilding cart...');
            
            // Call our emergency cart rebuild endpoint
            fetch(lci_ajax.ajax_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=lci_emergency_rebuild_cart&security=' + lci_ajax.nonce
            })
            .then(response => response.json())
            .then(data => {
                console.log('EMERGENCY: Cart rebuild response:', data);
                
                if (data.success) {
                    // Force reload the page to ensure everything is fresh
                    window.location.reload();
                } else {
                    console.error('EMERGENCY: Failed to rebuild cart:', data.message);
                }
            })
            .catch(error => {
                console.error('EMERGENCY: Error rebuilding cart:', error);
            });
        }
        
        // Add a button to the page for emergency cart rebuild
        const container = document.querySelector('.regalia-shop-container');
        if (container) {
            const emergencyButton = document.createElement('button');
            emergencyButton.className = 'btn btn-danger mb-4';
            emergencyButton.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> Fix Cart Issues';
            emergencyButton.style.display = 'block';
            emergencyButton.style.margin = '0 auto';
            emergencyButton.onclick = function(e) {
                e.preventDefault();
                rebuildCart();
            };
            
            // Insert at the top of the container
            container.insertBefore(emergencyButton, container.firstChild);
        }
        
        // Also add a button to the mini-cart
        const miniCartFooter = document.querySelector('.lci-modal-footer');
        if (miniCartFooter) {
            const emergencyButton = document.createElement('button');
            emergencyButton.className = 'lci-btn lci-btn-danger';
            emergencyButton.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i> Fix Cart';
            emergencyButton.style.marginRight = '0.5rem';
            emergencyButton.onclick = function(e) {
                e.preventDefault();
                rebuildCart();
            };
            
            // Insert at the beginning of the footer
            const footerContainer = miniCartFooter.querySelector('.d-flex');
            if (footerContainer) {
                footerContainer.insertBefore(emergencyButton, footerContainer.firstChild);
            }
        }
    });
})();
