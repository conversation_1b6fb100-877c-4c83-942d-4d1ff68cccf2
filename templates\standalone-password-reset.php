<?php
/**
 * Standalone Password Reset Template
 *
 * This template is used to display the password reset form without redirects.
 */

// Get URL parameters
$key = isset($_GET['key']) ? sanitize_text_field($_GET['key']) : '';
$login = isset($_GET['login']) ? sanitize_text_field($_GET['login']) : '';
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';

// Check if this is a valid password reset request
$is_valid_request = ($key && $login && $action === 'reset_password');

if (!$is_valid_request) {
    wp_redirect(home_url('/lci-dashboard/'));
    exit;
}

// Get the site logo
$logo_url = LCI2025_URL . 'assets/img/logo.png';

// Get the user
$user = get_user_by('login', $login);
if (!$user) {
    $user = get_user_by('email', $login);
}

$user_display_name = $user ? $user->display_name : 'User';
$first_name = $user ? get_user_meta($user->ID, 'first_name', true) : '';
$greeting_name = !empty($first_name) ? $first_name : $user_display_name;

get_header('empty');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password - LCI 2025</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #f8f9fa;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .reset-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 30px;
            max-width: 500px;
            width: 100%;
            margin: 50px auto;
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 150px;
            height: auto;
        }
        h2 {
            color: #36b1dc;
            text-align: center;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            font-weight: bold;
            color: #4a5568;
        }
        .form-control {
            border-radius: 5px;
            padding: 12px;
            border: 1px solid #e2e8f0;
        }
        .btn-primary {
            background-color: #36b1dc;
            border: none;
            border-radius: 5px;
            padding: 12px;
            font-weight: bold;
            width: 100%;
            margin-top: 10px;
        }
        .btn-primary:hover {
            background-color: #2d93b7;
        }
        .alert {
            margin-bottom: 20px;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #36b1dc;
            text-decoration: none;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        .password-strength {
            height: 5px;
            margin-top: 5px;
            border-radius: 5px;
            background-color: #e2e8f0;
            overflow: hidden;
        }
        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: width 0.3s ease;
        }
        .password-match {
            margin-top: 5px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="logo">
            <img src="<?php echo esc_url($logo_url); ?>" alt="LCI 2025 Logo">
        </div>

        <h2>Reset Your Password</h2>

        <div class="alert alert-info text-center">
            Hello <?php echo esc_html($greeting_name); ?>, please create a new password for your account.
        </div>

        <form id="standalone-reset-form">
            <input type="hidden" id="reset-login" value="<?php echo esc_attr($login); ?>">
            <input type="hidden" id="reset-key" value="<?php echo esc_attr($key); ?>">

            <div class="form-group">
                <label for="new-password" class="form-label">New Password</label>
                <input type="password" id="new-password" class="form-control" required>
                <div class="password-strength">
                    <div id="password-strength-bar" class="password-strength-bar"></div>
                </div>
                <small id="password-strength-text" class="text-muted">Password strength: None</small>
            </div>

            <div class="form-group">
                <label for="confirm-password" class="form-label">Confirm Password</label>
                <input type="password" id="confirm-password" class="form-control" required>
                <small id="password-match-text" class="password-match"></small>
            </div>

            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i> Save Password
            </button>
        </form>

        <div id="message-container" class="mt-3"></div>

        <div class="back-link">
            <a href="<?php echo esc_url(home_url('/lci-dashboard/')); ?>">
                <i class="fas fa-arrow-left me-1"></i> Back to Login
            </a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetForm = document.getElementById('standalone-reset-form');
            const messageContainer = document.getElementById('message-container');
            const newPasswordInput = document.getElementById('new-password');
            const confirmPasswordInput = document.getElementById('confirm-password');
            const passwordStrengthBar = document.getElementById('password-strength-bar');
            const passwordStrengthText = document.getElementById('password-strength-text');
            const passwordMatchText = document.getElementById('password-match-text');

            // Log the parameters for debugging
            const login = document.getElementById('reset-login').value;
            const key = document.getElementById('reset-key').value;
            console.log('Reset parameters:', { login, key });

            // Password strength checker
            newPasswordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;
                let color = '#e2e8f0';
                let text = 'None';

                if (password.length >= 8) strength += 1;
                if (password.match(/[a-z]+/)) strength += 1;
                if (password.match(/[A-Z]+/)) strength += 1;
                if (password.match(/[0-9]+/)) strength += 1;
                if (password.match(/[^a-zA-Z0-9]+/)) strength += 1;

                switch (strength) {
                    case 0:
                        color = '#e2e8f0';
                        text = 'None';
                        break;
                    case 1:
                        color = '#f56565';
                        text = 'Very Weak';
                        break;
                    case 2:
                        color = '#ed8936';
                        text = 'Weak';
                        break;
                    case 3:
                        color = '#ecc94b';
                        text = 'Medium';
                        break;
                    case 4:
                        color = '#48bb78';
                        text = 'Strong';
                        break;
                    case 5:
                        color = '#38a169';
                        text = 'Very Strong';
                        break;
                }

                passwordStrengthBar.style.width = (strength * 20) + '%';
                passwordStrengthBar.style.backgroundColor = color;
                passwordStrengthText.textContent = 'Password strength: ' + text;
            });

            // Password match checker
            confirmPasswordInput.addEventListener('input', function() {
                const password = newPasswordInput.value;
                const confirmPassword = this.value;

                if (!confirmPassword) {
                    passwordMatchText.textContent = '';
                    passwordMatchText.className = 'password-match';
                    return;
                }

                if (password === confirmPassword) {
                    passwordMatchText.textContent = 'Passwords match';
                    passwordMatchText.className = 'password-match text-success';
                } else {
                    passwordMatchText.textContent = 'Passwords do not match';
                    passwordMatchText.className = 'password-match text-danger';
                }
            });

            // Form submission
            resetForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const login = document.getElementById('reset-login').value;
                const key = document.getElementById('reset-key').value;
                const newPassword = document.getElementById('new-password').value;
                const confirmPassword = document.getElementById('confirm-password').value;

                // Validate passwords
                if (newPassword.length < 8) {
                    showMessage('Error', 'Password must be at least 8 characters long.', 'danger');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    showMessage('Error', 'Passwords do not match.', 'danger');
                    return;
                }

                // Disable the submit button to prevent multiple submissions
                const submitButton = resetForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';

                // Submit the form via AJAX
                fetch('<?php echo esc_url(admin_url('admin-ajax.php')); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_ajax_change_password',
                        login: login,
                        password: newPassword,
                        key: key
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Response:', data);
                    
                    if (data.success) {
                        showMessage('Success', data.data.message || 'Your password has been successfully changed.', 'success');
                        resetForm.reset();
                        
                        // Reset UI elements
                        passwordStrengthBar.style.width = '0%';
                        passwordStrengthText.textContent = 'Password strength: None';
                        passwordMatchText.textContent = '';
                        
                        // Redirect to login after 3 seconds
                        setTimeout(function() {
                            window.location.href = '<?php echo esc_url(home_url('/lci-dashboard/')); ?>';
                        }, 3000);
                    } else {
                        showMessage('Error', data.data.message || 'An error occurred. Please try again.', 'danger');
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="fas fa-save me-2"></i> Save Password';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('Error', 'An unexpected error occurred. Please try again.', 'danger');
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<i class="fas fa-save me-2"></i> Save Password';
                });
            });

            // Function to show messages
            function showMessage(title, message, type) {
                messageContainer.className = 'alert alert-' + type + ' text-center mt-3';
                messageContainer.innerHTML = '<strong>' + title + ':</strong> ' + message;
            }
        });
    </script>
</body>
</html>
<?php
get_footer('empty');
?>
