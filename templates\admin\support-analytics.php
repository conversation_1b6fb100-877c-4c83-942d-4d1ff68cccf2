<?php
/**
 * Admin Support Analytics Template
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get analytics data
global $wpdb;
$tickets_table = $wpdb->prefix . 'lci2025_support_tickets';
$replies_table = $wpdb->prefix . 'lci2025_ticket_replies';

// Total tickets
$total_tickets = $wpdb->get_var("SELECT COUNT(*) FROM $tickets_table");

// Tickets by status
$tickets_by_status = $wpdb->get_results(
    "SELECT status, COUNT(*) as count FROM $tickets_table GROUP BY status",
    ARRAY_A
);

// Tickets by category
$tickets_by_category = $wpdb->get_results(
    "SELECT category, COUNT(*) as count FROM $tickets_table GROUP BY category",
    ARRAY_A
);

// Average response time (in hours)
$avg_response_time = $wpdb->get_var(
    "SELECT AVG(TIMESTAMPDIFF(HOUR, t.created_at, MIN(r.created_at))) 
    FROM $tickets_table t 
    JOIN $replies_table r ON t.id = r.ticket_id 
    WHERE r.is_admin = 1 
    GROUP BY t.id"
);

// Tickets created in the last 30 days
$recent_tickets = $wpdb->get_var(
    $wpdb->prepare(
        "SELECT COUNT(*) FROM $tickets_table WHERE created_at >= %s",
        date('Y-m-d H:i:s', strtotime('-30 days'))
    )
);

// Tickets resolved in the last 30 days
$recent_resolved = $wpdb->get_var(
    $wpdb->prepare(
        "SELECT COUNT(*) FROM $tickets_table WHERE status = 'resolved' AND updated_at >= %s",
        date('Y-m-d H:i:s', strtotime('-30 days'))
    )
);

// Format status data for chart
$status_labels = [];
$status_data = [];
$status_colors = [
    'open' => '#ffc107',
    'in-progress' => '#36b1dc',
    'resolved' => '#4caf50',
    'closed' => '#6c757d'
];
$status_texts = [
    'open' => 'Open',
    'in-progress' => 'In Progress',
    'resolved' => 'Resolved',
    'closed' => 'Closed'
];

foreach ($tickets_by_status as $status) {
    $status_name = $status['status'];
    $status_labels[] = isset($status_texts[$status_name]) ? $status_texts[$status_name] : ucfirst($status_name);
    $status_data[] = intval($status['count']);
}

// Format category data for chart
$category_labels = [];
$category_data = [];
$category_texts = [
    'registration' => 'Registration Issues',
    'payment' => 'Payment & Invoices',
    'accommodation' => 'Accommodation',
    'tours' => 'Tours & Activities',
    'visa' => 'Visa & Travel',
    'other' => 'Other Questions'
];

foreach ($tickets_by_category as $category) {
    $category_name = $category['category'];
    $category_labels[] = isset($category_texts[$category_name]) ? $category_texts[$category_name] : ucfirst($category_name);
    $category_data[] = intval($category['count']);
}
?>

<div class="wrap lci-admin-wrap">
    <h1 class="text-3xl font-bold mb-6">Support Analytics</h1>
    
    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Tickets -->
        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <div class="flex items-center">
                <div class="bg-blue-100 p-3 rounded-full mr-4">
                    <svg class="h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Total Tickets</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo esc_html($total_tickets); ?></h3>
                </div>
            </div>
        </div>
        
        <!-- Average Response Time -->
        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <div class="flex items-center">
                <div class="bg-green-100 p-3 rounded-full mr-4">
                    <svg class="h-8 w-8 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Avg. Response Time</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo $avg_response_time ? round($avg_response_time, 1) . ' hrs' : 'N/A'; ?></h3>
                </div>
            </div>
        </div>
        
        <!-- Recent Tickets -->
        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <div class="flex items-center">
                <div class="bg-yellow-100 p-3 rounded-full mr-4">
                    <svg class="h-8 w-8 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">New Tickets (30 days)</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo esc_html($recent_tickets); ?></h3>
                </div>
            </div>
        </div>
        
        <!-- Resolved Tickets -->
        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <div class="flex items-center">
                <div class="bg-indigo-100 p-3 rounded-full mr-4">
                    <svg class="h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Resolved (30 days)</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo esc_html($recent_resolved); ?></h3>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Tickets by Status Chart -->
        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <h2 class="text-xl font-medium text-gray-800 mb-4">Tickets by Status</h2>
            <div class="h-64">
                <canvas id="statusChart"></canvas>
            </div>
        </div>
        
        <!-- Tickets by Category Chart -->
        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <h2 class="text-xl font-medium text-gray-800 mb-4">Tickets by Category</h2>
            <div class="h-64">
                <canvas id="categoryChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Resolution Rate -->
    <div class="bg-white rounded-xl shadow-neumorph p-6 mb-8">
        <h2 class="text-xl font-medium text-gray-800 mb-4">Resolution Rate</h2>
        <div class="flex items-center">
            <div class="w-full bg-gray-200 rounded-full h-4">
                <?php 
                $resolution_rate = $total_tickets > 0 ? round(($recent_resolved / $total_tickets) * 100) : 0;
                ?>
                <div class="bg-green-600 h-4 rounded-full" style="width: <?php echo esc_attr($resolution_rate); ?>%"></div>
            </div>
            <span class="ml-4 text-lg font-medium text-gray-800"><?php echo esc_html($resolution_rate); ?>%</span>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status Chart
    var statusCtx = document.getElementById('statusChart').getContext('2d');
    var statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($status_labels); ?>,
            datasets: [{
                data: <?php echo json_encode($status_data); ?>,
                backgroundColor: [
                    '#ffc107', // Open - Yellow
                    '#36b1dc', // In Progress - Blue
                    '#4caf50', // Resolved - Green
                    '#6c757d'  // Closed - Gray
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                }
            }
        }
    });
    
    // Category Chart
    var categoryCtx = document.getElementById('categoryChart').getContext('2d');
    var categoryChart = new Chart(categoryCtx, {
        type: 'bar',
        data: {
            labels: <?php echo json_encode($category_labels); ?>,
            datasets: [{
                label: 'Number of Tickets',
                data: <?php echo json_encode($category_data); ?>,
                backgroundColor: 'rgba(54, 177, 220, 0.7)',
                borderColor: 'rgba(54, 177, 220, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
});
</script>
