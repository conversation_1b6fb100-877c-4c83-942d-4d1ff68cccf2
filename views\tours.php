<?php
/**
 * Tours View
 *
 * Displays tours from WooCommerce category ID 21 (Tours)
 * Includes a mini cart and shows user's previous tour bookings
 */

// Scroll fix is now handled by tours-fix.js

// Enqueue the tours CSS
wp_enqueue_style('tours-css', LCI2025_URL . 'assets/css/tours.css', array(), LCI2025_VERSION);

// Enqueue the tours fix script
wp_enqueue_script('tours-fix', LCI2025_URL . 'assets/js/tours-fix.js', array('jquery', 'alpine-js'), LCI2025_VERSION, true);

// Enqueue the Alpine.js fix script
wp_enqueue_script('fix-alpine', LCI2025_URL . 'assets/js/fix-alpine.js', array('jquery', 'alpine-js'), LCI2025_VERSION, true);

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_email = $current_user->user_email;

// Category ID for Tours products
// Allow overriding the category ID via URL parameter
$tours_category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 21;

// Get user's orders containing products from the tours category
$user_orders = [];
$tours_products = [];

// Track products the user has already ordered
$user_ordered_product_ids = [];

// Councilor product ID that requires a warning
$councilor_product_id = 40;
$is_councilor = false;

// Check if WooCommerce is active
if (class_exists('WooCommerce')) {
    // Get all customer orders
    $customer_orders = wc_get_orders([
        'customer' => $user_id,
        'limit' => -1,
        'status' => ['processing', 'completed', 'on-hold'],
        'orderby' => 'date',
        'order' => 'DESC',
    ]);

    // Get all products from the tours category
    // Debug information
    echo '<!-- Debug: Attempting to get products from category ID: ' . $tours_category_id . ' -->';

    // Try multiple methods to get products
    $tours_products = array();

    // Method 1: Using WP_Query with tax_query
    $args = array(
        'post_type'      => 'product',
        'posts_per_page' => -1,
        'post_status'    => 'publish',
        'tax_query'      => array(
            array(
                'taxonomy' => 'product_cat',
                'field'    => 'term_id',
                'terms'    => $tours_category_id,
            ),
        ),
    );

    $products_query = new WP_Query($args);

    if ($products_query->have_posts()) {
        while ($products_query->have_posts()) {
            $products_query->the_post();
            $product_id = get_the_ID();
            $product = wc_get_product($product_id);
            if ($product) {
                $tours_products[] = $product;
            }
        }
        wp_reset_postdata();
    }

    echo '<!-- Debug Method 1: Found ' . count($tours_products) . ' products -->';

    // If no products found, try Method 2: Using wc_get_products
    if (empty($tours_products)) {
        $args = array(
            'status' => 'publish',
            'limit'  => -1,
            'category' => array($tours_category_id),
        );

        $method2_products = wc_get_products($args);
        if (!empty($method2_products)) {
            $tours_products = $method2_products;
        }

        echo '<!-- Debug Method 2: Found ' . count($tours_products) . ' products -->';
    }

    // If still no products found, try Method 3: Using get_posts
    if (empty($tours_products)) {
        $args = array(
            'post_type'      => 'product',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
            'tax_query'      => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'slug',
                    'terms'    => get_term_by('id', $tours_category_id, 'product_cat') ? get_term_by('id', $tours_category_id, 'product_cat')->slug : '',
                ),
            ),
        );

        $posts = get_posts($args);
        foreach ($posts as $post) {
            $product = wc_get_product($post->ID);
            if ($product) {
                $tours_products[] = $product;
            }
        }

        echo '<!-- Debug Method 3: Found ' . count($tours_products) . ' products -->';
    }

    // Filter orders to only include those with tours products
    foreach ($customer_orders as $order) {
        $has_tours = false;
        $tours_items = [];

        // Check if user is a councilor
        if (!$is_councilor) {
            foreach ($order->get_items() as $item) {
                $product_id = $item->get_product_id();
                if ($product_id == $councilor_product_id) {
                    $is_councilor = true;
                    break;
                }
            }
        }

        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $product = wc_get_product($product_id);

            if ($product) {
                $categories = $product->get_category_ids();

                if (in_array($tours_category_id, $categories)) {
                    $has_tours = true;
                    $tours_items[] = [
                        'id' => $product_id,
                        'name' => $item->get_name(),
                        'quantity' => $item->get_quantity(),
                        'total' => $item->get_total(),
                        'image' => wp_get_attachment_url($product->get_image_id()),
                    ];

                    // Add to the list of products the user has already ordered
                    if (!in_array($product_id, $user_ordered_product_ids)) {
                        $user_ordered_product_ids[] = $product_id;
                    }
                }
            }
        }

        if ($has_tours) {
            $user_orders[] = [
                'id' => $order->get_id(),
                'date' => $order->get_date_created()->date('Y-m-d H:i:s'),
                'status' => $order->get_status(),
                'total' => $order->get_total(),
                'items' => $tours_items,
            ];
        }
    }
}

// Get cart contents count
$cart_count = WC()->cart ? WC()->cart->get_cart_contents_count() : 0;

// Get cart total
$cart_total = WC()->cart ? WC()->cart->get_cart_total() : 0;
?>

<div class="tours-container">
    <!-- Header with title and mini cart -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-primary mb-0"><i class="fas fa-map-marked-alt me-2"></i> LCI 2025 Tours</h2>
        </div>

        <!-- Mini Cart Button -->
        <div class="mini-cart-wrapper">
            <?php echo lci_get_mini_cart_html(); ?>
        </div>
    </div>

    <?php if (isset($_GET['category_id'])): ?>
    <div class="mb-3">
        <a href="<?php echo esc_url(add_query_arg('tab', 'tours', remove_query_arg('category_id'))); ?>" class="btn btn-sm btn-outline-secondary d-inline-flex align-items-center">
            <i class="fas fa-arrow-left me-2"></i> Back to All Tours
        </a>
    </div>
    <?php endif; ?>

    <!-- My Tour Experiences Section -->
    <?php if (!empty($user_orders)): ?>
    <div class="mb-5">
        <div class="tours-section-heading wow-heading my-4">
            <h3 class="text-uppercase">MY TOUR EXPERIENCES</h3>
            <div class="heading-underline"></div>
        </div>

        <div class="tours-experiences-container" x-data="{ activeExperience: null }">
            <?php
            // Flatten all tour items from all orders
            $all_experiences = [];
            foreach ($user_orders as $order) {
                foreach ($order['items'] as $item) {
                    $item['order_id'] = $order['id'];
                    $item['order_date'] = $order['date'];
                    $item['order_status'] = $order['status'];
                    $all_experiences[] = $item;
                }
            }
            ?>

            <div class="tours-experiences-list">
                <?php foreach ($all_experiences as $index => $experience): ?>
                <div class="tours-experience-card"
                     style="--card-index: <?php echo $index; ?>;"
                     x-data="{ hover: false }"
                     @mouseenter="hover = true"
                     @mouseleave="hover = false"
                     @click="activeExperience = activeExperience === <?php echo $index; ?> ? null : <?php echo $index; ?>"
                     :class="{ 'tours-experience-active': activeExperience === <?php echo $index; ?> }">



                    <?php
                    // Get tour type and dates from meta data
                    $product = wc_get_product($experience['id']);
                    $tour_type = get_post_meta($experience['id'], '_tour_type', true);
                    $starting_day = get_post_meta($experience['id'], '_starting_day', true);
                    $ending_day = get_post_meta($experience['id'], '_ending_day', true);
                    $short_description = $product ? $product->get_short_description() : '';
                    $product_description = $product ? $product->get_description() : '';

                    // Get presentation link from meta data
                    $presentation_link = get_post_meta($experience['id'], '_presentation_link', true);

                    // Try alternative field names if the first attempt returns empty
                    if (empty($presentation_link)) $presentation_link = get_post_meta($experience['id'], 'presentation_link', true);
                    if (empty($presentation_link)) $presentation_link = get_post_meta($experience['id'], 'presentation_url', true);
                    if (empty($presentation_link)) $presentation_link = get_post_meta($experience['id'], 'tour_presentation', true);
                    if (empty($presentation_link)) $presentation_link = get_post_meta($experience['id'], 'tour_link', true);

                    // Set default values if empty
                    $tour_type = empty($tour_type) ? 'tour' : $tour_type;
                    $ribbon_text = ($tour_type == 'pretour') ? 'Main Pretour' : 'Tour';
                    $ribbon_class = ($tour_type == 'pretour') ? 'pretour' : '';
                    ?>

                    <!-- Tour Type Ribbon -->
                    <div class="tour-type-ribbon <?php echo esc_attr($ribbon_class); ?>">
                        <i class="fas <?php echo ($tour_type == 'pretour') ? 'fa-flag' : 'fa-map-marker-alt'; ?> me-1"></i>
                        <?php echo esc_html($ribbon_text); ?>
                    </div>

                    <!-- Main Content -->
                    <div class="tours-experience-content">

                        <div class="tours-experience-image">
                            <?php if (!empty($experience['image'])): ?>
                            <img src="<?php echo esc_url($experience['image']); ?>" alt="<?php echo esc_attr($experience['name']); ?>">
                            <?php else: ?>
                            <div class="tours-experience-no-image">
                                <i class="fas fa-mountain"></i>
                            </div>
                            <?php endif; ?>


                        </div>

                        <div class="tours-experience-details">
                            <div class="tours-experience-header">
                                <h4 class="tours-experience-title"><?php echo esc_html($experience['name']); ?></h4>
                            </div>

                            <?php if (!empty($short_description)): ?>
                            <div class="tour-short-description">
                                <?php echo wp_kses_post($short_description); ?>
                            </div>
                            <?php endif; ?>

                            <div class="tour-info-row">
                                <?php if (!empty($starting_day)): ?>
                                <div class="tour-dates">
                                    <i class="far fa-calendar-alt"></i>
                                    <?php
                                    // Format dates nicely
                                    $formatted_start_date = !empty($starting_day) ? date('jS \of F Y', strtotime($starting_day)) : '';
                                    $formatted_end_date = !empty($ending_day) ? date('jS \of F Y', strtotime($ending_day)) : '';

                                    if ($tour_type == 'pretour' && !empty($ending_day)): ?>
                                        <span>From <?php echo esc_html($formatted_start_date); ?> to <?php echo esc_html($formatted_end_date); ?></span>
                                    <?php else: ?>
                                        <span>Date: <?php echo esc_html($formatted_start_date); ?></span>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>

                                <div class="tours-experience-status tours-experience-status-<?php echo esc_attr($experience['order_status']); ?>">
                                    <i class="fas <?php echo ($experience['order_status'] === 'completed') ? 'fa-money-bill-wave' : 'fa-check-circle'; ?> me-1"></i>
                                    <?php echo ($experience['order_status'] === 'completed') ? 'Paid' : ucfirst(esc_html($experience['order_status'])); ?>
                                </div>
                            </div>

                            <!-- View Details Button -->
                            <div class="mt-3">
                                <?php if (!empty($presentation_link)): ?>
                                <a href="<?php echo esc_url($presentation_link); ?>" target="_blank" class="tours-experience-btn">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    <span>View Details</span>
                                </a>
                                <?php else: ?>
                                <button class="tours-experience-btn" @click.stop="activeExperience = activeExperience === <?php echo $index; ?> ? null : <?php echo $index; ?>">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span x-text="activeExperience === <?php echo $index; ?> ? 'Hide Details' : 'View Details'">View Details</span>
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Expandable Info Panel -->
                    <div class="tours-experience-expanded"
                         x-show="activeExperience === <?php echo $index; ?>"
                         x-transition:enter="transition ease-out duration-400"
                         x-transition:enter-start="opacity-0 max-h-0 overflow-hidden"
                         x-transition:enter-end="opacity-100 max-h-1000 overflow-hidden"
                         x-transition:leave="transition ease-in duration-300"
                         x-transition:leave-start="opacity-100 max-h-1000 overflow-hidden"
                         x-transition:leave-end="opacity-0 max-h-0 overflow-hidden">
                        <div class="tours-experience-expanded-content">
                            <div class="tours-experience-expanded-section">
                                <h5><i class="fas fa-info-circle me-2"></i>Tour Information</h5>
                                <?php if (!empty($short_description)): ?>
                                <div class="tour-short-description">
                                    <?php echo wpautop($short_description); ?>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($product_description)): ?>
                                <div class="tour-description mt-3">
                                    <h6 class="mb-2">Description</h6>
                                    <?php echo wpautop($product_description); ?>
                                </div>
                                <?php elseif (empty($short_description)): ?>
                                <p>Please arrive 15 minutes before the scheduled departure time. Don't forget to bring comfortable shoes, water, and your camera!</p>
                                <?php endif; ?>
                            </div>

                            <div class="tours-experience-expanded-section">
                                <h5><i class="fas fa-user me-2"></i>Booking Details</h5>
                                <div class="tours-experience-expanded-info">
                                    <div class="tours-experience-expanded-info-item">
                                        <span class="tours-experience-expanded-info-label">Booked for:</span>
                                        <span class="tours-experience-expanded-info-value"><?php echo esc_html($current_user->display_name); ?></span>
                                    </div>
                                    <div class="tours-experience-expanded-info-item">
                                        <span class="tours-experience-expanded-info-label">Contact:</span>
                                        <span class="tours-experience-expanded-info-value"><?php echo esc_html($user_email); ?></span>
                                    </div>
                                    <div class="tours-experience-expanded-info-item">
                                        <span class="tours-experience-expanded-info-label">Booked on:</span>
                                        <span class="tours-experience-expanded-info-value"><?php echo date('F j, Y', strtotime($experience['order_date'])); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="tours-experience-expanded-section">
                                <h5><i class="fas fa-qrcode me-2"></i>Digital Ticket</h5>
                                <div class="tours-experience-qr">
                                    <div class="tours-experience-qr-code">
                                        <i class="fas fa-qrcode"></i>
                                    </div>
                                    <div class="tours-experience-qr-text">
                                        <p>Scan this code at the tour meeting point</p>
                                        <span class="tours-experience-qr-id">ID: <?php echo esc_html($experience['id']); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>


        </div>
    </div>
    <?php endif; ?>

    <!-- Available Tours Section -->
    <div class="mb-5">
        <?php if (empty($tours_products)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No tours are currently available. Please check back later.
            </div>

            <!-- Debug information -->
            <div class="alert alert-warning mt-3">
                <h5><i class="fas fa-bug me-2"></i> Debug Information</h5>
                <p>Attempting to retrieve products from category ID: <?php echo $tours_category_id; ?></p>

                <?php
                // Check if the category exists
                $term = get_term_by('id', $tours_category_id, 'product_cat');
                if ($term) {
                    echo '<p>Category found: ' . esc_html($term->name) . ' (ID: ' . esc_html($term->term_id) . ')</p>';

                    // Get all product categories for comparison
                    $product_categories = get_terms(array(
                        'taxonomy' => 'product_cat',
                        'hide_empty' => false,
                    ));

                    if (!empty($product_categories) && !is_wp_error($product_categories)) {
                        echo '<p>Available product categories:</p>';
                        echo '<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-2 mb-3">';
                        foreach ($product_categories as $category) {
                            $current_url = add_query_arg(array('tab' => 'tours'), remove_query_arg('category_id'));
                            $category_url = add_query_arg(array('category_id' => $category->term_id), $current_url);

                            echo '<div class="col">';
                            echo '<a href="' . esc_url($category_url) . '" class="btn btn-outline-primary btn-sm d-block text-start overflow-hidden">';
                            echo esc_html($category->name) . ' <span class="badge bg-secondary float-end">' . esc_html($category->term_id) . '</span>';
                            echo '</a>';
                            echo '</div>';
                        }
                        echo '</div>';
                    } else {
                        echo '<p>No product categories found or error retrieving categories.</p>';
                    }
                } else {
                    echo '<p>Category with ID ' . esc_html($tours_category_id) . ' not found. Please check the category ID.</p>';
                }
                ?>
            </div>
        <?php else: ?>
            <?php if (empty($user_orders)): ?>
            <!-- CTA for users without tour bookings -->
            <div class="tours-cta-container mb-4">
                <div class="tours-cta-content text-center">
                    <h3 class="tours-cta-title"><i class="fas fa-map-marked-alt me-2"></i> Enhance Your LCI 2025 Experience!</h3>
                    <p class="tours-cta-text">Don't miss out on the full LCI 2025 experience! Our carefully selected tours will allow you to discover the beauty and culture of Brasov and its surroundings. Book your tours now to secure your spot and create unforgettable memories with fellow Circlers.</p>
                </div>
            </div>
            <?php endif; ?>

            <div class="tours-section-heading wow-heading my-4">
                <h3 class="text-uppercase">AVAILABLE TOURS</h3>
                <div class="heading-underline"></div>
            </div>

            <div class="tours-products-container">
                <div class="tours-products-grid">
                <?php
                // Filter out products the user has already ordered
                $available_tours = [];
                $hidden_tours_count = 0;

                foreach ($tours_products as $product) {
                    if (!in_array($product->get_id(), $user_ordered_product_ids)) {
                        $available_tours[] = $product;
                    } else {
                        $hidden_tours_count++;
                    }
                }

                // Loop through available tours
                foreach ($available_tours as $product): ?>
                    <div class="tours-product-card">
                        <?php
                        // Get tour type and dates from meta data
                        $tour_type = get_post_meta($product->get_id(), '_tour_type', true);
                        $starting_day = get_post_meta($product->get_id(), '_starting_day', true);
                        $ending_day = get_post_meta($product->get_id(), '_ending_day', true);

                        // Set default values if empty
                        $tour_type = empty($tour_type) ? 'tour' : $tour_type;
                        $ribbon_text = ($tour_type == 'pretour') ? 'Main Pretour' : 'Tour';
                        $ribbon_class = ($tour_type == 'pretour') ? 'pretour' : '';
                        ?>

                        <!-- Tour Type Ribbon -->
                        <div class="tours-badge-tour-type <?php echo esc_attr($ribbon_class); ?>">
                            <i class="fas <?php echo ($tour_type == 'pretour') ? 'fa-flag' : 'fa-map-marker-alt'; ?> me-1"></i>
                            <?php echo esc_html($ribbon_text); ?>
                        </div>

                        <div class="tours-product-image">
                            <img src="<?php echo esc_url(wp_get_attachment_url($product->get_image_id())); ?>" alt="<?php echo esc_attr($product->get_name()); ?>">

                            <!-- Badges -->
                            <div class="tours-badges-left">
                                <?php if ($product->is_on_sale()): ?>
                                <div class="tours-badge tours-badge-sale">
                                    <i class="fas fa-percentage mr-1"></i>Sale!
                                </div>
                                <?php endif; ?>
                                <?php if ($product->is_featured()): ?>
                                <div class="tours-badge tours-badge-featured">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="tours-product-content">
                            <h3 class="tours-product-title"><?php echo esc_html($product->get_name()); ?></h3>

                            <div class="tours-product-description">
                                <?php echo wp_kses_post($product->get_description()); ?>
                            </div>

                            <?php if (!empty($starting_day)): ?>
                            <div class="tours-product-dates">
                                <i class="far fa-calendar-alt"></i>
                                <?php
                                // Format dates nicely
                                $formatted_start_date = !empty($starting_day) ? date('jS \of F Y', strtotime($starting_day)) : '';
                                $formatted_end_date = !empty($ending_day) ? date('jS \of F Y', strtotime($ending_day)) : '';

                                if ($tour_type == 'pretour' && !empty($ending_day)): ?>
                                    <span>From <?php echo esc_html($formatted_start_date); ?> to <?php echo esc_html($formatted_end_date); ?></span>
                                <?php else: ?>
                                    <span>Date: <?php echo esc_html($formatted_start_date); ?></span>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($product->is_type('variable')): ?>
                            <div class="tours-product-variants">
                                <i class="fas fa-layer-group mr-1"></i>
                                <span>
                                <?php
                                    $variations = $product->get_available_variations();
                                    $count = count($variations);
                                    echo $count . ' ' . ($count == 1 ? 'option' : 'options') . ' available';
                                ?>
                                </span>
                            </div>
                            <?php endif; ?>

                            <div class="tours-product-actions">
                                <div class="tours-product-price">
                                    <?php if ($product->is_on_sale()): ?>
                                    <div class="tours-price-sale">
                                        <span class="tours-price-new"><?php echo wp_kses_post(wc_price($product->get_sale_price())); ?></span>
                                        <span class="tours-price-old"><?php echo wp_kses_post(wc_price($product->get_regular_price())); ?></span>
                                    </div>
                                    <?php else: ?>
                                    <span class="tours-price-regular"><?php echo wp_kses_post($product->get_price_html()); ?></span>
                                    <?php endif; ?>
                                </div>

                                <?php if ($product->is_in_stock()): ?>
                                    <?php if ($product->is_type('variable')): ?>
                                    <button type="button" class="tours-btn tours-btn-primary show-variations-btn" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                                        <i class="fas fa-ticket-alt"></i>
                                        <span>Book Now</span>
                                    </button>
                                    <?php else: ?>
                                    <button type="button" class="tours-btn tours-btn-primary show-quantity-btn" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                                        <i class="fas fa-ticket-alt"></i>
                                        <span>Book Now</span>
                                    </button>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <button class="tours-btn tours-btn-disabled" disabled>
                                        <i class="fas fa-times-circle"></i>
                                        <span>Sold Out</span>
                                    </button>
                                <?php endif; ?>
                            </div>

                            <!-- Variation/Quantity Card -->
                            <?php if ($product->is_type('variable')): ?>
                            <!-- Variable Product Card -->
                            <div class="tours-variation-card">
                                <div class="tours-variation-header">
                                    <h4>Select Options</h4>
                                    <button type="button" class="close-variation-card">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <div class="tours-variation-options">
                                    <?php
                                    $variation_attributes = $product->get_variation_attributes();
                                    foreach ($variation_attributes as $attribute_name => $attribute_values) {
                                        $attribute_label = wc_attribute_label($attribute_name);
                                        echo '<div class="tours-variation-option">';
                                        echo '<label>' . esc_html($attribute_label) . '</label>';
                                        echo '<select class="variation-dropdown" data-attribute="' . esc_attr(sanitize_title($attribute_name)) . '">';
                                        echo '<option value="">Choose ' . esc_html($attribute_label) . '</option>';

                                        foreach ($attribute_values as $attribute_value) {
                                            echo '<option value="' . esc_attr($attribute_value) . '">' . esc_html($attribute_value) . '</option>';
                                        }

                                        echo '</select>';
                                        echo '</div>';
                                    }
                                    ?>

                                    <div class="tours-variation-option">
                                        <label>Quantity</label>
                                        <div class="tours-quantity">
                                            <button type="button" class="quantity-minus">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" value="1" min="1" class="quantity-input-field">
                                            <button type="button" class="quantity-plus">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="tours-variation-actions">
                                    <button type="button" class="tours-btn tours-btn-primary add-variable-to-cart-btn" data-product-id="<?php echo esc_attr($product->get_id()); ?>" style="width: 100%; color: #fff;">
                                        <i class="fas fa-ticket-alt" style="color: #fff;"></i>
                                        <span>Confirm</span>
                                    </button>
                                </div>
                            </div>
                            <?php else: ?>
                            <!-- Simple Product Card -->
                            <div class="tours-quantity-card">
                                <div class="tours-quantity-header">
                                    <h4>Select Quantity</h4>
                                    <button type="button" class="close-quantity-card">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <div class="tours-quantity-options">
                                    <div class="tours-quantity">
                                        <button type="button" class="quantity-minus">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" value="1" min="1" class="quantity-input-field">
                                        <button type="button" class="quantity-plus">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="tours-quantity-actions">
                                    <button type="button" class="tours-btn tours-btn-primary add-to-cart-btn" data-product-id="<?php echo esc_attr($product->get_id()); ?>" style="width: 100%; color: #fff;">
                                        <i class="fas fa-ticket-alt" style="color: #fff;"></i>
                                        <span>Confirm</span>
                                    </button>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>


</div>

<!-- Tours Add to Cart Confirmation Modal -->
<?php include LCI2025_PATH . 'templates/components/tours-add-to-cart-confirm.php'; ?>

<!-- Councilor Warning Modal -->
<?php include LCI2025_PATH . 'templates/components/councilor-warning-modal.php'; ?>

<!-- Scroll fix is now handled by tours-fix.js -->

<script>
// Global function to show the tours confirmation modal
window.showAddToCartConfirmation = function(productName, productImage) {
    if (typeof window.showToursAddToCartConfirmation === 'function') {
        window.showToursAddToCartConfirmation(productName, productImage);
    } else {
        // Try alternative methods
        if (document.getElementById('tours-add-to-cart-confirm')) {
            document.getElementById('tours-add-to-cart-confirm').dispatchEvent(new CustomEvent('tours-show-confirmation', {
                detail: {
                    productName: productName,
                    productImage: productImage
                }
            }));
        }
    }
};

// Set flag for councilor warning
window.shouldShowCouncilorWarning = <?php echo $is_councilor ? 'true' : 'false'; ?>;

jQuery(document).ready(function($) {
    // Show councilor warning if needed
    if (window.shouldShowCouncilorWarning) {
        // Dispatch event to show the warning
        window.dispatchEvent(new CustomEvent('show-councilor-warning'));
    }

    // Scroll fix is now handled by tours-fix.js

    // Fix for multiple modal backdrops - more aggressive approach
    function cleanupModalBackdrops() {
        // Remove duplicate modal backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        if (backdrops.length > 1) {
            for (let i = 1; i < backdrops.length; i++) {
                backdrops[i].remove();
            }
        }

        // If no modals are visible, remove all backdrops
        if (!document.querySelector('.modal.show')) {
            backdrops.forEach(backdrop => {
                backdrop.remove();
            });

            // Also remove modal-open class from body
            document.body.classList.remove('modal-open');
        }
    }

    // Periodically check and clean up backdrops
    setInterval(cleanupModalBackdrops, 500);

    // Initial cleanup
    cleanupModalBackdrops();

    // Check for products already in cart
    checkProductsInCart();

    // Prevent mini cart from auto-opening on page load
    const miniCartContainer = document.getElementById('mini-cart-container');
    if (miniCartContainer && miniCartContainer.__x) {
        try {
            const data = miniCartContainer.__x.getUnobservedData();
            data.autoOpenCart = true; // Set to true to prevent auto-opening
        } catch (error) {
            // Console error removed
        }
    }

    // Disable variation and quantity cards - direct add to cart instead
    // Override the default behavior to prevent variation cards from showing

    // Quantity plus
    $('.quantity-plus').on('click', function() {
        var input = $(this).siblings('.quantity-input-field');
        var value = parseInt(input.val());
        input.val(value + 1);
    });

    // Quantity minus
    $('.quantity-minus').on('click', function() {
        var input = $(this).siblings('.quantity-input-field');
        var value = parseInt(input.val());
        if (value > 1) {
            input.val(value - 1);
        }
    });

    // Direct add to cart for tours
    $('.tours-product-card .show-variations-btn, .tours-product-card .show-quantity-btn').on('click', function() {
        var productId = $(this).data('product-id');
        // Add to cart with default quantity of 1
        addToCart(productId, 1);
    });

    // Function to add product to cart
    function addToCart(productId, quantity) {
        // Get product name and image for confirmation modal
        var productCard = $('.tours-product-card').has('[data-product-id="' + productId + '"]');
        var productName = productCard.find('.tours-product-title').text();
        var productImage = productCard.find('.tours-product-image img').attr('src');

        // Show loading state
        var addButton = $('.tours-product-card [data-product-id="' + productId + '"]');
        var originalText = addButton.html();
        addButton.html('<i class="fas fa-spinner fa-spin"></i> Adding...');
        addButton.prop('disabled', true);

        // Prepare data - simplified to always use quantity=1 and no variations
        var data = {
            action: 'lci-dashboard-add-to-cart',
            nonce: lci_ajax.nonce,
            security: lci_ajax.nonce, // Add security parameter as fallback
            product_id: productId,
            quantity: quantity
        };

        // Make AJAX request
        $.ajax({
            url: lci_ajax.ajax_url,
            type: 'POST',
            data: data,
            success: function(response) {
                // Reset button state
                addButton.html(originalText);
                addButton.prop('disabled', false);

                // Close cards
                $('.tours-variation-card, .tours-quantity-card').removeClass('active');

                if (response.success) {
                    // Show tours confirmation modal - use multiple approaches to ensure it works

                    // Try the global function first
                    if (typeof window.showToursAddToCartConfirmation === 'function') {
                        window.showToursAddToCartConfirmation(productName, productImage);
                    } else if (typeof window.showAddToCartConfirmation === 'function') {
                        window.showAddToCartConfirmation(productName, productImage);
                    } else {
                        // Try dispatching events as fallback
                        window.dispatchEvent(new CustomEvent('tours-show-confirmation-pending', {
                            detail: {
                                productName: productName,
                                productImage: productImage
                            }
                        }));

                        // Also try the element event
                        const confirmEl = document.getElementById('tours-add-to-cart-confirm');
                        if (confirmEl) {
                            confirmEl.dispatchEvent(new CustomEvent('tours-show-confirmation', {
                                detail: {
                                    productName: productName,
                                    productImage: productImage
                                }
                            }));
                        }
                    }

                    // Update mini cart
                    updateMiniCart(response.data.cart_count, response.data.cart_total);

                    // Replace the price and button with "Added to your cart" message
                    var productActions = productCard.find('.tours-product-actions');
                    productActions.html('<div class="tours-added-to-cart"><i class="fas fa-check-circle"></i> Added to your cart</div>');

                    // Add a class to the product card to mark it as added to cart
                    productCard.addClass('product-in-cart');
                } else {
                    alert(response.data.message || 'Error adding product to cart');
                }
            },
            error: function() {
                // Reset button state
                addButton.html(originalText);
                addButton.prop('disabled', false);

                alert('Error adding product to cart. Please try again.');
            }
        });
    }

    // Function to update mini cart
    function updateMiniCart(count, total) {
        var miniCartButton = $('#mini-cart-button');
        var countBadge = miniCartButton.find('.badge');
        var totalText = miniCartButton.find('.mini-cart-button-text');

        // Update count
        countBadge.text(count);
        countBadge.toggle(count > 0);

        // Update total
        if (totalText.length) {
            totalText.html(total);
        }

        // Update mini cart items
        loadMiniCartItems({
            cart_count: count,
            cart_total: total
        });
    }

    // Function to load mini cart items via AJAX
    function loadMiniCartItems(cartData) {
        // Dispatch event to update Alpine.js mini cart component
        window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
            detail: cartData || {}
        }));
    }

    // Function to check if products are already in the cart
    function checkProductsInCart() {
        // Make AJAX request to get cart contents
        $.ajax({
            url: lci_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'lci_get_cart_contents',
                nonce: lci_ajax.nonce,
                security: lci_ajax.nonce // Add security parameter as fallback
            },
            success: function(response) {
                if (response.success && response.data && response.data.cart_items) {
                    // Get product IDs in cart
                    var productsInCart = response.data.cart_items.map(function(item) {
                        return item.product_id.toString();
                    });

                    // Console log removed

                    // Loop through product cards and update UI for products in cart
                    $('.tours-product-card').each(function() {
                        var card = $(this);
                        var productBtn = card.find('[data-product-id]');

                        if (productBtn.length) {
                            var productId = productBtn.data('product-id').toString();
                            // Console log removed

                            if (productsInCart.includes(productId)) {
                                // Console log removed
                                // Replace the price and button with "Added to your cart" message
                                var productActions = card.find('.tours-product-actions');
                                productActions.html('<div class="tours-added-to-cart"><i class="fas fa-check-circle"></i> Added to your cart</div>');

                                // Add a class to the product card to mark it as added to cart
                                card.addClass('product-in-cart');
                            }
                        }
                    });
                } else {
                    // Console log removed
                    // Console log removed
                }
            },
            error: function(xhr, status, error) {
                // Console error removed
                // Console log removed
                // Console log removed
            }
        });
    }
});
</script>

<?php
// Debug section for product meta - only visible to administrators
if (current_user_can('administrator')) {
    echo '<div style="text-align: center; margin-top: 50px;">';
    echo '<button id="toggle-debug" style="padding: 10px 20px; background-color: #36b1dc; color: white; border: none; border-radius: 4px; cursor: pointer;">Show Product Meta Data</button>';
    echo '</div>';

    echo '<div id="debug-section" style="display: none; margin-top: 20px; padding: 20px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;">';
    echo '<h3 style="margin-top: 0;">Debug: Product Meta Data (Only visible to administrators)</h3>';

    // Get all products from the tours category (ID 21)
    $args = array(
        'post_type'      => 'product',
        'posts_per_page' => -1,
        'tax_query'      => array(
            array(
                'taxonomy' => 'product_cat',
                'field'    => 'term_id',
                'terms'    => 21, // Tours category ID
            ),
        ),
    );

    $products = wc_get_products($args);

    if (!empty($products)) {
        foreach ($products as $product) {
            echo '<div style="margin-bottom: 30px; padding: 15px; background-color: #fff; border: 1px solid #eee; border-radius: 4px;">';
            echo '<h4 style="margin-top: 0; color: #36b1dc;">' . esc_html($product->get_name()) . ' (ID: ' . $product->get_id() . ')</h4>';

            // Product details
            echo '<div style="margin-bottom: 15px;">';
            echo '<p><strong>SKU:</strong> ' . $product->get_sku() . '</p>';
            echo '<p><strong>Price:</strong> ' . $product->get_price_html() . '</p>';
            echo '<p><strong>Type:</strong> ' . $product->get_type() . '</p>';
            echo '<p><strong>Status:</strong> ' . $product->get_status() . '</p>';
            echo '</div>';

            // Important Tour Meta Fields
            $starting_day = get_post_meta($product->get_id(), 'starting_day', true);
            $ending_day = get_post_meta($product->get_id(), 'ending_day', true);
            $tour_type = get_post_meta($product->get_id(), 'tour_type', true);
            $presentation_link = get_post_meta($product->get_id(), 'presentation_link', true);

            // Try alternative field names if the first attempt returns empty
            if (empty($starting_day)) $starting_day = get_post_meta($product->get_id(), 'start_date', true);
            if (empty($starting_day)) $starting_day = get_post_meta($product->get_id(), 'tour_start_date', true);
            if (empty($starting_day)) $starting_day = get_post_meta($product->get_id(), 'tour_starting_date', true);

            if (empty($ending_day)) $ending_day = get_post_meta($product->get_id(), 'end_date', true);
            if (empty($ending_day)) $ending_day = get_post_meta($product->get_id(), 'tour_end_date', true);
            if (empty($ending_day)) $ending_day = get_post_meta($product->get_id(), 'tour_ending_date', true);

            if (empty($tour_type)) $tour_type = get_post_meta($product->get_id(), 'type', true);
            if (empty($tour_type)) $tour_type = get_post_meta($product->get_id(), 'experience_type', true);

            if (empty($presentation_link)) $presentation_link = get_post_meta($product->get_id(), 'presentation_url', true);
            if (empty($presentation_link)) $presentation_link = get_post_meta($product->get_id(), 'tour_presentation', true);
            if (empty($presentation_link)) $presentation_link = get_post_meta($product->get_id(), 'tour_link', true);

            echo '<div style="margin-bottom: 15px; padding: 15px; background-color: #f0f8ff; border-left: 4px solid #36b1dc; border-radius: 4px;">';
            echo '<h5 style="margin-top: 0; margin-bottom: 10px; color: #36b1dc;">Important Tour Meta Fields:</h5>';
            echo '<p><strong>Starting Day:</strong> ' . (empty($starting_day) ? 'Not found' : esc_html($starting_day)) . '</p>';
            echo '<p><strong>Ending Day:</strong> ' . (empty($ending_day) ? 'Not found' : esc_html($ending_day)) . '</p>';
            echo '<p><strong>Tour Type:</strong> ' . (empty($tour_type) ? 'Not found' : esc_html($tour_type)) . '</p>';
            echo '<p><strong>Presentation Link:</strong> ' . (empty($presentation_link) ? 'Not found' : '<a href="' . esc_url($presentation_link) . '" target="_blank">' . esc_html($presentation_link) . '</a>') . '</p>';
            echo '</div>';

            // Product categories
            $categories = get_the_terms($product->get_id(), 'product_cat');
            if (!empty($categories)) {
                echo '<div style="margin-bottom: 15px;">';
                echo '<h5 style="margin-top: 0; margin-bottom: 10px;">Categories:</h5>';
                echo '<ul style="margin: 0; padding-left: 20px;">';
                foreach ($categories as $category) {
                    echo '<li>' . $category->name . ' (ID: ' . $category->term_id . ')</li>';
                }
                echo '</ul>';
                echo '</div>';
            }

            // Product attributes
            $attributes = $product->get_attributes();
            if (!empty($attributes)) {
                echo '<div style="margin-bottom: 15px;">';
                echo '<h5 style="margin-top: 0; margin-bottom: 10px;">Attributes:</h5>';
                echo '<table style="width: 100%; border-collapse: collapse;">';
                echo '<tr style="background-color: #f2f2f2;">';
                echo '<th style="text-align: left; padding: 8px; border: 1px solid #ddd;">Name</th>';
                echo '<th style="text-align: left; padding: 8px; border: 1px solid #ddd;">Values</th>';
                echo '</tr>';

                foreach ($attributes as $attribute) {
                    echo '<tr>';
                    echo '<td style="padding: 8px; border: 1px solid #ddd;">' . wc_attribute_label($attribute->get_name()) . '</td>';
                    echo '<td style="padding: 8px; border: 1px solid #ddd;">';

                    if ($attribute->is_taxonomy()) {
                        $values = wc_get_product_terms($product->get_id(), $attribute->get_name(), array('fields' => 'names'));
                        echo implode(', ', $values);
                    } else {
                        echo implode(', ', $attribute->get_options());
                    }

                    echo '</td>';
                    echo '</tr>';
                }

                echo '</table>';
                echo '</div>';
            }

            // Search for potential date and tour type fields
            echo '<div style="margin-bottom: 15px;">';
            echo '<h5 style="margin-top: 0; margin-bottom: 10px;">Potential Date and Tour Fields:</h5>';
            echo '<table style="width: 100%; border-collapse: collapse;">';
            echo '<tr style="background-color: #f2f2f2;">';
            echo '<th style="text-align: left; padding: 8px; border: 1px solid #ddd;">Meta Key</th>';
            echo '<th style="text-align: left; padding: 8px; border: 1px solid #ddd;">Meta Value</th>';
            echo '</tr>';

            // Get all meta data for this product
            $meta_data = get_post_meta($product->get_id());
            $date_keywords = array('date', 'day', 'start', 'end', 'beginning', 'ending', 'time');
            $tour_keywords = array('tour', 'type', 'experience', 'presentation', 'link', 'url');
            $found_matches = false;

            foreach ($meta_data as $key => $values) {
                $is_date_field = false;
                $is_tour_field = false;

                // Check if the key contains any of our keywords
                foreach ($date_keywords as $keyword) {
                    if (stripos($key, $keyword) !== false) {
                        $is_date_field = true;
                        break;
                    }
                }

                foreach ($tour_keywords as $keyword) {
                    if (stripos($key, $keyword) !== false) {
                        $is_tour_field = true;
                        break;
                    }
                }

                // If this is a potential date or tour field, display it
                if ($is_date_field || $is_tour_field) {
                    $found_matches = true;
                    echo '<tr style="' . ($is_date_field ? 'background-color: #f0fff0;' : 'background-color: #fff0f0;') . '">';
                    echo '<td style="padding: 8px; border: 1px solid #ddd;">' . esc_html($key) . '</td>';
                    echo '<td style="padding: 8px; border: 1px solid #ddd;">';

                    if (is_array($values)) {
                        foreach ($values as $value) {
                            // Try to unserialize if it's serialized
                            $unserialized = maybe_unserialize($value);

                            if (is_array($unserialized) || is_object($unserialized)) {
                                echo '<pre>' . print_r($unserialized, true) . '</pre>';
                            } else {
                                echo esc_html($value) . '<br>';
                            }
                        }
                    }

                    echo '</td></tr>';
                }
            }

            if (!$found_matches) {
                echo '<tr><td colspan="2" style="padding: 8px; border: 1px solid #ddd;">No potential date or tour fields found.</td></tr>';
            }

            echo '</table>';
            echo '</div>';

            // Get all meta data for this product
            if (!empty($meta_data)) {
                echo '<h5 style="margin-top: 0; margin-bottom: 10px;">All Meta Data:</h5>';
                echo '<table style="width: 100%; border-collapse: collapse;">';
                echo '<tr style="background-color: #f2f2f2;">';
                echo '<th style="text-align: left; padding: 8px; border: 1px solid #ddd;">Meta Key</th>';
                echo '<th style="text-align: left; padding: 8px; border: 1px solid #ddd;">Meta Value</th>';
                echo '</tr>';

                foreach ($meta_data as $key => $values) {
                    // Skip internal WordPress meta
                    if (substr($key, 0, 1) === '_' && $key !== '_price' && $key !== '_regular_price' && $key !== '_sale_price') {
                        continue;
                    }

                    echo '<tr style="border: 1px solid #ddd;">';
                    echo '<td style="padding: 8px; border: 1px solid #ddd;">' . esc_html($key) . '</td>';
                    echo '<td style="padding: 8px; border: 1px solid #ddd;">';

                    if (is_array($values)) {
                        foreach ($values as $value) {
                            // Try to unserialize if it's serialized
                            $unserialized = maybe_unserialize($value);

                            if (is_array($unserialized) || is_object($unserialized)) {
                                echo '<pre>' . print_r($unserialized, true) . '</pre>';
                            } else {
                                echo esc_html($value) . '<br>';
                            }
                        }
                    }

                    echo '</td></tr>';
                }

                echo '</table>';
            } else {
                echo '<p>No meta data found for this product.</p>';
            }

            echo '</div>';
        }
    } else {
        echo '<p>No products found in the Tours category.</p>';
    }

    echo '</div>';

    // Add JavaScript for toggle button
    echo '<script>
        jQuery(document).ready(function($) {
            $("#toggle-debug").on("click", function() {
                var debugSection = $("#debug-section");
                var button = $(this);

                if (debugSection.is(":visible")) {
                    debugSection.hide();
                    button.text("Show Product Meta Data");
                } else {
                    debugSection.show();
                    button.text("Hide Product Meta Data");
                }
            });
        });
    </script>';
}
?>
