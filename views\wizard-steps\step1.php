<?php
// Step 1: Initial Question
// This step asks users about their accommodation preferences based on whether they have Main Pretour

// For users with Main Pretour (ID 743)
if ($wizard['has_main_pretour']) {
    ?>
    <h3 class="text-center mb-4">Accommodation for Main Pretour</h3>
    <div class="info-box">
        <i class="fas fa-info-circle me-2"></i>
        <span>Will you arrive early in Bucharest and need accommodation before the tour starts?</span>
    </div>

    <div class="option-cards">
        <label class="option-card <?php echo $wizard['bucharest']['selected'] === 'yes' ? 'selected' : ''; ?>">
            <input type="radio" name="bucharest_selected" value="yes" style="display: none;" <?php checked($wizard['bucharest']['selected'], 'yes'); ?>>
            <div class="option-card-image">
                <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2025/04/23123928/bucharest.jpg" alt="Bucharest Accommodation">
            </div>
            <div class="option-card-content">
                <h4 class="option-card-title">Yes, I need a place to stay in Bucharest</h4>
                <p class="option-card-description">Before the Main Pretour starts</p>
            </div>
        </label>

        <label class="option-card <?php echo $wizard['bucharest']['selected'] === 'no' ? 'selected' : ''; ?>">
            <input type="radio" name="bucharest_selected" value="no" style="display: none;" <?php checked($wizard['bucharest']['selected'], 'no'); ?>>
            <div class="option-card-image">
                <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2025/04/23123924/brasov.jpg" alt="Brasov Accommodation">
            </div>
            <div class="option-card-content">
                <h4 class="option-card-title">No, I need accommodation in Brasov</h4>
                <p class="option-card-description">I only need accommodation in Brasov</p>
            </div>
        </label>
    </div>

    <script>
    // Simple script to handle option card selection
    document.addEventListener('DOMContentLoaded', function() {
        const optionCards = document.querySelectorAll('.option-card');
        optionCards.forEach(card => {
            card.addEventListener('click', function() {
                // Find the radio input inside this card and check it
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;

                    // Remove selected class from all cards
                    optionCards.forEach(c => c.classList.remove('selected'));

                    // Add selected class to this card
                    this.classList.add('selected');

                    // Don't auto-submit - let the user click the Next button
                    // This gives them time to review their selection
                }
            });
        });
    });
    </script>
    <?php
} else {
    // For users without Main Pretour
    ?>
    <h3 class="text-center mb-4">Do you need accommodation in Brasov?</h3>
    <div class="info-box">
        <i class="fas fa-info-circle me-2"></i>
        <span>Please select if you need accommodation in Brasov. If you select "Yes", you'll immediately see options for pre-event, during event, or post-event accommodation.</span>
    </div>

    <div class="option-cards">
        <label class="option-card <?php echo $wizard['brasov']['selected'] === 'yes' ? 'selected' : ''; ?>">
            <input type="radio" name="brasov_selected" value="yes" style="display: none;" <?php checked($wizard['brasov']['selected'], 'yes'); ?>>
            <div class="option-card-image">
                <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121213/brasov-1.jpg" alt="Brasov Accommodation">
            </div>
            <div class="option-card-content">
                <h4 class="option-card-title">Yes, I need accommodation in Brasov</h4>
                <p class="option-card-description">Show me accommodation options for pre-event, during event, or post-event</p>
            </div>
        </label>

        <label class="option-card <?php echo $wizard['brasov']['selected'] === 'no' ? 'selected' : ''; ?>">
            <input type="radio" name="brasov_selected" value="no" style="display: none;" <?php checked($wizard['brasov']['selected'], 'no'); ?>>
            <div class="option-card-image">
                <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp" alt="No Accommodation">
            </div>
            <div class="option-card-content">
                <h4 class="option-card-title">No, I don't need accommodation</h4>
                <p class="option-card-description">I have my own accommodation</p>
            </div>
        </label>
    </div>

    <script>
    // Simple script to handle option card selection
    document.addEventListener('DOMContentLoaded', function() {
        const optionCards = document.querySelectorAll('.option-card');
        optionCards.forEach(card => {
            card.addEventListener('click', function() {
                // Find the radio input inside this card and check it
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;

                    // Remove selected class from all cards
                    optionCards.forEach(c => c.classList.remove('selected'));

                    // Add selected class to this card
                    this.classList.add('selected');

                    // If they select "No", show a Complete button
                    if (radio.value === 'no') {
                        // Show a Complete button instead of auto-submitting
                        const completeBtn = document.createElement('button');
                        completeBtn.type = 'submit';
                        completeBtn.name = 'wizard_action';
                        completeBtn.value = 'complete';
                        completeBtn.className = 'wizard-btn wizard-btn-success mt-4';
                        completeBtn.innerHTML = '<i class="fas fa-check me-2"></i> Complete Wizard';

                        // Remove any existing complete button
                        const existingBtn = document.getElementById('complete-wizard-btn');
                        if (existingBtn) {
                            existingBtn.remove();
                        }

                        // Add the button to the page
                        const container = document.createElement('div');
                        container.id = 'complete-wizard-btn';
                        container.className = 'text-center mt-4';
                        container.appendChild(completeBtn);

                        document.querySelector('.option-cards').after(container);
                    } else if (radio.value === 'yes') {
                        // Don't auto-submit - let the user click the Next button
                        // This gives them time to review their selection
                    } else {
                        // Remove the complete button if it exists
                        const existingBtn = document.getElementById('complete-wizard-btn');
                        if (existingBtn) {
                            existingBtn.remove();
                        }
                    }
                }
            });
        });

        // Add a special handler for the Next button
        const nextButton = document.querySelector('button[name="wizard_action"][value="next"]');
        if (nextButton) {
            nextButton.addEventListener('click', function(e) {
                // Check if "Yes" is selected
                const yesRadio = document.querySelector('input[name="brasov_selected"][value="yes"]');
                if (yesRadio && yesRadio.checked) {
                    e.preventDefault(); // Prevent default form submission

                    // Create a form to submit with brasov_selected=yes
                    const form = document.createElement('form');
                    form.method = 'post';
                    form.action = window.location.pathname + '?tab=accommodation-wizard-php';

                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'brasov_selected';
                    input.value = 'yes';

                    form.appendChild(input);
                    document.body.appendChild(form);
                    form.submit();
                }
                // If "No" is selected, let the normal form submission handle it
            });
        }
    });
    </script>
    <?php
}
?>
