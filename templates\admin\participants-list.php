<div class="wrap lci-admin-wrap">
    <div x-data="participantsTable">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">Participants</h1>

            <div class="flex space-x-2">
                <button @click="syncAll" class="btn btn-primary flex items-center" :class="{'opacity-50 cursor-not-allowed': isSyncing}">
                    <template x-if="!isSyncing">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </template>
                    <template x-if="isSyncing">
                        <svg class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </template>
                    <span x-text="isSyncing ? 'Syncing...' : 'Sync All'"></span>
                </button>
            </div>
        </div>

        <!-- Search and filters -->
        <div class="bg-white rounded-xl shadow-neumorph p-4 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="relative">
                    <input
                        type="text"
                        x-model="searchQuery"
                        @input="debouncedSearch"
                        placeholder="Search participants..."
                        class="form-input pl-10 w-full md:w-80"
                    >
                    <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                </div>

                <div class="flex flex-wrap items-center space-x-2">
                    <!-- Registration Type Filter -->
                    <div class="flex items-center space-x-2 mr-4">
                        <span class="text-sm text-gray-600">Type:</span>
                        <select
                            x-model="regTypeFilter"
                            @change="filterParticipants()"
                            class="form-input py-1 pl-2 pr-8"
                        >
                            <option value="">All</option>
                            <template x-for="type in availableRegTypes" :key="type">
                                <option :value="type" x-text="type"></option>
                            </template>
                        </select>
                    </div>

                    <!-- Payment Status Filter -->
                    <div class="flex items-center space-x-2 mr-4">
                        <span class="text-sm text-gray-600">Status:</span>
                        <select
                            x-model="paymentStatusFilter"
                            @change="filterParticipants()"
                            class="form-input py-1 pl-2 pr-8"
                        >
                            <option value="">All</option>
                            <template x-for="status in availablePaymentStatuses" :key="status">
                                <option :value="status" x-text="status"></option>
                            </template>
                        </select>
                    </div>

                    <!-- Items per page -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">Show:</span>
                        <select x-model="perPage" @change="updatePagination" class="form-input py-1 pl-2 pr-8">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading state -->
        <div x-show="isLoading" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
            <div class="spinner"></div>
            <span class="loading-text">Loading participants...</span>
        </div>

        <!-- Participants table -->
        <div x-show="!isLoading" class="bg-white rounded-xl shadow-neumorph overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="text-left bg-gray-50">
                            <th @click="sort('unique_reg_id')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    LCI ID
                                    <svg x-show="sortField === 'unique_reg_id'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('original_reg_id')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Original ID
                                    <svg x-show="sortField === 'original_reg_id'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('last_name')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Name
                                    <svg x-show="sortField === 'last_name'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('email')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Email
                                    <svg x-show="sortField === 'email'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('country')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Country
                                    <svg x-show="sortField === 'country'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('order_date')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Order Date
                                    <svg x-show="sortField === 'order_date'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('registration_type')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Registration Type
                                    <svg x-show="sortField === 'registration_type'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('payment_status')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Payment Status
                                    <svg x-show="sortField === 'payment_status'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th @click="sort('badge_status')" class="p-4 font-medium text-gray-600 cursor-pointer hover:bg-gray-100">
                                <div class="flex items-center">
                                    Badge Status
                                    <svg x-show="sortField === 'badge_status'" class="w-4 h-4 ml-1" :class="{'rotate-180': !sortAsc}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </th>
                            <th class="p-4 font-medium text-gray-600">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="participant in paginatedParticipants" :key="participant.id">
                            <tr class="border-t border-gray-200 hover:bg-gray-50">
                                <td class="p-4">
                                    <div class="font-medium text-primary" x-text="participant.unique_reg_id"></div>
                                </td>
                                <td class="p-4">
                                    <div class="text-gray-600" x-text="participant.original_reg_id"></div>
                                </td>
                                <td class="p-4">
                                    <div x-text="participant.first_name + ' ' + participant.last_name"></div>
                                </td>
                                <td class="p-4">
                                    <div x-text="participant.email"></div>
                                </td>
                                <td class="p-4">
                                    <div x-text="participant.country || 'N/A'"></div>
                                </td>
                                <td class="p-4">
                                    <div x-text="participant.order_date ? formatDate(participant.order_date) : 'N/A'"></div>
                                </td>
                                <td class="p-4">
                                    <div x-text="participant.registration_type"></div>
                                </td>
                                <td class="p-4">
                                    <span
                                        class="status-badge"
                                        :class="{
                                            'completed': participant.payment_status.toLowerCase() === 'completed',
                                            'processing': participant.payment_status.toLowerCase() === 'processing',
                                            'pending': participant.payment_status.toLowerCase() === 'pending',
                                            'failed': participant.payment_status.toLowerCase() === 'failed'
                                        }"
                                        x-text="participant.payment_status"
                                    ></span>
                                </td>
                                <td class="p-4">
                                    <span
                                        class="status-badge"
                                        :class="{
                                            'completed': participant.badge_status.toLowerCase() === 'picked up',
                                            'pending': participant.badge_status.toLowerCase() === 'pending'
                                        }"
                                        x-text="participant.badge_status"
                                    ></span>
                                </td>
                                <td class="p-4">
                                    <div class="flex space-x-2">
                                        <a :href="'admin.php?page=lci-participant-detail&id=' + participant.id" class="p-1 text-blue-600 rounded hover:bg-blue-100">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                        <button @click="syncParticipant(participant.order_id)" class="p-1 text-green-600 rounded hover:bg-green-100">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </template>

                        <tr x-show="filteredParticipants.length === 0">
                            <td colspan="8" class="p-4 text-center text-gray-500">
                                No participants found matching your search criteria.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="flex items-center justify-between p-4 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    Showing <span x-text="paginatedParticipants.length"></span> of <span x-text="filteredParticipants.length"></span> participants
                </div>
                <div class="flex space-x-1">
                    <button
                        @click="currentPage = Math.max(currentPage - 1, 1)"
                        :disabled="currentPage === 1"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                        class="px-3 py-1 text-gray-600 bg-gray-100 rounded hover:bg-gray-200"
                    >
                        Previous
                    </button>

                    <template x-for="page in Math.min(5, totalPages)" :key="page">
                        <button
                            @click="currentPage = page"
                            :class="{'bg-primary text-white': currentPage === page, 'bg-gray-100 text-gray-600 hover:bg-gray-200': currentPage !== page}"
                            class="px-3 py-1 rounded"
                            x-text="page"
                        ></button>
                    </template>

                    <template x-if="totalPages > 5 && currentPage < totalPages - 2">
                        <span class="px-3 py-1">...</span>
                    </template>

                    <template x-if="totalPages > 5 && currentPage < totalPages - 1">
                        <button
                            @click="currentPage = totalPages"
                            :class="{'bg-primary text-white': currentPage === totalPages, 'bg-gray-100 text-gray-600 hover:bg-gray-200': currentPage !== totalPages}"
                            class="px-3 py-1 rounded"
                            x-text="totalPages"
                        ></button>
                    </template>

                    <button
                        @click="currentPage = Math.min(currentPage + 1, totalPages)"
                        :disabled="currentPage === totalPages"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
                        class="px-3 py-1 text-gray-600 bg-gray-100 rounded hover:bg-gray-200"
                    >
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
