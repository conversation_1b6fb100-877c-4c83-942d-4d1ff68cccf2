/**
 * LCI Travel Widgets Styles
 * Modern 2025 UX/UI design for travel booking widgets
 */

/* Main Travel Widget Styles */
.lci-travel-widget {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    color: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.lci-travel-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.lci-travel-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.lci-travel-widget.flight-widget {
    background: linear-gradient(135deg, #369fcd 0%, #2563eb 100%);
}

/* Widget Title */
.widget-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
    z-index: 1;
}

/* Travel Search Form */
.travel-search-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    color: #374151;
    position: relative;
    z-index: 1;
}

.travel-search-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.travel-search-form .form-group {
    display: flex;
    flex-direction: column;
}

.travel-search-form label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.travel-search-form .form-control {
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.travel-search-form select.form-control {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.travel-search-form .form-control:focus {
    outline: none;
    border-color: #369fcd;
    box-shadow: 0 0 0 3px rgba(54, 159, 205, 0.1);
}

.travel-search-form .btn {
    padding: 0.875rem 2rem;
    background: linear-gradient(135deg, #369fcd 0%, #2563eb 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 1rem;
    width: 100%;
}

.travel-search-form .btn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
}

.travel-search-form .btn:active {
    transform: translateY(0);
}

/* Travel Results */
.travel-results {
    margin-top: 1.5rem;
    min-height: 100px;
    position: relative;
    z-index: 1;
}

.travel-results-list {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    color: #374151;
}

.travel-results-list h4 {
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.125rem;
}

.travel-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    background: white;
    transition: all 0.3s ease;
}

.travel-result-item:hover {
    border-color: #369fcd;
    box-shadow: 0 2px 8px rgba(54, 159, 205, 0.1);
    transform: translateX(5px);
}

.travel-result-item:last-child {
    margin-bottom: 0;
}

.result-info {
    flex: 1;
}

.result-info .airline {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.result-info .route {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.result-info .price {
    font-weight: 700;
    color: #369fcd;
    font-size: 1.125rem;
}

.result-actions .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    background: linear-gradient(135deg, #369fcd 0%, #2563eb 100%);
    color: white;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.result-actions .btn-sm:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(37, 99, 235, 0.3);
}

/* Loading States */
.travel-loading {
    text-align: center;
    padding: 2rem;
    color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error States */
.travel-error {
    padding: 1rem;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    color: #dc2626;
    text-align: center;
}

/* Airport Suggestions */
.airport-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.airport-suggestion {
    padding: 0.875rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    line-height: 1.4;
}

.airport-suggestion:hover {
    background-color: #369fcd;
    color: white;
    transform: translateX(2px);
}

.airport-suggestion:last-child {
    border-bottom: none;
    border-radius: 0 0 8px 8px;
}

.airport-suggestion:first-child {
    border-radius: 0;
}

/* Loading state for autocomplete */
.airport-suggestions.loading {
    padding: 1rem;
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
}

.airport-suggestions.loading::before {
    content: "🔍 Searching airports...";
}



/* Responsive Design */
@media (max-width: 768px) {
    .lci-travel-widget {
        padding: 1.5rem;
    }

    .travel-search-form .form-row {
        grid-template-columns: 1fr;
    }

    .travel-result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .result-actions {
        width: 100%;
    }

    .result-actions .btn-sm {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .widget-title {
        font-size: 1.25rem;
    }

    .travel-search-form {
        padding: 1rem;
    }

    .travel-results-list {
        padding: 1rem;
    }
}
