<?php
/**
 * Plugin Functions
 *
 * Contains helper functions for the LCI 2025 Dashboard plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// The lci_get_mini_cart_html() function is already defined in mini-cart.php

/**
 * Get add to cart confirmation HTML
 *
 * @return string Add to cart confirmation HTML
 */
function lci_get_add_to_cart_confirm_html() {
    $html = '<div class="add-to-cart-confirm-modal" id="addToCartConfirmModal" style="display: none;">';
    $html .= '<div class="add-to-cart-confirm-content">';
    $html .= '<div class="add-to-cart-confirm-header">';
    $html .= '<h3>Item Added to Cart</h3>';
    $html .= '<button type="button" class="close-modal" onclick="document.getElementById(\'addToCartConfirmModal\').style.display=\'none\';">&times;</button>';
    $html .= '</div>';
    $html .= '<div class="add-to-cart-confirm-body">';
    $html .= '<p>Your item has been added to the cart.</p>';
    $html .= '<div class="add-to-cart-confirm-buttons">';
    $html .= '<a href="' . esc_url(wc_get_cart_url()) . '" class="btn-view-cart">View Cart</a>';
    $html .= '<button type="button" class="btn-continue-shopping" onclick="document.getElementById(\'addToCartConfirmModal\').style.display=\'none\';">Continue Shopping</button>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';

    return $html;
}
