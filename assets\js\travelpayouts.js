/**
 * LCI Travelpayouts Integration JavaScript
 * Handles travel search widgets and affiliate tracking
 */

(function($) {
    'use strict';

    // Travel search functionality
    const TravelSearch = {
        init: function() {
            this.bindEvents();
            this.initDatePickers();
        },

        bindEvents: function() {
            // Flight search
            $(document).on('click', '#search-flights', this.searchFlights.bind(this));

            // Auto-complete for flight origins
            this.initFlightAutocomplete();
        },

        initDatePickers: function() {
            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            $('input[type="date"]').attr('min', today);

            // Set default dates for LCI event
            if (typeof lciTravelpayouts !== 'undefined' && lciTravelpayouts.event_dates) {
                $('#flight-departure, #hotel-checkin').val(lciTravelpayouts.event_dates.start);
                $('#flight-return, #hotel-checkout').val(lciTravelpayouts.event_dates.end);
            }
        },

        initFlightAutocomplete: function() {
            let searchTimeout;

            $('#flight-origin').on('input', function() {
                const input = $(this);
                const rawValue = input.val();

                // Handle undefined/null values
                if (rawValue === undefined || rawValue === null) {
                    $('.airport-suggestions').remove();
                    return;
                }

                const value = String(rawValue).trim();

                // Clear previous timeout
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                if (value.length < 2) {
                    $('.airport-suggestions').remove();
                    return;
                }

                // Debounce API calls
                searchTimeout = setTimeout(() => {
                    this.searchAirports(input, value);
                }, 300);
            }.bind(this));

            // Hide suggestions when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#flight-origin, .airport-suggestions').length) {
                    $('.airport-suggestions').remove();
                }
            });
        },

        searchAirports: function(input, query) {
            // Validate inputs
            if (!input || !input.length || !query || typeof query !== 'string') {
                $('.airport-suggestions').remove();
                return;
            }

            // Check if lciTravelpayouts is available
            if (typeof lciTravelpayouts === 'undefined') {
                console.error('Travelpayouts configuration not loaded');
                $('.airport-suggestions').remove();
                return;
            }

            // Show loading state
            this.showAirportLoading(input);

            // Use Travelpayouts autocomplete API
            $.ajax({
                url: lciTravelpayouts.ajax_url,
                type: 'POST',
                data: {
                    action: 'lci_search_airports',
                    nonce: lciTravelpayouts.nonce,
                    query: query
                },
                success: function(response) {
                    try {
                        if (response && response.success && response.data && response.data.length > 0) {
                            this.showAirportSuggestions(input, response.data);
                        } else {
                            $('.airport-suggestions').remove();
                        }
                    } catch (error) {
                        console.error('Error processing airport search response:', error);
                        $('.airport-suggestions').remove();
                    }
                }.bind(this),
                error: function(_, status, error) {
                    console.error('Airport search AJAX error:', status, error);
                    $('.airport-suggestions').remove();
                }
            });
        },

        showAirportLoading: function(input) {
            $('.airport-suggestions').remove();
            const loading = $('<div class="airport-suggestions loading"></div>');
            input.after(loading);
        },

        showAirportSuggestions: function(input, airports) {
            $('.airport-suggestions').remove();

            const suggestions = $('<div class="airport-suggestions"></div>');

            airports.slice(0, 8).forEach(airport => {
                // Format: "City, Country (CODE) - Airport Name"
                const displayName = `${airport.city_name || airport.name}, ${airport.country_name} (${airport.code})`;
                const airportName = airport.name || airport.airport_name || '';
                const fullDisplay = airportName ? `${displayName} - ${airportName}` : displayName;

                const suggestion = $(`<div class="airport-suggestion" data-code="${airport.code}" data-city="${airport.city_name || airport.name}">${fullDisplay}</div>`);

                suggestion.on('click', function() {
                    input.val(displayName);
                    input.data('airport-code', airport.code);
                    input.data('city-name', airport.city_name || airport.name);
                    suggestions.remove();
                });

                suggestions.append(suggestion);
            });

            input.after(suggestions);
        },

        searchFlights: function(e) {
            e.preventDefault();

            const origin = $('#flight-origin').data('airport-code') || this.extractAirportCode($('#flight-origin').val());
            const destination = $('#flight-destination').val(); // Get selected destination
            const departureDate = $('#flight-departure').val();
            const returnDate = $('#flight-return').val();

            if (!origin || !departureDate) {
                this.showError('#flight-results', 'Please select origin airport and departure date.');
                return;
            }

            this.showLoading('#flight-results');

            $.ajax({
                url: lciTravelpayouts.ajax_url,
                type: 'POST',
                data: {
                    action: 'lci_get_flight_prices',
                    nonce: lciTravelpayouts.nonce,
                    origin: origin,
                    destination: destination,
                    departure_date: departureDate,
                    return_date: returnDate
                },
                success: function(response) {
                    if (response.success) {
                        this.displayFlightResults(response.data);
                    } else {
                        this.showError('#flight-results', response.data.message || 'Error searching flights.');
                    }
                }.bind(this),
                error: function() {
                    this.showError('#flight-results', 'Network error. Please try again.');
                }.bind(this)
            });
        },



        displayFlightResults: function(data) {
            const container = $('#flight-results');

            if (!data || !data.data || data.data.length === 0) {
                this.showNoFlightsMessage(container);
                return;
            }

            let html = '<div class="travel-results-list">';
            html += '<h4>Available Flights</h4>';

            // Debug info (remove in production)
            if (data.debug) {
                console.log('Flight search debug:', data.debug);
            }

            data.data.slice(0, 8).forEach(flight => {
                const price = flight.price || flight.value;
                const airline = flight.airline || 'Various Airlines';
                const departDate = flight.depart_date || flight.departure_at;
                const returnDate = flight.return_date || flight.return_at;
                const bookingUrl = this.generateBookingUrl('flight', flight);

                html += `
                    <div class="travel-result-item">
                        <div class="result-info">
                            <div class="airline">${airline}</div>
                            <div class="route">${flight.origin} → ${flight.destination}</div>
                            ${departDate ? `<div class="date">Departure: ${this.formatDate(departDate)}</div>` : ''}
                            ${returnDate ? `<div class="date">Return: ${this.formatDate(returnDate)}</div>` : ''}
                            <div class="price">€${price}</div>
                        </div>
                        <div class="result-actions">
                            <a href="${bookingUrl}" target="_blank" class="btn btn-primary btn-sm" rel="nofollow">
                                Book Now
                            </a>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            container.html(html);
        },

        showNoFlightsMessage: function(container) {
            const origin = $('#flight-origin').val();
            const destination = $('#flight-destination option:selected').text();

            container.html(`
                <div class="travel-no-results">
                    <div class="no-results-icon">✈️</div>
                    <h4>No Direct Flights Found</h4>
                    <p>We couldn't find direct flights from <strong>${origin}</strong> to <strong>${destination}</strong> for your selected dates.</p>
                    <div class="suggestions">
                        <h5>Try these alternatives:</h5>
                        <ul>
                            <li>🗓️ <strong>Flexible dates:</strong> Try dates ±3 days around your preferred dates</li>
                            <li>🔄 <strong>Connecting flights:</strong> Consider flights with 1-2 stops</li>
                            <li>🏢 <strong>Nearby airports:</strong> Check flights to other Romanian airports</li>
                            <li>🌐 <strong>External search:</strong> Try searching on airline websites directly</li>
                        </ul>
                        <div class="external-search">
                            <a href="${this.generateExternalSearchUrl()}" target="_blank" class="btn btn-outline-primary">
                                Search on Aviasales
                            </a>
                        </div>
                    </div>
                </div>
            `);
        },

        formatDate: function(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
            });
        },

        generateExternalSearchUrl: function() {
            const origin = $('#flight-origin').data('airport-code') || 'OSL';
            const destination = $('#flight-destination').val() || 'OTP';
            const departDate = $('#flight-departure').val().replace(/-/g, '');
            const returnDate = $('#flight-return').val().replace(/-/g, '');

            let url = `https://www.aviasales.com/search/${origin}${destination}${departDate}`;
            if (returnDate) {
                url += returnDate;
            }

            // Add affiliate parameters
            url += `?marker=${lciTravelpayouts.marker}&utm_source=lci2025&utm_medium=dashboard`;

            return url;
        },



        generateBookingUrl: function(type, item) {
            // Generate affiliate tracking URL for flights only
            let baseUrl = '';

            if (type === 'flight') {
                baseUrl = 'https://www.aviasales.com/search/';
                baseUrl += `${item.origin}${item.destination}${item.depart_date}`;
                if (item.return_date) {
                    baseUrl += item.return_date;
                }
            }

            // Add affiliate parameters
            const params = new URLSearchParams({
                marker: lciTravelpayouts.marker,
                partner_id: lciTravelpayouts.partner_id,
                utm_source: 'lci2025',
                utm_medium: 'dashboard',
                utm_campaign: 'flight_widget'
            });

            return baseUrl + '?' + params.toString();
        },

        extractAirportCode: function(airportString) {
            // Extract 3-letter airport code from string like "London Heathrow (LHR)"
            const match = airportString.match(/\(([A-Z]{3})\)/);
            return match ? match[1] : airportString.substring(0, 3).toUpperCase();
        },

        showLoading: function(container) {
            $(container).html(`
                <div class="travel-loading">
                    <div class="loading-spinner"></div>
                    <p>Searching for best deals...</p>
                </div>
            `);
        },

        showError: function(container, message) {
            $(container).html(`
                <div class="travel-error">
                    <p class="error-message">${message}</p>
                </div>
            `);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Check if we're on a page with flight widgets
        if ($('#flight-origin').length > 0) {
            if (typeof lciTravelpayouts !== 'undefined') {
                TravelSearch.init();
            } else {
                console.warn('Travelpayouts configuration not loaded, but flight widget detected');
                // Try to initialize after a short delay in case scripts are loading
                setTimeout(function() {
                    if (typeof lciTravelpayouts !== 'undefined') {
                        TravelSearch.init();
                    }
                }, 1000);
            }
        }
    });

})(jQuery);
