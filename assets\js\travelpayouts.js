/**
 * LCI Travelpayouts Integration JavaScript
 * Handles travel search widgets and affiliate tracking
 */

(function($) {
    'use strict';

    // Travel search functionality
    const TravelSearch = {
        init: function() {
            this.bindEvents();
            this.initDatePickers();
        },

        bindEvents: function() {
            // Flight search
            $(document).on('click', '#search-flights', this.searchFlights.bind(this));
            
            // Hotel search
            $(document).on('click', '#search-hotels', this.searchHotels.bind(this));
            
            // Auto-complete for flight origins
            this.initFlightAutocomplete();
        },

        initDatePickers: function() {
            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            $('input[type="date"]').attr('min', today);
            
            // Set default dates for LCI event
            if (typeof lciTravelpayouts !== 'undefined' && lciTravelpayouts.event_dates) {
                $('#flight-departure, #hotel-checkin').val(lciTravelpayouts.event_dates.start);
                $('#flight-return, #hotel-checkout').val(lciTravelpayouts.event_dates.end);
            }
        },

        initFlightAutocomplete: function() {
            // Simple autocomplete for major airports
            const majorAirports = [
                { code: 'JFK', name: 'New York (JFK)', city: 'New York' },
                { code: 'LAX', name: 'Los Angeles (LAX)', city: 'Los Angeles' },
                { code: 'LHR', name: 'London Heathrow (LHR)', city: 'London' },
                { code: 'CDG', name: 'Paris Charles de Gaulle (CDG)', city: 'Paris' },
                { code: 'FRA', name: 'Frankfurt (FRA)', city: 'Frankfurt' },
                { code: 'AMS', name: 'Amsterdam (AMS)', city: 'Amsterdam' },
                { code: 'MAD', name: 'Madrid (MAD)', city: 'Madrid' },
                { code: 'FCO', name: 'Rome (FCO)', city: 'Rome' },
                { code: 'MUC', name: 'Munich (MUC)', city: 'Munich' },
                { code: 'ZUR', name: 'Zurich (ZUR)', city: 'Zurich' },
                { code: 'VIE', name: 'Vienna (VIE)', city: 'Vienna' },
                { code: 'BUD', name: 'Budapest (BUD)', city: 'Budapest' },
                { code: 'PRG', name: 'Prague (PRG)', city: 'Prague' },
                { code: 'WAW', name: 'Warsaw (WAW)', city: 'Warsaw' }
            ];

            $('#flight-origin').on('input', function() {
                const input = $(this);
                const value = input.val().toLowerCase();
                
                if (value.length < 2) {
                    $('.airport-suggestions').remove();
                    return;
                }

                const matches = majorAirports.filter(airport => 
                    airport.name.toLowerCase().includes(value) || 
                    airport.city.toLowerCase().includes(value) ||
                    airport.code.toLowerCase().includes(value)
                );

                if (matches.length > 0) {
                    this.showAirportSuggestions(input, matches);
                }
            }.bind(this));
        },

        showAirportSuggestions: function(input, airports) {
            $('.airport-suggestions').remove();
            
            const suggestions = $('<div class="airport-suggestions"></div>');
            
            airports.slice(0, 5).forEach(airport => {
                const suggestion = $(`<div class="airport-suggestion" data-code="${airport.code}">${airport.name}</div>`);
                suggestion.on('click', function() {
                    input.val(airport.name);
                    input.data('airport-code', airport.code);
                    suggestions.remove();
                });
                suggestions.append(suggestion);
            });
            
            input.after(suggestions);
        },

        searchFlights: function(e) {
            e.preventDefault();
            
            const origin = $('#flight-origin').data('airport-code') || this.extractAirportCode($('#flight-origin').val());
            const destination = 'OTP'; // Bucharest Otopeni (closest major airport to Brasov)
            const departureDate = $('#flight-departure').val();
            const returnDate = $('#flight-return').val();
            
            if (!origin || !departureDate) {
                this.showError('#flight-results', 'Please select origin airport and departure date.');
                return;
            }
            
            this.showLoading('#flight-results');
            
            $.ajax({
                url: lciTravelpayouts.ajax_url,
                type: 'POST',
                data: {
                    action: 'lci_get_flight_prices',
                    nonce: lciTravelpayouts.nonce,
                    origin: origin,
                    destination: destination,
                    departure_date: departureDate,
                    return_date: returnDate
                },
                success: function(response) {
                    if (response.success) {
                        this.displayFlightResults(response.data);
                    } else {
                        this.showError('#flight-results', response.data.message || 'Error searching flights.');
                    }
                }.bind(this),
                error: function() {
                    this.showError('#flight-results', 'Network error. Please try again.');
                }.bind(this)
            });
        },

        searchHotels: function(e) {
            e.preventDefault();
            
            const location = $('#hotel-location').val();
            const checkIn = $('#hotel-checkin').val();
            const checkOut = $('#hotel-checkout').val();
            const guests = $('#hotel-guests').val();
            
            if (!checkIn || !checkOut) {
                this.showError('#hotel-results', 'Please select check-in and check-out dates.');
                return;
            }
            
            this.showLoading('#hotel-results');
            
            $.ajax({
                url: lciTravelpayouts.ajax_url,
                type: 'POST',
                data: {
                    action: 'lci_get_hotel_prices',
                    nonce: lciTravelpayouts.nonce,
                    location: location,
                    check_in: checkIn,
                    check_out: checkOut,
                    guests: guests
                },
                success: function(response) {
                    if (response.success) {
                        this.displayHotelResults(response.data);
                    } else {
                        this.showError('#hotel-results', response.data.message || 'Error searching hotels.');
                    }
                }.bind(this),
                error: function() {
                    this.showError('#hotel-results', 'Network error. Please try again.');
                }.bind(this)
            });
        },

        displayFlightResults: function(data) {
            const container = $('#flight-results');
            
            if (!data || !data.data || data.data.length === 0) {
                this.showError('#flight-results', 'No flights found for your search criteria.');
                return;
            }
            
            let html = '<div class="travel-results-list">';
            html += '<h4>Available Flights</h4>';
            
            data.data.slice(0, 5).forEach(flight => {
                const price = flight.price || flight.value;
                const airline = flight.airline || 'Various Airlines';
                const bookingUrl = this.generateBookingUrl('flight', flight);
                
                html += `
                    <div class="travel-result-item">
                        <div class="result-info">
                            <div class="airline">${airline}</div>
                            <div class="route">${flight.origin} → ${flight.destination}</div>
                            <div class="price">€${price}</div>
                        </div>
                        <div class="result-actions">
                            <a href="${bookingUrl}" target="_blank" class="btn btn-primary btn-sm" rel="nofollow">
                                Book Now
                            </a>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.html(html);
        },

        displayHotelResults: function(data) {
            const container = $('#hotel-results');
            
            if (!data || !data.hotels || data.hotels.length === 0) {
                this.showError('#hotel-results', 'No hotels found for your search criteria.');
                return;
            }
            
            let html = '<div class="travel-results-list">';
            html += '<h4>Available Hotels</h4>';
            
            data.hotels.slice(0, 5).forEach(hotel => {
                const price = hotel.price || hotel.priceFrom;
                const name = hotel.name || hotel.hotelName;
                const rating = hotel.stars || hotel.rating || 0;
                const bookingUrl = this.generateBookingUrl('hotel', hotel);
                
                html += `
                    <div class="travel-result-item">
                        <div class="result-info">
                            <div class="hotel-name">${name}</div>
                            <div class="hotel-rating">${'★'.repeat(rating)}</div>
                            <div class="price">€${price}/night</div>
                        </div>
                        <div class="result-actions">
                            <a href="${bookingUrl}" target="_blank" class="btn btn-primary btn-sm" rel="nofollow">
                                Book Now
                            </a>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.html(html);
        },

        generateBookingUrl: function(type, item) {
            // Generate affiliate tracking URL
            let baseUrl = '';
            
            if (type === 'flight') {
                baseUrl = 'https://www.aviasales.com/search/';
                baseUrl += `${item.origin}${item.destination}${item.depart_date}`;
                if (item.return_date) {
                    baseUrl += item.return_date;
                }
            } else if (type === 'hotel') {
                baseUrl = 'https://www.hotellook.com/hotels/';
                baseUrl += encodeURIComponent(item.location || 'brasov-romania');
            }
            
            // Add affiliate parameters
            const params = new URLSearchParams({
                marker: lciTravelpayouts.marker,
                partner_id: lciTravelpayouts.partner_id,
                utm_source: 'lci2025',
                utm_medium: 'dashboard',
                utm_campaign: 'travel_widget'
            });
            
            return baseUrl + '?' + params.toString();
        },

        extractAirportCode: function(airportString) {
            // Extract 3-letter airport code from string like "London Heathrow (LHR)"
            const match = airportString.match(/\(([A-Z]{3})\)/);
            return match ? match[1] : airportString.substring(0, 3).toUpperCase();
        },

        showLoading: function(container) {
            $(container).html(`
                <div class="travel-loading">
                    <div class="loading-spinner"></div>
                    <p>Searching for best deals...</p>
                </div>
            `);
        },

        showError: function(container, message) {
            $(container).html(`
                <div class="travel-error">
                    <p class="error-message">${message}</p>
                </div>
            `);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof lciTravelpayouts !== 'undefined') {
            TravelSearch.init();
        }
    });

})(jQuery);
