<?php
// Step 3: Product Selection
// This step shows accommodation products based on previous selections

// Get the appropriate products based on user selections
$products = [];
$category_id = 0;
$meta_query = [];
$title = '';
$skip_query = false; // Initialize skip_query flag

if ($wizard['has_main_pretour'] && $wizard['bucharest']['selected'] === 'yes') {
    // For Bucharest accommodation, show specific product ID 2151
    $title = 'Bucharest Accommodation Options';

    // Get product directly by ID instead of using category
    $product_obj = wc_get_product(2151);

    // Debug information for administrators
    if (current_user_can('administrator')) {
        echo '<div class="alert alert-info mb-4">';
        echo '<h5><i class="fas fa-bug me-2"></i> Debug Information</h5>';
        echo '<p>Trying to load Bucharest accommodation product ID 2151</p>';
        echo '<p>Product exists: ' . ($product_obj ? 'Yes' : 'No') . '</p>';
        if ($product_obj) {
            echo '<p>Product name: ' . $product_obj->get_name() . '</p>';
            echo '<p>Product purchasable: ' . ($product_obj->is_purchasable() ? 'Yes' : 'No') . '</p>';
        }
        echo '</div>';
    }

    // If product doesn't exist, try to get any product from category 33
    if (!$product_obj) {
        $args = [
            'post_type' => 'product',
            'posts_per_page' => 1,
            'tax_query' => [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => 33
                ]
            ]
        ];

        $query = new WP_Query($args);

        if ($query->have_posts()) {
            $query->the_post();
            $product_obj = wc_get_product(get_the_ID());
            wp_reset_postdata();
        }
    }

    if ($product_obj) {
        // Check if product is in cart
        $in_cart = false;
        if (WC()->cart) {
            foreach (WC()->cart->get_cart() as $cart_item) {
                if ($cart_item['product_id'] == $product_obj->get_id()) {
                    $in_cart = true;
                    break;
                }
            }
        }

        $products[] = [
            'id' => $product_obj->get_id(),
            'name' => $product_obj->get_name(),
            'price' => $product_obj->get_price(),
            'price_html' => $product_obj->get_price_html(),
            'description' => $product_obj->get_short_description(),
            'image' => get_the_post_thumbnail_url($product_obj->get_id(), 'medium') ?: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121212/bucharest-1.jpg',
            'in_cart' => $in_cart
        ];
    } else {
        // Create a fallback product if no product is found
        $products[] = [
            'id' => 2151,
            'name' => 'Bucharest Accommodation',
            'price' => '150',
            'price_html' => '<span class="amount">€150</span> <small>per night</small>',
            'description' => 'Accommodation in Bucharest before the Main Pretour.',
            'image' => 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121212/bucharest-1.jpg',
            'in_cart' => false
        ];
    }

    // Skip the regular product query for Bucharest
    $skip_query = true;
} else {
    // Brasov accommodation
    // Use the category ID from the wizard state if available
    if (isset($wizard['brasov']['category_id'])) {
        $category_id = $wizard['brasov']['category_id'];

        // For now, don't use meta queries for pre/post event to show all products
        // We'll add a note to the UI about the timing
    } else {
        // Fallback to the old switch logic if category_id is not set
        switch ($wizard['brasov']['type']) {
            case 'pre':
                $category_id = 37; // Pre-event accommodation
                break;

            case 'main':
                $category_id = 1; // Main event accommodation
                break;

            case 'post':
                $category_id = 37; // Post-event accommodation
                break;
        }
    }

    // Set the title based on the type
    switch ($wizard['brasov']['type']) {
        case 'pre':
            $title = 'Pre-event Brasov Accommodation';
            break;
        case 'main':
            $title = 'Main Event Brasov Accommodation';
            break;
        case 'post':
            $title = 'Post-event Brasov Accommodation';
            break;
    }
}

// Only query for products if we're not showing a specific product
if (!isset($skip_query) || $skip_query !== true) {
    // Debug information for administrators
    if (current_user_can('administrator')) {
        echo '<div class="alert alert-info mb-4">';
        echo '<h5><i class="fas fa-bug me-2"></i> Debug Information</h5>';
        echo '<p>Category ID: ' . $category_id . '</p>';
        if (!empty($meta_query)) {
            echo '<p>Meta Query: ' . print_r($meta_query, true) . '</p>';
        }
        echo '</div>';
    }

    // First, try to get products with just the category, without meta query
    $category_args = [
        'post_type' => 'product',
        'posts_per_page' => -1,
        'tax_query' => [
            [
                'taxonomy' => 'product_cat',
                'field' => 'term_id',
                'terms' => $category_id
            ]
        ]
    ];

    $category_query = new WP_Query($category_args);
    $products_in_category = [];

    if ($category_query->have_posts()) {
        while ($category_query->have_posts()) {
            $category_query->the_post();
            $product = wc_get_product(get_the_ID());
            $products_in_category[] = [
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'timing' => get_post_meta($product->get_id(), '_timing', true)
            ];
        }
        wp_reset_postdata();
    }

    // Debug information for administrators
    if (current_user_can('administrator')) {
        echo '<div class="alert alert-info mb-4">';
        echo '<h5><i class="fas fa-bug me-2"></i> Products in Category ' . $category_id . '</h5>';
        if (!empty($products_in_category)) {
            echo '<ul>';
            foreach ($products_in_category as $p) {
                echo '<li>ID: ' . $p['id'] . ', Name: ' . $p['name'] . ', Timing: ' . ($p['timing'] ?: 'not set') . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p>No products found in this category.</p>';
        }
        echo '</div>';
    }

    // Now try with the full query including meta query if needed
    $args = [
        'post_type' => 'product',
        'posts_per_page' => -1,
        'tax_query' => [
            [
                'taxonomy' => 'product_cat',
                'field' => 'term_id',
                'terms' => $category_id
            ]
        ]
    ];

    if (!empty($meta_query)) {
        $args['meta_query'] = $meta_query;
    }

    $product_query = new WP_Query($args);

    // If no products found with meta query but products exist in the category,
    // try to get all products from the category and filter them manually
    if (!$product_query->have_posts() && !empty($products_in_category) && !empty($meta_query)) {
        // For pre-event accommodation
        if (isset($meta_query[0]['key']) && $meta_query[0]['key'] === '_timing' && $meta_query[0]['value'] === 'before') {
            // Just show all products from the category
            $args = [
                'post_type' => 'product',
                'posts_per_page' => -1,
                'tax_query' => [
                    [
                        'taxonomy' => 'product_cat',
                        'field' => 'term_id',
                        'terms' => $category_id
                    ]
                ]
            ];
            $product_query = new WP_Query($args);
        }
    }

    if ($product_query->have_posts()) {
        while ($product_query->have_posts()) {
            $product_query->the_post();
            $product = wc_get_product(get_the_ID());

            // Check if product is in cart
            $in_cart = false;
            if (WC()->cart) {
                foreach (WC()->cart->get_cart() as $cart_item) {
                    if ($cart_item['product_id'] == $product->get_id()) {
                        $in_cart = true;
                        break;
                    }
                }
            }

            $products[] = [
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'price' => $product->get_price(),
                'price_html' => $product->get_price_html(),
                'description' => $product->get_short_description(),
                'image' => get_the_post_thumbnail_url(get_the_ID(), 'medium') ?: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp',
                'in_cart' => $in_cart
            ];
        }
        wp_reset_postdata();
    }
}

// If no products found, show a message
if (empty($products)) {
    // We'll handle this with a message in the UI
}

// Store products in session for later use
$wizard['products'] = $products;
?>

<h3 class="text-center mb-4"><?php echo $title; ?></h3>
<div class="info-box">
    <i class="fas fa-info-circle me-2"></i>
    <span>Select an accommodation option that suits your needs</span>
    <?php if (isset($wizard['brasov']['type']) && ($wizard['brasov']['type'] === 'pre')): ?>
        <br><br><strong>Note:</strong> These accommodations are for your stay <strong>before</strong> the main event (before August 21, 2025).
    <?php elseif (isset($wizard['brasov']['type']) && ($wizard['brasov']['type'] === 'post')): ?>
        <br><br><strong>Note:</strong> These accommodations are for your stay <strong>after</strong> the main event (after August 24, 2025).
    <?php endif; ?>
</div>

<?php if (empty($products)) : ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        No accommodation options found for your selected criteria. Please try different options or contact us for assistance.
    </div>
<?php else : ?>
    <div class="product-cards" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; max-width: 1200px; margin: 0 auto;">
        <?php foreach ($products as $product) :
            // Get product meta data
            $product_obj = wc_get_product($product['id']);
            $hotel_website = get_post_meta($product['id'], '_hotel_website', true);
            $hotel_stars = get_post_meta($product['id'], '_hotel_stars', true) ?: 4; // Default to 4 stars if not set
            $features = get_post_meta($product['id'], '_hotel_features', true) ?: ['Breakfast included', 'Gala Dinner Venue']; // Default features

            // Get variations if this is a variable product
            $variations = [];
            if ($product_obj->is_type('variable')) {
                $available_variations = $product_obj->get_available_variations();
                foreach ($available_variations as $variation) {
                    $variation_obj = wc_get_product($variation['variation_id']);
                    $variation_name = implode(' - ', $variation_obj->get_variation_attributes());
                    $variations[] = [
                        'id' => $variation['variation_id'],
                        'name' => $variation_name,
                        'price' => $variation_obj->get_price(),
                        'price_html' => $variation_obj->get_price_html()
                    ];
                }
            }
        ?>
            <div class="brasov-accommodation-card" style="display: flex; flex-direction: column !important; height: 100%; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; background: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: all 0.3s ease;">
                <h3 class="brasov-accommodation-card-title" style="padding: 15px; margin: 0; font-size: 18px; color: #00b2e3; text-align: center; border-bottom: 1px solid #f0f0f0;"><?php echo $product['name']; ?></h3>

                <!-- Hotel Stars -->
                <div class="brasov-accommodation-card-stars" style="padding: 5px 15px; text-align: center; color: #ffc107;">
                    <?php for ($i = 0; $i < $hotel_stars; $i++) : ?>
                        <i class="fas fa-star"></i>
                    <?php endfor; ?>
                </div>

                <!-- Hotel Image -->
                <div class="brasov-accommodation-card-image" style="height: 180px; overflow: hidden;">
                    <img src="<?php echo $product['image']; ?>" alt="<?php echo esc_attr($product['name']); ?>" style="width: 100%; height: 100%; object-fit: cover;">
                </div>

                <!-- Accommodation Price Info -->
                <div class="brasov-accommodation-card-price-info" style="background: linear-gradient(to right, #00b2e3, #0099cc); color: white; text-align: center; padding: 8px; font-weight: bold; font-size: 13px;">
                    ACCOMMODATION FOR <?php echo $wizard['bucharest']['nights'] ?: $wizard['brasov']['nights']; ?> NIGHT<?php echo ($wizard['bucharest']['nights'] ?: $wizard['brasov']['nights']) > 1 ? 'S' : ''; ?>
                </div>

                <!-- Hotel Features -->
                <div class="brasov-accommodation-card-features" style="padding: 15px; flex-grow: 1; font-size: 14px;">
                    <?php if (is_array($features)) : ?>
                        <?php foreach ($features as $feature) : ?>
                            <div class="brasov-accommodation-card-feature" style="margin-bottom: 8px; display: flex; align-items: center;">
                                <i class="fas fa-check" style="color: #28a745; margin-right: 8px;"></i> <?php echo $feature; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <?php if ($hotel_website) : ?>
                        <div class="brasov-accommodation-card-website" style="margin-top: 10px;">
                            <a href="<?php echo esc_url($hotel_website); ?>" target="_blank" style="color: #00b2e3; text-decoration: none; display: inline-flex; align-items: center;">
                                <i class="fas fa-globe" style="margin-right: 5px;"></i> View Hotel website
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Room Options -->
                <div class="brasov-accommodation-card-rooms" style="padding: 0 15px 15px; border-top: 1px solid #f0f0f0; background-color: #f9f9f9;">
                    <?php if (!empty($variations)) : ?>
                        <?php
                        $has_in_stock_variation = false;
                        foreach ($variations as $index => $variation) :
                            // Check if variation is in stock
                            $variation_obj = wc_get_product($variation['id']);
                            $is_in_stock = $variation_obj->is_in_stock();

                            // Track if we have any in-stock variations
                            if ($is_in_stock) {
                                $has_in_stock_variation = true;
                            }

                            // Get attribute names for better display
                            $attribute_names = [];
                            foreach ($variation_obj->get_variation_attributes() as $attribute_name => $attribute_value) {
                                $taxonomy = str_replace('attribute_', '', $attribute_name);

                                // Get the attribute label
                                if (taxonomy_exists($taxonomy)) {
                                    $term = get_term_by('slug', $attribute_value, $taxonomy);
                                    if ($term) {
                                        $attribute_names[] = $term->name;
                                    } else {
                                        $attribute_names[] = ucfirst($attribute_value);
                                    }
                                } else {
                                    // For custom attributes
                                    $attribute_names[] = ucfirst(str_replace('-', ' ', $attribute_value));
                                }
                            }

                            // Create a better display name
                            $display_name = !empty($attribute_names) ? implode(' - ', $attribute_names) : $variation['name'];
                        ?>
                            <label class="brasov-accommodation-card-room" style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f0f0f0; cursor: <?php echo $is_in_stock ? 'pointer' : 'not-allowed'; ?>; opacity: <?php echo $is_in_stock ? '1' : '0.6'; ?>;">
                                <input type="radio" name="variation_id" value="<?php echo $variation['id']; ?>" <?php echo ($is_in_stock && $index === 0) ? 'checked' : ''; ?> style="margin-right: 10px;" <?php echo $is_in_stock ? '' : 'disabled'; ?>>
                                <span class="room-name" style="flex-grow: 1; font-size: 14px;"><?php echo $display_name; ?></span>
                                <?php if ($is_in_stock) : ?>
                                    <span class="room-price" style="font-weight: bold; color: #00b2e3;"><?php echo wc_price($variation['price']); ?></span>
                                <?php else : ?>
                                    <span class="room-sold-out" style="font-weight: bold; color: #dc3545; background-color: #f8d7da; padding: 2px 8px; border-radius: 4px; font-size: 12px;">SOLD OUT</span>
                                <?php endif; ?>
                            </label>
                        <?php endforeach; ?>

                        <?php
                        // Store whether we have any in-stock variations for the button
                        $product['has_in_stock_variation'] = $has_in_stock_variation;
                        ?>
                    <?php else : ?>
                        <?php
                        // Check if simple product is in stock
                        $product_obj = wc_get_product($product['id']);
                        $is_in_stock = $product_obj->is_in_stock();

                        // Store stock status for the button
                        $product['has_in_stock_variation'] = $is_in_stock;
                        ?>
                        <div class="brasov-accommodation-card-room single-option" style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; opacity: <?php echo $is_in_stock ? '1' : '0.6'; ?>;">
                            <span class="room-name" style="flex-grow: 1; font-size: 14px;">Standard Room</span>
                            <?php if ($is_in_stock) : ?>
                                <span class="room-price" style="font-weight: bold; color: #00b2e3;"><?php echo $product['price_html']; ?></span>
                            <?php else : ?>
                                <span class="room-sold-out" style="font-weight: bold; color: #dc3545; background-color: #f8d7da; padding: 2px 8px; border-radius: 4px; font-size: 12px;">SOLD OUT</span>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Action Button with AJAX functionality -->
                <?php if ($product['in_cart']) : ?>
                    <button type="button" class="brasov-accommodation-card-btn added" style="background: #28a745; color: white; border: none; width: 100%; padding: 12px; font-weight: bold; cursor: not-allowed; text-transform: uppercase;" disabled>
                        <i class="fas fa-check me-2"></i> SELECTED
                    </button>
                <?php elseif (isset($product['has_in_stock_variation']) && !$product['has_in_stock_variation']) : ?>
                    <!-- Sold Out Button -->
                    <button type="button" class="brasov-accommodation-card-btn sold-out"
                            style="background: #6c757d; color: white; border: none; width: 100%; padding: 12px; font-weight: bold; cursor: not-allowed; text-transform: uppercase; opacity: 0.7;" disabled>
                        <i class="fas fa-times-circle me-2"></i> SOLD OUT
                    </button>
                <?php else : ?>
                    <button type="button" class="add-brasov-to-cart-btn brasov-accommodation-card-btn"
                            data-product-id="<?php echo $product['id']; ?>"
                            data-product-name="<?php echo esc_attr($product['name']); ?>"
                            data-product-image="<?php echo esc_attr($product['image']); ?>"
                            style="background: linear-gradient(to right, #00b2e3, #0099cc); color: white; border: none; width: 100%; padding: 12px; font-weight: bold; cursor: pointer; text-transform: uppercase; transition: all 0.2s ease;">
                        <i class="fas fa-check-circle me-2"></i> SELECT
                    </button>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>

    <input type="hidden" name="product_id" id="product_id" value="">

    <?php if ($wizard['has_main_pretour'] && $wizard['bucharest']['selected'] === 'yes' && !empty($products)) : ?>
        <!-- Show a continue button for Bucharest accommodation -->
        <div class="text-center mt-4">
            <button type="submit" name="wizard_action" value="next" class="wizard-btn wizard-btn-primary">
                Continue to Brasov Accommodation <i class="fas fa-arrow-right ms-2"></i>
            </button>
        </div>
    <?php endif; ?>

    <!-- Add JavaScript for AJAX add to cart -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const addToCartBtns = document.querySelectorAll('.add-brasov-to-cart-btn');

        if (addToCartBtns.length > 0) {
            addToCartBtns.forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();

                    const productId = this.getAttribute('data-product-id');
                    const productName = this.getAttribute('data-product-name');
                    const productImage = this.getAttribute('data-product-image');
                    const originalText = this.innerHTML;
                    const button = this;

                    // Get selected variation if any
                    let variationId = null;
                    const variationRadios = button.closest('.brasov-accommodation-card').querySelectorAll('input[type="radio"][name="variation_id"]');
                    if (variationRadios.length > 0) {
                        const selectedVariation = Array.from(variationRadios).find(radio => radio.checked);
                        if (selectedVariation) {
                            variationId = selectedVariation.value;
                        } else {
                            alert('Please select a room type');
                            return;
                        }
                    }

                    // Show loading state
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2" style="color: white;"></i> <span style="color: white;">Adding...</span>';
                    button.disabled = true;

                    // Define the AJAX URL and nonce if not already defined
                    if (typeof lci_ajax === 'undefined') {
                        window.lci_ajax = {
                            ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
                            nonce: '<?php echo wp_create_nonce('lci-dashboard-add-to-cart-nonce'); ?>'
                        };
                    }

                    // Get the number of nights from the page
                    const nights = <?php echo isset($wizard['brasov']['nights']) ? intval($wizard['brasov']['nights']) : 1; ?>;
                    // Get nights for accommodation

                    // Add to cart via AJAX
                    const data = {
                        action: 'lci-dashboard-add-to-cart',
                        product_id: productId,
                        quantity: nights, // Multiply by number of nights
                        security: lci_ajax.nonce,
                        nights: nights // Add nights parameter
                    };

                    // If we have a variation ID, add it
                    if (variationId) {
                        data.variation_id = variationId;

                        // Get all variation attributes from the form
                        const variationForm = document.querySelector('form');
                        if (variationForm) {
                            const formData = new FormData(variationForm);
                            for (const [key, value] of formData.entries()) {
                                if (key.startsWith('attribute_')) {
                                    data[key] = value;
                                }
                            }
                        }
                    }

                    // Prepare data for AJAX request

                    // Use jQuery AJAX for better compatibility
                    jQuery.ajax({
                        url: lci_ajax.ajax_url,
                        type: 'POST',
                        data: data,
                        success: function(response) {
                            // Process the server response
                            if (response.success) {
                                // Show success state
                                button.innerHTML = '<i class="fas fa-check me-2" style="color: white;"></i> <span style="color: white;">Added to cart!</span>';

                                // Update mini cart
                                if (response.data && response.data.cart_count !== undefined) {
                                    // Dispatch event to update mini cart
                                    window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
                                        detail: {
                                            cart_count: response.data.cart_count,
                                            cart_total: response.data.cart_total
                                        }
                                    }));

                                    // Also trigger standard WooCommerce events
                                    jQuery(document.body).trigger('added_to_cart', [null, null, null]);
                                    jQuery(document.body).trigger('updated_cart_totals');
                                }

                                // Show confirmation modal
                                try {
                                    // Create product name with nights information
                                    const displayName = nights > 1
                                        ? productName + ' (' + nights + ' nights)'
                                        : productName;

                                    // Try multiple methods to show confirmation
                                    if (typeof window.showAddToCartConfirmation === 'function') {
                                        window.showAddToCartConfirmation(
                                            displayName,
                                            productImage
                                        );
                                    } else if (typeof window.showAddToCartConfirmationDirect === 'function') {
                                        window.showAddToCartConfirmationDirect(
                                            displayName,
                                            productImage
                                        );
                                    } else {
                                        // Dispatch event as fallback
                                        window.dispatchEvent(new CustomEvent('show-modal', {
                                            detail: {
                                                productName: displayName,
                                                productImage: productImage
                                            }
                                        }));
                                    }
                                } catch (error) {
                                    // Error occurred while showing confirmation
                                }

                                // Reset button after 2 seconds
                                setTimeout(function() {
                                    button.innerHTML = originalText;
                                    button.disabled = false;

                                    // Try a fallback method if cart count is still 0
                                    if (response.data.cart_count === 0) {
                                        // Cart count is still 0, trying fallback method

                                        // Create a hidden form and submit it
                                        const fallbackForm = document.createElement('form');
                                        fallbackForm.method = 'POST';
                                        fallbackForm.style.display = 'none';

                                        // Add product ID
                                        const productIdInput = document.createElement('input');
                                        productIdInput.type = 'hidden';
                                        productIdInput.name = 'product_id';
                                        productIdInput.value = productId;
                                        fallbackForm.appendChild(productIdInput);

                                        // Add variation ID if available
                                        if (variationId) {
                                            const variationIdInput = document.createElement('input');
                                            variationIdInput.type = 'hidden';
                                            variationIdInput.name = 'variation_id';
                                            variationIdInput.value = variationId;
                                            fallbackForm.appendChild(variationIdInput);
                                        }

                                        // Add nights information
                                        const nightsInput = document.createElement('input');
                                        nightsInput.type = 'hidden';
                                        nightsInput.name = 'brasov_nights';
                                        nightsInput.value = nights;
                                        fallbackForm.appendChild(nightsInput);

                                        // Add quantity
                                        const quantityInput = document.createElement('input');
                                        quantityInput.type = 'hidden';
                                        quantityInput.name = 'quantity';
                                        quantityInput.value = nights;
                                        fallbackForm.appendChild(quantityInput);

                                        // Add add to cart action
                                        const actionInput = document.createElement('input');
                                        actionInput.type = 'hidden';
                                        actionInput.name = 'add_to_cart';
                                        actionInput.value = '1';
                                        fallbackForm.appendChild(actionInput);

                                        // Add to document and submit
                                        document.body.appendChild(fallbackForm);
                                        fallbackForm.submit();
                                        return;
                                    }

                                    // Advance to next step or reload the page
                                    const nextButton = document.querySelector('button[name="wizard_action"][value="next"]');
                                    if (nextButton) {
                                        nextButton.click();
                                    } else {
                                        // Reload the page to show updated cart status
                                        window.location.reload();
                                    }
                                }, 2000);
                            } else {
                                // Show error
                                button.innerHTML = '<i class="fas fa-exclamation-triangle me-2" style="color: white;"></i> <span style="color: white;">Error</span>';

                                // Show error message
                                alert(response.data && response.data.message ? response.data.message : 'Could not add product to cart');

                                // Reset button after 2 seconds
                                setTimeout(function() {
                                    button.innerHTML = originalText;
                                    button.disabled = false;
                                }, 2000);
                            }
                        },
                        error: function(xhr, status, error) {
                            // Error occurred while adding to cart

                            // Show error
                            button.innerHTML = '<i class="fas fa-exclamation-triangle me-2" style="color: white;"></i> <span style="color: white;">Error</span>';

                            // Show error message
                            alert('Could not add product to cart. Please try again.');

                            // Reset button after 2 seconds
                            setTimeout(function() {
                                button.innerHTML = originalText;
                                button.disabled = false;
                            }, 2000);
                        }
                    });
                });
            });
        }
    });
    </script>
<?php endif; ?>
