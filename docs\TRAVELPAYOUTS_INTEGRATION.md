# Travelpayouts Flight API Integration for LCI 2025 Dashboard

## Overview

This integration adds flight booking monetization to the LCI 2025 dashboard through Travelpayouts.com affiliate program. Participants can search and book flights to Romania while generating commission revenue for the organization.

## Features

### 🛫 Flight Search Widget
- Search flights from participant's location to Romania
- **Multiple destination options**: Bucharest Otopeni (OTP), Brasov (GHV), Sibiu (SBZ)
- Pre-filled with LCI event dates (August 16-25, 2025)
- Airport autocomplete for major international airports
- Direct booking with affiliate tracking
- Real-time price comparison
- Multiple airline options

## Setup Instructions

### 1. Sign Up for Travelpayouts

1. Visit [travelpayouts.com](https://www.travelpayouts.com/)
2. Create a free affiliate account
3. Complete the registration process
4. Wait for account approval (usually 24-48 hours)

### 2. Get API Credentials

1. Log into your Travelpayouts dashboard
2. Go to **Tools → API**
3. Copy your API Token
4. Note your Marker (affiliate ID)
5. Get your Partner ID (if available)

### 3. Configure in WordPress Admin

1. Go to **LCI 2025 → Travel Monetization**
2. Enter your API credentials:
   - **API Token**: Your Travelpayouts API token
   - **Marker**: Your affiliate identifier
   - **Partner ID**: Additional tracking parameter (optional)
3. Click **Save Settings**

### 4. Add Widgets to Pages

Use these shortcodes to display flight widgets:

#### Flight Dashboard
```php
[lci_travel_dashboard]
```

#### Individual Flight Widget
```php
[lci_flight_widget origin="London, UK"]
```

## Revenue Potential

### Commission Rates
- **Flights**: 1-3% of booking value
- **Average Commission**: €15-50 per flight booking
- **Premium Airlines**: Higher commission rates
- **Volume Bonuses**: Additional rewards for milestones

### Estimated Earnings
Based on typical LCI event participation:
- **Total Participants**: ~500-1000
- **International Travelers**: ~30-40%
- **Flight Booking Conversion**: ~40-50%
- **Average Commission per Booking**: €25-40
- **Estimated Monthly Revenue**: €800-2,000

## Technical Implementation

### File Structure
```
includes/
├── class-lci-travelpayouts.php          # Main API integration class
└── shortcodes/
    └── travel-widgets.php               # Shortcode implementations

assets/
├── js/
│   └── travelpayouts.js                 # Frontend JavaScript
└── css/
    └── travel-widgets.css               # Widget styling

templates/admin/
└── travelpayouts-settings.php          # Admin settings page
```

### API Endpoints Used
- **Flight Prices**: `https://api.travelpayouts.com/v1/prices/cheap`
- **Hotel Search**: `https://api.travelpayouts.com/v1/hotels/search`
- **Affiliate Tracking**: Custom URL generation with marker parameters

### JavaScript Functions
- `TravelSearch.searchFlights()` - Handle flight searches
- `TravelSearch.searchHotels()` - Handle hotel searches
- `TravelSearch.generateBookingUrl()` - Create affiliate tracking URLs
- `TravelSearch.initFlightAutocomplete()` - Airport autocomplete

## Customization Options

### Widget Appearance
Modify `assets/css/travel-widgets.css` to customize:
- Color schemes
- Layout and spacing
- Responsive breakpoints
- Animation effects

### Default Values
Update in `includes/class-lci-travelpayouts.php`:
- Event dates
- Default destinations
- Currency preferences
- Search parameters

### Shortcode Parameters
```php
[lci_travel_dashboard
    show_flights="true"
    show_hotels="true"
    show_cars="false"
    show_insurance="false"
    title="Plan Your Trip to Romania"]
```

## Optimization Tips

### Maximize Conversions
1. **Strategic Placement**: Add widgets to high-traffic pages
2. **Pre-filled Data**: Use participant location data for better UX
3. **Event Integration**: Highlight connection to LCI event
4. **Mobile Optimization**: Ensure widgets work well on mobile devices

### SEO Benefits
1. **Content Value**: Travel widgets add useful content
2. **User Engagement**: Increased time on site
3. **Reduced Bounce Rate**: More interactive pages
4. **Local SEO**: Romania travel content

## Monitoring and Analytics

### Admin Dashboard
- View estimated earnings potential
- Monitor API configuration status
- Preview widget functionality
- Track participant engagement

### Travelpayouts Dashboard
- Real-time booking tracking
- Commission reports
- Payment processing
- Performance analytics

## Troubleshooting

### Common Issues

**Widgets Not Displaying**
- Check API credentials in admin settings
- Verify Travelpayouts account is approved
- Ensure shortcodes are properly placed

**Search Not Working**
- Verify API token is valid
- Check browser console for JavaScript errors
- Confirm AJAX endpoints are accessible

**No Results Returned**
- Check API rate limits
- Verify search parameters
- Test with different date ranges

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Security Considerations

### API Key Protection
- API tokens stored securely in WordPress options
- No sensitive data exposed to frontend
- AJAX requests include nonce verification

### Affiliate Link Integrity
- All booking URLs include proper affiliate tracking
- UTM parameters for campaign tracking
- Rel="nofollow" attributes on external links

## Future Enhancements

### Planned Features
1. **Advanced Analytics**: Detailed conversion tracking
2. **A/B Testing**: Widget design optimization
3. **Personalization**: Location-based recommendations
4. **Multi-language**: Support for international participants
5. **Mobile App**: Native mobile widget integration

### API Expansions
1. **Additional Providers**: Booking.com, Expedia integration
2. **Price Comparison**: Multi-provider price comparison
3. **Real-time Updates**: Live price monitoring
4. **Advanced Filters**: More search criteria options

## Support

For technical support or questions:
1. Check this documentation first
2. Review Travelpayouts API documentation
3. Contact LCI technical team
4. Submit support ticket through admin interface

## Legal Compliance

### Affiliate Disclosure
Ensure proper disclosure of affiliate relationships:
- Add disclosure text to widget areas
- Include in terms of service
- Follow FTC guidelines for affiliate marketing

### Data Privacy
- No personal data sent to Travelpayouts without consent
- GDPR compliance for EU participants
- Clear privacy policy regarding travel data

---

*Last updated: December 2024*
*Version: 1.0.0*
