<?php
/**
 * Tours Add to Cart Confirmation Template
 */
?>

<script>
// Define the function globally before Alpine initializes
window.showToursAddToCartConfirmation = function(name, image) {
    // Store the data for <PERSON> to use when it initializes
    window.pendingToursConfirmation = {
        productName: name,
        productImage: image
    };

    // Try to find the Alpine component
    const confirmEl = document.getElementById('tours-add-to-cart-confirm');
    if (confirmEl && confirmEl.__x) {
        // If Alpine is already initialized, call the method directly
        try {
            confirmEl.__x.getUnobservedData().showConfirmation(name, image);
        } catch (error) {
            // Try direct DOM manipulation as a fallback
            showToursConfirmationFallback(name, image);
        }
    } else {
        // Otherwise dispatch an event that will be handled when Alpine initializes
        window.dispatchEvent(new CustomEvent('tours-show-confirmation-pending', {
            detail: {
                productName: name,
                productImage: image
            }
        }));

        // Also try direct DOM manipulation as a fallback
        showToursConfirmationFallback(name, image);
    }
};

// Fallback function that uses direct DOM manipulation
function showToursConfirmationFallback(name, image) {

    // Create a simple modal if it doesn't exist
    let fallbackModal = document.getElementById('tours-fallback-modal');
    if (!fallbackModal) {
        fallbackModal = document.createElement('div');
        fallbackModal.id = 'tours-fallback-modal';
        fallbackModal.style.position = 'fixed';
        fallbackModal.style.top = '0';
        fallbackModal.style.left = '0';
        fallbackModal.style.right = '0';
        fallbackModal.style.bottom = '0';
        fallbackModal.style.backgroundColor = 'rgba(0,0,0,0.5)';
        fallbackModal.style.zIndex = '9999';
        fallbackModal.style.display = 'flex';
        fallbackModal.style.alignItems = 'center';
        fallbackModal.style.justifyContent = 'center';

        document.body.appendChild(fallbackModal);
    } else {
        fallbackModal.style.display = 'flex';
    }

    // Create the modal content
    fallbackModal.innerHTML = `
        <div style="background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.12); max-width: 450px; width: 95%; position: relative;">
            <button onclick="document.getElementById('tours-fallback-modal').style.display='none';" style="position: absolute; right: 1rem; top: 1rem; background: none; border: none; font-size: 1.25rem; color: #36b1dc; transition: all 0.2s ease; z-index: 10;">
                <i class="fas fa-times"></i>
            </button>
            <div style="padding: 2.5rem 1.5rem 1.5rem; text-align: center;">
                <div style="margin-bottom: 1.5rem;">
                    <i class="fas fa-check-circle" style="font-size: 3rem; color: #36b1dc;"></i>
                </div>
                <div>
                    <h4 style="color: #36b1dc; font-weight: 600; margin-bottom: 0.75rem;">${name}</h4>
                    <p style="color: #36b1dc; font-size: 1.1rem;">has been added to your LCI Tours Booking</p>
                </div>
            </div>
            <div style="padding: 1rem 1.5rem 1.5rem; display: flex; justify-content: space-between; gap: 1rem;">
                <button onclick="document.getElementById('tours-fallback-modal').style.display='none';" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #36b1dc; color: #fff; font-weight: 500; transition: all 0.2s ease;">Continue Booking</button>
                <button onclick="document.getElementById('tours-fallback-modal').style.display='none'; window.location.href='/cart/';" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #36b1dc; color: #fff; font-weight: 500; transition: all 0.2s ease;">
                    <i class="fas fa-shopping-bag me-2" style="color: #fff;"></i> View Bookings
                </button>
            </div>
            <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 4px; background-color: rgba(54, 177, 220, 0.1); overflow: hidden;">
                <div id="tours-fallback-progress" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: #36b1dc; transform-origin: left center; transition: transform 3s linear; transform: scaleX(0);"></div>
            </div>
        </div>
    `;

    // Start the progress bar animation
    setTimeout(() => {
        const progressBar = document.getElementById('tours-fallback-progress');
        if (progressBar) {
            progressBar.style.transform = 'scaleX(1)';
        }
    }, 50);

    // Auto-close after 3 seconds
    setTimeout(() => {
        if (fallbackModal) {
            fallbackModal.style.display = 'none';
        }
    }, 3000);
}

// Make sure the showAddToCartConfirmation function also works
window.showAddToCartConfirmation = window.showToursAddToCartConfirmation;
</script>

<style>
    @keyframes tours-pulse-scale {
        0% { transform: scale(0.5); opacity: 0; }
        50% { transform: scale(1.2); opacity: 1; }
        100% { transform: scale(1); opacity: 1; }
    }

    @keyframes tours-fade-in-up {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    .tours-confirmation-animation i {
        display: inline-block;
        animation: tours-pulse-scale 0.5s ease-out;
    }

    .tours-confirmation-text {
        animation: tours-fade-in-up 0.5s ease-out 0.2s both;
    }

    /* Button styles */
    #tours-add-to-cart-confirm .lci-btn {
        background-color: #36b1dc;
        color: #fff !important;
    }

    #tours-add-to-cart-confirm .lci-btn:hover {
        background-color: #2d93b7;
        color: #fff !important;
    }

    #tours-add-to-cart-confirm .lci-btn i {
        color: #fff !important;
    }

    /* Progress bar styles */
    .lci-progress-bar {
        z-index: 5;
    }

    .lci-progress-bar-inner {
        z-index: 6;
    }
</style>

<div
    x-data="{
    open: false,
    productName: '',
    productImage: '',
    progress: 0,
    timer: null,
    scrollPosition: 0,

    init() {
        // Store a reference to the original function
        const originalFunction = window.showToursAddToCartConfirmation;

        // Update the global reference to use this instance
        const self = this;
        window.showToursAddToCartConfirmation = function(name, image) {
            self.showConfirmation(name, image);
        };

        // Listen for custom event
        this.$el.addEventListener('tours-show-confirmation', (event) => {
            if (event.detail) {
                this.showConfirmation(event.detail.productName, event.detail.productImage);
            }
        });

        // Listen for window event
        window.addEventListener('tours-show-modal', (event) => {
            if (event.detail) {
                this.showConfirmation(event.detail.productName, event.detail.productImage);
            }
        });

        // Listen for pending confirmation event
        window.addEventListener('tours-show-confirmation-pending', (event) => {
            if (event.detail) {
                this.showConfirmation(event.detail.productName, event.detail.productImage);
            }
        });

        // Check if there's a pending confirmation
        if (window.pendingToursConfirmation) {
            this.showConfirmation(window.pendingToursConfirmation.productName, window.pendingToursConfirmation.productImage);
            window.pendingToursConfirmation = null;
        }
    },

    showConfirmation(name, image) {
        this.productName = name;
        this.productImage = image;
        this.open = true;
        this.startProgress();

        // Add modal-open class to body
        document.body.classList.add('modal-open');

        // Store the current scroll position
        this.scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

        // Add our own scroll management
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.top = `-${this.scrollPosition}px`;
        document.body.style.width = '100%';
    },

    closeConfirmation() {
        this.open = false;

        // Remove modal-open class from body
        document.body.classList.remove('modal-open');

        // Restore scroll position
        const scrollY = document.body.style.top;
        document.body.style.removeProperty('overflow');
        document.body.style.removeProperty('position');
        document.body.style.removeProperty('top');
        document.body.style.removeProperty('width');
        window.scrollTo(0, parseInt(scrollY || '0') * -1);

        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        // Call the callback function if it exists
        if (typeof window.toursAddToCartCloseCallback === 'function') {
            window.toursAddToCartCloseCallback();
            // Clear the callback after calling it
            window.toursAddToCartCloseCallback = null;
        }
    },

    startProgress() {
        this.progress = 0;
        const duration = 3000; // 3 seconds
        const interval = 30; // Update every 30ms
        const steps = duration / interval;
        const increment = 100 / steps;

        this.timer = setInterval(() => {
            this.progress += increment;
            if (this.progress >= 100) {
                clearInterval(this.timer);
                this.timer = null;
                this.closeConfirmation();
            }
        }, interval);
    },

    viewCart() {
        this.closeConfirmation();

        // Try multiple approaches to open the mini cart

        // 1. Try using the global function
        if (typeof window.openMiniCart === 'function') {
            window.openMiniCart();
            return;
        }

        // 2. Try using the stored reference
        if (window.lciComponents && window.lciComponents.miniCart) {
            try {
                const data = window.lciComponents.miniCart.getUnobservedData();
                data.openCart();
                return;
            } catch (error) {
                // Silent fail, try next method
            }
        }

        // 3. Try finding the element directly
        const miniCartEl = document.getElementById('mini-cart-container') || document.querySelector('.lci-mini-cart-container');
        if (miniCartEl && miniCartEl.__x) {
            try {
                miniCartEl.__x.getUnobservedData().openCart();
                return;
            } catch (error) {
                // Silent fail, try next method
            }
        }

        // 4. Dispatch a window event as last resort
        window.dispatchEvent(new CustomEvent('open-mini-cart'));
    }
}" x-cloak id="tours-add-to-cart-confirm">
    <div
        x-show="open"
        x-transition:enter="fade-in"
        x-transition:leave="fade-out"
        class="lci-modal-backdrop"
        @click.self="closeConfirmation"
        style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center; overflow-y: auto; padding: 20px 0;"
    >
        <div
            class="lci-modal"
            x-transition:enter="slide-in"
            x-transition:leave="slide-out"
            style="background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.12); max-width: 450px; width: 95%; position: relative;"
        >
            <button @click="closeConfirmation" class="lci-modal-close" style="position: absolute; right: 1rem; top: 1rem; background: none; border: none; font-size: 1.25rem; color: #36b1dc; transition: all 0.2s ease; z-index: 10;">
                <i class="fas fa-times"></i>
            </button>

            <div class="lci-modal-body text-center" style="padding: 2.5rem 1.5rem 1.5rem;">
                <div class="tours-confirmation-animation" style="margin-bottom: 1.5rem;">
                    <i class="fas fa-check-circle" style="font-size: 3rem; color: #36b1dc;"></i>
                </div>

                <div class="tours-confirmation-text">
                    <h4 style="color: #36b1dc; font-weight: 600; margin-bottom: 0.75rem;" x-text="productName"></h4>
                    <p style="color: #36b1dc; font-size: 1.1rem;">has been added to your LCI Tours Booking</p>
                </div>
            </div>

            <div class="lci-modal-footer" style="padding: 1rem 1.5rem 1.5rem; display: flex; justify-content: space-between; gap: 1rem;">
                <button @click="closeConfirmation" class="lci-btn" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #36b1dc; color: #fff !important; font-weight: 500; transition: all 0.2s ease;">Continue Booking</button>
                <button @click="viewCart" class="lci-btn" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #36b1dc; color: #fff !important; font-weight: 500; transition: all 0.2s ease;">
                    <i class="fas fa-shopping-bag me-2" style="color: #fff !important;"></i> View Bookings
                </button>
            </div>

            <div class="lci-progress-bar" style="position: absolute; bottom: 0; left: 0; right: 0; height: 4px; background-color: rgba(54, 177, 220, 0.1); overflow: hidden;">
                <div class="lci-progress-bar-inner" :style="{transform: 'scaleX(' + (progress/100) + ')'}" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: #36b1dc; transform-origin: left center; transition: transform 0.1s linear;"></div>
            </div>
        </div>
    </div>
</div>
