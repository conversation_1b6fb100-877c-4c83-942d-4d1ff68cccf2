/**
 * Support Ticket System Admin JavaScript
 */

document.addEventListener('alpine:init', function() {
    Alpine.data('supportAdmin', function() {
        return {
            tickets: [],
            isLoading: true,
            activeTicket: null,
            showTicketDetails: false,
            replyMessage: '',
            isSubmitting: false,
            formSuccess: false,
            formError: false,
            errorMessage: '',
            successMessage: '',
            statusFilter: '',
            categoryFilter: '',
            searchQuery: '',
            currentPage: 1,
            totalPages: 1,
            perPage: 20,

            init() {
                this.loadTickets();
            },

            loadTickets() {
                this.isLoading = true;

                jQuery.ajax({
                    url: lciAdminSupport.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'lci_admin_get_tickets',
                        nonce: lciAdminSupport.nonce,
                        status: this.statusFilter,
                        category: this.categoryFilter,
                        search: this.searchQuery,
                        limit: this.perPage,
                        offset: (this.currentPage - 1) * this.perPage
                    },
                    success: (response) => {
                        if (response.success) {
                            this.tickets = response.data.tickets;
                            this.totalPages = response.data.pages;
                        } else {
                            this.showError(response.data.message);
                        }
                    },
                    error: () => {
                        this.showError('Error loading tickets. Please try again.');
                    },
                    complete: () => {
                        this.isLoading = false;
                    }
                });
            },

            applyFilters() {
                this.currentPage = 1;
                this.loadTickets();
            },

            resetFilters() {
                this.statusFilter = '';
                this.categoryFilter = '';
                this.searchQuery = '';
                this.currentPage = 1;
                this.loadTickets();
            },

            changePage(page) {
                if (page < 1 || page > this.totalPages) return;
                this.currentPage = page;
                this.loadTickets();
            },

            viewTicket(ticketId) {
                this.isLoading = true;
                this.showTicketDetails = true;

                jQuery.ajax({
                    url: lciAdminSupport.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'lci_get_ticket_details',
                        nonce: lciAdminSupport.nonce,
                        ticket_id: ticketId
                    },
                    success: (response) => {
                        if (response.success) {
                            this.activeTicket = response.data.ticket;
                        } else {
                            this.showError(response.data.message);
                            this.showTicketDetails = false;
                        }
                    },
                    error: () => {
                        this.showError('Error loading ticket details. Please try again.');
                        this.showTicketDetails = false;
                    },
                    complete: () => {
                        this.isLoading = false;
                    }
                });
            },

            closeTicketDetails() {
                this.showTicketDetails = false;
                this.activeTicket = null;
                this.replyMessage = '';
            },

            submitReply() {
                if (!this.replyMessage.trim()) {
                    this.showError('Please enter a reply message.');
                    return;
                }

                this.isSubmitting = true;

                const formData = new FormData();
                formData.append('action', 'lci_reply_to_ticket');
                formData.append('nonce', lciAdminSupport.nonce);
                formData.append('ticket_id', this.activeTicket.id);
                formData.append('message', this.replyMessage);

                const attachmentInput = document.getElementById('admin-reply-attachment');
                if (attachmentInput && attachmentInput.files.length > 0) {
                    formData.append('attachment', attachmentInput.files[0]);
                }

                jQuery.ajax({
                    url: lciAdminSupport.ajaxUrl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: (response) => {
                        if (response.success) {
                            this.showSuccess(lciAdminSupport.messages.replySubmitted);
                            this.replyMessage = '';

                            // Reload ticket details to show the new reply
                            this.viewTicket(this.activeTicket.id);

                            // Clear file input
                            if (attachmentInput) {
                                attachmentInput.value = '';
                            }
                        } else {
                            this.showError(response.data.message);
                        }
                    },
                    error: () => {
                        this.showError(lciAdminSupport.messages.replyError);
                    },
                    complete: () => {
                        this.isSubmitting = false;
                    }
                });
            },

            updateTicketStatus(status) {
                this.isSubmitting = true;

                jQuery.ajax({
                    url: lciAdminSupport.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'lci_update_ticket_status',
                        nonce: lciAdminSupport.nonce,
                        ticket_id: this.activeTicket.id,
                        status: status
                    },
                    success: (response) => {
                        if (response.success) {
                            this.showSuccess(lciAdminSupport.messages.statusUpdated);

                            // Update local status
                            this.activeTicket.status = status;

                            // Reload tickets list to reflect the status change
                            this.loadTickets();
                        } else {
                            this.showError(response.data.message);
                        }
                    },
                    error: () => {
                        this.showError(lciAdminSupport.messages.statusError);
                    },
                    complete: () => {
                        this.isSubmitting = false;
                    }
                });
            },

            showError(message) {
                this.errorMessage = message;
                this.formError = true;
                this.formSuccess = false;

                setTimeout(() => {
                    this.formError = false;
                }, 5000);
            },

            showSuccess(message) {
                this.successMessage = message;
                this.formSuccess = true;
                this.formError = false;

                setTimeout(() => {
                    this.formSuccess = false;
                }, 5000);
            },

            getStatusClass(status) {
                switch (status) {
                    case 'open':
                        return 'bg-yellow-100 text-yellow-800';
                    case 'in-progress':
                        return 'bg-blue-100 text-blue-800';
                    case 'resolved':
                        return 'bg-green-100 text-green-800';
                    case 'closed':
                        return 'bg-gray-100 text-gray-800';
                    default:
                        return 'bg-gray-100 text-gray-800';
                }
            },

            getStatusText(status) {
                switch (status) {
                    case 'open':
                        return 'Open';
                    case 'in-progress':
                        return 'In Progress';
                    case 'resolved':
                        return 'Resolved';
                    case 'closed':
                        return 'Closed';
                    default:
                        return status.charAt(0).toUpperCase() + status.slice(1);
                }
            },

            getCategoryText(category) {
                switch (category) {
                    case 'registration':
                        return 'Registration Issues';
                    case 'payment':
                        return 'Payment & Invoices';
                    case 'accommodation':
                        return 'Accommodation';
                    case 'tours':
                        return 'Tours & Activities';
                    case 'visa':
                        return 'Visa & Travel';
                    case 'other':
                        return 'Other Questions';
                    default:
                        return category.charAt(0).toUpperCase() + category.slice(1);
                }
            },

            formatDate(dateString) {
                if (!dateString) return '';

                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            },

            getUserName(userId) {
                // This would ideally fetch the user's name from WordPress
                // For now, we'll just return the user ID
                return 'User #' + userId;
            }
        };
    });
});
