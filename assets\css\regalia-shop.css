/* Regalia Shop CSS - Modern Grid Layout */

/* Import Open Sans font */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap');

/* Fundraising CTA and Progress Bar */
.regalia-fundraising-cta {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9f5fb 100%);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(54, 177, 220, 0.2);
    position: relative;
    overflow: hidden;
    margin-top: 1rem;
    margin-bottom: 2rem;
    width: 100%;
    clear: both;
    display: block;
}

.regalia-fundraising-cta::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2336b1dc" opacity="0.1"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>');
    background-repeat: no-repeat;
    background-size: contain;
    opacity: 0.1;
    transform: rotate(15deg);
}

.regalia-cta-headline {
    font-size: 1.5rem;
    font-weight: 700;
    color: #36b1dc;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
}

.regalia-cta-text {
    font-size: 0.90rem;
    line-height: 1.2;
    color: #4a5568;
    margin-bottom: 1rem;
    text-align: justify;
}

.regalia-progress-container {
    background-color: white;
    border-radius: 0.75rem;
    padding: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.regalia-progress-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.regalia-progress-raised {
    font-size: 1.25rem;
    font-weight: 700;
    color: #36b1dc;
}

.regalia-progress-goal {
    font-size: 1.25rem;
    font-weight: 500;
    color: #718096;
    text-align: right;
}

.regalia-progress-raised span,
.regalia-progress-goal span {
    font-size: 0.875rem;
    font-weight: 400;
    display: block;
    color: #718096;
}

.regalia-progress-bar-container {
    height: 1.5rem;
    background-color: #e2e8f0;
    border-radius: 0.75rem;
    position: relative;
    margin-bottom: 0.2rem;
    overflow: hidden;
}

.regalia-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #36b1dc 0%, #2d93b7 100%);
    border-radius: 0.75rem;
    transition: width 1s ease;
    position: relative;
}

.regalia-milestone {
    position: absolute;
    top: -10px;
    transform: translateX(-50%);
    color: #2d93b7;
    font-size: 1.25rem;
    z-index: 5;
    background-color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 2px solid #36b1dc;
}

.regalia-impact-message {
    text-align: center;
    font-size: 1.1rem;
    color: #4a5568;
    font-style: italic;
}

.regalia-impact-message strong {
    color: #36b1dc;
    font-weight: 700;
    font-style: normal;
}

.regalia-contributor-count {
    margin-top: 0rem;
margin-bottom: 0rem;
    font-size: 0.95rem;
    background-color: #fff;
    padding: 0.5rem;
    border-radius: 0.5rem;
    display: inline-block;
}

@media (max-width: 768px) {
    .regalia-cta-headline {
        font-size: 1.25rem;
    }

    .regalia-progress-raised,
    .regalia-progress-goal {
        font-size: 1rem;
    }

    .regalia-impact-message {
        font-size: 0.9rem;
    }
}

/* Base font for all elements */
.regalia-products-container,
.regalia-product-card,
.regalia-product-title,
.regalia-product-description,
.regalia-btn,
.regalia-variation-card,
.regalia-variation-option,
.regalia-quantity input {
    font-family: 'Open Sans', sans-serif;
}

/* Mini Cart Styles */
.mini-cart-items-container {
    max-height: 400px;
    overflow-y: auto;
}

.mini-cart-item {
    transition: all 0.2s ease;
}

.mini-cart-item:hover {
    background-color: #f8f9fa;
}

.mini-cart-item-image img {
    transition: all 0.3s ease;
}

.mini-cart-item:hover .mini-cart-item-image img {
    transform: scale(1.05);
}

.mini-cart-item-name {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.mini-cart-item-price {
    color: #36b1dc;
    font-weight: 600;
}

.mini-cart-item-quantity {
    background-color: rgba(54, 177, 220, 0.1);
    color: #333 !important;
}

.mini-cart-item-remove a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.mini-cart-item-remove a:hover {
    background-color: #fab33a;
    color: white !important;
}

.mini-cart-total-container {
    font-size: 1.1rem;
}

.mini-cart-modal-total {
    color: #36b1dc;
    font-weight: 700;
}

/* Modal Styling */
.modal-backdrop {
    z-index: 1040 !important;
}

.modal {
    z-index: 1050 !important;
}

.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.12);
}

.modal-header,
.modal-footer {
    padding: 1rem;
    border-color: rgba(0, 0, 0, 0.05);
}
#miniCartModal .modal-content{
    padding: 0 !   important;
}
.modal-body {
    padding: 0;
}

/* Mini Cart Modal */
#miniCartModal .modal-content {
    overflow: hidden;
}

#miniCartModal .modal-header {
    background-color: #f8f9fa;
}

#miniCartModal .modal-footer {
    background-color: #f8f9fa;
    justify-content: space-between;
}

/* Mobile styles for mini cart footer */
@media (max-width: 576px) {
    .mini-cart-total-container {
        width: 100%;
        text-align: center;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    #miniCartModal .modal-footer .d-flex,
    .lci-modal-footer .d-flex {
        flex-direction: column;
        width: 100%;
    }

    #miniCartModal .modal-footer .d-flex > div:last-child,
    .lci-modal-footer .d-flex > div:last-child {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    #miniCartModal .modal-footer .btn,
    .lci-modal-footer .lci-btn {
        flex: 1;
        margin: 0 5px;
        white-space: nowrap;
    }
}

/* Prevent multiple backdrops - more aggressive approach */
body > .modal-backdrop:not(:first-of-type) {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* Force only one backdrop to be visible */
body.modal-open {
    overflow: hidden;
    padding-right: 0 !important;
}

/* Add to Cart Confirmation Modal */
#addToCartConfirmModal .modal-body {
    padding: 2rem;
}

#addToCartConfirmModal .modal-content {
    border: none;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

#addToCartConfirmModal .modal-header {
    border-bottom: 1px solid rgba(54, 177, 220, 0.2);
}

#addToCartConfirmModal .modal-footer {
    border-top: 1px solid rgba(54, 177, 220, 0.2);
}

#added-product-image {
    max-height: 150px;
    object-fit: contain;
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 10px;
    margin: 0 auto;
    display: block;
    border: 1px solid rgba(54, 177, 220, 0.2);
}

#added-product-name {
    color: #36b1dc;
    font-weight: 600;
}

#autoCloseProgress {
    height: 5px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
}

#autoCloseProgress .progress-bar {
    transition: width 3s linear;
}

/* Animation for added product */
@keyframes productAdded {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}

#addToCartConfirmModal .modal-body img {
    animation: productAdded 0.5s ease forwards;
}
.regalia-products-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.regalia-products-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.5rem;
    width: 100%;
}

/* Responsive grid - 3 columns on larger screens */
@media (min-width: 640px) {
    .regalia-products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .regalia-products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Product Card Styles */
.regalia-product-card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

.regalia-product-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-0.25rem);
}

.regalia-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #36b1dc, #2d93b7);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.regalia-product-card:hover::before {
    opacity: 1;
}

/* Product Image */
.regalia-product-image {
    position: relative;
    padding-bottom: 100%; /* 1:1 Aspect Ratio */
}

.regalia-product-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 1rem;
    transition: transform 0.3s ease;
}

.regalia-product-card:hover .regalia-product-image img {
    transform: scale(1.05);
}

/* Badges */
.regalia-badges-left {
    position: absolute;
    top: 1rem;
    left: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.regalia-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    color: white;
}

.regalia-badge-sale {
    background: linear-gradient(to right, #f56565, #e53e3e);
}

.regalia-badge-featured {
    background-color: #36b1dc;
}

/* Product Content */
.regalia-product-content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    position: relative;
}

.regalia-product-title {
    font-weight: 600;
    color: #1a202c;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    line-height: 1.5;
    position: relative;
    padding-bottom: 0.75rem;
}

.regalia-product-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background-color: #36b1dc;
    border-radius: 2px;
}

/* Product Description */
.regalia-product-description {
    font-size: 0.9rem;
    color: #4a5568;
    overflow: visible;
    max-height: none;
    text-align: justify;
    margin-top: 1rem;
    margin-bottom: 1rem;
    line-height: 1.5;
    padding: 0;
}

/* Variants Count */
.regalia-product-variants {
    font-size: 0.75rem;
    color: #718096;
    text-align: center;
    margin-top: 0.5rem;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Product Actions */
.regalia-product-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 1rem;
}

/* Price */
.regalia-product-price {
    width: 30%;
    text-align: left;
}

.regalia-price-regular {
    color: #36b1dc;
    font-weight: 700;
    font-size: 1.1rem;
    line-height: 2rem;
}

.regalia-price-sale {
    display: flex;
    flex-direction: column;
}

.regalia-price-new {
    color: #36b1dc;
    font-weight: 700;
    font-size: 1.1rem;
    line-height: 2rem;
}

.regalia-price-old {
    color: #a0aec0;
    text-decoration: line-through;
    font-size: 0.75rem;
    margin-top: -0.25rem;
}

/* Buttons */
.regalia-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 70%;
    text-transform: uppercase;
}

.regalia-btn-primary {
    background-color: #36b1dc;
    color: #fff !important;
    border: none;
}

.regalia-btn-primary i {
    color: #fff !important;
}

.regalia-btn-primary:hover {
    background-color: #2d93b7;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(45, 147, 183, 0.3);
}

.regalia-btn-disabled {
    background-color: #a0aec0;
    color: #fff !important;
    cursor: not-allowed;
    width: 70%;
}

.regalia-btn-disabled i {
    color: #fff !important;
}

/* Variation Card */
.regalia-variation-card {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem;
    box-shadow: 0 -5px 10px -3px rgba(0, 0, 0, 0.05);
    transform: translateY(100%) scale(0.9);
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1),
                opacity 0.5s ease,
                max-height 0.7s ease,
                scale 0.6s cubic-bezier(0.34, 1.56, 0.64, 1),
                box-shadow 0.6s ease;
    transform-origin: bottom center;
    z-index: 50;
}

.regalia-variation-card.active {
    transform: translateY(0) scale(1);
    opacity: 1;
    max-height: 80vh; /* Large enough to contain all content */
    overflow: auto;
    box-shadow: 0 -10px 25px -5px rgba(0, 0, 0, 0.1), 0 -8px 10px -6px rgba(0, 0, 0, 0.05);
}

.regalia-variation-card.closing {
    transform: translateY(100%) scale(0.9);
    opacity: 0;
    max-height: 0;
    transition: transform 0.6s cubic-bezier(0.68, -0.6, 0.32, 1.6),
                opacity 0.5s ease,
                max-height 0.6s ease,
                scale 0.6s cubic-bezier(0.68, -0.6, 0.32, 1.6),
                box-shadow 0.6s ease;
}

.regalia-variation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.regalia-variation-header h4 {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #1a202c;
    margin: 0;
}

.close-variation-card {
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close-variation-card:hover {
    color: #4a5568;
}

.regalia-variation-options {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.regalia-variation-option {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.regalia-variation-option label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
}

.regalia-variation-option select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #1a202c;
    transition: border-color 0.2s ease;
}

.regalia-variation-option select:focus {
    outline: none;
    border-color: #36b1dc;
    box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);
}

.regalia-variation-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid #e2e8f0;
}

.regalia-quantity {
    display: flex;
    align-items: center;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
}

.regalia-quantity button {
    padding: 0.5rem 0.75rem;
    background: none;
    border: none;
    color: #4a5568;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.regalia-quantity button:hover {
    background-color: #f7fafc;
}

.regalia-quantity input {
    width: 3rem;
    text-align: center;
    border: none;
    border-left: 1px solid #e2e8f0;
    border-right: 1px solid #e2e8f0;
    padding: 0.5rem 0;
    -moz-appearance: textfield;
}

.regalia-quantity input::-webkit-outer-spin-button,
.regalia-quantity input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Special styling for quantity inputs in both simple and variable products */
.regalia-quantity-card .regalia-quantity,
.regalia-variation-option .regalia-quantity {
    display: flex;
    align-items: stretch;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
    flex-direction: row;
    align-content: center;
    justify-content: space-between;
}

.regalia-quantity-card .regalia-quantity input,
.regalia-variation-option .regalia-quantity input {
    width: 11rem;
    text-align: center;
    border: none;
    border-left: 1px solid #e2e8f0;
    border-right: 1px solid #e2e8f0;
    padding: 0.5rem 0;
    -moz-appearance: textfield;
}

/* Sentinel for infinite scrolling */
#sentinel {
    height: 5rem;
    width: 100%;
}

/* Enhanced Mini Cart Styling */
/* Mini Cart Button */
#mini-cart-button {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
}

#mini-cart-button .badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.mini-cart-button-text {
    font-weight: 600;
    white-space: nowrap;
}

/* Mini Cart Item */
.mini-cart-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Product Image */
.mini-cart-item-image {
    flex: 0 0 50px;
    margin-right: 10px;
}

.mini-cart-item-image img {
    width: 50px;
    height: 50px;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Quantity Badge */
.mini-cart-item-quantity-container {
    flex: 0 0 auto;
    margin-right: 10px;
}

.mini-cart-item-quantity {
    background-color: rgba(54, 177, 220, 0.1);
    color: #333 !important;
    font-size: 0.85rem;
    padding: 4px 8px;
    border-radius: 4px;
}

/* Product Details */
.mini-cart-item-details {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 0; /* Prevents flex items from overflowing */
}

.mini-cart-item-name-container {
    flex: 1;
    min-width: 0;
    padding-right: 10px;
}

.mini-cart-item-name {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.mini-cart-item-price-container {
    flex: 0 0 auto;
    text-align: right;
}

/* Remove Button */
.mini-cart-item-remove {
    flex: 0 0 auto;
    margin-left: 10px;
}

.mini-cart-item-remove a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
    color: #dc3545;
}

.mini-cart-item-remove a:hover {
    background-color: #fab33a;
    color: white !important;
}
