<?php
/**
 * LCI 2025 Dashboard Database Repair
 * 
 * Provides tools to repair database issues
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class LCI_Database_Repair {
    /**
     * Fix duplicate registration IDs
     * 
     * @return array Results of the repair operation
     */
    public static function fix_duplicate_reg_ids() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';
        
        // Find all duplicate registration IDs
        $duplicates = $wpdb->get_results(
            "SELECT unique_reg_id, COUNT(*) as count 
             FROM $table_name 
             GROUP BY unique_reg_id 
             HAVING COUNT(*) > 1"
        );
        
        if (empty($duplicates)) {
            return [
                'success' => true,
                'message' => 'No duplicate registration IDs found.',
                'fixed_count' => 0
            ];
        }
        
        $fixed_count = 0;
        $errors = [];
        
        foreach ($duplicates as $duplicate) {
            // Get all participants with this duplicate ID
            $participants = $wpdb->get_results($wpdb->prepare(
                "SELECT id, order_id FROM $table_name WHERE unique_reg_id = %s ORDER BY id ASC",
                $duplicate->unique_reg_id
            ));
            
            // Skip the first one (keep original)
            array_shift($participants);
            
            // Fix the rest
            foreach ($participants as $participant) {
                // Generate a new unique ID
                $new_reg_id = LCI_Database::generate_unique_reg_id();
                
                // Update the participant
                $result = $wpdb->update(
                    $table_name,
                    ['unique_reg_id' => $new_reg_id],
                    ['id' => $participant->id]
                );
                
                if ($result !== false) {
                    $fixed_count++;
                } else {
                    $errors[] = "Failed to update participant ID {$participant->id} (Order: {$participant->order_id}): {$wpdb->last_error}";
                }
            }
        }
        
        if (!empty($errors)) {
            return [
                'success' => false,
                'message' => 'Some duplicates could not be fixed. See errors for details.',
                'fixed_count' => $fixed_count,
                'errors' => $errors
            ];
        }
        
        return [
            'success' => true,
            'message' => "Fixed $fixed_count duplicate registration IDs.",
            'fixed_count' => $fixed_count
        ];
    }
    
    /**
     * Repair database tables
     * 
     * @return array Results of the repair operation
     */
    public static function repair_tables() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        
        if (!$table_exists) {
            // Create the table if it doesn't exist
            LCI_Database::create_tables();
            
            return [
                'success' => true,
                'message' => 'Database tables created successfully.'
            ];
        }
        
        // Check and repair table structure
        $current_columns = $wpdb->get_results("DESCRIBE $table_name");
        $current_column_names = array_map(function($col) {
            return $col->Field;
        }, $current_columns);
        
        // Check for room_share column length
        $room_share_col = array_filter($current_columns, function($col) {
            return $col->Field === 'room_share';
        });
        
        if (!empty($room_share_col)) {
            $room_share_col = reset($room_share_col);
            
            // If room_share is VARCHAR(10), update it to VARCHAR(50)
            if (strpos($room_share_col->Type, 'varchar(10)') !== false) {
                $wpdb->query("ALTER TABLE $table_name MODIFY room_share VARCHAR(50)");
            }
        }
        
        // Run REPAIR TABLE
        $repair_result = $wpdb->get_results("REPAIR TABLE $table_name");
        $repair_success = true;
        
        foreach ($repair_result as $result) {
            if ($result->Msg_type === 'error') {
                $repair_success = false;
                break;
            }
        }
        
        if (!$repair_success) {
            return [
                'success' => false,
                'message' => 'Failed to repair database table.',
                'details' => $repair_result
            ];
        }
        
        return [
            'success' => true,
            'message' => 'Database tables repaired successfully.'
        ];
    }
}
