document.addEventListener("DOMContentLoaded", () => {
  // Initialize Alpine.js store for association selection
  if (window.Alpine && !window.Alpine.store('selectedAssociation')) {
    const associationInput = document.getElementById('your_association');
    const initialValue = associationInput ? associationInput.value : '';
    window.Alpine.store('selectedAssociation', initialValue);
  }

  // Initialize Choices.js (if needed as fallback)
  if (typeof Choices !== 'undefined') {
    const associationElement = document.querySelector('.choices-association');
    if (associationElement) {
      new Choices(associationElement, {
        searchEnabled: false,
        itemSelectText: '',
        shouldSort: false,
        position: 'bottom',
        classNames: {
          containerOuter: 'choices choices-outer',
          containerInner: 'choices__inner choices-inner',
          item: 'choices__item choices__item--selectable',
          itemSelectable: 'choices__item--selectable',
          highlightedState: 'is-highlighted',
          selectedState: 'is-selected'
        }
      });
    }
  }
  // Elements
  const avatarBtn = document.getElementById("lci-avatar-btn");
  const avatarInput = document.getElementById("lci-avatar-input");
  const avatarImage = document.getElementById("lci-avatar-img");
  const profileForm = document.getElementById("lci-profile-form");
  const passwordForm = document.getElementById("lci-password-form");
  const passwordToggles = document.querySelectorAll(".password-toggle");
  const newPasswordInput = document.getElementById("lci-new-password");
  const confirmPasswordInput = document.getElementById("lci-confirm-password");
  const passwordStrengthBar = document.querySelector(".progress-bar");
  const passwordStrengthText = document.getElementById("password-strength-text");
  const passwordMatchText = document.getElementById("password-match");

  // Helper function for showing notifications
  const showNotification = (title, message, type) => {
    // Use our custom Alpine.js modal instead of SweetAlert
    showLciModal(
      title,
      message,
      type,
      true, // showConfirmButton
      'OK',
      false, // showCancelButton
      'Cancel',
      type === 'success', // autoClose
      type === 'success' ? 3000 : 0 // autoCloseDelay
    );
  };

  // Avatar upload functionality
  const avatarContainer = document.querySelector('.avatar-container');

  // Make the avatar container clickable only if it exists, avatarInput exists,
  // and it's not the dashboard avatar (which should just redirect to profile)
  if (avatarContainer && avatarInput && !avatarContainer.classList.contains('dashboard-avatar')) {
    avatarContainer.addEventListener("click", (e) => {
      e.preventDefault();
      avatarInput.click();
    });
  }

  // Keep the button functionality as well
  avatarBtn?.addEventListener("click", (e) => {
    e.preventDefault();
    avatarInput.click();
  });

  avatarInput?.addEventListener("change", async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      showNotification("Error", "Please select a valid image file", "error");
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      showNotification("Error", "Image file size should be less than 2MB", "error");
      return;
    }

    // Show loading state
    avatarImage.classList.add("avatar-glow");
    avatarImage.classList.add("avatar-uploading");

    const formData = new FormData();
    formData.append("action", "lci_upload_avatar");
    formData.append("avatar", file);

    try {
      const response = await fetch(lci_ajax.ajax_url, {
        method: "POST",
        body: formData
      });

      const result = await response.json();

      if (result.success && result.data.url) {
        avatarImage.src = result.data.url;
        showNotification("Success", "Avatar updated successfully!", "success");
      } else {
        showNotification("Error", result.data?.message || "Upload failed", "error");
      }
    } catch (error) {
      showNotification("Error", "An error occurred while uploading the avatar", "error");
      console.error("Avatar upload error:", error);
    } finally {
      avatarImage.classList.remove("avatar-glow");
      avatarImage.classList.remove("avatar-uploading");
    }
  });

  // Profile update functionality
  profileForm?.addEventListener("submit", async (e) => {
    e.preventDefault();

    const email = document.getElementById("lci-email").value;
    const phone = document.getElementById("lci-phone").value;
    const association = document.getElementById("your_association").value;
    const clubNo = document.getElementById("lci-club-no").value;
    const clubPosition = document.getElementById("lci-club-position").value;
    const nonce = document.getElementById("lci_profile_nonce").value;

    // Get the selected dietary preference
    const dietRadios = document.querySelectorAll('input[name="specific_diet"]');
    let specificDiet = '';
    for (const radio of dietRadios) {
      if (radio.checked) {
        specificDiet = radio.value;
        break;
      }
    }

    // Get the selected allergies
    const allergyCheckboxes = document.querySelectorAll('input[name="allergies[]"]');
    const allergies = [];
    for (const checkbox of allergyCheckboxes) {
      if (checkbox.checked) {
        allergies.push(checkbox.value);
      }
    }

    // Get association details based on selected association
    let associationDetails = {};
    if (association === 'RT') {
      const rtAssociation = document.getElementById('rt_association');
      if (rtAssociation) {
        associationDetails.rt_association = rtAssociation.value;
        // Clear other association fields to avoid conflicts
        associationDetails.lc_association = '';
        associationDetails.tangent_association = '';
        associationDetails.agora_association = '';
        associationDetails.c41_club = '';
      }
    } else if (association === 'LC') {
      const lcAssociation = document.getElementById('lc_association');
      if (lcAssociation) {
        associationDetails.lc_association = lcAssociation.value;
        // Clear other association fields
        associationDetails.rt_association = '';
        associationDetails.tangent_association = '';
        associationDetails.agora_association = '';
        associationDetails.c41_club = '';
      }
    } else if (association === 'TANGENT') {
      const tangentAssociation = document.getElementById('tangent_association');
      if (tangentAssociation) {
        associationDetails.tangent_association = tangentAssociation.value;
        // Clear other association fields
        associationDetails.rt_association = '';
        associationDetails.lc_association = '';
        associationDetails.agora_association = '';
        associationDetails.c41_club = '';
      }
    } else if (association === 'AGORA') {
      const agoraAssociation = document.getElementById('agora_association');
      if (agoraAssociation) {
        associationDetails.agora_association = agoraAssociation.value;
        // Clear other association fields
        associationDetails.rt_association = '';
        associationDetails.lc_association = '';
        associationDetails.tangent_association = '';
        associationDetails.c41_club = '';
      }
    } else if (association === 'C41') {
      const c41Club = document.getElementById('c41_club');
      if (c41Club) {
        associationDetails.c41_club = c41Club.value;
        // Clear other association fields
        associationDetails.rt_association = '';
        associationDetails.lc_association = '';
        associationDetails.tangent_association = '';
        associationDetails.agora_association = '';
      }
    } else {
      // If 'Other' or no association is selected, clear all association fields
      associationDetails.rt_association = '';
      associationDetails.lc_association = '';
      associationDetails.tangent_association = '';
      associationDetails.agora_association = '';
      associationDetails.c41_club = '';
    }

    // Validate email
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      showNotification("Error", "Please enter a valid email address", "error");
      return;
    }

    // Show loading state
    const submitBtn = profileForm.querySelector("button[type='submit']");
    const originalBtnText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    submitBtn.disabled = true;

    try {
      const response = await fetch(lci_ajax.ajax_url, {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams({
          action: "lci_update_profile",
          email: email,
          phone: phone,
          association: association,
          club_no: clubNo,
          club_position: clubPosition,
          specific_diet: specificDiet,
          allergies: JSON.stringify(allergies),
          ...associationDetails,
          security: nonce
        })
      });

      const result = await response.json();

      if (result.success) {
        showNotification("Success", "Profile updated successfully!", "success");
      } else {
        showNotification("Error", result.data?.message || "Failed to update profile", "error");
      }
    } catch (error) {
      showNotification("Error", "An error occurred while updating your profile", "error");
      console.error("Profile update error:", error);
    } finally {
      submitBtn.innerHTML = originalBtnText;
      submitBtn.disabled = false;
    }
  });

  // Password toggle functionality
  passwordToggles.forEach(toggle => {
    toggle.addEventListener("click", () => {
      const input = toggle.parentElement.querySelector("input");
      const icon = toggle.querySelector("i");

      if (input.type === "password") {
        input.type = "text";
        icon.classList.remove("fa-eye");
        icon.classList.add("fa-eye-slash");
      } else {
        input.type = "password";
        icon.classList.remove("fa-eye-slash");
        icon.classList.add("fa-eye");
      }
    });
  });

  // Only run password-related code if the password form exists
  if (passwordForm && newPasswordInput && confirmPasswordInput) {
    // Password strength checker
    newPasswordInput.addEventListener("input", () => {
      const password = newPasswordInput.value;
    let strength = 0;
    let feedback = "None";

    if (password.length > 0) {
      // Length check
      if (password.length >= 8) strength += 25;

      // Complexity checks
      if (/[A-Z]/.test(password)) strength += 25; // Uppercase
      if (/[a-z]/.test(password)) strength += 25; // Lowercase
      if (/[0-9]/.test(password)) strength += 12.5; // Numbers
      if (/[^A-Za-z0-9]/.test(password)) strength += 12.5; // Special chars

      // Set feedback text
      if (passwordStrengthBar) {
        if (strength < 25) {
          feedback = "Very Weak";
          passwordStrengthBar.className = "progress-bar bg-danger";
        } else if (strength < 50) {
          feedback = "Weak";
          passwordStrengthBar.className = "progress-bar bg-warning";
        } else if (strength < 75) {
          feedback = "Good";
          passwordStrengthBar.className = "progress-bar bg-info";
        } else {
          feedback = "Strong";
          passwordStrengthBar.className = "progress-bar bg-success";
        }
      }
    }

    // Update UI
    if (passwordStrengthBar) {
      passwordStrengthBar.style.width = `${strength}%`;
      passwordStrengthBar.setAttribute("aria-valuenow", strength);
    }

    if (passwordStrengthText) {
      passwordStrengthText.textContent = feedback;
    }

    // Check password match if confirm field has value
    if (confirmPasswordInput && confirmPasswordInput.value) {
      checkPasswordMatch();
    }
  });

  // Password match checker
  const checkPasswordMatch = () => {
    // Check if all required elements exist
    if (!newPasswordInput || !confirmPasswordInput || !passwordMatchText) {
      return;
    }

    const password = newPasswordInput.value;
    const confirmPassword = confirmPasswordInput.value;

    if (!confirmPassword) {
      passwordMatchText.textContent = "";
      passwordMatchText.className = "form-text";
      return;
    }

    if (password === confirmPassword) {
      passwordMatchText.textContent = "Passwords match";
      passwordMatchText.className = "form-text text-success";
    } else {
      passwordMatchText.textContent = "Passwords do not match";
      passwordMatchText.className = "form-text text-danger";
    }
  };

  confirmPasswordInput?.addEventListener("input", checkPasswordMatch);

  // Password change functionality
  passwordForm?.addEventListener("submit", async (e) => {
    e.preventDefault();

    const currentPassword = document.getElementById("lci-current-password").value;
    const newPassword = newPasswordInput.value;
    const confirmPassword = confirmPasswordInput.value;

    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      showNotification("Error", "All password fields are required", "error");
      return;
    }

    if (newPassword !== confirmPassword) {
      showNotification("Error", "New passwords do not match", "error");
      return;
    }

    if (newPassword.length < 8) {
      showNotification("Error", "Password must be at least 8 characters long", "error");
      return;
    }

    // Show loading state
    const submitBtn = passwordForm.querySelector("button[type='submit']");
    const originalBtnText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;

    try {
      const response = await fetch(lci_ajax.ajax_url, {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams({
          action: "lci_update_password",
          current_password: currentPassword,
          password: newPassword
        })
      });

      const result = await response.json();

      if (result.success) {
        showNotification("Success", "Password changed successfully!", "success");
        passwordForm.reset();

        // Reset UI elements if they exist
        if (passwordStrengthBar) {
          passwordStrengthBar.style.width = "0%";
        }

        if (passwordStrengthText) {
          passwordStrengthText.textContent = "None";
        }

        if (passwordMatchText) {
          passwordMatchText.textContent = "";
        }
      } else {
        showNotification("Error", result.data?.message || "Failed to change password", "error");
      }
    } catch (error) {
      showNotification("Error", "An error occurred while changing your password", "error");
      console.error("Password change error:", error);
    } finally {
      submitBtn.innerHTML = originalBtnText;
      submitBtn.disabled = false;
    }
  });
  } // Close the passwordForm conditional block
});
