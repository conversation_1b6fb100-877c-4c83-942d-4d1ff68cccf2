<?php
/**
 * My Invoices view for LCI 2025 Dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Get all customer orders
$customer_orders = wc_get_orders([
    'customer' => $user_id,
    'limit' => -1,
    'status' => ['processing', 'completed', 'on-hold'],
    'orderby' => 'date',
    'order' => 'DESC',
]);

// Filter orders to include those with invoice or proforma data
$invoiced_orders = [];
$proforma_orders = [];

foreach ($customer_orders as $order) {
    $order_status = $order->get_status();
    $order_data = [
        'order_id' => $order->get_id(),
        'order_number' => $order->get_order_number(),
        'order_status' => $order_status,
        'order_date' => date('F j, Y', strtotime($order->get_date_created())),
        'total' => $order->get_formatted_order_total(),
    ];

    // Check for invoice data (completed orders)
    $invoice_link = $order->get_meta('oblio_invoice_link');
    if (!empty($invoice_link)) {
        $invoice_series = $order->get_meta('oblio_invoice_series_name');
        $invoice_number = $order->get_meta('oblio_invoice_number');
        $invoice_date = $order->get_meta('oblio_invoice_date');

        // Format the invoice date if it exists
        $formatted_date = !empty($invoice_date) ? date('F j, Y', strtotime($invoice_date)) : date('F j, Y', strtotime($order->get_date_created()));

        $order_data['invoice_series'] = $invoice_series;
        $order_data['invoice_number'] = $invoice_number;
        $order_data['invoice_date'] = $formatted_date;
        $order_data['invoice_link'] = $invoice_link;
        $order_data['document_type'] = 'invoice';

        $invoiced_orders[] = $order_data;
    }

    // Check for proforma data (on-hold or processing orders)
    if ($order_status === 'on-hold' || $order_status === 'processing') {
        $proforma_link = $order->get_meta('oblio_proforma_link');
        if (!empty($proforma_link)) {
            $proforma_series = $order->get_meta('oblio_proforma_series_name');
            $proforma_number = $order->get_meta('oblio_proforma_number');
            $proforma_date = $order->get_meta('oblio_proforma_date');

            // Format the proforma date if it exists
            $formatted_date = !empty($proforma_date) ? date('F j, Y', strtotime($proforma_date)) : date('F j, Y', strtotime($order->get_date_created()));

            $order_data['proforma_series'] = $proforma_series;
            $order_data['proforma_number'] = $proforma_number;
            $order_data['proforma_date'] = $formatted_date;
            $order_data['proforma_link'] = $proforma_link;
            $order_data['document_type'] = 'proforma';

            $proforma_orders[] = $order_data;
        }
    }
}
?>

<div class="my-invoices-container">
    <!-- Header Section - 2025 UX/UI Style -->
    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 40px 20px; position: relative; overflow: hidden; border-radius: 0 0 30px 30px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2); margin-bottom: 40px;">
        <!-- Animated background elements -->
        <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%); border-radius: 50%; animation: pulse 8s infinite alternate;"></div>
        <div class="position-absolute" style="bottom: -30px; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%); border-radius: 50%; animation: float 12s infinite alternate;"></div>
        <div class="position-absolute" style="top: 20%; right: 20%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%); border-radius: 50%; animation: float 7s infinite alternate-reverse;"></div>

        <!-- Glassmorphism overlay -->
        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>

        <div class="container position-relative" style="z-index: 2;">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <!-- Modern 3D icon with shadow and glow -->
                    <div style="background: rgba(255, 255, 255, 0.2); width: 60px; height: 60px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); transform: perspective(500px) rotateX(10deg); border: 1px solid rgba(255, 255, 255, 0.3);">
                        <div style="position: relative;">
                            <i class="fas fa-file-invoice" style="color: white; font-size: 28px; filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.8), transparent 70%); filter: blur(8px); opacity: 0.5; animation: glow 3s infinite alternate;"></div>
                        </div>
                    </div>

                    <!-- Typography with modern styling -->
                    <div>
                        <h1 style="color: white; font-size: 28px; font-weight: 800; margin: 0; letter-spacing: -0.5px; text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">My Invoices</h1>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; margin: 0; margin-top: 4px; font-weight: 300; letter-spacing: 0.5px;">View and download your invoices and proforma documents</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices Section - 2025 UX/UI Style -->
    <div class="section-container" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden; margin-bottom: 30px;">
        <!-- Decorative elements -->
        <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
        <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

        <!-- Section header with 3D effect -->
        <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
            <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                <i class="fas fa-file-invoice-dollar" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
            </div>
            <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Invoices</h3>
        </div>

        <?php if (empty($invoiced_orders)) : ?>
            <!-- Empty state with modern styling -->
            <div style="background: rgba(54, 177, 220, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 20px; border: 1px solid rgba(54, 177, 220, 0.2); display: flex; align-items: flex-start; position: relative; z-index: 1;">
                <div style="background: rgba(54, 177, 220, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                    <i class="fas fa-info-circle" style="color: #36b1dc; font-size: 20px;"></i>
                </div>
                <div style="flex: 1;">
                    <p style="color: #2c3e50; margin: 0; font-size: 15px; line-height: 1.6;">You don't have any invoices yet. Invoices are generated automatically after your payment is processed and your order is completed.</p>
                </div>
            </div>
        <?php else : ?>
            <!-- Invoices cards with modern styling -->
            <div style="display: grid; grid-template-columns: repeat(1, 1fr); gap: 20px; margin-top: 20px;">
                <?php foreach ($invoiced_orders as $index => $order) : ?>
                    <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.8); transition: all 0.3s ease; animation: fadeIn 0.5s ease forwards; animation-delay: calc(<?php echo $index; ?> * 0.1s); opacity: 0;"
                        onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.1)';"
                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0, 0, 0, 0.05)';">

                        <!-- Card Header with gradient background -->
                        <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 20px; color: white; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1), transparent 70%);"></div>

                            <div style="display: flex; justify-content: space-between; align-items: center; position: relative; z-index: 1;">
                                <h4 style="margin: 0; font-weight: 600; font-size: 18px; color: white; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">
                                    <?php
                                    if (!empty($order['invoice_series']) && !empty($order['invoice_number'])) {
                                        echo esc_html('Invoice ' . $order['invoice_series'] . $order['invoice_number']);
                                    } else {
                                        echo 'Invoice #' . esc_html($order['order_number']);
                                    }
                                    ?>
                                </h4>

                                <div style="display: flex; align-items: center; background-color: rgba(255, 255, 255, 0.2); padding: 5px 10px; border-radius: 20px;">
                                    <i class="fas fa-check-circle" style="color: #4cd964; margin-right: 5px; font-size: 14px;"></i>
                                    <span style="font-size: 14px; color: white; font-weight: 500;">Completed</span>
                                </div>
                            </div>
                        </div>

                        <!-- Card Content -->
                        <div style="padding: 20px;">
                            <div style="display: flex; flex-direction: column; gap: 15px;">
                                <!-- Invoice Details -->
                                <div style="display: flex; align-items: center; gap: 15px; background-color: #f8f9fa; padding: 12px; border-radius: 10px; margin-bottom: 10px;">
                                    <div style="flex: 1;">
                                        <div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Invoice Date</div>
                                        <div style="display: flex; align-items: center;">
                                            <i class="far fa-calendar-alt" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>
                                            <span style="font-size: 14px; color: #2c3e50; font-weight: 500;"><?php echo esc_html($order['invoice_date']); ?></span>
                                        </div>
                                    </div>

                                    <div style="width: 1px; height: 30px; background-color: #dee2e6;"></div>

                                    <div style="flex: 1;">
                                        <div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Order Number</div>
                                        <div style="display: flex; align-items: center;">
                                            <i class="fas fa-shopping-cart" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>
                                            <span style="font-size: 14px; color: #2c3e50; font-weight: 500;">#<?php echo esc_html($order['order_number']); ?></span>
                                        </div>
                                    </div>

                                    <div style="width: 1px; height: 30px; background-color: #dee2e6;"></div>

                                    <div style="flex: 1;">
                                        <div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Amount</div>
                                        <div style="display: flex; align-items: center;">
                                            <i class="fas fa-money-bill-wave" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>
                                            <span style="font-size: 14px; color: #2c3e50; font-weight: 500;"><?php echo wp_kses_post($order['total']); ?></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Download Button -->
                                <a href="<?php echo esc_url($order['invoice_link']); ?>" target="_blank" style="display: inline-flex; width: 100%; align-items: center; justify-content: center; padding: 12px 20px; background-color: #36b1dc; color: white; font-weight: 600; border-radius: 12px; box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3); transition: all 0.3s ease; text-decoration: none; text-transform: uppercase; font-size: 14px; letter-spacing: 0.5px; white-space: nowrap;">
                                    <i class="fas fa-download me-2" style="color: white;"></i> <span>Download Invoice</span>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Proforma Invoices Section - 2025 UX/UI Style -->
    <?php if (!empty($proforma_orders)) : ?>
    <div class="section-container" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden; margin-bottom: 30px;">
        <!-- Decorative elements -->
        <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 193, 7, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
        <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(255, 193, 7, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

        <!-- Section header with 3D effect -->
        <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
            <div style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(255, 193, 7, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                <i class="fas fa-file-invoice" style="color: #ffc107; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
            </div>
            <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Proforma Invoices</h3>
        </div>

        <!-- Proforma cards with modern styling -->
        <div style="display: grid; grid-template-columns: repeat(1, 1fr); gap: 20px; margin-top: 20px;">
            <?php foreach ($proforma_orders as $index => $order) : ?>
                <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.8); transition: all 0.3s ease; animation: fadeIn 0.5s ease forwards; animation-delay: calc(<?php echo $index; ?> * 0.1s); opacity: 0;"
                    onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.1)';"
                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0, 0, 0, 0.05)';">

                    <!-- Card Header with gradient background -->
                    <div style="background: linear-gradient(135deg, #ffc107, #ff9800); padding: 20px; color: white; position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1), transparent 70%);"></div>

                        <div style="display: flex; justify-content: space-between; align-items: center; position: relative; z-index: 1;">
                            <h4 style="margin: 0; font-weight: 600; font-size: 18px; color: white; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">
                                <?php
                                if (!empty($order['proforma_series']) && !empty($order['proforma_number'])) {
                                    echo esc_html('Proforma ' . $order['proforma_series'] . $order['proforma_number']);
                                } else {
                                    echo 'Proforma #' . esc_html($order['order_number']);
                                }
                                ?>
                            </h4>

                            <div style="display: flex; align-items: center; background-color: rgba(255, 255, 255, 0.2); padding: 5px 10px; border-radius: 20px;">
                                <i class="fas fa-clock" style="color: white; margin-right: 5px; font-size: 14px;"></i>
                                <span style="font-size: 14px; color: white; font-weight: 500;"><?php echo ucfirst($order['order_status']); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Card Content -->
                    <div style="padding: 20px;">
                        <div style="display: flex; flex-direction: column; gap: 15px;">
                            <!-- Proforma Details -->
                            <div style="display: flex; align-items: center; gap: 15px; background-color: #f8f9fa; padding: 12px; border-radius: 10px; margin-bottom: 10px;">
                                <div style="flex: 1;">
                                    <div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Proforma Date</div>
                                    <div style="display: flex; align-items: center;">
                                        <i class="far fa-calendar-alt" style="color: #ffc107; font-size: 14px; margin-right: 5px;"></i>
                                        <span style="font-size: 14px; color: #2c3e50; font-weight: 500;"><?php echo esc_html($order['proforma_date']); ?></span>
                                    </div>
                                </div>

                                <div style="width: 1px; height: 30px; background-color: #dee2e6;"></div>

                                <div style="flex: 1;">
                                    <div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Order Number</div>
                                    <div style="display: flex; align-items: center;">
                                        <i class="fas fa-shopping-cart" style="color: #ffc107; font-size: 14px; margin-right: 5px;"></i>
                                        <span style="font-size: 14px; color: #2c3e50; font-weight: 500;">#<?php echo esc_html($order['order_number']); ?></span>
                                    </div>
                                </div>

                                <div style="width: 1px; height: 30px; background-color: #dee2e6;"></div>

                                <div style="flex: 1;">
                                    <div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Amount</div>
                                    <div style="display: flex; align-items: center;">
                                        <i class="fas fa-money-bill-wave" style="color: #ffc107; font-size: 14px; margin-right: 5px;"></i>
                                        <span style="font-size: 14px; color: #2c3e50; font-weight: 500;"><?php echo wp_kses_post($order['total']); ?></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Download Button -->
                            <a href="<?php echo esc_url($order['proforma_link']); ?>" target="_blank" style="display: inline-flex; width: 100%; align-items: center; justify-content: center; padding: 12px 20px; background-color: #ffc107; color: white; font-weight: 600; border-radius: 12px; box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3); transition: all 0.3s ease; text-decoration: none; text-transform: uppercase; font-size: 14px; letter-spacing: 0.5px; white-space: nowrap;">
                                <i class="fas fa-download me-2" style="color: white;"></i> <span>Download Proforma</span>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Info Section -->
    <div class="mt-4 mb-5">
        <div style="background: rgba(54, 177, 220, 0.05); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 20px; border: 1px solid rgba(54, 177, 220, 0.1); display: flex; align-items: flex-start;">
            <div style="background: rgba(54, 177, 220, 0.1); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                <i class="fas fa-info-circle" style="color: #36b1dc; font-size: 20px;"></i>
            </div>
            <div style="flex: 1;">
                <p style="color: #2c3e50; margin: 0; font-size: 15px; line-height: 1.6;">
                    Invoices are generated automatically after your payment is processed. Proforma invoices are issued for orders that are pending payment. If you need assistance with your invoices, please contact support.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
    /* Animations */
    @keyframes pulse {
        0% { transform: scale(1); opacity: 0.5; }
        100% { transform: scale(1.2); opacity: 0.2; }
    }
    @keyframes float {
        0% { transform: translateY(0) translateX(0); }
        50% { transform: translateY(-20px) translateX(10px); }
        100% { transform: translateY(10px) translateX(-10px); }
    }
    @keyframes glow {
        0% { opacity: 0.3; }
        100% { opacity: 0.7; }
    }
    @keyframes fadeIn {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    /* Mobile Responsive Styles */
    @media (max-width: 767px) {
        /* Reduce padding on all containers */
        .container {
            padding-left: 10px !important;
            padding-right: 10px !important;
        }

        /* Make cards take full width with no padding */
        .section-container {
            padding: 0 !important;
            border-radius: 20px !important;
        }

        /* Adjust card styles */
        [style*="border-radius: 20px"] {
            border-radius: 15px !important;
        }

        /* Add padding only to content areas */
        [style*="padding: 20px; flex-grow: 1;"],
        [style*="padding: 20px;"] {
            padding: 15px !important;
        }

        /* Make booking info rows stack on mobile */
        [style*="display: flex; align-items: center; gap: 15px; background-color: #f8f9fa;"] {
            flex-direction: column !important;
            gap: 10px !important;
        }

        /* Remove divider on mobile */
        [style*="width: 1px; height: 30px; background-color: #dee2e6;"] {
            display: none !important;
        }

        /* Adjust spacing */
        .mb-5 {
            margin-bottom: 15px !important;
        }

        /* Make header more compact */
        h1 {
            font-size: 24px !important;
        }

        h3 {
            font-size: 18px !important;
        }

        p {
            font-size: 14px !important;
        }

        /* Center align text on mobile */
        h4, p {
            text-align: center !important;
        }

        /* Make button text smaller on mobile to fit on one line */
        [style*="display: inline-flex; width: 100%; align-items: center; justify-content: center"] {
            font-size: 12px !important;
            padding: 10px !important;
            white-space: nowrap !important;
        }

        /* Keep icons visible but make text smaller */
        [style*="display: inline-flex; width: 100%; align-items: center; justify-content: center"] span {
            font-size: 12px !important;
        }
    }

    .my-invoices-container .btn-primary {
        background-color: var(--primary-color, #36b1dc);
        border-color: var(--primary-color, #36b1dc);
    }

    .my-invoices-container .btn-primary:hover {
        background-color: #2a8fb3;
        border-color: #2a8fb3;
    }
</style>
