// Accommodation Wizard Alpine.js Component
function accommodationWizard() {
    return {
        // Wizard state
        currentStep: 1,
        flowStep: 1,
        totalSteps: 4,
        progress: 25,
        loading: false,
        showModal: false,
        modalProgress: 0,
        progressInterval: null,
        isLastStep: false,
        wizardCompleted: false,
        
        // User data
        hasMainPretour: false,
        bucharest: {
            selected: null, // 'yes' or 'no'
            nights: 1
        },
        brasov: {
            selected: null, // 'yes', 'no', or 'type'
            type: null, // 'pre', 'main', or 'post'
            nights: 1
        },
        
        // Products data
        products: [],
        selectedProduct: null,
        
        // Initialize the wizard
        init() {
            console.log('Initializing accommodation wizard');
            // Check if user has Main Pretour (product ID 743)
            this.checkUserProducts();
            
            // Update progress bar
            this.updateProgress();
        },
        
        // Check if user has purchased specific products
        async checkUserProducts() {
            this.loading = true;
            console.log('Checking if user has Main Pretour (product ID 743)');
            
            try {
                const response = await fetch(lci_ajax.ajax_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'check_user_products',
                        security: lci_ajax.nonce,
                        product_id: 743 // Main Pretour ID
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('User products check response:', data);
                
                if (data.success && data.data) {
                    this.hasMainPretour = data.data.has_product;
                    console.log('User has Main Pretour:', this.hasMainPretour);
                }
            } catch (error) {
                console.error('Error checking user products:', error);
                // Fallback to false if there's an error
                this.hasMainPretour = false;
            } finally {
                this.loading = false;
            }
        },
        
        // Select Bucharest option (Yes/No)
        selectBucharest(option) {
            console.log('Selecting Bucharest option:', option);
            this.bucharest.selected = option;
            
            // Auto-advance if they select 'no'
            if (option === 'no') {
                this.flowStep = 4; // Skip to Brasov options
                setTimeout(() => this.nextStep(), 300);
            } else if (option === 'yes') {
                this.flowStep = 2; // Go to nights selection
                setTimeout(() => this.nextStep(), 300);
            }
        },
        
        // Select Brasov Yes/No option
        selectBrasovYesNo(option) {
            console.log('Selecting Brasov Yes/No option:', option);
            this.brasov.selected = option;
            
            if (option === 'yes') {
                // If they select yes, change to type selection
                setTimeout(() => {
                    this.brasov.selected = 'type';
                    this.flowStep = 1; // Set flow step for Brasov type selection
                    this.nextStep();
                }, 300);
            } else if (option === 'no') {
                // If they select no, complete the wizard
                setTimeout(() => {
                    this.wizardCompleted = true;
                    // Redirect to accommodation page
                    window.location.href = '?tab=accommodation&wizard-completed=1';
                }, 1000);
            }
        },
        
        // Select Brasov type option (pre/main/post)
        selectBrasovType(option) {
            console.log('Selecting Brasov type option:', option);
            this.brasov.type = option;
            
            if (option === 'pre' || option === 'post') {
                // For pre/post event, go to nights selection
                this.flowStep = this.hasMainPretour ? 5 : 2;
                setTimeout(() => this.nextStep(), 300);
            } else if (option === 'main') {
                // For main event, go directly to products
                this.flowStep = this.hasMainPretour ? 6 : 3;
                setTimeout(() => {
                    this.loadProducts('brasov');
                    this.nextStep();
                }, 300);
            }
        },
        
        // Increase nights count
        increaseNights(location) {
            console.log(`Increasing ${location} nights`);
            if (location === 'bucharest') {
                this.bucharest.nights++;
            } else {
                this.brasov.nights++;
            }
        },
        
        // Decrease nights count
        decreaseNights(location) {
            console.log(`Decreasing ${location} nights`);
            if (location === 'bucharest' && this.bucharest.nights > 1) {
                this.bucharest.nights--;
            } else if (location === 'brasov' && this.brasov.nights > 1) {
                this.brasov.nights--;
            }
        },
        
        // Get step title based on current step
        getStepTitle() {
            if (this.hasMainPretour) {
                // Titles for users with Main Pretour
                if (this.currentStep === 1) {
                    return 'Bucharest Accommodation';
                } else if (this.currentStep === 2) {
                    // Check if we're asking about Bucharest or Brasov nights
                    if (this.bucharest.selected === 'yes' && this.flowStep === 2) {
                        return 'Nights in Bucharest';
                    } else if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                        return 'Nights in Brasov';
                    } else {
                        return 'Brasov Accommodation';
                    }
                } else if (this.currentStep === 3) {
                    // Check if we're showing Bucharest or Brasov products
                    if (this.bucharest.selected === 'yes' && this.flowStep === 3) {
                        return 'Bucharest Accommodation Options';
                    } else {
                        return 'Brasov Accommodation Options';
                    }
                } else if (this.currentStep === 4) {
                    return 'Brasov Accommodation Type';
                }
            } else {
                // Titles for users without Main Pretour
                if (this.currentStep === 1) {
                    return 'Brasov Accommodation';
                } else if (this.currentStep === 2) {
                    if (this.brasov.selected === 'type') {
                        return 'Brasov Accommodation Type';
                    } else if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                        return 'Nights in Brasov';
                    }
                    return 'Brasov Accommodation Type';
                } else if (this.currentStep === 3) {
                    return 'Brasov Accommodation Options';
                }
            }
            
            return 'Accommodation Wizard';
        },
        
        // Get products title based on current selection
        getProductsTitle() {
            if (this.hasMainPretour && this.bucharest.selected === 'yes' && this.flowStep === 3) {
                return 'Bucharest Accommodation Options';
            } else if (this.brasov.type === 'pre') {
                return 'Pre-event Brasov Accommodation';
            } else if (this.brasov.type === 'main') {
                return 'Main Event Brasov Accommodation';
            } else if (this.brasov.type === 'post') {
                return 'Post-event Brasov Accommodation';
            }
            
            return 'Accommodation Options';
        },
        
        // Update progress bar
        updateProgress() {
            const totalSteps = this.hasMainPretour ? 4 : 3;
            this.totalSteps = totalSteps;
            this.progress = (this.currentStep / totalSteps) * 100;
            
            // Check if this is the last step
            this.isLastStep = (this.currentStep === totalSteps);
        },
        
        // Go to next step
        nextStep() {
            console.log('Current step before next:', this.currentStep, 'Flow step:', this.flowStep);
            
            if (this.hasMainPretour) {
                // Logic for users with Main Pretour
                if (this.currentStep === 1) {
                    // From initial question to next step
                    this.currentStep++;
                } else if (this.currentStep === 2) {
                    // From nights selection or Brasov options to products
                    if (this.bucharest.selected === 'yes' && this.flowStep === 2) {
                        // From Bucharest nights to Bucharest products
                        this.flowStep = 3;
                        this.loadProducts('bucharest');
                        this.currentStep++;
                    } else if (this.brasov.type && this.flowStep === 5) {
                        // From Brasov nights to Brasov products
                        this.flowStep = 6;
                        this.loadProducts('brasov');
                        this.currentStep++;
                    }
                } else if (this.currentStep === 3 && this.bucharest.selected === 'yes') {
                    // From Bucharest products to Brasov options
                    this.flowStep = 4;
                    this.currentStep++;
                } else {
                    // Regular progression
                    this.currentStep++;
                }
            } else {
                // Logic for users without Main Pretour
                if (this.currentStep === 1) {
                    // From initial question to next step
                    this.currentStep++;
                } else if (this.currentStep === 2) {
                    // From Brasov type or nights to products
                    if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                        // From nights selection to products
                        this.flowStep = 3;
                        this.loadProducts('brasov');
                    } else if (this.brasov.type === 'main') {
                        // Already loaded products for main event
                        this.flowStep = 3;
                    }
                    this.currentStep++;
                } else {
                    // Regular progression
                    this.currentStep++;
                }
            }
            
            this.updateProgress();
            console.log('Current step after next:', this.currentStep, 'Flow step:', this.flowStep);
        },
        
        // Go to previous step
        prevStep() {
            console.log('Current step before prev:', this.currentStep, 'Flow step:', this.flowStep);
            
            if (this.currentStep > 1) {
                if (this.hasMainPretour) {
                    // Logic for users with Main Pretour
                    if (this.currentStep === 4 && this.bucharest.selected === 'yes') {
                        // From Brasov options back to Bucharest products
                        this.flowStep = 3;
                        this.currentStep--;
                    } else if (this.currentStep === 3) {
                        // From products back to nights selection or initial question
                        if (this.bucharest.selected === 'yes' && this.flowStep === 3) {
                            // From Bucharest products to nights selection
                            this.flowStep = 2;
                            this.currentStep--;
                        } else if (this.flowStep === 6) {
                            // From Brasov products to nights selection
                            this.flowStep = 5;
                            this.currentStep--;
                        }
                    } else {
                        // Regular back navigation
                        this.currentStep--;
                    }
                } else {
                    // Logic for users without Main Pretour
                    if (this.currentStep === 3 && this.flowStep === 3) {
                        // From products back to nights selection or type selection
                        if (this.brasov.type === 'pre' || this.brasov.type === 'post') {
                            // Back to nights selection
                            this.flowStep = 2;
                        } else {
                            // Back to type selection
                            this.flowStep = 1;
                        }
                        this.currentStep--;
                    } else {
                        // Regular back navigation
                        this.currentStep--;
                    }
                }
                
                this.updateProgress();
            }
            
            console.log('Current step after prev:', this.currentStep, 'Flow step:', this.flowStep);
        },
        
        // Complete the wizard
        completeWizard() {
            console.log('Completing wizard');
            this.wizardCompleted = true;
            window.location.href = '?tab=accommodation&wizard-completed=1';
        },
        
        // Show success modal
        showSuccessModal() {
            this.showModal = true;
            this.startModalProgress();
        },
        
        // Close modal
        closeModal() {
            this.showModal = false;
            this.stopModalProgress();
        },
        
        // Start modal progress bar
        startModalProgress() {
            this.modalProgress = 0;
            clearInterval(this.progressInterval);
            
            const duration = 3000; // 3 seconds
            const interval = 30; // Update every 30ms
            const steps = duration / interval;
            const increment = 100 / steps;
            
            this.progressInterval = setInterval(() => {
                this.modalProgress += increment;
                
                if (this.modalProgress >= 100) {
                    this.stopModalProgress();
                    this.closeModal();
                }
            }, interval);
        },
        
        // Stop modal progress bar
        stopModalProgress() {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        },
        
        // Load products based on selection
        async loadProducts(type) {
            this.loading = true;
            this.products = [];
            console.log('Loading products for type:', type);

            let categoryId;
            let metaQuery = {};

            // Determine which products to load
            if (type === 'bucharest') {
                // Bucharest accommodation (category ID 33)
                categoryId = 33;
                if (this.bucharest.nights > 0) {
                    metaQuery = {
                        key: '_nights',
                        value: this.bucharest.nights.toString(),
                        compare: '='
                    };
                }
                console.log('Loading Bucharest products from category', categoryId, 'with nights:', this.bucharest.nights);
            } else if (type === 'brasov') {
                // Brasov accommodation
                switch (this.brasov.type) {
                    case 'pre':
                        categoryId = 37; // Pre-event accommodation
                        metaQuery = {
                            key: '_timing',
                            value: 'before',
                            compare: '='
                        };
                        console.log('Loading Pre-event Brasov products from category', categoryId);
                        break;
                    case 'main':
                        categoryId = 1; // Main event accommodation
                        console.log('Loading Main Event Brasov products from category', categoryId);
                        break;
                    case 'post':
                        categoryId = 37; // Post-event accommodation
                        metaQuery = {
                            key: '_timing',
                            value: 'after',
                            compare: '='
                        };
                        console.log('Loading Post-event Brasov products from category', categoryId);
                        break;
                    default:
                        console.error('Invalid Brasov type:', this.brasov.type);
                        this.loading = false;
                        return;
                }
            } else {
                console.error('Invalid product type:', type);
                this.loading = false;
                return;
            }

            try {
                console.log('Sending AJAX request to:', lci_ajax.ajax_url, 'with nonce:', lci_ajax.nonce);
                
                const response = await fetch(lci_ajax.ajax_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'get_accommodation_products',
                        security: lci_ajax.nonce,
                        category_id: categoryId,
                        meta_query: JSON.stringify(metaQuery)
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('AJAX response for products:', data);

                if (data.success && data.data && data.data.products) {
                    this.products = data.data.products;
                    console.log('Loaded', this.products.length, 'products');
                } else {
                    console.error('Error loading products:', data);
                    // Fallback to empty array if there's an error
                    this.products = [];
                }
            } catch (error) {
                console.error('Error loading products:', error);
                // Fallback to empty array if there's an error
                this.products = [];

                // Provide fallback products for testing if needed
                if (type === 'bucharest') {
                    this.products = [
                        {
                            id: 101,
                            name: 'Bucharest Hotel (Fallback)',
                            price: '€120',
                            price_html: '<span class="amount">€120</span> <small>per night</small>',
                            description: 'Fallback product for testing.',
                            image: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp',
                            added: false
                        }
                    ];
                } else if (type === 'brasov') {
                    this.products = [
                        {
                            id: 201,
                            name: 'Brasov Hotel (Fallback)',
                            price: '€100',
                            price_html: '<span class="amount">€100</span> <small>per night</small>',
                            description: 'Fallback product for testing.',
                            image: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp',
                            added: false
                        }
                    ];
                }
            } finally {
                this.loading = false;
            }
        },
        
        // Add product to cart
        async addToCart(productId) {
            const product = this.products.find(p => p.id === productId);
            if (!product) {
                console.error('Product not found:', productId);
                return;
            }

            this.loading = true;
            console.log('Adding product to cart:', product.name, '(ID:', productId, ')');

            try {
                const response = await fetch(lci_ajax.ajax_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'lci-dashboard-add-to-cart',
                        security: lci_ajax.nonce,
                        product_id: productId,
                        quantity: 1
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('AJAX response for add to cart:', data);

                if (data.success) {
                    // Show success modal
                    this.selectedProduct = product;
                    this.showSuccessModal();

                    // Mark product as added
                    product.added = true;

                    // Update mini cart if available
                    if (typeof updateMiniCart === 'function') {
                        try {
                            updateMiniCart(data.data.cart_count, data.data.cart_total);
                        } catch (e) {
                            console.warn('Could not update mini cart:', e);
                        }
                    }

                    console.log('Product added to cart successfully');
                } else {
                    console.error('Error adding product to cart:', data);
                    alert('Error adding product to cart. Please try again.');
                }
            } catch (error) {
                console.error('Error adding product to cart:', error);
                alert('Error adding product to cart. Please try again.');
            } finally {
                this.loading = false;
            }
        }
    };
}

// Make sure accommodationWizard is available globally
window.accommodationWizard = accommodationWizard;
