/**
 * Merchandise Shop JavaScript
 *
 * Handles the merchandise shop functionality for the shortcode
 * This is a clone of the regalia shop functionality
 */
(function($) {
    'use strict';

    // Wait for DOM to be ready
    $(document).ready(function() {
        console.log('Merchandise shop script loaded');

        // Initialize Bootstrap modals
        initializeModals();

        // Initialize variation cards
        initializeVariationCards();

        // Initialize quantity buttons
        initializeQuantityButtons();

        // Initialize add to cart buttons
        initializeAddToCart();

        // Initialize variable product functionality
        initializeVariationSelection();

        // Initialize sentinel for infinite scroll
        initializeSentinel();

        // Debug: Add click event listeners directly
        console.log('Adding direct click handlers for variation buttons');
        $('.show-variations-btn, .show-quantity-btn').on('click', function() {
            console.log('Button clicked:', this);
            const productCard = $(this).closest('.regalia-product-card');
            const variationCard = productCard.find('.regalia-variation-card');
            console.log('Variation card found:', variationCard.length);
            variationCard.addClass('active');
        });
    });

    /**
     * Initialize Bootstrap modals
     */
    function initializeModals() {
        // Fix for multiple modal backdrops
        const fixModalBackdrops = function() {
            // Remove duplicate modal backdrops
            const backdrops = document.querySelectorAll('.modal-backdrop');
            if (backdrops.length > 1) {
                for (let i = 1; i < backdrops.length; i++) {
                    backdrops[i].remove();
                }
            }
        };

        // Listen for modal events
        ['show.bs.modal', 'shown.bs.modal', 'hide.bs.modal', 'hidden.bs.modal'].forEach(function(event) {
            document.body.addEventListener(event, function() {
                setTimeout(fixModalBackdrops, 0);
            });
        });

        // Periodically check and clean up backdrops
        setInterval(fixModalBackdrops, 500);
    }

    /**
     * Initialize variation cards
     */
    function initializeVariationCards() {
        // Show variation card when clicking on "Order" button for variable products
        $(document).on('click', '.show-variations-btn', function() {
            const productCard = $(this).closest('.regalia-product-card');
            const variationCard = productCard.find('.regalia-variation-card');

            // Hide all other variation cards with animation
            $('.regalia-variation-card.active').each(function() {
                const card = $(this);
                card.addClass('closing');
                setTimeout(function() {
                    card.removeClass('active closing');
                }, 600); // Match the transition duration
            });

            // Show this variation card with animation
            setTimeout(function() {
                variationCard.addClass('active');
            }, 100);

            // Scroll to the product card
            $('html, body').animate({
                scrollTop: productCard.offset().top - 100
            }, 300);
        });

        // Show quantity card when clicking on "Order" button for simple products
        $(document).on('click', '.show-quantity-btn', function() {
            const productCard = $(this).closest('.regalia-product-card');
            const quantityCard = productCard.find('.regalia-quantity-card');

            // Hide all other variation cards with animation
            $('.regalia-variation-card.active').each(function() {
                const card = $(this);
                card.addClass('closing');
                setTimeout(function() {
                    card.removeClass('active closing');
                }, 600); // Match the transition duration
            });

            // Show this quantity card with animation
            setTimeout(function() {
                quantityCard.addClass('active');
            }, 100);

            // Scroll to the product card
            $('html, body').animate({
                scrollTop: productCard.offset().top - 100
            }, 300);
        });

        // Close variation card when clicking on close button
        $(document).on('click', '.close-variation-card', function() {
            const variationCard = $(this).closest('.regalia-variation-card');

            // Add closing class for animation
            variationCard.addClass('closing');

            // Remove active class after animation completes
            setTimeout(function() {
                variationCard.removeClass('active closing');
            }, 600); // Match the transition duration
        });

        // Close variation cards when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.regalia-variation-card').length &&
                !$(e.target).closest('.show-variations-btn').length &&
                !$(e.target).closest('.show-quantity-btn').length) {

                // Add closing class for animation
                $('.regalia-variation-card.active').each(function() {
                    const card = $(this);
                    card.addClass('closing');
                    setTimeout(function() {
                        card.removeClass('active closing');
                    }, 600); // Match the transition duration
                });
            }
        });
    }
    function initializeAddToCart() {
        // Add to cart button click handler for simple products
        $(document).on('click', '.add-to-cart-btn', function(e) {
            e.preventDefault();

            const button = $(this);
            const productId = button.data('product-id');
            const originalText = button.html();

            // Get quantity from input field
            const quantityInput = button.closest('.regalia-variation-card').find('.quantity-input-field');
            const quantity = quantityInput.length ? parseInt(quantityInput.val(), 10) : 1;

            // Show loading state
            button.html('<i class="fas fa-spinner fa-spin me-2"></i> Adding...');
            button.prop('disabled', true);

            // Get product title and image for confirmation modal
            const productCard = button.closest('.regalia-product-card');
            const productTitle = productCard.find('.regalia-product-title').text();
            const productImage = productCard.find('.regalia-product-image img').attr('src');

            // Close the variation card
            productCard.find('.regalia-variation-card').removeClass('active');

            // Add to cart via AJAX
            $.ajax({
                url: lci_ajax_object.ajax_url,
                type: 'POST',
                data: {
                    action: 'lci_merchandise_add_to_cart',
                    product_id: productId,
                    quantity: quantity,
                    security: lci_ajax_object.add_to_cart_nonce
                },
                success: function(response) {
                    // Reset button state
                    button.html(originalText);
                    button.prop('disabled', false);

                    if (response.success) {
                        // Show success message on the main product card button
                        const mainButton = productCard.find('.show-quantity-btn');
                        const originalMainText = mainButton.html();

                        mainButton.html('<i class="fas fa-check me-2"></i> Added to cart!');

                        // Reset button text after 2 seconds
                        setTimeout(function() {
                            mainButton.html(originalMainText);
                        }, 2000);

                        // Show confirmation modal
                        showAddToCartConfirmation(productTitle, productImage);

                        // Update mini cart
                        updateMiniCart(response.data);

                        // Trigger WooCommerce events
                        $(document.body).trigger('added_to_cart', [null, null, null]);
                        $(document.body).trigger('updated_cart_totals');
                    } else {
                        // Show error message
                        alert(response.data.message || 'Failed to add product to cart.');
                    }
                },
                error: function() {
                    // Reset button state
                    button.html(originalText);
                    button.prop('disabled', false);

                    // Show error message
                    alert('An error occurred. Please try again.');
                }
            });
        });
    }

    /**
     * Initialize variation selection functionality
     */
    function initializeVariationSelection() {
        // Add variable product to cart button click handler
        $(document).on('click', '.add-variable-to-cart-btn', function(e) {
            e.preventDefault();

            const button = $(this);
            const productId = button.data('product-id');
            const originalText = button.html();

            // Get the variation card
            const variationCard = button.closest('.regalia-variation-card');

            // Get all variation dropdowns
            const variationDropdowns = variationCard.find('.variation-dropdown');

            // Check if all variations are selected
            let allSelected = true;
            let variationData = {};

            variationDropdowns.each(function() {
                const dropdown = $(this);
                const attributeName = dropdown.data('attribute');
                const value = dropdown.val();

                if (!value) {
                    allSelected = false;
                    dropdown.addClass('is-invalid');
                } else {
                    dropdown.removeClass('is-invalid');
                    variationData['attribute_' + attributeName] = value;
                }
            });

            // If not all variations are selected, show error
            if (!allSelected) {
                alert('Please select all options before adding to cart.');
                return;
            }

            // Get quantity
            const quantityInput = variationCard.find('.quantity-input-field');
            const quantity = quantityInput.length ? parseInt(quantityInput.val(), 10) : 1;

            // Get product title and image for confirmation modal
            const productCard = button.closest('.regalia-product-card');
            let productTitle = productCard.find('.regalia-product-title').text();
            let productImage = productCard.find('.regalia-product-image img').attr('src');

            // Close the variation card
            variationCard.removeClass('active');

            // Prepare data for AJAX request
            const data = {
                action: 'lci_merchandise_add_to_cart',
                product_id: productId,
                quantity: quantity,
                security: lci_ajax_object.add_to_cart_nonce
            };

            // Add variation data
            $.extend(data, variationData);

            // Show loading state
            button.html('<i class="fas fa-spinner fa-spin me-2"></i> Adding...');
            button.prop('disabled', true);

            // Add to cart via AJAX
            $.ajax({
                url: lci_ajax_object.ajax_url,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Reset button state
                    button.html(originalText);
                    button.prop('disabled', false);

                    if (response.success) {
                        // Show success message on the main product card button
                        const mainButton = productCard.find('.show-variations-btn');
                        const originalMainText = mainButton.html();

                        mainButton.html('<i class="fas fa-check me-2"></i> Added to cart!');

                        // Reset button text after 2 seconds
                        setTimeout(function() {
                            mainButton.html(originalMainText);
                        }, 2000);

                        // Show confirmation modal
                        showAddToCartConfirmation(productTitle, productImage);

                        // Update mini cart
                        updateMiniCart(response.data);

                        // Trigger WooCommerce events
                        $(document.body).trigger('added_to_cart', [null, null, null]);
                        $(document.body).trigger('updated_cart_totals');
                    } else {
                        // Show error message
                        alert(response.data.message || 'Failed to add product to cart.');
                    }
                },
                error: function() {
                    // Reset button state
                    button.html(originalText);
                    button.prop('disabled', false);

                    // Show error message
                    alert('An error occurred. Please try again.');
                }
            });
        });
    }



    /**
     * Initialize quantity buttons
     */
    function initializeQuantityButtons() {
        // Quantity plus button
        $(document).on('click', '.quantity-plus', function() {
            const input = $(this).siblings('input[type="number"]');
            const max = parseInt(input.attr('max'), 10) || 100;
            const value = parseInt(input.val(), 10);

            if (value < max) {
                input.val(value + 1);
                input.trigger('change');
            }
        });

        // Quantity minus button
        $(document).on('click', '.quantity-minus', function() {
            const input = $(this).siblings('input[type="number"]');
            const min = parseInt(input.attr('min'), 10) || 1;
            const value = parseInt(input.val(), 10);

            if (value > min) {
                input.val(value - 1);
                input.trigger('change');
            }
        });

        // Prevent invalid input
        $(document).on('input', '.quantity-input-field', function() {
            const input = $(this);
            const min = parseInt(input.attr('min'), 10) || 1;
            const max = parseInt(input.attr('max'), 10) || 100;
            let value = parseInt(input.val(), 10);

            if (isNaN(value) || value < min) {
                input.val(min);
            } else if (value > max) {
                input.val(max);
            }
        });
    }

    /**
     * Show add to cart confirmation modal
     *
     * @param {string} productName Product name
     * @param {string} productImage Product image URL
     */
    function showAddToCartConfirmation(productName, productImage) {
        // Set product details in modal
        $('#added-product-name').text(productName);
        $('#added-product-image').attr('src', productImage);

        // Show modal
        const modal = $('#addToCartConfirmModal');
        modal.modal('show');

        // Auto-close progress bar
        const progressBar = modal.find('.progress-bar');
        progressBar.css('width', '0%');

        // Animate progress bar
        setTimeout(function() {
            progressBar.css('width', '100%');
        }, 100);

        // Auto-close modal after 3 seconds
        setTimeout(function() {
            modal.modal('hide');
        }, 3000);
    }

    /**
     * Update mini cart
     *
     * @param {Object} data Cart data
     */
    function updateMiniCart(data) {
        // Update cart count and total
        if (data.cart_count !== undefined) {
            // Dispatch event to update Alpine.js mini cart component
            window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
                detail: {
                    cart_count: data.cart_count,
                    cart_total: data.cart_total
                }
            }));
        }

        // Also refresh the fundraising message if available
        if (typeof refreshFundraisingMessage === 'function') {
            refreshFundraisingMessage();
        } else {
            // Fallback: trigger the event that refreshFundraisingMessage listens for
            $(document.body).trigger('updated_cart_totals');
        }
    }

    /**
     * Initialize sentinel for infinite scroll
     */
    function initializeSentinel() {
        // Check if sentinel element exists
        const sentinel = document.getElementById('sentinel');
        if (!sentinel) return;

        // Create IntersectionObserver
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // When sentinel is visible, close variation cards that are off-screen
                    const cards = document.querySelectorAll('.regalia-variation-card.active');
                    cards.forEach(card => {
                        const rect = card.getBoundingClientRect();
                        if (rect.bottom > window.innerHeight || rect.top < 0) {
                            // Add closing class for animation
                            card.classList.add('closing');

                            // Remove active class after animation completes
                            setTimeout(function() {
                                card.classList.remove('active', 'closing');
                            }, 600); // Match the transition duration
                        }
                    });
                }
            });
        });

        // Start observing the sentinel
        observer.observe(sentinel);

        // Handle mobile-specific behavior
        if (window.innerWidth < 768) {
            // Add a backdrop for mobile variation cards
            $(document).on('click', '.show-variations-btn, .show-quantity-btn', function() {
                // Create backdrop if it doesn't exist
                if (!$('#variation-backdrop').length) {
                    $('body').append('<div id="variation-backdrop" class="variation-backdrop"></div>');
                }

                // Show backdrop
                setTimeout(function() {
                    $('#variation-backdrop').addClass('active');
                }, 10);
            });

            // Remove backdrop when closing variation cards
            $(document).on('click', '.close-variation-card, #variation-backdrop', function() {
                $('#variation-backdrop').removeClass('active');
                setTimeout(function() {
                    $('#variation-backdrop').remove();
                }, 300);
            });
        }
    }
})(jQuery);
