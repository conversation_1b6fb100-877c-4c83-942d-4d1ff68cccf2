/**
 * LCI Payment Handler
 */
(function($) {
    'use strict';

    // Payment data
    let paymentData = {
        orderId: null,
        paymentMethod: null
    };

    // Initialize payment handler
    function initPaymentHandler() {
        console.log('Initializing payment handler');

        // Handle payment method selection
        $('input[name="payment_method"]').on('change', function() {
            paymentData.paymentMethod = $(this).val();

            // Show/hide payment instructions
            if (paymentData.paymentMethod === 'bacs') {
                $('#lci-bacs-instructions').hide(); // Hide until order is created
            } else {
                $('#lci-bacs-instructions').hide();
            }
        });

        // Handle form submission
        $('#lci-payment-form').on('submit', function(e) {
            e.preventDefault();

            // Get selected payment method
            const selectedMethod = $('input[name="payment_method"]:checked').val();
            if (!selectedMethod) {
                $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> Please select a payment method.</p></div>');
                return;
            }

            paymentData.paymentMethod = selectedMethod;

            // Process payment
            processPayment();
        });
    }

    // Process payment
    function processPayment() {
        console.log('Processing payment with method:', paymentData.paymentMethod);

        // Show loading state
        const submitButton = $('#lci-payment-form button[type="submit"]');
        submitButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i> ' + lciPaymentData.i18n.processingMessage);
        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-info"><p><i class="fas fa-spinner fa-spin me-2"></i> ' + lciPaymentData.i18n.processingMessage + '</p></div>');

        // Get form data
        const formData = new FormData($('#lci-payment-form')[0]);
        formData.append('action', 'lci_create_order');
        formData.append('security', lciPaymentData.nonce);
        formData.append('payment_method', paymentData.paymentMethod);

        // Send AJAX request to create order
        $.ajax({
            url: lciPaymentData.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    console.log('Order created successfully', response.data);

                    // Store order ID
                    paymentData.orderId = response.data.order_id;

                    // Process payment for the created order
                    processOrderPayment();
                } else {
                    console.error('Error creating order', response.data);
                    $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + (response.data.message || lciPaymentData.i18n.errorMessage) + '</p></div>');
                    submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error', error);
                $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + lciPaymentData.i18n.errorMessage + '</p></div>');
                submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
            }
        });
    }

    // Process payment for created order
    function processOrderPayment() {
        console.log('Processing payment for order:', paymentData.orderId);

        // Send AJAX request to process payment
        $.ajax({
            url: lciPaymentData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lci_process_payment',
                security: lciPaymentData.nonce,
                order_id: paymentData.orderId,
                payment_method: paymentData.paymentMethod
            },
            success: function(response) {
                if (response.success) {
                    console.log('Payment processed successfully', response.data);

                    // Handle different payment methods
                    if (paymentData.paymentMethod === 'bacs') {
                        // Show BACS instructions
                        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-success"><p><i class="fas fa-check-circle me-2"></i> Order created successfully. Please make the bank transfer using the details below.</p></div>');
                        $('#lci-bacs-instructions').html(response.data.instructions).show();

                        // Reset form
                        const submitButton = $('#lci-payment-form button[type="submit"]');
                        submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
                    } else if (response.data.redirect_url) {
                        // Show redirect message
                        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-info"><p><i class="fas fa-info-circle me-2"></i> Redirecting to payment gateway...</p></div>');

                        // Redirect to payment gateway
                        setTimeout(function() {
                            window.location.href = response.data.redirect_url;
                        }, 1000);
                    } else {
                        // Show success message
                        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-success"><p><i class="fas fa-check-circle me-2"></i> ' + lciPaymentData.i18n.successMessage + '</p></div>');

                        // Redirect to success page if provided
                        if (response.data.return_url) {
                            setTimeout(function() {
                                window.location.href = response.data.return_url;
                            }, 1500);
                        }
                    }
                } else {
                    console.error('Error processing payment', response.data);
                    $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + (response.data.message || lciPaymentData.i18n.errorMessage) + '</p></div>');

                    // Reset form
                    const submitButton = $('#lci-payment-form button[type="submit"]');
                    submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error', error);
                $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + lciPaymentData.i18n.errorMessage + '</p></div>');

                // Reset form
                const submitButton = $('#lci-payment-form button[type="submit"]');
                submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
            }
        });
    }

    // Initialize on document ready
    $(document).ready(function() {
        initPaymentHandler();
    });
})(jQuery);