/**
 * Hide BeTheme's built-in mini cart
 */

/* Hide the cart icon in the header */
.top-cart-menu-icon,
.icon-bag-fine,
.header-cart-icon,
.header-cart-count,
.header-cart,
.top-cart,
.top_bar_right_wrapper .top_cart,
#header_cart,
.shopping-cart-icon,
.woocommerce-mini-cart,
.mfn-header-icon.mfn-header-cart,
.mfn-cart-holder,
.mfn-header-icon-box.mfn-header-cart-icon {
    display: none !important;
}

/* Hide any BeTheme mini cart dropdown */
.shopping-cart-items,
.shopping-cart-dropdown,
.mfn-cart-menu-item,
.mfn-header-cart-menu-item,
.mfn-cart-holder {
    display: none !important;
}

/* Hide any BeTheme cart widget */
.widget_shopping_cart,
.widget_shopping_cart_content,
.mfn-woo-cart-holder {
    display: none !important;
}

/* Hide BeTheme cart overlay without affecting scrollbar */
.mfn-cart-overlay,
#mfn-cart-overlay,
.mfn-cart-menu-overlay,
.mfn-header-overlay,
.mfn-cart-holder-overlay,
div[class*="mfn-cart-overlay"],
div[id*="mfn-cart-overlay"] {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    z-index: -1 !important;
    position: fixed !important;
    top: -9999px !important;
    left: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Ensure body scrolling is preserved */
body.mfn-hidden-overlay,
body.mfn-cart-opened,
body.mfn-cart-showing,
body.mfn-active-cart,
body.loaded.mfn-hidden-overlay,
body.loaded.mfn-cart-opened,
body.loaded.mfn-cart-showing,
body.loaded.mfn-active-cart,
body.mfn-hidden-overlay.loaded,
body.mfn-cart-opened.loaded,
body.mfn-cart-showing.loaded,
body.mfn-active-cart.loaded,
body.shop-sidecart-active,
body.mfn-ajax-add-to-cart,
body.mfn-cart-button-wrap {
    overflow: auto !important;
    overflow-x: hidden !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
    position: static !important;
}

/* Fix any padding adjustments BeTheme might be making */
html.mfn-hidden-scrollbar,
html.mfn-cart-showing,
html.mfn-active-cart,
html.mfn-cart-opened,
html.loaded.mfn-hidden-scrollbar,
html.loaded.mfn-cart-showing,
html.loaded.mfn-active-cart,
html.loaded.mfn-cart-opened,
html.mfn-hidden-scrollbar.loaded,
html.mfn-cart-showing.loaded,
html.mfn-active-cart.loaded,
html.mfn-cart-opened.loaded {
    margin-right: 0 !important;
    overflow: auto !important;
    overflow-y: scroll !important;
    padding-right: 0 !important;
}

/* Target specific BeTheme classes */
.shop-sidecart-active,
.mfn-ajax-add-to-cart,
.mfn-cart-button-wrap {
    overflow: auto !important;
    overflow-x: hidden !important;
}

/* Force scrollbar to be visible */
body {
    overflow-y: auto !important;
}

/* Ensure our modal backdrop handles scrolling properly */
.lci-modal-backdrop {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
}

/* Make sure our mini cart is visible */
#mini-cart-button,
.mini-cart-container,
#mini-cart-container {
    display: flex !important;
}
