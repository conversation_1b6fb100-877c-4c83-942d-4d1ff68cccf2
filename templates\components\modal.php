<?php
/**
 * Alpine.js Modal Component Template
 */
?>
<!-- Alpine.js Modal -->
<div x-data="lciModal" x-cloak>
    <div 
        x-show="open" 
        x-transition:enter="fade-in"
        x-transition:enter-start="lci-modal-enter"
        x-transition:leave="fade-out"
        x-transition:leave-end="lci-modal-leave"
        class="lci-modal-backdrop"
        @click.self="closeModal()"
    >
        <div 
            class="lci-modal"
            x-transition:enter="slide-in"
            x-transition:leave="slide-out"
        >
            <div class="lci-modal-header">
                <img :src="logoUrl" alt="LCI 2025 Logo" class="lci-modal-logo">
                <h2 class="lci-modal-title" x-text="title"></h2>
            </div>
            
            <div class="lci-modal-body">
                <div class="mb-4">
                    <i :class="['fas', getIconClass(), 'lci-modal-icon']"></i>
                </div>
                <p x-text="message"></p>
            </div>
            
            <div class="lci-modal-footer">
                <template x-if="showCancelButton">
                    <button 
                        @click="cancel()" 
                        class="lci-btn lci-btn-secondary"
                        x-text="cancelButtonText"
                    ></button>
                </template>
                
                <template x-if="showConfirmButton">
                    <button 
                        @click="confirm()" 
                        class="lci-btn lci-btn-primary"
                        x-text="confirmButtonText"
                    ></button>
                </template>
            </div>
            
            <template x-if="autoClose">
                <div class="lci-progress-bar">
                    <div 
                        class="lci-progress-bar-inner" 
                        :style="`transform: scaleX(0); transition-duration: ${autoCloseDelay}ms;`"
                        x-init="$nextTick(() => { $el.style.transform = 'scaleX(0)'; setTimeout(() => { $el.style.transform = 'scaleX(1)'; }, 50); })"
                    ></div>
                </div>
            </template>
        </div>
    </div>
</div>
