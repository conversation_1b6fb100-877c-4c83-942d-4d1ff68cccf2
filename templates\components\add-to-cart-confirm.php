<?php
/**
 * Add to Cart Confirmation Template
 */
?>

<script>
// Define the function globally before Alpine initializes
window.showAddToCartConfirmationDirect = function(name, image) {
    // Store data for Alpine initialization
    // Store the data for Alpine to use when it initializes
    window.pendingConfirmation = {
        productName: name,
        productImage: image
    };

    // Try to find the Alpine component
    const confirmEl = document.getElementById('add-to-cart-confirm');
    if (confirmEl && confirmEl.__x) {
        // If Alpine is already initialized, call the method directly
        try {
            confirmEl.__x.getUnobservedData().showConfirmation(name, image);
        } catch (error) {
            // Console error removed

            // Try to reinitialize Alpine.js on the component
            if (window.Alpine) {
                try {
                    window.Alpine.initTree(confirmEl);
                    // Try again after initialization
                    setTimeout(function() {
                        if (confirmEl.__x) {
                            confirmEl.__x.getUnobservedData().showConfirmation(name, image);
                        }
                    }, 100);
                } catch (initError) {
                    // Console error removed
                }
            }
        }
    } else {
        // Otherwise dispatch an event that will be handled when Alpine initializes
        window.dispatchEvent(new CustomEvent('show-confirmation-pending', {
            detail: {
                productName: name,
                productImage: image
            }
        }));

        // Try to initialize Alpine.js if available
        if (window.Alpine && confirmEl) {
            try {
                window.Alpine.initTree(confirmEl);
                // Try again after initialization
                setTimeout(function() {
                    if (confirmEl.__x) {
                        confirmEl.__x.getUnobservedData().showConfirmation(name, image);
                    }
                }, 100);
            } catch (error) {
                // Console error removed
            }
        }
    }
};

// Also define the regular function for compatibility
window.showAddToCartConfirmation = window.showAddToCartConfirmationDirect;

// Ensure Alpine.js is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (typeof Alpine === 'undefined') {
        // Console warning removed
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js';
        script.defer = true;
        document.head.appendChild(script);
    }
});
</script>
<style>
    @keyframes pulse-scale {
        0% { transform: scale(0.5); opacity: 0; }
        50% { transform: scale(1.2); opacity: 1; }
        100% { transform: scale(1); opacity: 1; }
    }

    @keyframes fade-in-up {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    .confirmation-animation i {
        display: inline-block;
        animation: pulse-scale 0.5s ease-out;
    }

    .confirmation-text {
        animation: fade-in-up 0.5s ease-out 0.2s both;
    }
</style>

<div
    x-data="{
    open: false,
    productName: '',
    productImage: '',
    progress: 0,
    timer: null,
    scrollPosition: 0,

    init() {
        // Initialize component

        // Update the global reference to use this instance
        const self = this;
        const originalFunction = window.showAddToCartConfirmationDirect;
        window.showAddToCartConfirmationDirect = function(name, image) {
            // Update the function to use this instance
            self.showConfirmation(name, image);
        };

        // Also update the regular function for compatibility
        window.showAddToCartConfirmation = window.showAddToCartConfirmationDirect;

        // Listen for custom event
        this.$el.addEventListener('show-confirmation', (event) => {
            // Process show-confirmation event
            if (event.detail) {
                this.showConfirmation(event.detail.productName, event.detail.productImage);
            }
        });

        // Listen for window event
        window.addEventListener('show-modal', (event) => {
            if (event.detail) {
                this.showConfirmation(event.detail.productName, event.detail.productImage);
            }
        });

        // Listen for pending confirmation event
        window.addEventListener('show-confirmation-pending', (event) => {
            // Process show-confirmation-pending event
            if (event.detail) {
                this.showConfirmation(event.detail.productName, event.detail.productImage);
            }
        });

        // Check if there's a pending confirmation
        if (window.pendingConfirmation) {
            // Process pending confirmation
            this.showConfirmation(window.pendingConfirmation.productName, window.pendingConfirmation.productImage);
            window.pendingConfirmation = null;
        }
    },

    showConfirmation(name, image) {
        // Show confirmation modal
        this.productName = name;
        this.productImage = image;
        this.open = true;
        this.startProgress();

        // Add modal-open class to body
        document.body.classList.add('modal-open');

        // Store the current scroll position
        this.scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

        // Add our own scroll management
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.top = `-${this.scrollPosition}px`;
        document.body.style.width = '100%';
    },

    closeConfirmation() {
        this.open = false;

        // Remove modal-open class from body
        document.body.classList.remove('modal-open');

        // Restore scroll position
        const scrollY = document.body.style.top;
        document.body.style.removeProperty('overflow');
        document.body.style.removeProperty('position');
        document.body.style.removeProperty('top');
        document.body.style.removeProperty('width');
        window.scrollTo(0, parseInt(scrollY || '0') * -1);

        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        // Call the callback function if it exists
        if (typeof window.addToCartCloseCallback === 'function') {
            window.addToCartCloseCallback();
            // Clear the callback after calling it
            window.addToCartCloseCallback = null;
        }
    },

    startProgress() {
        this.progress = 0;
        const duration = 3000; // 3 seconds
        const interval = 30; // Update every 30ms
        const steps = duration / interval;
        const increment = 100 / steps;

        this.timer = setInterval(() => {
            this.progress += increment;
            if (this.progress >= 100) {
                clearInterval(this.timer);
                this.timer = null;
                this.closeConfirmation();
            }
        }, interval);
    },

    viewCart() {
        this.closeConfirmation();

        // Try multiple approaches to open the mini cart

        // 1. Try using the global function
        if (typeof window.openMiniCart === 'function') {
            // Use global function
            window.openMiniCart();
            return;
        }

        // 2. Try using the stored reference
        if (window.lciComponents && window.lciComponents.miniCart) {
            // Use stored reference
            try {
                const data = window.lciComponents.miniCart.getUnobservedData();
                data.openCart();
                return;
            } catch (error) {
                // Console error removed
            }
        }

        // 3. Try finding the element directly
        const miniCartEl = document.getElementById('mini-cart-container') || document.querySelector('.lci-mini-cart-container');
        if (miniCartEl && miniCartEl.__x) {
            // Use element directly
            try {
                miniCartEl.__x.getUnobservedData().openCart();
                return;
            } catch (error) {
                // Console error removed
            }
        }

        // 4. Dispatch a window event as last resort
        // Dispatch event as fallback
        window.dispatchEvent(new CustomEvent('open-mini-cart'));
    }
}" x-cloak id="add-to-cart-confirm">
    <div
        x-show="open"
        x-transition:enter="fade-in"
        x-transition:leave="fade-out"
        class="lci-modal-backdrop"
        @click.self="closeConfirmation"
        style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center; overflow-y: auto; padding: 20px 0;"
    >
        <div
            class="lci-modal"
            x-transition:enter="slide-in"
            x-transition:leave="slide-out"
            style="background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.12); max-width: 450px; width: 95%; position: relative;"
        >
            <button @click="closeConfirmation" class="lci-modal-close" style="position: absolute; right: 1rem; top: 1rem; background: none; border: none; font-size: 1.25rem; color: #36b1dc; transition: all 0.2s ease; z-index: 10;">
                <i class="fas fa-times"></i>
            </button>

            <div class="lci-modal-body text-center" style="padding: 2.5rem 1.5rem 1.5rem;">
                <div class="confirmation-animation" style="margin-bottom: 1.5rem;">
                    <i class="fas fa-check-circle" style="font-size: 3rem; color: #36b1dc;"></i>
                </div>
                <div class="confirmation-text">
                    <h4 style="color: #36b1dc; font-weight: 600; margin-bottom: 0.75rem;" x-text="productName"></h4>
                    <p style="color: #36b1dc; font-size: 1.1rem;">has been added to your LCI Goodies Bag</p>
                </div>
            </div>

            <div class="lci-modal-footer" style="padding: 1rem 1.5rem 1.5rem; display: flex; justify-content: space-between; gap: 1rem;">
                <button @click="closeConfirmation" class="lci-btn lci-btn-secondary" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #f0f0f0; color: #333; font-weight: 500; transition: all 0.2s ease;">Continue Shopping</button>
                <button @click="viewCart" class="lci-btn lci-btn-primary" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #36b1dc; color: white; font-weight: 500; transition: all 0.2s ease;">
                    <i class="fas fa-shopping-bag me-2"></i> View Bag
                </button>
            </div>

            <div class="lci-progress-bar" style="position: absolute; bottom: 0; left: 0; right: 0; height: 4px; background-color: rgba(54, 177, 220, 0.1); overflow: hidden;">
                <div class="lci-progress-bar-inner" :style="{transform: 'scaleX(' + (progress/100) + ')'}" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: #36b1dc; transform-origin: left center; transition: transform 0.1s linear;"></div>
            </div>
        </div>
    </div>
</div>
