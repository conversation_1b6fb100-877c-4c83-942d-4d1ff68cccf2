<?php
/**
 * Generate Invitation Letter Page
 *
 * This page allows users to generate an invitation letter for visa applications
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Get current user
$user_id = get_current_user_id();
if (!$user_id) {
    wp_redirect(home_url());
    exit;
}

// Get user data
$user = get_userdata($user_id);
$full_name = $user->display_name;
$country = get_user_meta($user_id, 'country', true);

// Check if user already has a generated invitation letter
$invitation_letter_url = get_user_meta($user_id, 'invitation_letter_pdf', true);
$has_invitation = !empty($invitation_letter_url);

// Check if delete action was requested
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'delete_invitation_letter')) {
    // Delete the invitation letter
    delete_user_meta($user_id, 'invitation_letter_pdf');
    delete_user_meta($user_id, 'invitation_letter_generated_date');
    delete_user_meta($user_id, 'invitation_letter_unique_id');

    // Redirect back to this page without the action parameter
    wp_redirect(remove_query_arg(array('action', '_wpnonce')));
    exit;
}

// Determine user type based on purchased products
$user_type = determine_user_type($user_id);
?>

<div class="container-fluid p-0">
    <div class="row g-0">
        <div class="col-12">
            <!-- Header Section - 2025 UX/UI Style -->
            <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 40px 20px; position: relative; overflow: hidden; border-radius: 0 0 30px 30px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2);">
                <!-- Animated background elements -->
                <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%); border-radius: 50%; animation: pulse 8s infinite alternate;"></div>
                <div class="position-absolute" style="bottom: -30px; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%); border-radius: 50%; animation: float 12s infinite alternate;"></div>
                <div class="position-absolute" style="top: 20%; right: 20%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%); border-radius: 50%; animation: float 7s infinite alternate-reverse;"></div>

                <!-- Glassmorphism overlay -->
                <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>

                <div class="container position-relative" style="z-index: 2;">
                    <div class="d-flex align-items-center justify-content-center flex-column text-center">
                        <!-- Modern 3D icon with shadow and glow -->
                        <div style="background: rgba(255, 255, 255, 0.2); width: 80px; height: 80px; border-radius: 24px; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); transform: perspective(500px) rotateX(10deg); border: 1px solid rgba(255, 255, 255, 0.3);">
                            <div style="position: relative;">
                                <i class="fas fa-file-pdf" style="color: white; font-size: 36px; filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));"></i>
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.8), transparent 70%); filter: blur(8px); opacity: 0.5; animation: glow 3s infinite alternate;"></div>
                            </div>
                        </div>

                        <!-- Typography with modern styling -->
                        <h1 style="color: white; font-size: 32px; font-weight: 800; margin: 0; letter-spacing: -0.5px; text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">Generate Invitation Letter</h1>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 18px; margin: 0; margin-top: 8px; max-width: 600px; font-weight: 300; letter-spacing: 0.5px;">Create an official invitation letter for your visa application</p>

                        <!-- Animated indicator -->
                        <div class="mt-4" style="animation: bounce 2s infinite;">
                            <i class="fas fa-chevron-down" style="color: rgba(255, 255, 255, 0.7); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                @keyframes pulse {
                    0% { transform: scale(1); opacity: 0.5; }
                    100% { transform: scale(1.2); opacity: 0.2; }
                }
                @keyframes float {
                    0% { transform: translateY(0) translateX(0); }
                    50% { transform: translateY(-20px) translateX(10px); }
                    100% { transform: translateY(10px) translateX(-10px); }
                }
                @keyframes glow {
                    0% { opacity: 0.3; }
                    100% { opacity: 0.7; }
                }
                @keyframes bounce {
                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                    40% { transform: translateY(-10px); }
                    60% { transform: translateY(-5px); }
                }

                /* Mobile Responsive Styles */
                @media (max-width: 767px) {
                    /* Reduce padding on all containers */
                    .container {
                        padding-left: 10px !important;
                        padding-right: 10px !important;
                    }

                    /* Make cards take full width with minimal padding */
                    [style*="border-radius: 30px"],
                    [style*="border-radius: 24px"] {
                        padding: 20px !important;
                        border-radius: 20px !important;
                        margin-left: -5px !important;
                        margin-right: -5px !important;
                        width: calc(100% + 10px) !important;
                    }

                    /* Make form inputs full width */
                    input[type="text"],
                    input[type="date"],
                    select,
                    textarea {
                        width: 100% !important;
                        max-width: 100% !important;
                        box-sizing: border-box !important;
                    }

                    /* Adjust button width */
                    button[type="submit"] {
                        width: 100% !important;
                        min-width: 100% !important;
                    }

                    /* Adjust spacing */
                    .mb-5 {
                        margin-bottom: 15px !important;
                    }

                    .py-5 {
                        padding-top: 15px !important;
                        padding-bottom: 15px !important;
                    }

                    /* Make header more compact */
                    h1 {
                        font-size: 24px !important;
                    }

                    p {
                        font-size: 14px !important;
                    }

                    /* Adjust form field spacing */
                    .position-relative {
                        margin-bottom: 15px !important;
                    }

                    /* Make the back button more compact */
                    [href*="my-visa"] {
                        padding: 8px 16px !important;
                        font-size: 14px !important;
                    }

                    /* Adjust feature list for mobile */
                    .col-md-6 {
                        width: 100% !important;
                    }

                    /* Improve form fields on mobile */
                    .position-relative label {
                        font-size: 12px !important;
                    }

                    input[type="text"],
                    input[type="date"] {
                        padding: 14px 12px !important;
                        font-size: 16px !important; /* Keep 16px to prevent iOS zoom */
                    }

                    /* Make floating icons smaller on mobile */
                    [style*="width: 100px; height: 100px"] {
                        width: 70px !important;
                        height: 70px !important;
                    }

                    /* Adjust icon sizes */
                    [style*="font-size: 40px"] {
                        font-size: 30px !important;
                    }

                    /* Make feature list more compact */
                    [style*="margin-bottom: 12px"] {
                        margin-bottom: 8px !important;
                    }

                    /* Adjust card spacing */
                    [style*="margin-bottom: 30px"] {
                        margin-bottom: 20px !important;
                    }

                    /* Make the header more compact */
                    [style*="padding: 40px 20px"] {
                        padding: 25px 15px !important;
                    }

                    /* Adjust row spacing */
                    .row {
                        margin-left: -5px !important;
                        margin-right: -5px !important;
                    }

                    .col-md-4, .col-md-6, .col-md-10, .col-md-2 {
                        padding-left: 5px !important;
                        padding-right: 5px !important;
                    }

                    /* Center align text on mobile */
                    h4, p {
                        text-align: center !important;
                    }

                    /* Make the form take full width */
                    form {
                        width: 100% !important;
                    }

                    /* Adjust alert box */
                    [style*="background: rgba(255, 193, 7, 0.1)"] {
                        padding: 15px !important;
                    }

                    /* Make the download button full width */
                    .col-md-6 a {
                        width: 100% !important;
                    }
                }
            </style>

            <!-- Main Content - 2025 UX/UI Style -->
            <div class="container py-5" style="position: relative; z-index: 1;">
                <!-- Decorative background elements -->
                <div class="position-absolute" style="top: 10%; right: 5%; width: 300px; height: 300px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: -1;"></div>
                <div class="position-absolute" style="bottom: 10%; left: 5%; width: 250px; height: 250px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.03), transparent 70%); border-radius: 50%; z-index: -1;"></div>

                <!-- Back Button - Modern Floating Style -->
                <div class="mb-5 position-relative">
                    <a href="<?php echo add_query_arg('tab', 'my-visa', remove_query_arg(array('action', '_wpnonce'))); ?>" class="d-inline-flex align-items-center" style="text-decoration: none; background: rgba(255, 255, 255, 0.8); padding: 12px 24px; border-radius: 50px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.8); transition: all 0.3s ease; color: #2c3e50; font-weight: 500;">
                        <div style="background: rgba(54, 177, 220, 0.1); width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            <i class="fas fa-arrow-left" style="color: #36b1dc; font-size: 14px;"></i>
                        </div>
                        <span>Back to Visa Information</span>
                        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1)); border-radius: 50px; opacity: 0; transition: opacity 0.3s ease; z-index: -1;"></div>
                    </a>
                </div>

                <?php if ($has_invitation): ?>
                    <!-- User already has an invitation letter - 2025 UX/UI Style -->
                    <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 40px; margin-bottom: 40px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden;">
                        <!-- Decorative elements -->
                        <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(76, 175, 80, 0.1), transparent 70%); border-radius: 50%;"></div>
                        <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(76, 175, 80, 0.05), transparent 70%); border-radius: 50%;"></div>

                        <div class="text-center mb-5 position-relative">
                            <!-- 3D success icon with animation -->
                            <div style="position: relative; width: 120px; height: 120px; margin: 0 auto 30px; transform-style: preserve-3d; perspective: 1000px; animation: float 6s ease-in-out infinite;">
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1)); border-radius: 50%; transform: translateZ(-10px); box-shadow: 0 10px 30px rgba(76, 175, 80, 0.2);"></div>
                                <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7)); border-radius: 50%; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.9), inset 0 -2px 3px rgba(0, 0, 0, 0.05); transform: translateZ(10px);">
                                    <i class="fas fa-check-circle" style="color: #4caf50; font-size: 60px; filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.1));"></i>
                                </div>
                                <!-- Glow effect -->
                                <div style="position: absolute; top: -10px; left: -10px; right: -10px; bottom: -10px; background: radial-gradient(circle at center, rgba(76, 175, 80, 0.5), transparent 70%); filter: blur(15px); opacity: 0.5; border-radius: 50%; animation: glow 3s infinite alternate;"></div>
                            </div>

                            <!-- Modern typography -->
                            <h2 style="color: #2c3e50; font-size: 32px; font-weight: 800; margin-bottom: 16px; letter-spacing: -0.5px;">Invitation Letter Generated</h2>
                            <p style="color: #7f8c8d; font-size: 18px; max-width: 600px; margin: 0 auto; line-height: 1.6; font-weight: 300;">You have already generated an invitation letter for your visa application. You can download it or create a new one if needed.</p>
                        </div>

                        <!-- Download button with 3D effect -->
                        <div class="row justify-content-center mb-5">
                            <div class="col-md-6">
                                <a href="<?php echo esc_url($invitation_letter_url); ?>" target="_blank" class="d-block position-relative" style="text-decoration: none; transform-style: preserve-3d; perspective: 1000px;">
                                    <!-- Button shadow/base -->
                                    <div style="position: absolute; top: 5px; left: 0; right: 0; height: 100%; background: linear-gradient(to bottom, #2980b9, #2573a7); border-radius: 16px; transform: translateZ(-5px); filter: blur(2px); opacity: 0.7;"></div>

                                    <!-- Button main -->
                                    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; font-weight: 700; text-transform: uppercase; letter-spacing: 1px; padding: 20px; border-radius: 16px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.3), inset 0 2px 0 rgba(255, 255, 255, 0.3); text-align: center; transform: translateZ(0); transition: all 0.3s ease; border: 1px solid rgba(255, 255, 255, 0.2);">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div style="background: rgba(255, 255, 255, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.3);">
                                                <i class="fas fa-download" style="color: white; font-size: 20px;"></i>
                                            </div>
                                            <span style="font-size: 18px;">DOWNLOAD INVITATION LETTER</span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Delete option with modern styling -->
                        <div class="text-center">
                            <p style="color: #7f8c8d; font-size: 16px; margin-bottom: 20px; font-weight: 300;">Need to make changes to your invitation letter?</p>
                            <a href="<?php echo add_query_arg(array('action' => 'delete', '_wpnonce' => wp_create_nonce('delete_invitation_letter'))); ?>" onclick="return confirm('Are you sure you want to delete your invitation letter? You will need to generate a new one.');" style="display: inline-flex; align-items: center; text-decoration: none; background: rgba(255, 255, 255, 0.8); color: #e74c3c; padding: 12px 24px; border-radius: 50px; box-shadow: 0 8px 20px rgba(231, 76, 60, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border: 1px solid rgba(231, 76, 60, 0.1); transition: all 0.3s ease; font-weight: 500;">
                                <div style="background: rgba(231, 76, 60, 0.1); width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                    <i class="fas fa-trash-alt" style="color: #e74c3c; font-size: 14px;"></i>
                                </div>
                                <span>Delete and Generate New Letter</span>
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- User does not have an invitation letter yet - 2025 UX/UI Style -->
                    <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 40px; margin-bottom: 40px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden;">
                        <!-- Decorative elements -->
                        <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%;"></div>
                        <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%;"></div>

                        <!-- Form header with modern typography -->
                        <div class="text-center mb-5">
                            <h2 style="color: #2c3e50; font-size: 28px; font-weight: 800; margin-bottom: 16px; letter-spacing: -0.5px;">Create Your Invitation Letter</h2>
                            <p style="color: #7f8c8d; font-size: 18px; max-width: 700px; margin: 0 auto 30px; line-height: 1.6; font-weight: 300;">Please provide the following information to generate your official invitation letter for visa application purposes.</p>
                        </div>

                        <form id="invitation-letter-form" method="post">
                            <input type="hidden" name="action" value="generate_invitation_letter">
                            <?php wp_nonce_field('generate_invitation_letter_nonce', 'security'); ?>

                            <!-- Passport Information Section - Glassmorphism card -->
                            <div style="background: rgba(255, 255, 255, 0.6); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); border-radius: 24px; padding: 30px; margin-bottom: 30px; border: 1px solid rgba(255, 255, 255, 0.7); box-shadow: 0 10px 30px rgba(0, 0, 0, 0.03); position: relative; overflow: hidden;">
                                <!-- Card decorative elements -->
                                <div class="position-absolute" style="top: -10px; right: -10px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.08), transparent 70%); border-radius: 50%;"></div>

                                <!-- Section header with 3D icon -->
                                <div class="d-flex align-items-center mb-4">
                                    <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                                        <i class="fas fa-passport" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                                    </div>
                                    <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Passport Information</h3>
                                </div>

                                <!-- Modern form fields with floating labels effect -->
                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="position-relative" style="margin-bottom: 24px;">
                                            <label for="passport_number" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Passport Number*</label>
                                            <input type="text" id="passport_number" name="passport_number" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="position-relative" style="margin-bottom: 24px;">
                                            <label for="nationality" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Nationality*</label>
                                            <input type="text" id="nationality" name="nationality" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                                        </div>
                                    </div>
                                </div>

                                <div class="position-relative" style="margin-bottom: 24px;">
                                    <label for="place_of_birth" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Place of Birth*</label>
                                    <input type="text" id="place_of_birth" name="place_of_birth" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                                </div>

                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <div class="position-relative" style="margin-bottom: 24px;">
                                            <label for="date_of_birth" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Date of Birth*</label>
                                            <input type="date" id="date_of_birth" name="date_of_birth" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="position-relative" style="margin-bottom: 24px;">
                                            <label for="passport_issue_date" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Passport Issue Date*</label>
                                            <input type="date" id="passport_issue_date" name="passport_issue_date" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="position-relative" style="margin-bottom: 24px;">
                                            <label for="passport_expiry_date" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Passport Expiry Date*</label>
                                            <input type="date" id="passport_expiry_date" name="passport_expiry_date" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Travel Information Section - Glassmorphism card -->
                            <div style="background: rgba(255, 255, 255, 0.6); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); border-radius: 24px; padding: 30px; margin-bottom: 30px; border: 1px solid rgba(255, 255, 255, 0.7); box-shadow: 0 10px 30px rgba(0, 0, 0, 0.03); position: relative; overflow: hidden;">
                                <!-- Card decorative elements -->
                                <div class="position-absolute" style="top: -10px; right: -10px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.08), transparent 70%); border-radius: 50%;"></div>

                                <!-- Section header with 3D icon -->
                                <div class="d-flex align-items-center mb-4">
                                    <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                                        <i class="fas fa-plane-departure" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                                    </div>
                                    <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Travel Information</h3>
                                </div>

                                <!-- Modern form fields with floating labels effect -->
                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="position-relative" style="margin-bottom: 24px;">
                                            <label for="arrival_date" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Arrival Date*</label>
                                            <input type="date" id="arrival_date" name="arrival_date" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="position-relative" style="margin-bottom: 24px;">
                                            <label for="departure_date" style="position: absolute; top: -10px; left: 15px; background: white; padding: 0 8px; font-size: 14px; color: #36b1dc; font-weight: 600; border-radius: 4px; z-index: 1; transition: all 0.3s ease;">Departure Date*</label>
                                            <input type="date" id="departure_date" name="departure_date" required style="width: 100%; padding: 16px 20px; background: rgba(255, 255, 255, 0.9); border: 1px solid rgba(54, 177, 220, 0.3); border-radius: 16px; font-size: 16px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.02); transition: all 0.3s ease; -webkit-appearance: none; -moz-appearance: none; appearance: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Important Note - Modern alert style -->
                            <div style="background: rgba(255, 193, 7, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; padding: 24px; margin-bottom: 30px; border: 1px solid rgba(255, 193, 7, 0.2); box-shadow: 0 10px 30px rgba(255, 193, 7, 0.05); position: relative; overflow: hidden;">
                                <!-- Alert decorative elements -->
                                <div class="position-absolute" style="top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(255, 193, 7, 0.1), transparent 70%); border-radius: 50%;"></div>

                                <div class="d-flex">
                                    <div style="background: rgba(255, 193, 7, 0.15); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 8px 16px rgba(255, 193, 7, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); flex-shrink: 0;">
                                        <i class="fas fa-exclamation-triangle" style="color: #ffc107; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                                    </div>
                                    <div>
                                        <h4 style="color: #2c3e50; font-size: 18px; font-weight: 700; margin-top: 0; margin-bottom: 8px;">Important Information</h4>
                                        <p style="color: #7f8c8d; font-size: 16px; margin: 0; line-height: 1.6; font-weight: 300;">Please ensure all information is accurate and matches your passport exactly. This information will be used to generate your official invitation letter for visa application purposes.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button - 3D effect -->
                            <div class="text-center">
                                <button type="submit" class="position-relative" style="background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; font-weight: 700; text-transform: uppercase; letter-spacing: 1px; padding: 20px 40px; border-radius: 16px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.3), inset 0 2px 0 rgba(255, 255, 255, 0.3); border: none; font-size: 18px; min-width: 300px; cursor: pointer; transform-style: preserve-3d; perspective: 1000px; transition: all 0.3s ease; overflow: hidden;">
                                    <!-- Button shadow/base -->
                                    <div class="position-absolute" style="top: 5px; left: 0; right: 0; height: 100%; background: linear-gradient(to bottom, #2980b9, #2573a7); border-radius: 16px; transform: translateZ(-5px); filter: blur(2px); opacity: 0.7; z-index: -1;"></div>

                                    <!-- Button content -->
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div style="background: rgba(255, 255, 255, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.3);">
                                            <i class="fas fa-file-pdf" style="color: white; font-size: 20px;"></i>
                                        </div>
                                        <span>GENERATE LETTER</span>
                                    </div>

                                    <!-- Button hover effect -->
                                    <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1)); opacity: 0; transition: opacity 0.3s ease; z-index: 1;"></div>
                                </button>
                            </div>
                        </form>
                    </div>
                <?php endif; ?>

                <!-- Information About Letter Types - 2025 UX/UI Style -->
                <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 40px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden;">
                    <!-- Decorative elements -->
                    <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                    <div class="position-absolute" style="bottom: -50px; left: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(76, 175, 80, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                    <!-- Section header with 3D effect -->
                    <div class="text-center mb-5 position-relative" style="z-index: 1;">
                        <div style="display: inline-block; background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); padding: 12px 30px; border-radius: 50px; margin-bottom: 20px; box-shadow: 0 8px 20px rgba(54, 177, 220, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.5);">
                            <h3 style="color: #2c3e50; font-size: 22px; font-weight: 800; margin: 0; letter-spacing: -0.3px;">About Your Invitation Letter</h3>
                        </div>
                        <p style="color: #7f8c8d; font-size: 16px; max-width: 700px; margin: 0 auto; line-height: 1.6; font-weight: 300;">Your invitation letter is tailored to your registration type and includes all necessary information for visa application purposes.</p>
                    </div>

                    <!-- Letter type card with 3D effect -->
                    <?php if ($user_type === 'delegate'): ?>
                        <div style="background: rgba(255, 255, 255, 0.6); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); border-radius: 24px; padding: 30px; margin-bottom: 30px; border: 1px solid rgba(255, 255, 255, 0.7); box-shadow: 0 20px 40px rgba(54, 177, 220, 0.05); position: relative; overflow: hidden; transform: perspective(1000px) rotateX(2deg); transform-style: preserve-3d;">
                            <!-- Card decorative elements -->
                            <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                            <div class="row align-items-center position-relative" style="z-index: 1;">
                                <div class="col-md-2 text-center mb-4 mb-md-0">
                                    <!-- 3D floating icon -->
                                    <div style="position: relative; width: 100px; height: 100px; margin: 0 auto; transform-style: preserve-3d; perspective: 1000px; animation: float 6s ease-in-out infinite;">
                                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); border-radius: 50%; transform: translateZ(-10px); box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2);"></div>
                                        <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7)); border-radius: 50%; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.9), inset 0 -2px 3px rgba(0, 0, 0, 0.05); transform: translateZ(10px);">
                                            <i class="fas fa-user-tie" style="color: #36b1dc; font-size: 40px; filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.1));"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-10">
                                    <h4 style="color: #2c3e50; font-size: 24px; font-weight: 700; margin-top: 0; margin-bottom: 16px; letter-spacing: -0.3px;">Delegate Invitation Letter</h4>
                                    <p style="color: #7f8c8d; font-size: 16px; margin: 0; line-height: 1.6; font-weight: 300;">You will receive a <strong>Delegate Invitation Letter</strong> as you have registered as an official delegate for the LCI 2025 AGM. This letter confirms your role as a delegate representing your national Circle and includes all necessary details required by consular authorities.</p>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div style="background: rgba(255, 255, 255, 0.6); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); border-radius: 24px; padding: 30px; margin-bottom: 30px; border: 1px solid rgba(255, 255, 255, 0.7); box-shadow: 0 20px 40px rgba(54, 177, 220, 0.05); position: relative; overflow: hidden; transform: perspective(1000px) rotateX(2deg); transform-style: preserve-3d;">
                            <!-- Card decorative elements -->
                            <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                            <div class="row align-items-center position-relative" style="z-index: 1;">
                                <div class="col-md-2 text-center mb-4 mb-md-0">
                                    <!-- 3D floating icon -->
                                    <div style="position: relative; width: 100px; height: 100px; margin: 0 auto; transform-style: preserve-3d; perspective: 1000px; animation: float 6s ease-in-out infinite;">
                                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); border-radius: 50%; transform: translateZ(-10px); box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2);"></div>
                                        <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7)); border-radius: 50%; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.9), inset 0 -2px 3px rgba(0, 0, 0, 0.05); transform: translateZ(10px);">
                                            <i class="fas fa-user" style="color: #36b1dc; font-size: 40px; filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.1));"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-10">
                                    <h4 style="color: #2c3e50; font-size: 24px; font-weight: 700; margin-top: 0; margin-bottom: 16px; letter-spacing: -0.3px;">Participant Invitation Letter</h4>
                                    <p style="color: #7f8c8d; font-size: 16px; margin: 0; line-height: 1.6; font-weight: 300;">You will receive a <strong>Participant Invitation Letter</strong> as you have registered as a participant for the LCI 2025 AGM. This letter confirms your attendance at the event and includes all necessary details required by consular authorities.</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Features card with modern design -->
                    <div style="background: rgba(255, 255, 255, 0.6); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); border-radius: 24px; padding: 30px; border: 1px solid rgba(255, 255, 255, 0.7); box-shadow: 0 20px 40px rgba(76, 175, 80, 0.05); position: relative; overflow: hidden;">
                        <!-- Card decorative elements -->
                        <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(76, 175, 80, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                        <div class="row align-items-center position-relative" style="z-index: 1;">
                            <div class="col-md-2 text-center mb-4 mb-md-0">
                                <!-- 3D floating icon -->
                                <div style="position: relative; width: 100px; height: 100px; margin: 0 auto; transform-style: preserve-3d; perspective: 1000px; animation: float 6s ease-in-out infinite alternate;">
                                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1)); border-radius: 50%; transform: translateZ(-10px); box-shadow: 0 10px 30px rgba(76, 175, 80, 0.2);"></div>
                                    <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7)); border-radius: 50%; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.9), inset 0 -2px 3px rgba(0, 0, 0, 0.05); transform: translateZ(10px);">
                                        <i class="fas fa-info-circle" style="color: #4caf50; font-size: 40px; filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.1));"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-10">
                                <h4 style="color: #2c3e50; font-size: 24px; font-weight: 700; margin-top: 0; margin-bottom: 16px; letter-spacing: -0.3px;">Letter Features</h4>

                                <!-- Modern feature list with icons -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul style="list-style: none; padding: 0; margin: 0;">
                                            <li style="display: flex; align-items: center; margin-bottom: 12px;">
                                                <div style="background: rgba(76, 175, 80, 0.1); width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                                    <i class="fas fa-language" style="color: #4caf50; font-size: 16px;"></i>
                                                </div>
                                                <span style="color: #7f8c8d; font-size: 16px; font-weight: 300;">Bilingual format (English and Romanian)</span>
                                            </li>
                                            <li style="display: flex; align-items: center; margin-bottom: 12px;">
                                                <div style="background: rgba(76, 175, 80, 0.1); width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                                <i class="fas fa-file-signature" style="color: #4caf50; font-size: 16px;"></i>
                                                </div>
                                                <span style="color: #7f8c8d; font-size: 16px; font-weight: 300;">Official letterhead and signatures</span>
                                            </li>
                                            <li style="display: flex; align-items: center; margin-bottom: 12px;">
                                                <div style="background: rgba(76, 175, 80, 0.1); width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                                <i class="fas fa-qrcode" style="color: #4caf50; font-size: 16px;"></i>
                                                </div>
                                                <span style="color: #7f8c8d; font-size: 16px; font-weight: 300;">QR code for verification</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul style="list-style: none; padding: 0; margin: 0;">
                                            <li style="display: flex; align-items: center; margin-bottom: 12px;">
                                                <div style="background: rgba(76, 175, 80, 0.1); width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                                <i class="fas fa-info-circle" style="color: #4caf50; font-size: 16px;"></i>
                                                </div>
                                                <span style="color: #7f8c8d; font-size: 16px; font-weight: 300;">Detailed event information</span>
                                            </li>
                                            <li style="display: flex; align-items: center; margin-bottom: 12px;">
                                                <div style="background: rgba(76, 175, 80, 0.1); width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                                <i class="fas fa-address-book" style="color: #4caf50; font-size: 16px;"></i>
                                                </div>
                                                <span style="color: #7f8c8d; font-size: 16px; font-weight: 300;">Contact details for embassy inquiries</span>
                                            </li>
                                            <li style="display: flex; align-items: center; margin-bottom: 12px;">
                                                <div style="background: rgba(76, 175, 80, 0.1); width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0;">
                                                <i class="fas fa-shield-alt" style="color: #4caf50; font-size: 16px;"></i>
                                                </div>
                                                <span style="color: #7f8c8d; font-size: 16px; font-weight: 300;">Secure digital verification system</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('invitation-letter-form');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Generating...';
            submitBtn.disabled = true;

            // Create FormData object
            const formData = new FormData(form);

            // Convert FormData to object
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });

            // Send AJAX request
            jQuery.ajax({
                type: 'POST',
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                data: data,
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        alert('Your invitation letter has been generated successfully!');

                        // Reload page to show download button
                        window.location.reload();
                    } else {
                        // Show error message with details if available
                        let errorMsg = 'Error generating invitation letter.';
                        if (response.data && response.data.message) {
                            errorMsg = 'Error: ' + response.data.message;
                        }
                        alert(errorMsg);
                        console.error('Error response:', response);

                        // Reset button
                        submitBtn.innerHTML = originalBtnText;
                        submitBtn.disabled = false;
                    }
                },
                error: function(xhr, status, error) {
                    // Show detailed error message
                    let errorMsg = 'An error occurred. Please try again later.';
                    if (xhr.responseText) {
                        try {
                            const jsonResponse = JSON.parse(xhr.responseText);
                            if (jsonResponse.message) {
                                errorMsg += ' Error: ' + jsonResponse.message;
                            }
                        } catch (e) {
                            // If response is not JSON, try to extract error message from HTML
                            const errorMatch = xhr.responseText.match(/<b>Fatal error<\/b>:(.+?)<br/);
                            if (errorMatch && errorMatch[1]) {
                                errorMsg += ' Error: ' + errorMatch[1].trim();
                            }
                        }
                    }

                    alert(errorMsg);
                    console.error('AJAX Error:', status, error, xhr.responseText);

                    // Reset button
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                }
            });
        });
    }
});
</script>
