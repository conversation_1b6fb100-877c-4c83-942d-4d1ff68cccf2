<?php
/**
 * LCI 2025 Dashboard Accommodation Unified
 *
 * Handles accommodation data management for the LCI 2025 Dashboard plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class LCI_Accommodation_Unified {
    /**
     * Initialize the class
     */
    public static function init() {
        // Register AJAX handlers
        add_action('wp_ajax_lci_sync_accommodation_unified', [__CLASS__, 'ajax_sync_accommodation']);
        add_action('wp_ajax_lci_get_accommodation_unified', [__CLASS__, 'ajax_get_accommodation']);
        add_action('wp_ajax_lci_get_accommodation_hotels', [__CLASS__, 'ajax_get_hotels']);
        add_action('wp_ajax_lci_update_accommodation_participant', [__CLASS__, 'ajax_update_participant']);
        add_action('wp_ajax_lci_delete_accommodation_participant', [__CLASS__, 'ajax_delete_participant']);
        add_action('wp_ajax_lci_clear_accommodation_table_unified', [__CLASS__, 'ajax_clear_table']);
        add_action('wp_ajax_lci_recreate_accommodation_table_unified', [__CLASS__, 'ajax_recreate_table']);
        add_action('wp_ajax_lci_update_table_structure_unified', [__CLASS__, 'ajax_update_table_structure']);
        add_action('wp_ajax_lci_bulk_update_accommodation_dates', [__CLASS__, 'ajax_bulk_update_dates']);
    }

    /**
     * Check if the accommodation table exists
     */
    public static function check_accommodation_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

        return [
            'exists' => $table_exists,
            'table_name' => $table_name
        ];
    }

    /**
     * Create the accommodation table
     */
    public static function create_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            order_id bigint(20) NOT NULL,
            product_id bigint(20) NOT NULL,
            variation_id bigint(20) DEFAULT NULL,
            variation_name varchar(255) DEFAULT NULL,
            category_id bigint(20) DEFAULT NULL,
            first_name varchar(255) DEFAULT NULL,
            last_name varchar(255) DEFAULT NULL,
            email varchar(255) DEFAULT NULL,
            phone varchar(255) DEFAULT NULL,
            country varchar(255) DEFAULT NULL,
            payment_status varchar(50) DEFAULT NULL,
            accommodation_type varchar(255) DEFAULT NULL,
            hotel_name varchar(255) DEFAULT NULL,
            room_share_option varchar(50) DEFAULT NULL,
            share_room_with varchar(255) DEFAULT NULL,
            checkin_date datetime DEFAULT NULL,
            checkout_date datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY user_id (user_id),
            KEY order_id (order_id),
            KEY product_id (product_id),
            KEY variation_id (variation_id),
            KEY category_id (category_id),
            KEY email (email),
            KEY hotel_name (hotel_name),
            KEY accommodation_type (accommodation_type),
            KEY payment_status (payment_status),
            KEY room_share_option (room_share_option)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        return true;
    }

    /**
     * Update the accommodation table structure
     * This method adds any missing columns to the table
     */
    public static function update_table_structure() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        if (!$table_exists) {
            return self::create_table();
        }

        // Get the current table structure
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
        $column_names = array_map(function($column) {
            return $column->Field;
        }, $columns);

        // Check if variation_name column exists
        if (!in_array('variation_name', $column_names)) {
            error_log("Adding variation_name column to $table_name");
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN variation_name varchar(255) DEFAULT NULL AFTER variation_id");
        }

        // Check if room_share_option column exists
        if (!in_array('room_share_option', $column_names)) {
            error_log("Adding room_share_option column to $table_name");
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN room_share_option varchar(50) DEFAULT NULL AFTER hotel_name");
        }

        // Check if share_room_with column exists
        if (!in_array('share_room_with', $column_names)) {
            error_log("Adding share_room_with column to $table_name");
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN share_room_with varchar(255) DEFAULT NULL AFTER room_share_option");
        }

        return true;
    }

    /**
     * AJAX handler for syncing accommodation data
     */
    public static function ajax_sync_accommodation() {
        // Suppress errors to prevent them from breaking JSON output
        @error_reporting(0);

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        // Check if the table exists, create it if not
        $table_status = self::check_accommodation_table();
        if (!$table_status['exists']) {
            self::create_table();
        } else {
            // Update the table structure to add any missing columns
            self::update_table_structure();
        }

        // Sync accommodation data
        try {
            $result = self::sync_accommodation_data();

            if ($result['success']) {
                wp_send_json_success([
                    'message' => $result['message'],
                    'count' => $result['count']
                ]);
            } else {
                wp_send_json_error([
                    'message' => $result['message']
                ]);
            }
        } catch (Exception $e) {
            // Log the exception
            error_log('Exception in sync_accommodation_data: ' . $e->getMessage());

            // Return error response
            wp_send_json_error([
                'message' => 'Error syncing accommodation data: ' . $e->getMessage()
            ]);
        } catch (Error $e) {
            // Log the error
            error_log('Error in sync_accommodation_data: ' . $e->getMessage());

            // Return error response
            wp_send_json_error([
                'message' => 'Error syncing accommodation data: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Sync accommodation data from WooCommerce orders
     */
    public static function sync_accommodation_data() {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

            // Enable error logging
            $wpdb->show_errors();

            // Log the start of the sync process
            error_log('Starting accommodation sync process');

            // Get accommodation category IDs
            $accommodation_categories = [18, 20, 37, 39, 30]; // Main Event, Councillors, Additional night in Brasov, Additional night in Bucharest, Additional night after pretour

            // Get specific product IDs that should be included
            $accommodation_product_ids = [
                40, 41, 42   // Main Event
            ];

            // Get orders with accommodation products
            // First try with the standard WooCommerce function
            $args = [
                'limit' => -1,
                'status' => ['completed', 'processing', 'on-hold', 'pending'],
                'category' => $accommodation_categories
            ];

            $orders = wc_get_orders($args);

            // Log the query used by WooCommerce
            error_log("WooCommerce query args: " . print_r($args, true));

            // If no orders found, try a direct database query as a fallback
            if (empty($orders)) {
                error_log("No orders found with wc_get_orders, trying direct database query");

                // Get all orders with any status
                $order_statuses = "'wc-completed', 'wc-processing', 'wc-on-hold', 'wc-pending'";

                // Make sure we have valid product IDs
                $valid_product_ids = array_filter($accommodation_product_ids, 'is_numeric');
                $specific_product_ids = !empty($valid_product_ids) ? implode(',', $valid_product_ids) : '0';

                // Make sure we have valid category IDs
                $valid_category_ids = array_filter($accommodation_categories, 'is_numeric');
                $category_ids_str = !empty($valid_category_ids) ? implode(',', $valid_category_ids) : '0';

                // Simplified query to just get orders with products
                $orders_query = "
                    SELECT DISTINCT p.ID
                    FROM {$wpdb->posts} p
                    JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
                    JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
                    WHERE p.post_type = 'shop_order'
                    AND p.post_status IN ($order_statuses)
                    AND oim.meta_key = '_product_id'
                ";

                error_log("Direct query: " . $orders_query);

                $order_ids = $wpdb->get_col($orders_query);
                error_log("Found " . count($order_ids) . " orders with direct query");

                if (!empty($order_ids)) {
                    $orders = array_map('wc_get_order', $order_ids);
                }
            }

            // Log the number of orders found
            error_log('Found ' . count($orders) . ' orders with accommodation products');

            if (empty($orders)) {
                error_log('No orders found with accommodation products');
                return [
                    'success' => false,
                    'message' => 'No orders found with accommodation products'
                ];
            }

            // Counter for synced participants
            $synced_count = 0;

            // Process each order
            foreach ($orders as $order) {
                $order_id = $order->get_id();
                $user_id = $order->get_user_id();
                $payment_status = $order->get_status();

                // Get customer data
                $first_name = $order->get_billing_first_name();
                $last_name = $order->get_billing_last_name();
                $email = $order->get_billing_email();
                $phone = $order->get_billing_phone();
                $country = $order->get_billing_country();

                // Log order details
                error_log("Processing order #{$order_id} for user #{$user_id} with status {$payment_status}");

                // Process each item in the order
                foreach ($order->get_items() as $item) {
                    $product_id = $item->get_product_id();
                    $variation_id = $item->get_variation_id();

                    error_log("Order #{$order_id} - Processing item: Product ID {$product_id}, Variation ID {$variation_id}");

                    // Get variation name
                    $variation_name = '';
                    if ($variation_id) {
                        $variation = wc_get_product($variation_id);
                        if ($variation) {
                            $variation_name = $variation->get_name();
                            // Remove the parent product name from the variation name
                            $parent_name = $variation->get_title();
                            if (strpos($variation_name, $parent_name) === 0) {
                                $variation_name = trim(str_replace($parent_name, '', $variation_name), ' -');
                            }
                            error_log("Order #{$order_id} - Variation name: {$variation_name}");
                        } else {
                            error_log("Order #{$order_id} - Could not get variation product for ID {$variation_id}");
                        }
                    }

                    // Get product categories
                    $product = wc_get_product($product_id);
                    if (!$product) {
                        error_log("Order #{$order_id} - Could not get product for ID {$product_id}");
                        continue;
                    }

                    $category_ids = $product->get_category_ids();
                    error_log("Order #{$order_id} - Product #{$product_id} has categories: " . implode(', ', $category_ids));

                    // Check if the product is in an accommodation category or is a specific product ID
                    $accommodation_category_id = null;
                    $is_specific_product = in_array($product_id, $accommodation_product_ids);

                    if ($is_specific_product) {
                        error_log("Order #{$order_id} - Product #{$product_id} is a specific accommodation product");
                        // Use a default category ID for specific products if they don't have a category
                        $accommodation_category_id = 18; // Default to Main Event category
                    } else {
                        foreach ($category_ids as $cat_id) {
                            if (in_array($cat_id, $accommodation_categories)) {
                                $accommodation_category_id = $cat_id;
                                error_log("Order #{$order_id} - Product #{$product_id} is in accommodation category {$cat_id}");
                                break;
                            }
                        }
                    }

                    if (!$accommodation_category_id && !$is_specific_product) {
                        error_log("Order #{$order_id} - Product #{$product_id} is not in any accommodation category and is not a specific product, skipping");
                        continue;
                    }

                    // Determine accommodation type based on category or product ID
                    $accommodation_type = '';

                    // First check specific product IDs
                    if (in_array($product_id, [40, 41, 42])) {
                        $accommodation_type = 'Main Event';
                    } else {
                        // Fall back to category-based determination
                        switch ($accommodation_category_id) {
                            case 18:
                                $accommodation_type = 'Main Event';
                                break;
                            case 20:
                                $accommodation_type = 'Councillors';
                                break;
                            case 37:
                                $accommodation_type = 'Additional night in Brasov';
                                break;
                            case 39:
                                $accommodation_type = 'Additional night in Bucharest';
                                break;
                            case 30:
                                $accommodation_type = 'Additional night after pretour';
                                break;
                        }
                    }

                    error_log("Order #{$order_id} - Product #{$product_id} accommodation type: {$accommodation_type}");

                    // Determine hotel based on product ID
                    $hotel_name = self::get_hotel_for_product($product_id);

                    // Get room sharing information from order meta
                    $room_share_option = '';
                    $share_room_with = '';

                    // Get room sharing option from order meta
                    $room_sharing_meta = $order->get_meta('room_sharing_option');
                    if ($room_sharing_meta) {
                        $room_share_option = $room_sharing_meta === 'yes' ? 'shared' : 'single';
                    } else {
                        // Try to get from item meta
                        $item_room_sharing = wc_get_order_item_meta($item->get_id(), 'room_sharing_option', true);
                        if ($item_room_sharing) {
                            $room_share_option = $item_room_sharing === 'yes' ? 'shared' : 'single';
                        }

                        // If still not found, try to determine from variation name
                        if (!$room_share_option && $variation_name) {
                            if (stripos($variation_name, 'single') !== false) {
                                $room_share_option = 'single';
                            } elseif (stripos($variation_name, 'shared') !== false || stripos($variation_name, 'double') !== false) {
                                $room_share_option = 'shared';
                            }
                        }
                    }

                    // Get share room with from order meta
                    $share_room_with_meta = $order->get_meta('share_room_with');
                    if ($share_room_with_meta) {
                        $share_room_with = $share_room_with_meta;
                    } else {
                        // Try to get from item meta
                        $item_share_with = wc_get_order_item_meta($item->get_id(), 'share_room_with', true);
                        if ($item_share_with) {
                            $share_room_with = $item_share_with;
                        }
                    }

                    // Set default check-in and check-out dates based on category
                    $checkin_date = null;
                    $checkout_date = null;

                    switch ($accommodation_category_id) {
                        case 18: // Main Event
                            $checkin_date = '2025-08-21 14:00:00';
                            $checkout_date = '2025-08-24 12:00:00';
                            break;
                        case 20: // Councillors
                            $checkin_date = '2025-08-20 14:00:00';
                            $checkout_date = '2025-08-24 12:00:00';
                            break;
                    }

                    // Check if participant already exists
                    $query = $wpdb->prepare(
                        "SELECT id FROM $table_name WHERE order_id = %d AND product_id = %d AND variation_id = %d",
                        $order_id, $product_id, $variation_id
                    );
                    error_log("Order #{$order_id} - Checking if participant exists: " . $query);

                    $existing = $wpdb->get_var($query);

                    if ($existing) {
                        error_log("Order #{$order_id} - Updating existing participant #{$existing}");

                        // Update existing participant
                        $result = $wpdb->update(
                            $table_name,
                            [
                                'user_id' => $user_id,
                                'first_name' => $first_name,
                                'last_name' => $last_name,
                                'email' => $email,
                                'phone' => $phone,
                                'country' => $country,
                                'payment_status' => $payment_status,
                                'accommodation_type' => $accommodation_type,
                                'hotel_name' => $hotel_name,
                                'variation_name' => $variation_name,
                                'room_share_option' => $room_share_option,
                                'share_room_with' => $share_room_with,
                                'checkin_date' => $checkin_date,
                                'checkout_date' => $checkout_date
                            ],
                            ['id' => $existing]
                        );

                        if ($result === false) {
                            error_log("Order #{$order_id} - Error updating participant: " . $wpdb->last_error);
                        } else {
                            error_log("Order #{$order_id} - Successfully updated participant #{$existing}");
                        }
                    } else {
                        error_log("Order #{$order_id} - Inserting new participant");

                        // Insert new participant
                        $result = $wpdb->insert(
                            $table_name,
                            [
                                'user_id' => $user_id,
                                'order_id' => $order_id,
                                'product_id' => $product_id,
                                'variation_id' => $variation_id,
                                'variation_name' => $variation_name,
                                'category_id' => $accommodation_category_id,
                                'first_name' => $first_name,
                                'last_name' => $last_name,
                                'email' => $email,
                                'phone' => $phone,
                                'country' => $country,
                                'payment_status' => $payment_status,
                                'accommodation_type' => $accommodation_type,
                                'hotel_name' => $hotel_name,
                                'room_share_option' => $room_share_option,
                                'share_room_with' => $share_room_with,
                                'checkin_date' => $checkin_date,
                                'checkout_date' => $checkout_date
                            ]
                        );

                        if ($result === false) {
                            error_log("Order #{$order_id} - Error inserting participant: " . $wpdb->last_error);
                        } else {
                            $new_id = $wpdb->insert_id;
                            error_log("Order #{$order_id} - Successfully inserted participant #{$new_id}");
                        }
                    }

                    $synced_count++;
                }
            }

            // Log the end of the sync process
            error_log("Synced $synced_count accommodation participants");

            // Check if any participants were actually synced
            if ($synced_count === 0) {
                error_log("WARNING: No participants were synced despite finding orders");
            }

            // Verify data in the table
            $count_check = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            error_log("Total participants in the table after sync: $count_check");

            return [
                'success' => true,
                'message' => "Synced $synced_count accommodation participants",
                'count' => $synced_count
            ];

        } catch (Exception $e) {
            // Log the exception
            error_log('Exception in sync_accommodation_data: ' . $e->getMessage());

            // Return error response
            return [
                'success' => false,
                'message' => 'Error syncing accommodation data: ' . $e->getMessage()
            ];
        } catch (Error $e) {
            // Log the error
            error_log('Error in sync_accommodation_data: ' . $e->getMessage());

            // Return error response
            return [
                'success' => false,
                'message' => 'Error syncing accommodation data: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get hotel name for a product ID
     */
    public static function get_hotel_for_product($product_id) {
        // Map product IDs to hotels
        $hotel_map = [
            // Hotel Aro Palace
            51 => 'Hotel Aro Palace',
            124 => 'Hotel Aro Palace',
            2310 => 'Hotel Aro Palace',
            40 => 'Hotel Aro Palace',  // Main Event

            // Hotel Kronwell
            127 => 'Hotel Kronwell',
            1551 => 'Hotel Kronwell',
            2312 => 'Hotel Kronwell',
            1683 => 'Hotel Kronwell',
            41 => 'Hotel Kronwell',  // Main Event

            // No Hotel
            137 => 'No Hotel',
            1679 => 'No Hotel',

            // Hotel Radisson Blu
            146 => 'Hotel Radisson Blu',
            131 => 'Hotel Radisson Blu',
            2333 => 'Hotel Radisson Blu',

            // Hotel Gott
            1555 => 'Hotel Gott',
            1548 => 'Hotel Gott',
            2336 => 'Hotel Gott',
            1680 => 'Hotel Gott',
            42 => 'Hotel Gott',  // Main Event

            // ARMATTI HOTEL
            1615 => 'ARMATTI HOTEL',
            1618 => 'ARMATTI HOTEL',
            2339 => 'ARMATTI HOTEL',
            1686 => 'ARMATTI HOTEL',

            // Pullman Bucharest
            2151 => 'Pullman Bucharest',

            // Additional hotels
            2152 => 'Hotel Capitol',
            2153 => 'Hotel Ambient',
            2154 => 'Hotel Belfort',
            2155 => 'Hotel Coroana',
            2156 => 'Hotel Cubix',
            2157 => 'Hotel Decebal',
            2158 => 'Hotel Golden Time',
            2159 => 'Hotel Brasov',
            2160 => 'Hotel Citrin',
            2161 => 'Hotel Curtea Brasoveana',
            2162 => 'Hotel Dacia',
            2163 => 'Hotel Kolping',
            2164 => 'Hotel Ramada',
            2165 => 'Hotel Residence Ambient',
            2166 => 'Hotel Safrano Palace',
            2167 => 'Hotel Bella Muzica',
            2168 => 'Hotel Casa Wagner'
        ];

        return isset($hotel_map[$product_id]) ? $hotel_map[$product_id] : 'Unknown';
    }

    /**
     * AJAX handler for getting accommodation data
     */
    public static function ajax_get_accommodation() {
        // Suppress errors to prevent them from breaking JSON output
        @error_reporting(0);

        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

        // Enable error logging
        $wpdb->show_errors();

        // Log the start of the get accommodation data process
        error_log('Getting accommodation data');

        // Check if the table exists
        $table_status = self::check_accommodation_table();
        if (!$table_status['exists']) {
            error_log('Accommodation table does not exist');
            wp_send_json_error(['message' => 'Accommodation table does not exist']);
        }

        // Check if there's any data in the table
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        error_log("Found $count participants in the accommodation table");

        try {
            // Build the query
            $query = "SELECT * FROM $table_name WHERE 1=1";
            $query_args = [];

            // Add filters
            if (isset($_GET['search']) && !empty($_GET['search'])) {
                $search = sanitize_text_field($_GET['search']);
                $query .= " AND (first_name LIKE %s OR last_name LIKE %s OR email LIKE %s)";
                $query_args[] = "%$search%";
                $query_args[] = "%$search%";
                $query_args[] = "%$search%";
            }

            if (isset($_GET['hotel']) && !empty($_GET['hotel'])) {
                $hotel = sanitize_text_field($_GET['hotel']);
                $query .= " AND hotel_name = %s";
                $query_args[] = $hotel;
            }

            if (isset($_GET['accommodation_type']) && !empty($_GET['accommodation_type'])) {
                $type = sanitize_text_field($_GET['accommodation_type']);
                $query .= " AND accommodation_type = %s";
                $query_args[] = $type;
            }

            if (isset($_GET['country']) && !empty($_GET['country'])) {
                $country = sanitize_text_field($_GET['country']);
                $query .= " AND country = %s";
                $query_args[] = $country;
            }

            if (isset($_GET['payment_status']) && !empty($_GET['payment_status'])) {
                $status = sanitize_text_field($_GET['payment_status']);
                $query .= " AND payment_status = %s";
                $query_args[] = $status;
            }

            // Prepare the query
            if (!empty($query_args)) {
                $query = $wpdb->prepare($query, $query_args);
            }

            // Get the participants
            $participants = $wpdb->get_results($query, ARRAY_A);

            // Log the number of participants returned after filtering
            error_log("Returning " . count($participants) . " participants after applying filters");

            // If no participants were found, log the query for debugging
            if (empty($participants)) {
                error_log("Query that returned no results: " . $query);
                if (!empty($query_args)) {
                    error_log("Query args: " . print_r($query_args, true));
                }
            }

            wp_send_json_success([
                'participants' => $participants
            ]);
        } catch (Exception $e) {
            // Log the exception
            error_log('Exception in ajax_get_accommodation: ' . $e->getMessage());

            // Return error response
            wp_send_json_error([
                'message' => 'Error getting accommodation data: ' . $e->getMessage()
            ]);
        } catch (Error $e) {
            // Log the error
            error_log('Error in ajax_get_accommodation: ' . $e->getMessage());

            // Return error response
            wp_send_json_error([
                'message' => 'Error getting accommodation data: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for getting hotels
     */
    public static function ajax_get_hotels() {
        // Suppress errors to prevent them from breaking JSON output
        @error_reporting(0);

        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

        // Check if the table exists
        $table_status = self::check_accommodation_table();
        if (!$table_status['exists']) {
            wp_send_json_error(['message' => 'Accommodation table does not exist']);
        }

        try {
            // Get distinct hotels
            $hotels = $wpdb->get_col("SELECT DISTINCT hotel_name FROM $table_name WHERE hotel_name IS NOT NULL AND hotel_name != '' ORDER BY hotel_name");

            wp_send_json_success([
                'hotels' => $hotels
            ]);
        } catch (Exception $e) {
            // Log the exception
            error_log('Exception in ajax_get_hotels: ' . $e->getMessage());

            // Return error response
            wp_send_json_error([
                'message' => 'Error getting hotels: ' . $e->getMessage()
            ]);
        } catch (Error $e) {
            // Log the error
            error_log('Error in ajax_get_hotels: ' . $e->getMessage());

            // Return error response
            wp_send_json_error([
                'message' => 'Error getting hotels: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for getting countries
     *
     * This function is not directly used, as we're reusing the lci_get_tours_countries AJAX action
     * from the LCI_Tours_Unified class. This is here for reference and potential future use.
     */
    public static function ajax_get_countries() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

        // Check if the table exists
        $table_status = self::check_accommodation_table();
        if (!$table_status['exists']) {
            wp_send_json_error(['message' => 'Accommodation table does not exist']);
        }

        // Get distinct countries
        $countries = $wpdb->get_col("SELECT DISTINCT country FROM $table_name WHERE country IS NOT NULL AND country != '' ORDER BY country");

        wp_send_json_success([
            'countries' => $countries
        ]);
    }

    /**
     * AJAX handler for updating a participant
     */
    public static function ajax_update_participant() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        // Check required fields
        if (!isset($_POST['id']) || empty($_POST['id'])) {
            wp_send_json_error(['message' => 'Participant ID is required']);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

        // Check if the table exists
        $table_status = self::check_accommodation_table();
        if (!$table_status['exists']) {
            wp_send_json_error(['message' => 'Accommodation table does not exist']);
        }

        // Get participant ID
        $participant_id = intval($_POST['id']);

        // Check if participant exists
        $participant = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $participant_id));
        if (!$participant) {
            wp_send_json_error(['message' => 'Participant not found']);
        }

        // Prepare data for update
        $data = [
            'first_name' => isset($_POST['first_name']) ? sanitize_text_field($_POST['first_name']) : $participant->first_name,
            'last_name' => isset($_POST['last_name']) ? sanitize_text_field($_POST['last_name']) : $participant->last_name,
            'email' => isset($_POST['email']) ? sanitize_email($_POST['email']) : $participant->email,
            'phone' => isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : $participant->phone,
            'country' => isset($_POST['country']) ? sanitize_text_field($_POST['country']) : $participant->country,
            'payment_status' => isset($_POST['payment_status']) ? sanitize_text_field($_POST['payment_status']) : $participant->payment_status,
            'accommodation_type' => isset($_POST['accommodation_type']) ? sanitize_text_field($_POST['accommodation_type']) : $participant->accommodation_type,
            'hotel_name' => isset($_POST['hotel_name']) ? sanitize_text_field($_POST['hotel_name']) : $participant->hotel_name,
            'variation_name' => isset($_POST['variation_name']) ? sanitize_text_field($_POST['variation_name']) : $participant->variation_name,
            'room_share_option' => isset($_POST['room_share_option']) ? sanitize_text_field($_POST['room_share_option']) : $participant->room_share_option,
            'share_room_with' => isset($_POST['share_room_with']) ? sanitize_text_field($_POST['share_room_with']) : $participant->share_room_with,
            'checkin_date' => isset($_POST['checkin_date']) && !empty($_POST['checkin_date']) ? sanitize_text_field($_POST['checkin_date']) . ' 14:00:00' : $participant->checkin_date,
            'checkout_date' => isset($_POST['checkout_date']) && !empty($_POST['checkout_date']) ? sanitize_text_field($_POST['checkout_date']) . ' 12:00:00' : $participant->checkout_date
        ];

        // Update participant
        $result = $wpdb->update($table_name, $data, ['id' => $participant_id]);

        if ($result !== false) {
            wp_send_json_success([
                'message' => 'Participant updated successfully',
                'participant' => $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $participant_id), ARRAY_A)
            ]);
        } else {
            wp_send_json_error([
                'message' => 'Error updating participant: ' . $wpdb->last_error
            ]);
        }
    }

    /**
     * AJAX handler for deleting a participant
     */
    public static function ajax_delete_participant() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        // Check required fields
        if (!isset($_POST['participant_id']) || empty($_POST['participant_id'])) {
            wp_send_json_error(['message' => 'Participant ID is required']);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

        // Check if the table exists
        $table_status = self::check_accommodation_table();
        if (!$table_status['exists']) {
            wp_send_json_error(['message' => 'Accommodation table does not exist']);
        }

        // Get participant ID
        $participant_id = intval($_POST['participant_id']);

        // Delete participant
        $result = $wpdb->delete($table_name, ['id' => $participant_id]);

        if ($result !== false) {
            wp_send_json_success([
                'message' => 'Participant deleted successfully'
            ]);
        } else {
            wp_send_json_error([
                'message' => 'Error deleting participant: ' . $wpdb->last_error
            ]);
        }
    }

    /**
     * AJAX handler for clearing the accommodation table
     */
    public static function ajax_clear_table() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

        // Check if the table exists
        $table_status = self::check_accommodation_table();
        if (!$table_status['exists']) {
            wp_send_json_error(['message' => 'Accommodation table does not exist']);
        }

        // Clear the table
        $result = $wpdb->query("TRUNCATE TABLE $table_name");

        if ($result !== false) {
            wp_send_json_success([
                'message' => 'Accommodation table cleared successfully'
            ]);
        } else {
            wp_send_json_error([
                'message' => 'Error clearing accommodation table: ' . $wpdb->last_error
            ]);
        }
    }

    /**
     * AJAX handler for recreating the accommodation table
     */
    public static function ajax_recreate_table() {
        // Suppress errors to prevent them from breaking JSON output
        @error_reporting(0);

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

            // Get the current data if needed
            $backup_data = [];
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if ($table_exists) {
                // Backup the data
                $backup_data = $wpdb->get_results("SELECT * FROM $table_name", ARRAY_A);

                // Drop the table
                $wpdb->query("DROP TABLE IF EXISTS $table_name");
            }

            // Create the table
            $result = self::create_table();

            // Restore the data if there was any
            if ($result && !empty($backup_data)) {
                foreach ($backup_data as $row) {
                    // Remove the id column to let it auto-increment
                    unset($row['id']);

                    // Insert the row
                    $wpdb->insert($table_name, $row);
                }
            }

            if ($result) {
                wp_send_json_success([
                    'message' => 'Accommodation table recreated successfully'
                ]);
            } else {
                wp_send_json_error([
                    'message' => 'Error recreating accommodation table'
                ]);
            }
        } catch (Exception $e) {
            error_log('Exception in ajax_recreate_table: ' . $e->getMessage());
            wp_send_json_error([
                'message' => 'Error recreating accommodation table: ' . $e->getMessage()
            ]);
        } catch (Error $e) {
            error_log('Error in ajax_recreate_table: ' . $e->getMessage());
            wp_send_json_error([
                'message' => 'Error recreating accommodation table: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for updating the accommodation table structure
     */
    public static function ajax_update_table_structure() {
        // Suppress errors to prevent them from breaking JSON output
        @error_reporting(0);

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        try {
            // Update the table structure
            $result = self::update_table_structure();

            if ($result) {
                wp_send_json_success([
                    'message' => 'Accommodation table structure updated successfully'
                ]);
            } else {
                wp_send_json_error([
                    'message' => 'Error updating accommodation table structure'
                ]);
            }
        } catch (Exception $e) {
            error_log('Exception in ajax_update_table_structure: ' . $e->getMessage());
            wp_send_json_error([
                'message' => 'Error updating accommodation table structure: ' . $e->getMessage()
            ]);
        } catch (Error $e) {
            error_log('Error in ajax_update_table_structure: ' . $e->getMessage());
            wp_send_json_error([
                'message' => 'Error updating accommodation table structure: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for bulk updating dates
     */
    public static function ajax_bulk_update_dates() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
        }

        // Check required fields
        if (!isset($_POST['category_id']) || empty($_POST['category_id'])) {
            wp_send_json_error(['message' => 'Category ID is required']);
        }

        if (!isset($_POST['checkin_date']) || empty($_POST['checkin_date'])) {
            wp_send_json_error(['message' => 'Check-in date is required']);
        }

        if (!isset($_POST['checkout_date']) || empty($_POST['checkout_date'])) {
            wp_send_json_error(['message' => 'Check-out date is required']);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_accommodation_unified';

        // Check if the table exists
        $table_status = self::check_accommodation_table();
        if (!$table_status['exists']) {
            wp_send_json_error(['message' => 'Accommodation table does not exist']);
        }

        // Get parameters
        $category_id = intval($_POST['category_id']);
        $checkin_date = sanitize_text_field($_POST['checkin_date']) . ' 14:00:00';
        $checkout_date = sanitize_text_field($_POST['checkout_date']) . ' 12:00:00';

        // Update dates for the category
        $result = $wpdb->query($wpdb->prepare(
            "UPDATE $table_name SET checkin_date = %s, checkout_date = %s WHERE category_id = %d",
            $checkin_date, $checkout_date, $category_id
        ));

        if ($result !== false) {
            wp_send_json_success([
                'message' => "Updated dates for $result participants"
            ]);
        } else {
            wp_send_json_error([
                'message' => 'Error updating dates: ' . $wpdb->last_error
            ]);
        }
    }
}
