<?php
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Add custom styles for form elements
add_action('wp_head', function() {
  echo '<style>
    .form-check-input:checked {
      background-color: #39bfda !important;
      border-color: #39bfda !important;
    }
    .form-check-input:focus {
      border-color: #39bfda !important;
      box-shadow: 0 0 0 0.25rem rgba(57, 191, 218, 0.25) !important;
    }
    .nav-link.active {
      color: white !important;
      background-color: #36b1dc !important;
      font-weight: 600;
      text-decoration: none !important;
    }
    #profileTabs .nav-link.active i {
      color: #fff !important;
      background-color: #36b1dc;
      font-weight: 600;
    }
    .btn-primary, .btn-primary:hover, .btn-primary:focus, .btn-primary:active,
    button[style*="background-color: #36b1dc"], button[style*="background-color: #36b1dc"]:hover,
    button[style*="background-color: #36b1dc"]:focus, button[style*="background-color: #36b1dc"]:active {
      color: white !important;
      background-color: #36b1dc !important;
    }
    .btn-primary i, .btn-primary:hover i,
    button[style*="background-color: #36b1dc"] i, button[style*="background-color: #36b1dc"]:hover i {
      color: white !important;
    }
    button[style*="background-color: #36b1dc"]:hover {
      background-color: #2d93b7 !important;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .password-toggle i {
      color: #36b1dc !important;
    }
    .password-toggle:hover i {
      color: #39bfda !important;
    }
    /* Tooltip styling */
    input[readonly][title]:hover {
      cursor: not-allowed;
    }
  </style>';
});
$avatar_id = get_user_meta($user_id, 'wp_user_avatar', true);
$avatar_url = $avatar_id ? wp_get_attachment_url($avatar_id) : get_avatar_url($user_id);
$first_name = get_user_meta($user_id, 'first_name', true);
$last_name = get_user_meta($user_id, 'last_name', true);
$phone = get_user_meta($user_id, 'billing_phone', true);
$email = $current_user->user_email;

// Get association data
$your_association = get_user_meta($user_id, 'your_association', true);

// Get club number and position data
$club_no = get_user_meta($user_id, 'club_no', true);
$club_position = get_user_meta($user_id, 'club_position', true);
$position = get_user_meta($user_id, 'position', true);

// Get dietary preference
$specific_diet = get_user_meta($user_id, 'specific_diet', true);

// Get allergies
$allergies = get_user_meta($user_id, 'alergies', true);
$allergies_array = !empty($allergies) ? maybe_unserialize($allergies) : [];

// Create nonce for security
$profile_nonce = wp_create_nonce('lci_profile_nonce');

// Enqueue Choices.js
wp_enqueue_style('choices-css', 'https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/styles/choices.min.css');
wp_enqueue_script('choices-js', 'https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/scripts/choices.min.js', array(), null, true);

// Enqueue Alpine.js if not already loaded
if (!wp_script_is('alpine-js', 'enqueued')) {
    wp_enqueue_script('alpine-js', 'https://cdn.jsdelivr.net/npm/alpinejs@3.13.0/dist/cdn.min.js', array(), null, true);
}

// Enqueue custom profile CSS
wp_enqueue_style('lci-profile-css', plugin_dir_url(dirname(__FILE__)) . 'assets/css/profile.css', array(), '1.0.0');
?>

<!-- Profile Header -->
<div class="card shadow p-4 rounded-4 border-0 mb-4">
  <div class="d-flex align-items-center">
    <div class="position-relative me-4">
      <div class="avatar-container position-relative" style="width: 100px; height: 100px;">
        <img src="<?php echo esc_url($avatar_url); ?>" alt="Avatar" id="lci-avatar-img" class="rounded-circle border border-4 border-primary shadow" width="100" height="100">
        <div class="avatar-overlay position-absolute top-0 start-0 w-100 h-100 rounded-circle d-flex align-items-center justify-content-center" style="background-color: rgba(54, 177, 220, 0.7); opacity: 0; transition: opacity 0.3s ease;">
          <i class="fas fa-camera text-white" style="font-size: 1.5rem;"></i>
        </div>
      </div>
      <button type="button" class="btn btn-primary position-absolute bottom-0 end-0 rounded-circle p-2 shadow" id="lci-avatar-btn" title="Upload new avatar" style="width: 36px; height: 36px; z-index: 2;">
        <i class="fas fa-camera"></i>
      </button>
      <input type="file" id="lci-avatar-input" accept="image/jpeg,image/png,image/gif,image/webp" style="display:none;">
    </div>
    <div>
      <h4 class="mb-0"><?php echo esc_html($first_name . ' ' . $last_name); ?></h4>
      <small class="text-muted">Edit your profile information below</small>
    </div>
  </div>
</div>

<!-- Profile Navigation -->
<ul class="nav nav-tabs mb-4" id="profileTabs" role="tablist">
  <li class="nav-item" role="presentation">
    <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab" aria-controls="personal" aria-selected="true">
      <i class="fas fa-user me-2"></i>Personal Information
    </button>
  </li>
  <li class="nav-item" role="presentation">
    <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
      <i class="fas fa-lock me-2"></i>Security
    </button>
  </li>

</ul>

<!-- Tab Content -->
<div class="tab-content" id="profileTabsContent">
  <!-- Personal Information Tab -->
  <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
    <div class="card shadow p-4 rounded-4 border-0">
      <h5 class="mb-4 text-primary">Personal Information</h5>

      <form id="lci-profile-form">
        <input type="hidden" id="lci_profile_nonce" value="<?php echo $profile_nonce; ?>">

        <div class="row mb-3">
          <div class="col-md-6 mb-3 mb-md-0">
            <label for="lci-first-name" class="form-label">First Name</label>
            <div class="input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-user text-primary"></i>
              </span>
              <input type="text" class="form-control border-0" id="lci-first-name" value="<?php echo esc_attr($first_name); ?>" readonly title="First name cannot be changed">
            </div>
          </div>

          <div class="col-md-6">
            <label for="lci-last-name" class="form-label">Last Name</label>
            <div class="input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-user text-primary"></i>
              </span>
              <input type="text" class="form-control border-0" id="lci-last-name" value="<?php echo esc_attr($last_name); ?>" readonly title="Last name cannot be changed">
            </div>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6 mb-3 mb-md-0">
            <label for="lci-email" class="form-label">Email Address</label>
            <div class="input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-envelope text-primary"></i>
              </span>
              <input type="email" class="form-control border-0" id="lci-email" value="<?php echo esc_attr($email); ?>" required>
            </div>
          </div>

          <div class="col-md-6">
            <label for="lci-phone" class="form-label">Phone Number</label>
            <div class="input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-phone text-primary"></i>
              </span>
              <input type="tel" class="form-control border-0" id="lci-phone" value="<?php echo esc_attr($phone); ?>" required>
            </div>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6 mb-3 mb-md-0">
            <label for="your_association" class="form-label">Association</label>
            <!-- Alpine.js Dropdown -->
            <div x-data="{
              open: false,
              selected: '<?php echo esc_attr($your_association); ?>',
              selectedText: '<?php echo esc_attr($your_association ? ($your_association == "C41" ? "C41 / OT" : ($your_association == "LC" ? "Ladies` Circle" : ($your_association == "RT" ? "Round Table" : ($your_association == "TANGENT" ? "Tangent" : ($your_association == "AGORA" ? "AGORA" : "Other"))))) : "Select your association"); ?>',
              options: [
                { value: 'LC', text: 'Ladies` Circle' },
                { value: 'RT', text: 'Round Table' },
                { value: 'TANGENT', text: 'Tangent' },
                { value: 'AGORA', text: 'AGORA' },
                { value: 'C41', text: 'C41 / OT' },
                { value: 'Other', text: 'Other' }
              ],
              select(option) {
                this.selected = option.value;
                this.selectedText = option.text;
                this.open = false;
              },
              clear() {
                this.selected = '';
                this.selectedText = 'Select your association';
              }
            }" class="relative">
              <div class="input-group rounded shadow-sm overflow-hidden">
                <span class="input-group-text bg-white border-0">
                  <i class="fas fa-users text-primary"></i>
                </span>
                <div class="form-control border-0 d-flex align-items-center justify-content-between" @click="open = !open">
                  <span x-text="selectedText"></span>
                  <div class="d-flex align-items-center">
                    <button x-show="selected" @click.stop="clear()" type="button" class="btn btn-sm text-secondary me-2">&times;</button>
                    <i class="fas" :class="open ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                  </div>
                </div>
                <input type="hidden" id="your_association" name="your_association" x-model="selected">
              </div>
              <div x-show="open" @click.away="open = false" class="position-absolute w-100 mt-1 rounded shadow-sm bg-white z-index-dropdown" style="z-index: 1000;">
                <div class="py-1">
                  <template x-for="option in options" :key="option.value">
                    <div @click="select(option)" class="px-3 py-2 cursor-pointer hover-bg-light d-flex align-items-center" :class="{ 'bg-light': selected === option.value }" style="cursor: pointer;">
                      <span x-text="option.text"></span>
                      <i x-show="selected === option.value" class="fas fa-check ms-auto text-primary"></i>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>

          <!-- Association Details Field - Shows dynamically based on selected association -->
          <div class="col-md-6"
            data-rt_association="<?php echo esc_attr(get_user_meta($user_id, 'rt_association', true) ?: ''); ?>"
            data-lc_association="<?php echo esc_attr(get_user_meta($user_id, 'lc_association', true) ?: ''); ?>"
            data-tangent_association="<?php echo esc_attr(get_user_meta($user_id, 'tangent_association', true) ?: ''); ?>"
            data-agora_association="<?php echo esc_attr(get_user_meta($user_id, 'agora_association', true) ?: ''); ?>"
            data-c41_club="<?php echo esc_attr(get_user_meta($user_id, 'c41_club', true) ?: ''); ?>"
            x-data="{
            associationType: '<?php echo esc_attr($your_association); ?>',
            open: false,
            searchTerm: '',
            selectedValue: '<?php
              // Get the appropriate association value based on the selected association type
              $association_value = '';
              if ($your_association === 'RT') {
                $association_value = get_user_meta($user_id, 'rt_association', true) ?: '';
              } elseif ($your_association === 'LC') {
                $association_value = get_user_meta($user_id, 'lc_association', true) ?: '';
              } elseif ($your_association === 'TANGENT') {
                $association_value = get_user_meta($user_id, 'tangent_association', true) ?: '';
              } elseif ($your_association === 'AGORA') {
                $association_value = get_user_meta($user_id, 'agora_association', true) ?: '';
              } elseif ($your_association === 'C41') {
                $association_value = get_user_meta($user_id, 'c41_club', true) ?: '';
              }
              echo esc_attr($association_value);
            ?>',
            selectedText: '',

            // Association options
            rtOptions: [
              'RT_Albania', 'RT_Arabian_Gulf', 'RT_Austria', 'RT_BELGIUM', 'RT_BOTSWANA', 'RT_Bulgaria',
              'RT_CAMEROON', 'RT_Canada', 'RT_Cyprus', 'RT_Czech Republic', 'RT_Denmark', 'RT_Eastern_Africa',
              'RT_Estonia', 'RT_France', 'RT_Germany', 'RT_Gibraltar', 'RT_GBI', 'RT_Hong_Kong', 'RT_Hungary',
              'RT_Iceland', 'RT_India', 'RT_Israel', 'RT_Italy', 'RT_Latvia', 'RT_Lithuania', 'RT_Luxembourg',
              'RT_Madagascar', 'RT_Malawi', 'RT_Malta', 'RT_Mauritius', 'RT_Moldova', 'RT_Monaco', 'RT_Morocco',
              'RT_Nepal', 'RT_The_Netherlands', 'RT_New_Zealand', 'RT_Nigeria', 'RT_Norway', 'RT_Philippines',
              'RT_Portugal', 'RT_Romania', 'RT_Russia', 'RT_San_Marino', 'RT_Senegal', 'RT_Seychelles',
              'RT_Singapore', 'RT_Southern_Africa', 'RT_Sri_Lanka', 'RT_Suomi_Finland', 'RT_Suriname',
              'RT_Sweden', 'RT_Switzerland', 'RT_Trinidad_Tobago', 'RT_Tunisia', 'RT_USA', 'RT_Zambia', 'RT_Zimbabwe'
            ],
            lcOptions: [
              'LC_Austria', 'LC_Bangladesh', 'LC_Belgium', 'LC_Botswana', 'LC_Cameroon', 'LC_Cyprus',
              'LC_Denmark', 'LC_Estonia', 'LC_Finland', 'LC_France', 'LC_Germany', 'LC_GBI', 'LC_Iceland',
              'LC_India', 'LC_Israel', 'LC_Italy', 'LC_Lithuania', 'LC_Luxembourg', 'LC_Madagascar', 'LC_Malta',
              'LC_Mauritius', 'LC_Morocco', 'LC_Namibia', 'LC_Nepal', 'LC_Norway', 'LC_Philippines', 'LC_Romania',
              'LC_Seychelles', 'LC_Singapore', 'LC_South_Africa', 'LC_Spain', 'LC_Sri_Lanka', 'LC_Sweden',
              'LC_Switzerland', 'LC_Netherlands', 'LC_United_Arab_Emerates', 'LC_USA', 'LC_Zambia', 'LC_Zimbabwe'
            ],
            tangentOptions: [
              'TANGENT_AUSTRIA', 'TANGENT_BELGIUM', 'TANGENT_BOTSWANA', 'TANGENT_CYPRUS', 'TANGENT_DENMARK', 'TANGENT_FINLAND',
              'TANGENT_FRANCE', 'TANGENT_GERMANY', 'TANGENT_GB_IRELAND', 'TANGENT_ICELAND', 'TANGENT_INDIA', 'TANGENT_ITALY',
              'TANGENT_LUXEMBOURG', 'TANGENT_MALTA', 'TANGENT_MAURITIUS', 'TANGENT_MOROCCO', 'TANGENT_NAMIBIA', 'TANGENT_NEPAL',
              'TANGENT_NETHERLANDS', 'TANGENT_NORWAY', 'TANGENT_PORTUGAL', 'TANGENT_ROMANIA', 'TANGENT_SOUTH_AFRICA',
              'TANGENT_SWEDEN', 'TANGENT_SWITZERLAND', 'TANGENT_ZAMBIA', 'TANGENT_ZIMBABWE'
            ],
            agoraOptions: [
              'AGORA_BELGIUM', 'AGORA_BOTSWANA', 'AGORA_CYPRUS', 'AGORA_FRANCE', 'AGORA_GERMANY',
              'AGORA_ICELAND', 'AGORA_ISRAEL', 'AGORA_INDIA', 'AGORA_ITALY', 'AGORA_MALTA', 'AGORA_MOROCCO',
              'AGORA_NORWAY', 'AGORA_ROMANIA', 'AGORA_SOUTH_AFRICA', 'AGORA_THE_NETHERLANDS', 'AGORA_UNITED_KINGDOM',
              'AGORA_USA', 'AGORA_ZAMBIA', 'AGORA_AUSTRIA', 'AGORA_LUXEMBOURG', 'AGORA_MADAGASCAR', 'AGORA_NEPAL',
              'AGORA_SWITZERLAND', 'AGORA_ZIMBABWE'
            ],
            c41Options: [
              'C41_AUSTRIA', 'C41_BELGIUM', 'C41_BOTSWANA', 'C41_BULGARIA', 'C41_CYPRUS', 'C41_DENMARK', 'C41_ESTONIA',
              'C41_FINLAND', 'C41_GERMANY', 'C41_GBI', 'C41_HUNGARY', 'C41_ICELAND', 'C41_INDIA', 'C41_ISRAEL', 'C41_ITALY',
              'C41_KENYA', 'C41_LITHUANIA', 'C41_MADAGASCAR', 'C41_MALAWI', 'C41_MALTA', 'C41_MAURITIUS', 'C41_MOROCCO',
              'C41_NEPAL', 'C41_NETHERLANDS', 'C41_NORWAY', 'C41_PORTUGAL', 'C41_ROMANIA', 'C41_SENEGAL', 'C41_SOUTH_AFRICA',
              'C41_SPAIN', 'C41_SRI_LANKA', 'C41_SWEDEN', 'C41_SWITZERLAND', 'C41_SURINAME', 'C41_USA', 'C41_ZAMBIA'
            ],

            get currentOptions() {
              if (this.associationType === 'RT') return this.rtOptions;
              if (this.associationType === 'LC') return this.lcOptions;
              if (this.associationType === 'TANGENT') return this.tangentOptions;
              if (this.associationType === 'AGORA') return this.agoraOptions;
              if (this.associationType === 'C41') return this.c41Options;
              return [];
            },

            get filteredOptions() {
              if (!this.searchTerm) return this.currentOptions;
              return this.currentOptions.filter(option =>
                option.toLowerCase().replace('_', ' ').includes(this.searchTerm.toLowerCase())
              );
            },

            get fieldName() {
              if (this.associationType === 'RT') return 'rt_association';
              if (this.associationType === 'LC') return 'lc_association';
              if (this.associationType === 'TANGENT') return 'tangent_association';
              if (this.associationType === 'AGORA') return 'agora_association';
              if (this.associationType === 'C41') return 'c41_club';
              return '';
            },

            get labelText() {
              if (this.associationType === 'RT') return 'Round Table Association';
              if (this.associationType === 'LC') return 'Ladies` Circle Association';
              if (this.associationType === 'TANGENT') return 'TANGENT Association';
              if (this.associationType === 'AGORA') return 'AGORA Association';
              if (this.associationType === 'C41') return 'C41/OT Association';
              return 'Association Details';
            },

            formatOptionText(option) {
              // Format the option text to display properly
              let text = option.replace(/_/g, ' ');

              // Handle specific association prefixes
              if (text.startsWith('RT ')) {
                return 'Round Table ' + text.substring(3);
              } else if (text.startsWith('LC ')) {
                return 'Ladies` Circle ' + text.substring(3);
              } else if (text.startsWith('TANGENT ')) {
                return 'TANGENT ' + text.substring(8);
              } else if (text.startsWith('AGORA ')) {
                // Special case for AGORA TANGENT GERMANY
                if (text === 'AGORA GERMANY') {
                  return 'AGORA TANGENT GERMANY';
                }
                return 'AGORA ' + text.substring(6);
              } else if (text.startsWith('C41 ')) {
                // Special case for C41 GBI
                if (text === 'C41 GBI') {
                  return 'Club 41 GB & I';
                }
                return 'Club 41 ' + text.substring(4);
              }

              return text;
            },

            select(option) {
              this.selectedValue = option;
              this.selectedText = this.formatOptionText(option);
              this.open = false;
              this.searchTerm = '';
            },

            clear() {
              this.selectedValue = '';
              this.selectedText = '';
              this.searchTerm = '';
            },

            init() {
              // Set initial selectedText value
              this.selectedText = this.formatOptionText(this.selectedValue);

              // Watch for changes in the association dropdown
              this.$watch('$store.selectedAssociation', value => {
                this.associationType = value;

                // Clear the selection when changing association type
                this.selectedValue = '';
                this.selectedText = '';

                // Check if we have a saved value for this association type
                if (value) {
                  // We'll use a small delay to ensure the DOM is updated
                  setTimeout(() => {
                    const fieldName = this.fieldName;
                    if (fieldName) {
                      // Try to get the saved value from the hidden input or data attribute
                      const savedValue = document.querySelector(`[data-${fieldName}]`)?.getAttribute(`data-${fieldName}`) || '';
                      if (savedValue) {
                        this.selectedValue = savedValue;
                        this.selectedText = this.formatOptionText(savedValue);
                      }
                    }
                  }, 50);
                }
              });

              // Initialize the store if it doesn't exist
              if (!window.Alpine.store('selectedAssociation')) {
                window.Alpine.store('selectedAssociation', this.associationType);
              }

              // Watch for changes in the first dropdown
              document.getElementById('your_association').addEventListener('change', (e) => {
                window.Alpine.store('selectedAssociation', e.target.value);
                this.associationType = e.target.value;
              });
            }
          }"
          x-init="init()"
          x-show="associationType && associationType !== 'Other'">
            <label :for="fieldName" class="form-label" x-text="labelText"></label>
            <div class="input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-flag text-primary"></i>
              </span>
              <div class="form-control border-0 d-flex align-items-center justify-content-between" @click="open = !open">
                <span x-text="selectedText || 'Select your ' + labelText"></span>
                <div class="d-flex align-items-center">
                  <button x-show="selectedValue" @click.stop="clear()" type="button" class="btn btn-sm text-secondary me-2">&times;</button>
                  <i class="fas" :class="open ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                </div>
              </div>
              <input type="hidden" :id="fieldName" :name="fieldName" x-model="selectedValue">
            </div>
            <div x-show="open" @click.away="open = false" class="position-absolute w-100 mt-1 rounded shadow-sm bg-white z-index-dropdown" style="z-index: 1000;">
              <div class="p-2">
                <input type="text" class="form-control form-control-sm mb-2" placeholder="Search..." x-model="searchTerm" @click.stop>
              </div>
              <div class="py-1 dropdown-scrollable" style="max-height: 300px; overflow-y: auto;">
                <template x-for="option in filteredOptions" :key="option">
                  <div @click="select(option)" class="px-3 py-2 cursor-pointer hover-bg-light d-flex align-items-center" :class="{ 'bg-light': selectedValue === option }" style="cursor: pointer;">
                    <span x-text="formatOptionText(option)"></span>
                    <i x-show="selectedValue === option" class="fas fa-check ms-auto text-primary"></i>
                  </div>
                </template>
                <div x-show="filteredOptions.length === 0" class="px-3 py-2 text-muted">
                  No results found
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6 mb-3 mb-md-0">
            <label for="lci-club-no" class="form-label">Club No / Name</label>
            <div class="input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-id-badge text-primary"></i>
              </span>
              <input type="text" class="form-control border-0" id="lci-club-no" value="<?php echo esc_attr($club_no); ?>" placeholder="Enter your club number or name">
            </div>
          </div>

          <div class="col-md-6">
            <label for="lci-club-position" class="form-label">Position in Club</label>
            <div class="input-group rounded shadow-sm overflow-hidden">
              <span class="input-group-text bg-white border-0">
                <i class="fas fa-user-tie text-primary"></i>
              </span>
              <input type="text" class="form-control border-0" id="lci-club-position" value="<?php echo esc_attr($position ?: $club_position); ?>" placeholder="Enter your position in the club">
            </div>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-12">
            <label class="form-label">General Dietary Preferences</label>
            <div class="bg-white rounded shadow-sm p-3">
              <div class="form-check form-check-inline">
                <input class="form-check-input text-primary" type="radio" name="specific_diet" id="specific_diet_Vegetarian" value="Vegetarian" <?php checked($specific_diet, 'Vegetarian'); ?>>
                <label class="form-check-label" for="specific_diet_Vegetarian">Vegetarian</label>
              </div>
              <div class="form-check form-check-inline">
                <input class="form-check-input text-primary" type="radio" name="specific_diet" id="specific_diet_Vegan" value="Vegan" <?php checked($specific_diet, 'Vegan'); ?>>
                <label class="form-check-label" for="specific_diet_Vegan">Vegan</label>
              </div>
              <div class="form-check form-check-inline">
                <input class="form-check-input text-primary" type="radio" name="specific_diet" id="specific_diet_None" value="None" <?php checked($specific_diet, 'None'); ?> <?php if (empty($specific_diet)) echo 'checked'; ?>>
                <label class="form-check-label" for="specific_diet_None">None</label>
              </div>
            </div>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-12">
            <label class="form-label">Food allergies or intolerances</label>
            <div class="bg-white rounded shadow-sm p-3">
              <div class="d-flex flex-wrap">
                <div class="form-check me-3 mb-2">
                  <input class="form-check-input text-primary" type="checkbox" name="allergies[]" id="allergies_Gluten" value="Gluten" <?php echo in_array('Gluten', $allergies_array) ? 'checked' : ''; ?>>
                  <label class="form-check-label" for="allergies_Gluten">Gluten</label>
                </div>
                <div class="form-check me-3 mb-2">
                  <input class="form-check-input text-primary" type="checkbox" name="allergies[]" id="allergies_Dairy" value="Dairy/Lactose" <?php echo in_array('Dairy/Lactose', $allergies_array) ? 'checked' : ''; ?>>
                  <label class="form-check-label" for="allergies_Dairy">Dairy/Lactose</label>
                </div>
                <div class="form-check me-3 mb-2">
                  <input class="form-check-input text-primary" type="checkbox" name="allergies[]" id="allergies_Nuts" value="Nuts" <?php echo in_array('Nuts', $allergies_array) ? 'checked' : ''; ?>>
                  <label class="form-check-label" for="allergies_Nuts">Nuts</label>
                </div>
                <div class="form-check me-3 mb-2">
                  <input class="form-check-input text-primary" type="checkbox" name="allergies[]" id="allergies_Shellfish" value="Shellfish" <?php echo in_array('Shellfish', $allergies_array) ? 'checked' : ''; ?>>
                  <label class="form-check-label" for="allergies_Shellfish">Shellfish</label>
                </div>
                <div class="form-check me-3 mb-2">
                  <input class="form-check-input text-primary" type="checkbox" name="allergies[]" id="allergies_Eggs" value="Eggs" <?php echo in_array('Eggs', $allergies_array) ? 'checked' : ''; ?>>
                  <label class="form-check-label" for="allergies_Eggs">Eggs</label>
                </div>
                <div class="form-check me-3 mb-2">
                  <input class="form-check-input text-primary" type="checkbox" name="allergies[]" id="allergies_Other" value="other" <?php echo in_array('other', $allergies_array) ? 'checked' : ''; ?>>
                  <label class="form-check-label" for="allergies_Other">Other (please specify)</label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <?php
        // Get user's orders to check for allergies in order meta
        $customer_orders = wc_get_orders(array(
          'customer_id' => $user_id,
          'limit' => -1
        ));

        $order_allergies = array();
        $order_diet = '';
        $has_order_allergies = false;
        $has_order_diet = false;
        $debug_info = array(
          'orders_count' => count($customer_orders),
          'order_ids' => array(),
          'found_allergies' => array(),
          'found_diet' => array()
        );

        // Loop through orders to find allergies and dietary preferences
        foreach ($customer_orders as $order) {
          $order_id = $order->get_id();
          $debug_info['order_ids'][] = $order_id;

          // Check for allergies in order meta with different possible key formats
          $order_allergy_meta = $order->get_meta('allergies');
          $debug_info['found_allergies'][$order_id . '_allergies'] = $order_allergy_meta;

          // Also check for 'alergies' (note the spelling)
          $order_alergies_meta = $order->get_meta('alergies');
          $debug_info['found_allergies'][$order_id . '_alergies'] = $order_alergies_meta;

          // Check for _allergies (with underscore prefix)
          $order_allergies_underscore = $order->get_meta('_allergies');
          $debug_info['found_allergies'][$order_id . '__allergies'] = $order_allergies_underscore;

          // Check for 'other' meta key which appears to contain allergy information
          $order_other_meta = $order->get_meta('other');
          $debug_info['found_allergies'][$order_id . '_other'] = $order_other_meta;

          // Process allergies meta
          foreach ([$order_allergy_meta, $order_alergies_meta, $order_allergies_underscore, $order_other_meta] as $allergy_meta) {
            if (!empty($allergy_meta)) {
              $has_order_allergies = true;
              if (is_string($allergy_meta) && is_serialized($allergy_meta)) {
                $allergy_array = maybe_unserialize($allergy_meta);
                if (is_array($allergy_array)) {
                  foreach ($allergy_array as $allergy) {
                    if (!in_array($allergy, $order_allergies)) {
                      $order_allergies[] = $allergy;
                    }
                  }
                }
              } elseif (is_string($allergy_meta)) {
                if (!in_array($allergy_meta, $order_allergies)) {
                  $order_allergies[] = $allergy_meta;
                }
              } elseif (is_array($allergy_meta)) {
                foreach ($allergy_meta as $allergy) {
                  if (!in_array($allergy, $order_allergies)) {
                    $order_allergies[] = $allergy;
                  }
                }
              }
            }
          }

          // Check for dietary preferences in order meta
          $diet_meta = $order->get_meta('specific_diet');
          $debug_info['found_diet'][$order_id . '_specific_diet'] = $diet_meta;

          // Also check for _specific_diet (with underscore prefix)
          $diet_meta_underscore = $order->get_meta('_specific_diet');
          $debug_info['found_diet'][$order_id . '__specific_diet'] = $diet_meta_underscore;

          // Process diet meta
          foreach ([$diet_meta, $diet_meta_underscore] as $diet_meta_value) {
            if (!empty($diet_meta_value) && $diet_meta_value !== 'None') {
              $has_order_diet = true;
              $order_diet = $diet_meta_value;
            }
          }

          // Check for allergies in order items meta
          foreach ($order->get_items() as $item_id => $item) {
            // Check different possible meta keys for allergies
            $item_allergies = $item->get_meta('allergies');
            $debug_info['found_allergies'][$order_id . '_item_' . $item_id . '_allergies'] = $item_allergies;

            // Also check for 'alergies' (note the spelling)
            $item_alergies = $item->get_meta('alergies');
            $debug_info['found_allergies'][$order_id . '_item_' . $item_id . '_alergies'] = $item_alergies;

            // Check for _allergies (with underscore prefix)
            $item_allergies_underscore = $item->get_meta('_allergies');
            $debug_info['found_allergies'][$order_id . '_item_' . $item_id . '__allergies'] = $item_allergies_underscore;

            // Check for 'other' meta key which appears to contain allergy information
            $item_other = $item->get_meta('other');
            $debug_info['found_allergies'][$order_id . '_item_' . $item_id . '_other'] = $item_other;

            // Process allergies meta
            foreach ([$item_allergies, $item_alergies, $item_allergies_underscore, $item_other] as $allergy_meta) {
              if (!empty($allergy_meta)) {
                $has_order_allergies = true;
                if (is_string($allergy_meta) && is_serialized($allergy_meta)) {
                  $allergy_array = maybe_unserialize($allergy_meta);
                  if (is_array($allergy_array)) {
                    foreach ($allergy_array as $allergy) {
                      if (!in_array($allergy, $order_allergies)) {
                        $order_allergies[] = $allergy;
                      }
                    }
                  }
                } elseif (is_string($allergy_meta)) {
                  if (!in_array($allergy_meta, $order_allergies)) {
                    $order_allergies[] = $allergy_meta;
                  }
                } elseif (is_array($allergy_meta)) {
                  foreach ($allergy_meta as $allergy) {
                    if (!in_array($allergy, $order_allergies)) {
                      $order_allergies[] = $allergy;
                    }
                  }
                }
              }
            }

            // Check for dietary preferences in order items meta
            $item_diet = $item->get_meta('specific_diet');
            $debug_info['found_diet'][$order_id . '_item_' . $item_id . '_specific_diet'] = $item_diet;

            // Also check for _specific_diet (with underscore prefix)
            $item_diet_underscore = $item->get_meta('_specific_diet');
            $debug_info['found_diet'][$order_id . '_item_' . $item_id . '__specific_diet'] = $item_diet_underscore;

            // Process diet meta
            foreach ([$item_diet, $item_diet_underscore] as $diet_meta_value) {
              if (!empty($diet_meta_value) && $diet_meta_value !== 'None') {
                $has_order_diet = true;
                $order_diet = $diet_meta_value;
              }
            }
          }

          // Check order notes for allergy information
          $order_notes = wc_get_order_notes([
            'order_id' => $order_id,
            'type' => 'customer',
          ]);

          $debug_info['order_notes'][$order_id] = [];

          foreach ($order_notes as $note) {
            $note_content = $note->content;
            $debug_info['order_notes'][$order_id][] = $note_content;

            // Check if note contains allergy information
            if (stripos($note_content, 'allerg') !== false) {
              $has_order_allergies = true;
              $order_allergies[] = 'From order note: ' . $note_content;
            }

            // Check if note contains dietary information
            if (stripos($note_content, 'diet') !== false ||
                stripos($note_content, 'vegan') !== false ||
                stripos($note_content, 'vegetarian') !== false) {
              $has_order_diet = true;
              $order_diet = 'From order note: ' . $note_content;
            }
          }
        }

        // We've identified that allergies are stored in the 'other' meta key
        // No need to keep the debug info in production
        ?><?php

        // Display order allergies and dietary preferences if found
        if (($has_order_allergies && !empty($order_allergies)) || $has_order_diet):
        ?>
        <div class="row mb-4">
          <div class="col-12">
            <div style="position: relative; margin-bottom: 15px;">
              <div style="position: absolute; top: 0; right: 0; background-color: #ff6b6b; color: white; padding: 5px 15px; border-radius: 0 0 0 10px; font-size: 12px; font-weight: 600; z-index: 2;">
                <i class="fas fa-exclamation-triangle me-1"></i> Important Health Information
              </div>
              <div style="background: rgba(255, 107, 107, 0.1); border-left: 4px solid #ff6b6b; padding: 20px; border-radius: 10px; margin-top: 20px;">
                <h6 style="color: #ff6b6b; margin-bottom: 10px; font-weight: 600;">
                  <i class="fas fa-exclamation-circle me-2"></i>Dietary Information from Your Registration
                </h6>
                <p style="font-size: 14px; color: #555; margin-bottom: 15px;">
                  The following dietary information was recorded during your registration process. This information is important for your safety and comfort during the event.
                </p>

                <?php if ($has_order_diet): ?>
                <div class="mb-3">
                  <h6 style="font-size: 14px; color: #555; margin-bottom: 10px; font-weight: 600;">Dietary Preference:</h6>
                  <div class="badge bg-primary text-white me-2 mb-2" style="padding: 8px 12px; border-radius: 20px; font-size: 13px; font-weight: 500;">
                    <i class="fas fa-utensils me-1"></i> <?php echo esc_html($order_diet); ?>
                  </div>
                </div>
                <?php endif; ?>

                <?php if ($has_order_allergies && !empty($order_allergies)): ?>
                <div>
                  <h6 style="font-size: 14px; color: #555; margin-bottom: 10px; font-weight: 600;">Food Allergies or Intolerances:</h6>
                  <div class="d-flex flex-wrap" style="width: 100%;">
                    <?php
                    // Process allergies for display
                    $formatted_allergies = [];

                    foreach ($order_allergies as $allergy) {
                      // If it's a comma-separated list, split it
                      if (strpos($allergy, ',') !== false) {
                        $allergy_parts = explode(',', $allergy);
                        foreach ($allergy_parts as $part) {
                          $part = trim($part);
                          if (!empty($part) && !in_array($part, $formatted_allergies)) {
                            $formatted_allergies[] = $part;
                          }
                        }
                      } else {
                        // Remove "From order note: " prefix if present
                        if (strpos($allergy, 'From order note: ') === 0) {
                          $allergy = substr($allergy, 17);
                        }

                        $allergy = trim($allergy);
                        if (!empty($allergy) && !in_array($allergy, $formatted_allergies)) {
                          $formatted_allergies[] = $allergy;
                        }
                      }
                    }

                    // Display each allergy as a badge
                    foreach ($formatted_allergies as $allergy):
                    ?>
                      <div class="badge bg-light text-dark me-2 mb-2" style="padding: 6px 10px; border-radius: 20px; font-size: 13px; font-weight: 500; border: 1px solid rgba(0,0,0,0.1); max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: normal; word-wrap: break-word; display: inline-flex; align-items: center;">
                        <i class="fas fa-allergies me-1" style="color: #ff6b6b; flex-shrink: 0;"></i>
                        <span style="overflow: hidden; text-overflow: ellipsis;"><?php echo esc_html($allergy); ?></span>
                      </div>
                    <?php endforeach; ?>
                  </div>
                </div>
                <?php endif; ?>

                <?php if (!$has_order_diet && (!$has_order_allergies || empty($order_allergies))): ?>
                <div class="alert alert-info" style="margin-top: 15px;">
                  <i class="fas fa-info-circle me-2"></i> No dietary preferences or allergies found in your registration data. If you have any dietary requirements, please update your profile or contact support.
                </div>
                <?php endif; ?>

                <p style="font-size: 13px; color: #777; margin-top: 15px; margin-bottom: 0;">
                  <i class="fas fa-info-circle me-1"></i> If this information is incorrect or needs to be updated, please contact support.
                </p>
              </div>
            </div>
          </div>
        </div>
        <?php endif; ?>

        <div class="d-grid gap-2">
          <button type="submit" class="btn px-4 text-white" style="background-color: #36b1dc;">
            <i class="fas fa-save me-2 text-white"></i>Save Changes
          </button>
        </div>
      </form>
    </div>
  </div>



  <!-- Security Tab -->
  <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
    <div class="card shadow p-4 rounded-4 border-0">
      <h5 class="mb-4 text-primary">Change Password</h5>

      <form id="lci-password-form">
        <div class="mb-3">
          <label for="lci-current-password" class="form-label">Current Password</label>
          <div class="input-group rounded shadow-sm overflow-hidden">
            <span class="input-group-text bg-white border-0">
              <i class="fas fa-lock text-primary"></i>
            </span>
            <input type="password" class="form-control border-0" id="lci-current-password" required>
            <button class="btn bg-white border-0 password-toggle" type="button">
              <i class="fas fa-eye"></i>
            </button>
          </div>
        </div>

        <div class="mb-3">
          <label for="lci-new-password" class="form-label">New Password</label>
          <div class="input-group rounded shadow-sm overflow-hidden">
            <span class="input-group-text bg-white border-0">
              <i class="fas fa-key text-primary"></i>
            </span>
            <input type="password" class="form-control border-0" id="lci-new-password" required>
            <button class="btn bg-white border-0 password-toggle" type="button">
              <i class="fas fa-eye"></i>
            </button>
          </div>
          <div class="password-strength mt-2">
            <div class="progress" style="height: 5px;">
              <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
            <small class="text-muted">Password strength: <span id="password-strength-text">None</span></small>
          </div>
        </div>

        <div class="mb-4">
          <label for="lci-confirm-password" class="form-label">Confirm New Password</label>
          <div class="input-group rounded shadow-sm overflow-hidden">
            <span class="input-group-text bg-white border-0">
              <i class="fas fa-key text-primary"></i>
            </span>
            <input type="password" class="form-control border-0" id="lci-confirm-password" required>
            <button class="btn bg-white border-0 password-toggle" type="button">
              <i class="fas fa-eye"></i>
            </button>
          </div>
          <div id="password-match" class="form-text"></div>
        </div>

        <div class="d-grid gap-2">
          <button type="submit" class="btn px-4 text-white" style="background-color: #36b1dc;">
            <i class="fas fa-key me-2 text-white"></i>Change Password
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
