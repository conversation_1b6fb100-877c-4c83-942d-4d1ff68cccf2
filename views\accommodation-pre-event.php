<?php
/**
 * Pre-Event Brasov Accommodation View
 *
 * Displays Pre-Event accommodation options in Brasov
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Output script to ensure functions are globally accessible
?>
<script>
// Define global function stubs to ensure they are available
window.addToCartPreEvent = function() {
    console.log("addToCartPreEvent called");
    // The real implementation will be loaded later
};

window.closePreEventConfirmModal = function() {
    console.log("closePreEventConfirmModal called");
    // Direct DOM manipulation to close the modal
    const confirmModal = document.getElementById('preEventCartConfirmModal');
    if (confirmModal) {
        confirmModal.style.display = 'none';
    }
};

window.showPreEventConfirmModal = function(productId) {
    console.log("showPreEventConfirmModal called with", productId);
    // The real implementation will be loaded later
};

window.showPreEventModal = function(event) {
    console.log("showPreEventModal called");
    // The real implementation will be loaded later
};

window.closePreEventModal = function() {
    console.log("closePreEventModal called");
    // The real implementation will be loaded later
};

window.openCustomMiniCart = function() {
    console.log("openCustomMiniCart called");
    // Direct DOM manipulation to open the mini cart
    const modal = document.getElementById('custom-mini-cart-modal');
    if (modal) {
        modal.style.display = 'block';
        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';

        // Try to fetch cart items if the function exists
        if (typeof fetchCustomMiniCartItems === 'function') {
            fetchCustomMiniCartItems();
        } else if (typeof window.fetchCustomMiniCartItems === 'function') {
            window.fetchCustomMiniCartItems();
        } else {
            console.log("fetchCustomMiniCartItems function not available yet");
            // Show loading state
            const loadingElement = document.getElementById('custom-mini-cart-loading');
            if (loadingElement) loadingElement.style.display = 'flex';

            // Hide other elements
            const emptyElement = document.getElementById('custom-mini-cart-empty');
            if (emptyElement) emptyElement.style.display = 'none';

            const itemsElement = document.getElementById('custom-mini-cart-items');
            if (itemsElement) itemsElement.style.display = 'none';

            const fundraisingElement = document.getElementById('custom-mini-cart-fundraising');
            if (fundraisingElement) fundraisingElement.style.display = 'none';
        }
    }
};

window.closeCustomMiniCart = function() {
    console.log("closeCustomMiniCart called");
    // Direct DOM manipulation to close the mini cart
    const modal = document.getElementById('custom-mini-cart-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('overflow');
    }
};
</script>
<?php

// Enqueue the accommodation CSS
wp_enqueue_style('accommodation-css', LCI2025_URL . 'assets/css/accommodation.css', array(), LCI2025_VERSION);

// Localize script with AJAX URL and nonce
wp_localize_script('jquery', 'lci_ajax', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lci_ajax_nonce')
));

// Remove the dashboard mini cart container
remove_action('lci_dashboard_before_content', 'lci_add_mini_cart_to_dashboard');

// Add inline CSS for the accommodation products
$accommodation_products_css = "
.accommodation-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.accommodation-product-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.accommodation-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accommodation-product-image {
    height: 350px;
    overflow: hidden;
    position: relative;
}

.accommodation-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.accommodation-product-card:hover .accommodation-product-image img {
    transform: scale(1.05);
}

.accommodation-product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.accommodation-product-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.accommodation-product-title {
    color: #00b2e3;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.accommodation-product-description {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
    flex-grow: 1;
}

.accommodation-product-price {
    font-weight: bold;
    font-size: 18px;
    color: #343a40;
    margin-bottom: 15px;
}

.accommodation-product-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #36b1dc;
    color: white !important;
    border: none;
    width: 100%;
    box-shadow: 0 4px 6px rgba(54, 177, 220, 0.2);
}

.accommodation-product-button i {
    color: white;
}

.accommodation-product-button:hover {
    background-color: #2d9cc3;
    color: white !important;
    box-shadow: 0 6px 12px rgba(54, 177, 220, 0.3);
    transform: translateY(-2px);
}

.accommodation-product-button:hover i {
    color: white !important;
}

.accommodation-product-variations {
    margin-top: 15px;
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.accommodation-product-variation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.accommodation-product-variation-name {
    font-size: 14px;
    color: #495057;
}

.accommodation-product-variation-price {
    font-weight: bold;
    color: #00b2e3;
}

.accommodation-product-stars {
    display: flex;
    margin-bottom: 10px;
    color: #ffc107;
}

.accommodation-product-soldout {
    opacity: 0.7;
    position: relative;
}

.accommodation-product-soldout::after {
    content: 'SOLD OUT';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-15deg);
    background-color: rgba(220, 53, 69, 0.8);
    color: white;
    padding: 10px 20px;
    font-weight: bold;
    font-size: 24px;
    border-radius: 5px;
    z-index: 10;
}

.accommodation-product-variation.soldout {
    opacity: 0.5;
    text-decoration: line-through;
}

.nights-selector {
    max-width: 383px;
    margin: 0px auto;
}

.nights-selector-container {
    background: white;
    border: 2px solid #00b2e3;
    border-radius: 50px;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nights-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #00b2e3;
    color: white;
    border: none;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nights-btn:disabled {
    background: #e0e0e0;
    cursor: not-allowed;
}

.nights-display {
    flex-grow: 1;
    text-align: center;
}

.nights-display span:first-child {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.nights-count {
    font-size: 24px;
    font-weight: bold;
    color: #00b2e3;
}

.nights-label {
    font-size: 16px;
    color: #555;
    margin-left: 5px;
}

@media (max-width: 768px) {
    .accommodation-products-grid {
        grid-template-columns: 1fr;
    }
}
";

wp_add_inline_style('accommodation-css', $accommodation_products_css);

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Get Pre-Event accommodation products (category ID 37)
$pre_event_products = [];
$args = [
    'post_type' => 'product',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'tax_query' => array(
        array(
            'taxonomy' => 'product_cat',
            'field' => 'id',
            'terms' => 37, // Pre/Post Event accommodation category ID
        ),
    ),
];

$posts = get_posts($args);
foreach ($posts as $post) {
    $product = wc_get_product($post->ID);
    if ($product) {
        $pre_event_products[] = $product;
    }
}

// Check if any products are in the cart
$products_in_cart = [];
if (WC()->cart) {
    foreach (WC()->cart->get_cart() as $cart_item) {
        $products_in_cart[] = $cart_item['product_id'];
    }
}

// Initialize nights value
$nights = isset($_POST['nights']) ? intval($_POST['nights']) : 1;
?>

<div class="accommodation-container">
    <!-- Header Section - 2025 UX/UI Style -->
    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 40px 20px; position: relative; overflow: hidden; border-radius: 0 0 30px 30px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2); margin-bottom: 40px;">
        <!-- Animated background elements -->
        <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%); border-radius: 50%; animation: pulse 8s infinite alternate;"></div>
        <div class="position-absolute" style="bottom: -30px; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%); border-radius: 50%; animation: float 12s infinite alternate;"></div>
        <div class="position-absolute" style="top: 20%; right: 20%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%); border-radius: 50%; animation: float 7s infinite alternate-reverse;"></div>

        <!-- Glassmorphism overlay -->
        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>

        <div class="container position-relative" style="z-index: 2;">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <!-- Modern 3D icon with shadow and glow -->
                    <div style="background: rgba(255, 255, 255, 0.2); width: 60px; height: 60px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); transform: perspective(500px) rotateX(10deg); border: 1px solid rgba(255, 255, 255, 0.3);">
                        <div style="position: relative;">
                            <i class="fas fa-hotel" style="color: white; font-size: 28px; filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.8), transparent 70%); filter: blur(8px); opacity: 0.5; animation: glow 3s infinite alternate;"></div>
                        </div>
                    </div>

                    <!-- Typography with modern styling -->
                    <div>
                        <!-- Title -->
                        <h1 style="color: white; font-size: 28px; font-weight: 800; margin: 0; letter-spacing: -0.5px; text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">Pre-Event Accommodation</h1>

                        <!-- Simple subtitle text -->
                        <p style="color: white; font-size: 16px; margin: 10px 0 0 0; font-weight: 400; letter-spacing: 0.5px; text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);">Book your stay in Brasov before the main event</p>

                        <!-- Back button positioned below the subtitle -->
                        <div style="margin-top: 12px;">
                            <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation', remove_query_arg('category_id'))); ?>" class="d-inline-flex align-items-center back-to-accommodations-btn" style="background: rgba(255, 255, 255, 0.2); color: white; border: none; padding: 6px 14px; border-radius: 8px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; font-size: 12px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); transition: all 0.3s ease; position: relative; overflow: hidden; text-decoration: none;">
                                <i class="fas fa-arrow-left me-1" style="color: white; position: relative; z-index: 1; font-size: 10px;"></i> <span style="color: white; position: relative; z-index: 1;">Back to All Accommodations</span>
                            </a>
                        </div>


                    </div>
                </div>

                <!-- Modern Trendy Mini Cart Button -->
                <div style="position: relative; z-index: 10;">
                    <button id="custom-mini-cart-button" class="custom-mini-cart-btn" onclick="openCustomMiniCart()">
                        <div class="custom-mini-cart-btn-inner">
                            <div class="custom-mini-cart-icon-wrapper">
                                <div class="custom-mini-cart-icon-bg">
                                    <i class="fas fa-shopping-bag custom-mini-cart-icon"></i>
                                </div>
                                <span class="custom-mini-cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                            </div>
                            <div class="custom-mini-cart-info">
                                <span class="custom-mini-cart-label">Your Cart</span>
                                <span class="custom-mini-cart-total"><?php echo WC()->cart->get_cart_total(); ?></span>
                            </div>
                        </div>
                    </button>
                </div>

                <style>
                /* Modern Trendy Mini Cart Button Styles */
                .custom-mini-cart-btn {
                    position: relative;
                    background: rgba(255, 255, 255, 0.15);
                    backdrop-filter: blur(10px);
                    -webkit-backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 16px;
                    padding: 0;
                    overflow: hidden;
                    cursor: pointer;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
                }

                .custom-mini-cart-btn-inner {
                    position: relative;
                    z-index: 2;
                    display: flex;
                    align-items: center;
                    padding: 10px 16px;
                }

                .custom-mini-cart-icon-wrapper {
                    position: relative;
                    margin-right: 14px;
                }

                .custom-mini-cart-icon-bg {
                    width: 42px;
                    height: 42px;
                    background: linear-gradient(135deg, #36b1dc, #2980b9);
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 4px 10px rgba(54, 177, 220, 0.3);
                    transition: all 0.3s ease;
                }

                .custom-mini-cart-icon {
                    color: white;
                    font-size: 18px;
                    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
                }

                .custom-mini-cart-count {
                    position: absolute;
                    top: -6px;
                    right: -6px;
                    background: #ff6b6b;
                    color: white;
                    font-size: 11px;
                    font-weight: 700;
                    min-width: 20px;
                    height: 20px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                    padding: 0 6px;
                    border: 2px solid white;
                    transition: all 0.3s ease;
                }

                .custom-mini-cart-info {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                }

                .custom-mini-cart-label {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: 500;
                    margin-bottom: 2px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .custom-mini-cart-total {
                    color: white;
                    font-weight: 700;
                    font-size: 16px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                }

                /* Hover effects */
                .custom-mini-cart-btn:hover {
                    background: rgba(255, 255, 255, 0.25);
                    transform: translateY(-2px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                }

                .custom-mini-cart-btn:hover .custom-mini-cart-icon-bg {
                    transform: scale(1.05);
                    box-shadow: 0 6px 15px rgba(54, 177, 220, 0.4);
                }

                .custom-mini-cart-btn:hover .custom-mini-cart-count {
                    transform: scale(1.1);
                    background: #ff5252;
                }

                .custom-mini-cart-btn:active {
                    transform: translateY(1px);
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                }

                /* Pulse animation for the count badge when items are added */
                @keyframes countPulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.3); }
                    100% { transform: scale(1); }
                }

                .count-pulse {
                    animation: countPulse 0.5s ease-out;
                }
                </style>
            </div>
        </div>
    </div>

    <style>
    .back-to-accommodations-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(54, 177, 220, 0.3);
        background: rgba(255, 255, 255, 0.3) !important;
    }

    .back-to-accommodations-btn:hover i,
    .back-to-accommodations-btn:hover span {
        color: white !important;
    }

    /* Add ripple effect on button click */
    .back-to-accommodations-btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%, -50%);
        transform-origin: 50% 50%;
    }

    .back-to-accommodations-btn:active::after {
        animation: backBtnRipple 0.6s ease-out;
    }

    @keyframes backBtnRipple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(20, 20);
            opacity: 0;
        }
    }
    </style>

    <!-- Introduction Section - 2025 UX/UI Style -->
    <div class="mb-5">
        <div class="section-container" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden; margin-bottom: 30px;">
            <!-- Decorative elements -->
            <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
            <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

            <!-- Section header with 3D effect -->
            <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
                <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                    <i class="fas fa-calendar-alt" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                </div>
                <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Coming Early to Brasov?</h3>
            </div>

            <p style="margin-bottom: 20px; color: #37474f; font-size: 15px; line-height: 1.5; position: relative; z-index: 1;">
                Book your accommodation in Brasov before the main event starts (before August 21, 2025).
                Select the number of nights you need and choose from our recommended hotels.
            </p>

                <div id="current-settings-display" style="background-color: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); margin-top: 10px;">
                    <div style="margin-bottom: 12px;">
                        <div style="font-weight: 600; color: #36b1dc; font-size: 16px;">YOUR STAY PREFERENCES</div>
                    </div>

                    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px;">
                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">DURATION</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-moon me-1" style="color: #36b1dc;"></i>
                                <span id="current-nights">1</span> <span id="current-nights-label">night</span>
                            </div>
                        </div>

                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">ROOM TYPE</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-bed me-1" style="color: #36b1dc;"></i>
                                <span id="current-room-type">Single Room</span>
                            </div>
                        </div>
                    </div>

                    <div style="height: 1px; background-color: #e0e0e0; margin: 15px 0;"></div>

                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">CHECK-IN</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-calendar-check me-1" style="color: #36b1dc;"></i>
                                <span id="current-checkin-date">20/08/2025</span>
                            </div>
                        </div>

                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">CHECK-OUT</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-calendar-times me-1" style="color: #36b1dc;"></i>
                                21/08/2025
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (empty($pre_event_products)): ?>
        <!-- No products available message -->
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span>No accommodation options are currently available for pre-event. Please check back later.</span>
        </div>
        <?php else: ?>
        <!-- Products Grid -->
        <div class="accommodation-products-grid">
            <?php foreach ($pre_event_products as $product):
                $product_id = $product->get_id();
                $in_cart = in_array($product_id, $products_in_cart);
                $image_url = wp_get_attachment_url($product->get_image_id());
                $price_html = $product->get_price_html();
                $description = $product->get_short_description();
                $is_in_stock = $product->is_in_stock();

                // Get hotel stars from product meta
                $hotel_stars = get_post_meta($product_id, '_number_of_stars', true);
                if (!$hotel_stars) $hotel_stars = 3; // Default to 3 stars

                // Get hotel features from product meta
                $hotel_features = get_post_meta($product_id, '_hotel_features', true);
                if ($hotel_features) {
                    $features = explode("\n", $hotel_features);
                } else {
                    $features = [];
                }

                // Get hotel website from product meta
                $hotel_website = get_post_meta($product_id, '_hotel_website', true);

                // Add soldout class if product is out of stock
                $soldout_class = !$is_in_stock ? 'accommodation-product-soldout' : '';
            ?>
            <div class="accommodation-product-card">
                <div class="accommodation-product-image">
                    <?php if ($in_cart): ?>
                    <div class="accommodation-product-badge">In Cart</div>
                    <?php endif; ?>
                    <img src="<?php echo esc_url($image_url ? $image_url : 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121214/pre-event.jpg'); ?>" alt="<?php echo esc_attr($product->get_name()); ?>">
                </div>
                <div class="accommodation-product-content">
                    <h4 class="accommodation-product-title"><?php echo esc_html($product->get_name()); ?></h4>

                    <!-- Hotel Stars (moved under hotel name) -->
                    <div class="accommodation-product-stars">
                        <?php for ($i = 0; $i < $hotel_stars; $i++): ?>
                            <i class="fas fa-star"></i>
                        <?php endfor; ?>
                        <span class="accommodation-product-star-description">
                            <?php echo $hotel_stars; ?>-star hotel
                        </span>
                    </div>

                    <!-- Description List with Checkmarks -->
                    <div class="accommodation-product-features">
                        <ul class="accommodation-features-list">
                            <?php
                            // Get description fields from product meta
                            $descriere = get_post_meta($product_id, '_descriere', true);
                            $descriere1 = get_post_meta($product_id, '_descriere1', true);
                            $descriere2 = get_post_meta($product_id, '_descriere2', true);
                            $descriere3 = get_post_meta($product_id, '_descriere3', true);

                            // Display description fields if they exist
                            if ($descriere) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere) . '</li>';
                            if ($descriere1) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere1) . '</li>';
                            if ($descriere2) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere2) . '</li>';
                            if ($descriere3) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere3) . '</li>';
                            ?>
                        </ul>
                    </div>

                    <div class="accommodation-product-description">
                        <?php if (!empty($features)): ?>
                        <ul class="mt-2 mb-0" style="padding-left: 20px;">
                            <?php foreach ($features as $feature): ?>
                            <li><?php echo esc_html(trim($feature)); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>

                        <?php if ($hotel_website): ?>
                        <div class="mt-2">
                            <a href="<?php echo esc_url($hotel_website); ?>" target="_blank" style="color: #00b2e3; text-decoration: none; display: inline-flex; align-items: center;">
                                <i class="fas fa-globe me-1"></i> Visit hotel website
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($product->is_type('variable')):
                        $variations = $product->get_available_variations();
                    ?>
                    <div class="accommodation-product-variations" data-product-id="<?php echo esc_attr($product_id); ?>">
                        <h5 class="accommodation-variations-title">Available Room Types:</h5>
                        <?php
                        $first_variation = true;
                        foreach ($variations as $variation):
                            $variation_obj = wc_get_product($variation['variation_id']);
                            if (!$variation_obj || !$variation_obj->is_in_stock()) continue;

                            $variation_id = $variation['variation_id'];

                            // Get attribute names instead of values
                            $attribute_names = [];
                            $attribute_name = '';
                            $attribute_value = '';

                            foreach ($variation['attributes'] as $key => $value) {
                                $taxonomy = str_replace('attribute_', '', $key);
                                $term = get_term_by('slug', $value, $taxonomy);

                                // Store the first attribute for use in cart
                                if (empty($attribute_name)) {
                                    $attribute_name = $taxonomy;
                                    $attribute_value = $value;
                                }

                                if ($term) {
                                    $attribute_names[] = $term->name;
                                } else {
                                    // For custom product attributes
                                    $attribute_names[] = $value;
                                }
                            }

                            $variation_name = implode(' - ', $attribute_names);
                            $variation_price_html = $variation_obj->get_price_html();

                            // Check if this is a double room variation
                            $is_double_room = stripos($variation_name, 'double') !== false;

                            // Get variation price
                            $variation_price = $variation_obj->get_price();
                        ?>
                        <div class="accommodation-product-variation">
                            <label class="accommodation-variation-radio-label">
                                <input type="radio" name="variation_<?php echo esc_attr($product_id); ?>"
                                       value="<?php echo esc_attr($variation_id); ?>"
                                       data-variation-name="<?php echo esc_attr($variation_name); ?>"
                                       data-is-double="<?php echo $is_double_room ? 'true' : 'false'; ?>"
                                       data-price="<?php echo esc_attr($variation_price); ?>"
                                       data-attribute-name="<?php echo esc_attr($attribute_name); ?>"
                                       data-attribute-value="<?php echo esc_attr($attribute_value); ?>"
                                       <?php echo $first_variation ? 'checked' : ''; ?>>
                                <div class="accommodation-product-variation-name"><?php echo esc_html($variation_name); ?></div>
                                <div class="accommodation-product-variation-price"><?php echo $variation_price_html; ?></div>
                            </label>
                        </div>
                        <?php
                        $first_variation = false;
                        endforeach;
                        ?>
                    </div>
                    <?php else: ?>
                    <div class="accommodation-product-single-price">
                        <?php echo $price_html; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ($in_cart): ?>
                    <button class="accommodation-product-button" disabled style="background: #28a745;">
                        <i class="fas fa-check me-2"></i> BOOKED
                    </button>
                    <?php elseif (!$is_in_stock): ?>
                    <button class="accommodation-product-button" disabled style="background: #6c757d;">
                        <i class="fas fa-times me-2"></i> SOLD OUT
                    </button>
                    <?php else: ?>
                    <button class="accommodation-product-button add-to-cart-btn"
                            data-product-id="<?php echo esc_attr($product_id); ?>"
                            data-product-name="<?php echo esc_attr($product->get_name()); ?>"
                            data-product-type="<?php echo esc_attr($product->get_type()); ?>"
                            data-price="<?php echo esc_attr($product->get_price()); ?>"
                            onclick="showPreEventConfirmModal(<?php echo esc_attr($product_id); ?>)">
                        <i class="fas fa-bed me-2"></i> BOOK
                    </button>
                    <button onclick="showPreEventModal(event)" class="accommodation-settings-button">
                        <i class="fas fa-cog me-2"></i> CHANGE STAY SETTINGS
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Custom UX2025 Mini Cart Modal -->
<div id="custom-mini-cart-modal" class="custom-mini-cart-modal" style="display: none;">
    <div class="custom-mini-cart-overlay" onclick="closeCustomMiniCart()"></div>
    <div class="custom-mini-cart-content">
        <div class="custom-mini-cart-header">
            <h3><i class="fas fa-shopping-cart me-2" style="color: white;"></i> LCI AGM 2025</h3>
            <button type="button" class="custom-mini-cart-close" onclick="closeCustomMiniCart()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="custom-mini-cart-body">
            <!-- Loading state -->
            <div id="custom-mini-cart-loading" class="custom-mini-cart-loading">
                <div class="custom-mini-cart-loading-spinner"></div>
                <p>Loading your cart...</p>
            </div>

            <!-- Empty cart state -->
            <div id="custom-mini-cart-empty" class="custom-mini-cart-empty" style="display: none;">
                <div class="custom-mini-cart-empty-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h4>Your cart is empty</h4>
                <p>You haven't added any items to your cart yet.</p>
            </div>

            <!-- Cart items -->
            <div id="custom-mini-cart-items" class="custom-mini-cart-items" style="display: none;">
                <!-- Items will be loaded here via JavaScript -->
            </div>

            <!-- Fundraising message -->
            <div id="custom-mini-cart-fundraising" class="custom-mini-cart-fundraising" style="display: none;">
                <div class="custom-mini-cart-fundraising-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="custom-mini-cart-fundraising-content">
                    <h4>Thank You for Your Support!</h4>
                    <p>Your purchase includes <span id="custom-mini-cart-fundraising-amount">€0.00</span> that will go towards supporting Lions Clubs International Foundation.</p>
                </div>
            </div>
        </div>
        <div class="custom-mini-cart-footer">
            <div class="custom-mini-cart-total-container">
                <span class="custom-mini-cart-total-label">Total:</span>
                <span id="custom-mini-cart-total-value" class="custom-mini-cart-total-value"><?php echo WC()->cart->get_cart_total(); ?></span>
            </div>
            <div class="custom-mini-cart-buttons">
                <button type="button" class="custom-mini-cart-btn-continue" onclick="closeCustomMiniCart()">
                    Continue Shopping
                </button>
                <a href="<?php echo esc_url(wc_get_checkout_url()); ?>" class="custom-mini-cart-btn-checkout">
                    <i class="fas fa-credit-card me-2"></i> Checkout
                </a>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom UX2025 Mini Cart Modal Styles */
.custom-mini-cart-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: none;
    animation: customFadeIn 0.3s ease;
}

@keyframes customFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.custom-mini-cart-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.custom-mini-cart-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 700px;
    overflow: hidden;
    animation: customModalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    display: flex;
    flex-direction: column;
    max-height: 90vh;
}

@keyframes customModalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -45%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.custom-mini-cart-header {
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    padding: 18px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Animated background elements for header */
.custom-mini-cart-header::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

.custom-mini-cart-header::after {
    content: '';
    position: absolute;
    bottom: -30px;
    left: 10%;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

.custom-mini-cart-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
}

.custom-mini-cart-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.custom-mini-cart-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.custom-mini-cart-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.custom-mini-cart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.custom-mini-cart-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(54, 177, 220, 0.1);
    border-radius: 50%;
    border-top-color: #36b1dc;
    animation: customSpin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes customSpin {
    to { transform: rotate(360deg); }
}

.custom-mini-cart-loading p {
    color: #5d6d7e;
    font-size: 16px;
    margin: 0;
}

.custom-mini-cart-empty {
    text-align: center;
    padding: 40px 0;
}

.custom-mini-cart-empty-icon {
    width: 80px;
    height: 80px;
    background: rgba(54, 177, 220, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.custom-mini-cart-empty-icon i {
    font-size: 32px;
    color: #36b1dc;
}

.custom-mini-cart-empty h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
}

.custom-mini-cart-empty p {
    color: #5d6d7e;
    font-size: 14px;
    margin: 0;
}

.custom-mini-cart-items {
    max-height: 400px;
    overflow-y: auto;
}

.custom-mini-cart-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    transition: all 0.3s ease;
}

.custom-mini-cart-item:hover {
    background: rgba(54, 177, 220, 0.03);
}

.custom-mini-cart-item-image {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    overflow: hidden;
    margin-right: 15px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.custom-mini-cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.custom-mini-cart-item-details {
    flex: 1;
}

.custom-mini-cart-item-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 15px;
}

.custom-mini-cart-item-meta {
    color: #5d6d7e;
    font-size: 13px;
    margin-bottom: 5px;
}

.custom-mini-cart-item-price {
    font-weight: 600;
    color: #36b1dc;
    font-size: 14px;
}

.custom-mini-cart-item-quantity {
    background: rgba(54, 177, 220, 0.1);
    color: #36b1dc;
    font-size: 12px;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 12px;
    margin-right: 10px;
}

.custom-mini-cart-item-remove {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.custom-mini-cart-item-remove:hover {
    background: rgba(220, 53, 69, 0.2);
    transform: rotate(90deg);
}

.custom-mini-cart-fundraising {
    margin-top: 20px;
    background: linear-gradient(135deg, rgba(54, 177, 220, 0.1), rgba(54, 177, 220, 0.05));
    border-radius: 15px;
    padding: 15px;
    display: flex;
    align-items: center;
    border-left: 4px solid #36b1dc;
}

.custom-mini-cart-fundraising-icon {
    width: 40px;
    height: 40px;
    background: rgba(54, 177, 220, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.custom-mini-cart-fundraising-icon i {
    color: #36b1dc;
    font-size: 18px;
}

.custom-mini-cart-fundraising-content {
    flex: 1;
}

.custom-mini-cart-fundraising-content h4 {
    color: #36b1dc;
    font-size: 16px;
    margin: 0 0 5px 0;
}

.custom-mini-cart-fundraising-content p {
    color: #5d6d7e;
    font-size: 14px;
    margin: 0;
}

.custom-mini-cart-fundraising-amount {
    font-weight: 600;
    color: #36b1dc;
}

.custom-mini-cart-footer {
    padding: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(248, 249, 250, 0.7);
}

.custom-mini-cart-total-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.custom-mini-cart-total-label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
}

.custom-mini-cart-total-value {
    font-weight: 700;
    color: #36b1dc;
    font-size: 18px;
}

.custom-mini-cart-buttons {
    display: flex;
    gap: 15px;
}

.custom-mini-cart-btn-continue {
    flex: 1;
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: none;
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.custom-mini-cart-btn-continue:hover {
    background: rgba(108, 117, 125, 0.2);
}

.custom-mini-cart-btn-checkout {
    flex: 2;
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-mini-cart-btn-checkout:hover {
    background: linear-gradient(135deg, #2980b9, #1c6ea4);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(54, 177, 220, 0.3);
}

/* Responsive styles */
@media (max-width: 576px) {
    .custom-mini-cart-buttons {
        flex-direction: column;
    }

    .custom-mini-cart-btn-continue,
    .custom-mini-cart-btn-checkout {
        width: 100%;
    }
}
</style>

<!-- Add to Cart Confirmation Modal -->
<div class="accommodation-cart-confirm-modal" id="preEventCartConfirmModal" style="display: none;">
    <div class="accommodation-cart-confirm-overlay" style="backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>
    <div class="accommodation-cart-confirm-content">
        <div class="accommodation-cart-confirm-header">
            <h3 id="confirmModalTitle"><i class="fas fa-check-circle me-2" style="color: white;"></i> Confirm Your Selection</h3>
            <button type="button" class="accommodation-cart-confirm-close btn-close btn-close-white" onclick="closePreEventConfirmModal()" aria-label="Close" style="position: relative; z-index: 1;"></button>
        </div>
        <div class="accommodation-cart-confirm-body">
            <!-- UX2025 styled intro text - will change during cart process -->
            <p id="confirmModalIntro" style="color: #37474f; font-size: 15px; margin-bottom: 20px; position: relative; z-index: 1;">Please confirm your pre-event accommodation selection:</p>

            <!-- Decorative elements -->
            <div style="position: absolute; top: 20px; right: 20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>
            <div style="position: absolute; bottom: 30px; left: 10px; width: 120px; height: 120px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.03), transparent 70%); border-radius: 50%; z-index: 0;"></div>

            <!-- UX2025 styled details container with glassmorphism -->
            <div class="accommodation-cart-confirm-details" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05); border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden;">
                <div class="accommodation-cart-confirm-row" style="transition: all 0.3s ease;">
                    <span class="accommodation-cart-confirm-label" style="color: #5d6d7e; font-size: 13px; text-transform: uppercase; letter-spacing: 0.5px;">Hotel:</span>
                    <span class="accommodation-cart-confirm-value" id="preEventConfirmProductName" style="font-weight: 600; color: #2c3e50;"></span>
                </div>

                <div class="accommodation-cart-confirm-row" id="confirmRoomTypeRow" style="display: none; transition: all 0.3s ease;">
                    <span class="accommodation-cart-confirm-label" style="color: #5d6d7e; font-size: 13px; text-transform: uppercase; letter-spacing: 0.5px;">Room Type:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmRoomType" style="font-weight: 600; color: #2c3e50;"></span>
                </div>

                <div class="accommodation-cart-confirm-row" style="transition: all 0.3s ease;">
                    <span class="accommodation-cart-confirm-label" style="color: #5d6d7e; font-size: 13px; text-transform: uppercase; letter-spacing: 0.5px;">Number of Nights:</span>
                    <span class="accommodation-cart-confirm-value" id="preEventConfirmNights" style="font-weight: 600; color: #2c3e50;"></span>
                </div>

                <div class="accommodation-cart-confirm-row" style="transition: all 0.3s ease;">
                    <span class="accommodation-cart-confirm-label" style="color: #5d6d7e; font-size: 13px; text-transform: uppercase; letter-spacing: 0.5px;">Check-in Date:</span>
                    <span class="accommodation-cart-confirm-value" id="preEventConfirmCheckinDate" style="font-weight: 600; color: #2c3e50;"></span>
                </div>

                <div class="accommodation-cart-confirm-row" style="transition: all 0.3s ease;">
                    <span class="accommodation-cart-confirm-label" style="color: #5d6d7e; font-size: 13px; text-transform: uppercase; letter-spacing: 0.5px;">Check-out Date:</span>
                    <span class="accommodation-cart-confirm-value" style="font-weight: 600; color: #2c3e50;">21/08/2025</span>
                </div>



                <div class="accommodation-cart-confirm-row" id="confirmDoubleRoomRow" style="display: none; transition: all 0.3s ease;">
                    <span class="accommodation-cart-confirm-label" style="color: #5d6d7e; font-size: 13px; text-transform: uppercase; letter-spacing: 0.5px;">Double Room:</span>
                    <span class="accommodation-cart-confirm-value" style="font-weight: 600; color: #2c3e50;">Yes (quantity will be doubled)</span>
                </div>

                <div class="accommodation-cart-confirm-row accommodation-cart-confirm-total" style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.1), rgba(54, 177, 220, 0.05)); border-radius: 10px; transition: all 0.3s ease;">
                    <span class="accommodation-cart-confirm-label" style="color: #2c3e50; font-weight: 700;">Total Price:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmTotalPrice" style="color: #36b1dc; font-weight: 700; font-size: 18px;">Calculating...</span>
                </div>
            </div>

            <div class="accommodation-cart-confirm-notice" id="confirmDoubleRoomNotice" style="display: none; background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border-radius: 16px; border-left: 4px solid #ffc107; padding: 16px; margin-top: 20px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
                <i class="fas fa-info-circle" style="color: #ffc107; font-size: 20px; margin-right: 15px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                <div>
                    <strong style="color: #856404; font-weight: 600; font-size: 15px;">Important:</strong> When booking a Double Room, the price is calculated per person.
                    <p class="mt-2 mb-0" style="color: #495057; line-height: 1.5;">Since this room is for 2 people, the system will automatically charge for both guests (<span id="confirmDoubleRoomCalc" style="font-weight: 600; color: #36b1dc;"></span> total).</p>
                    <p class="mt-2 mb-0" style="color: #495057; line-height: 1.5;">If you are booking alone, please make sure you will be sharing the room with someone, or choose a Single Room instead.</p>
                </div>
            </div>
        </div>
        <div class="accommodation-cart-confirm-footer" style="padding: 25px 30px;">
            <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-secondary" onclick="closePreEventConfirmModal()" style="text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px; margin-right: 15px;">
                <i class="fas fa-times me-2" style="color: white !important;"></i> <span style="color: white !important;">CANCEL</span>
            </button>
            <button type="button" id="confirmBookButton" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-primary" onclick="addToCartPreEvent()" style="text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                <i class="fas fa-bed me-2" style="color: white !important;"></i> <span style="color: white !important;">BOOK</span>
            </button>
        </div>
    </div>
</div>

<style>
/* Accommodation Cart Confirm Modal Styles - UX2025 */
.accommodation-cart-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.accommodation-cart-confirm-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.accommodation-cart-confirm-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 700px;
    overflow: hidden;
    animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -45%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Processing animation for the modal */
@keyframes processingPulse {
    0% { box-shadow: 0 0 0 0 rgba(54, 177, 220, 0.4); }
    70% { box-shadow: 0 0 0 15px rgba(54, 177, 220, 0); }
    100% { box-shadow: 0 0 0 0 rgba(54, 177, 220, 0); }
}

.accommodation-cart-confirm-content.processing {
    animation: processingPulse 1.5s infinite;
}

.accommodation-cart-confirm-header {
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    padding: 18px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Animated background elements for header */
.accommodation-cart-confirm-header::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

.accommodation-cart-confirm-header::after {
    content: '';
    position: absolute;
    bottom: -30px;
    left: 10%;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

.accommodation-cart-confirm-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
}

.accommodation-cart-confirm-header h3 i {
    color: white;
    margin-right: 8px;
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
}

.accommodation-cart-confirm-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    position: relative;
    z-index: 1;
}

.accommodation-cart-confirm-body {
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* Responsive styles for laptops with lower resolutions */
@media (max-height: 768px) {
    .accommodation-cart-confirm-body {
        padding: 15px;
        max-height: calc(90vh - 120px);
        overflow-y: auto;
    }

    .accommodation-cart-confirm-details {
        margin-bottom: 15px;
    }
}

@media (min-width: 768px) {
    .accommodation-cart-confirm-header {
        padding: 20px 25px;
    }

    .accommodation-cart-confirm-header h3 {
        font-size: 22px;
    }

    .accommodation-cart-confirm-body {
        padding: 25px 30px;
    }

    .accommodation-cart-confirm-details {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.8);
    }

    .accommodation-cart-confirm-row {
        flex-direction: column;
        background-color: rgba(248, 249, 250, 0.7);
        padding: 12px 15px;
        border-radius: 10px;
        border-bottom: none;
        margin-bottom: 0;
        transition: all 0.3s ease;
    }

    .accommodation-cart-confirm-row:hover {
        background-color: rgba(248, 249, 250, 0.9);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .accommodation-cart-confirm-label {
        margin-bottom: 5px;
        color: #5d6d7e;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .accommodation-cart-confirm-value {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
    }

    .accommodation-cart-confirm-total {
        grid-column: 1 / -1;
        margin-top: 15px;
        background: linear-gradient(135deg, rgba(54, 177, 220, 0.1), rgba(54, 177, 220, 0.05));
        border-top: none;
        padding-top: 0;
        border-radius: 10px;
    }

    .accommodation-cart-confirm-total .accommodation-cart-confirm-label {
        color: #2c3e50;
        font-weight: 700;
    }

    .accommodation-cart-confirm-total .accommodation-cart-confirm-value {
        color: #36b1dc;
        font-weight: 700;
        font-size: 18px;
    }
}

.accommodation-cart-confirm-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.accommodation-cart-confirm-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.accommodation-cart-confirm-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.accommodation-cart-confirm-total {
    margin-top: 15px;
    border-top: 2px solid #e9ecef;
    padding-top: 15px;
    font-weight: bold;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-label {
    font-size: 16px;
    color: #343a40;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-value {
    font-size: 16px;
    color: #36b1dc;
}

.accommodation-cart-confirm-label {
    font-weight: 600;
    color: #495057;
}

.accommodation-cart-confirm-value {
    color: #343a40;
}

.accommodation-cart-confirm-notice {
    background-color: #e8f4fd;
    color: #0c5460;
    padding: 16px;
    border-radius: 8px;
    margin-top: 15px;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid #36b1dc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.accommodation-cart-confirm-notice i {
    margin-right: 12px;
    font-size: 18px;
    margin-top: 2px;
    color: #36b1dc;
}

.accommodation-cart-confirm-notice p {
    line-height: 1.5;
}

.accommodation-cart-confirm-notice strong {
    color: #0c5460;
    font-weight: 600;
}

.accommodation-cart-confirm-footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(233, 236, 239, 0.5);
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 1;
    background: #f8f9fa;
}

/* Add subtle gradient to footer */
.accommodation-cart-confirm-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
    z-index: -1;
}

/* For smaller laptop screens */
@media (max-height: 768px) {
    .accommodation-cart-confirm-footer {
        padding: 12px 15px;
    }

    .accommodation-cart-confirm-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
}

@media (min-width: 768px) {
    .accommodation-cart-confirm-footer {
        padding: 20px 25px;
    }

    .accommodation-cart-confirm-btn {
        min-width: 150px;
        padding: 12px 20px;
    }
}

.accommodation-cart-confirm-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

/* Add ripple effect on button click */
.accommodation-cart-confirm-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.accommodation-cart-confirm-btn:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

.accommodation-cart-confirm-btn-secondary {
    width: 30%;
    background: linear-gradient(135deg, #6c757d, #495057);
    box-shadow: 0 4px 10px rgba(108, 117, 125, 0.2);
}

.accommodation-cart-confirm-btn-primary {
    width: 70%;
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2);
}

.accommodation-cart-confirm-btn i {
    color: white !important;
    margin-right: 8px;
}

.accommodation-cart-confirm-btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1c6ea4);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(54, 177, 220, 0.3);
    color: white !important;
}

.accommodation-cart-confirm-btn-primary:hover i,
.accommodation-cart-confirm-btn-primary:hover span {
    color: white !important;
}

.accommodation-cart-confirm-btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #343a40);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(108, 117, 125, 0.3);
    color: white !important;
}

.accommodation-cart-confirm-btn-secondary:hover i,
.accommodation-cart-confirm-btn-secondary:hover span {
    color: white !important;
}

.accommodation-cart-confirm-dates-note {
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
}

.accommodation-cart-confirm-dates-note i {
    color: #36b1dc;
    margin-right: 6px;
    font-size: 14px;
}
</style>

<script>
// Variables to store product information for the confirmation modal
let preEventProductId = 0;
let preEventVariationId = 0;
let preEventNights = 1;
let preEventIsDoubleRoom = false;

// Function to show the confirmation modal
window.showPreEventConfirmModal = function(productId) {
    preEventProductId = productId;

    // Get saved nights from session storage
    preEventNights = parseInt(sessionStorage.getItem('preEventNights')) || 1;

    // Get product type
    const productType = document.querySelector(`.add-to-cart-btn[data-product-id="${productId}"]`).getAttribute('data-product-type');

    // Get selected variation if it's a variable product
    if (productType === 'variable') {
        const variationContainer = document.querySelector(`.accommodation-product-variations[data-product-id="${productId}"]`);
        if (variationContainer) {
            const selectedVariation = variationContainer.querySelector('input[type="radio"]:checked');
            if (selectedVariation) {
                preEventVariationId = parseInt(selectedVariation.value);
                console.log('Selected variation ID:', preEventVariationId);
                preEventIsDoubleRoom = selectedVariation.getAttribute('data-is-double') === 'true';
            } else {
                // No variation selected, show alert and return
                alert('Please select a room type before booking.');
                return;
            }
        }
    } else {
        preEventVariationId = 0;
        preEventIsDoubleRoom = false;
    }

    // Debug log the selected product and variation
    console.log('Product selected:', {
        product_id: preEventProductId,
        variation_id: preEventVariationId,
        nights: preEventNights,
        is_double_room: preEventIsDoubleRoom
    });

    // Get product name
    const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${productId}"]`).closest('.accommodation-product-card');
    const productName = productCard.querySelector('.accommodation-product-title').textContent;

    // Get variation name if applicable
    let roomType = '';
    if (preEventVariationId) {
        const variationRadio = document.querySelector(`input[value="${preEventVariationId}"]`);
        if (variationRadio) {
            roomType = variationRadio.getAttribute('data-variation-name');
        }
    }

    // Calculate check-in date
    const checkinDate = calculatePreEventCheckinDate(preEventNights);

    // Update modal content
    document.getElementById('preEventConfirmProductName').textContent = productName;

    if (roomType) {
        document.getElementById('confirmRoomTypeRow').style.display = 'flex';
        document.getElementById('confirmRoomType').textContent = roomType;
    } else {
        document.getElementById('confirmRoomTypeRow').style.display = 'none';
    }

    document.getElementById('preEventConfirmCheckinDate').textContent = checkinDate;
    document.getElementById('preEventConfirmNights').textContent = `${preEventNights} ${preEventNights === 1 ? 'night' : 'nights'}`;

    // Hide double room notice (no longer needed)
    document.getElementById('confirmDoubleRoomRow').style.display = 'none';
    document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

    // Calculate and display the total price
    let productPrice = 0;

    if (preEventVariationId) {
        // Get price from the selected variation
        const selectedVariation = document.querySelector(`input[value="${preEventVariationId}"]`);
        if (selectedVariation) {
            productPrice = parseFloat(selectedVariation.getAttribute('data-price') || 0);
        }
    } else {
        // Get price from the button's data attribute
        const addToCartBtn = document.querySelector(`.add-to-cart-btn[data-product-id="${productId}"]`);
        if (addToCartBtn) {
            productPrice = parseFloat(addToCartBtn.getAttribute('data-price') || 0);
        }
    }

    // Calculate total price based on nights
    const totalPrice = productPrice * preEventNights;

    // Format the price with currency symbol
    const formattedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(totalPrice);

    // Update the price display
    document.getElementById('confirmTotalPrice').textContent = formattedPrice;

    // Show modal
    const confirmModal = document.getElementById('preEventCartConfirmModal');
    confirmModal.style.display = 'block';
}

// Function to close the confirmation modal
window.closePreEventConfirmModal = function() {
    const confirmModal = document.getElementById('preEventCartConfirmModal');
    confirmModal.style.display = 'none';
}

// Function to calculate check-in date
window.calculatePreEventCheckinDate = function(nights) {
    // Base date is 21/08/2025 (check-out date)
    const baseDate = new Date(2025, 7, 21); // Month is 0-indexed, so 7 = August

    // Calculate check-in date by subtracting nights from base date
    const checkinDate = new Date(baseDate);
    checkinDate.setDate(baseDate.getDate() - nights);

    // Format the date as DD/MM/YYYY
    const day = String(checkinDate.getDate()).padStart(2, '0');
    const month = String(checkinDate.getMonth() + 1).padStart(2, '0');
    const year = checkinDate.getFullYear();

    return `${day}/${month}/${year}`;
}

// Function to handle successful add to cart
function handleAddToCartSuccess(response, productName, roomType, nights, totalPrice, productImage, modalTitle, modalContent, modalIntro, modalDetails, modalFooter, productCard, confirmModal) {
    // Add a slight delay to show the animation
    setTimeout(() => {
        // Update modal to show success message
        if (modalTitle) {
            modalTitle.innerHTML = '<i class="fas fa-check-circle me-2" style="color: white;"></i> Added to Cart';
        }

        if (modalContent) {
            modalContent.classList.remove('processing');
        }

        // Create success content for the modal
        if (modalIntro) {
            modalIntro.innerHTML = `
                <div style="text-align: center; padding: 20px 0;">
                    <div class="success-animation" style="margin: 0 auto 20px auto; width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #28a745, #20883b); display: flex; align-items: center; justify-content: center; box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3); animation: successPulse 1s 1;">
                        <i class="fas fa-check" style="color: white; font-size: 32px;"></i>
                    </div>
                    <p style="color: #2c3e50; font-size: 18px; font-weight: 600; margin-bottom: 10px;">Successfully Added to Cart</p>

                    <div style="background: rgba(248, 249, 250, 0.7); border-radius: 10px; padding: 15px; margin: 20px auto; max-width: 400px; text-align: left;">
                        <div style="margin-bottom: 8px;">
                            <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Hotel:</span>
                            <span style="color: #2c3e50; font-weight: 600;">${productName}</span>
                        </div>

                        ${roomType ? `
                        <div style="margin-bottom: 8px;">
                            <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Room Type:</span>
                            <span style="color: #2c3e50; font-weight: 600;">${roomType}</span>
                        </div>
                        ` : ''}

                        <div style="margin-bottom: 8px;">
                            <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Stay Duration:</span>
                            <span style="color: #2c3e50; font-weight: 600;">${nights}</span>
                        </div>

                        <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.1), rgba(54, 177, 220, 0.05)); padding: 8px 12px; border-radius: 8px; margin-top: 10px;">
                            <span style="color: #2c3e50; font-weight: 700; font-size: 14px;">Total: ${totalPrice}</span>
                        </div>
                    </div>
                </div>
                <style>
                    @keyframes successPulse {
                        0% { transform: scale(0.5); opacity: 0; }
                        50% { transform: scale(1.2); }
                        100% { transform: scale(1); opacity: 1; }
                    }
                </style>
            `;
        }

        // Update footer to show a single close button
        if (modalFooter) {
            modalFooter.innerHTML = `
                <button type="button" onclick="closePreEventConfirmModal()" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-primary" style="width: 100%; text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                    <i class="fas fa-check me-2" style="color: white !important;"></i> <span style="color: white !important;">DONE</span>
                </button>
            `;
            modalFooter.style.display = 'flex';
        }

        // Show success message on the product card
        const addToCartBtn = productCard.querySelector('.add-to-cart-btn');

        // Replace button with success message
        addToCartBtn.innerHTML = '<i class="fas fa-check me-2"></i> ADDED TO CART';
        addToCartBtn.style.backgroundColor = '#28a745';
        addToCartBtn.disabled = true;

        // Hide settings button
        const settingsBtn = productCard.querySelector('.accommodation-settings-button');
        if (settingsBtn) {
            settingsBtn.style.display = 'none';
        }

        // Immediately update the minicart button content
        const customMiniCartCount = document.querySelector('.custom-mini-cart-count');
        const customMiniCartTotal = document.querySelector('.custom-mini-cart-total');

        if (customMiniCartCount) {
            const currentCount = parseInt(customMiniCartCount.textContent) || 0;
            customMiniCartCount.textContent = currentCount + 1;

            // Add pulse animation
            customMiniCartCount.classList.remove('count-pulse');
            void customMiniCartCount.offsetWidth; // Trigger reflow to restart animation
            customMiniCartCount.classList.add('count-pulse');
        }

        if (customMiniCartTotal) {
            // Calculate and update the total immediately
            const productPrice = parseFloat(totalPrice.replace(/[^0-9.-]+/g, ''));
            const currentTotal = parseFloat(customMiniCartTotal.textContent.replace(/[^0-9.-]+/g, '')) || 0;
            const newTotal = (currentTotal + productPrice).toFixed(2);
            customMiniCartTotal.textContent = '€' + newTotal;
        }

        // Also refresh our custom mini cart if it's open
        if (document.getElementById('custom-mini-cart-modal').style.display === 'block') {
            fetchCustomMiniCartItems();
        }

        // Refresh the mini-cart contents in the background
        setTimeout(() => {
            refreshMiniCart();
        }, 500);

        // Auto-close the modal after 5 seconds
        setTimeout(() => {
            closePreEventConfirmModal(); // This function uses the correct modal ID 'preEventCartConfirmModal'

            // Reset modal state for next time
            if (modalTitle) {
                modalTitle.innerHTML = '<i class="fas fa-check-circle me-2" style="color: white;"></i> Confirm Your Selection';
            }

            if (modalIntro) {
                modalIntro.innerHTML = 'Please confirm your pre-event accommodation selection:';
            }

            const bookButton = document.getElementById('confirmBookButton');
            if (bookButton) {
                bookButton.disabled = false;
                bookButton.innerHTML = '<i class="fas fa-bed me-2" style="color: white !important;"></i> <span style="color: white !important;">BOOK</span>';
            }

            // Show the details again for next time
            if (modalDetails) {
                modalDetails.style.display = 'block';
            }

            // Reset footer for next time
            if (modalFooter) {
                modalFooter.innerHTML = `
                    <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-secondary" onclick="closePreEventConfirmModal()" style="margin-right: 15px; text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                        <i class="fas fa-times me-2" style="color: white !important;"></i> <span style="color: white !important;">CANCEL</span>
                    </button>
                    <button type="button" id="confirmBookButton" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-primary" onclick="addToCartPreEvent()" style="text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                        <i class="fas fa-bed me-2" style="color: white !important;"></i> <span style="color: white !important;">BOOK</span>
                    </button>
                `;
            }
        }, 5000);
    }, 1500); // 1.5 second delay to show the animation
}

// Function to handle error in add to cart
function handleAddToCartError(modalTitle, modalIntro, bookButton, modalContent, modalDetails, modalFooter, confirmModal) {
    // Reset modal state
    if (modalTitle) {
        modalTitle.innerHTML = '<i class="fas fa-check-circle me-2" style="color: white;"></i> Confirm Your Selection';
    }

    if (modalIntro) {
        modalIntro.innerHTML = 'Please confirm your pre-event accommodation selection:';
    }

    if (bookButton) {
        bookButton.disabled = false;
        bookButton.innerHTML = '<i class="fas fa-bed me-2" style="color: white !important;"></i> <span style="color: white !important;">BOOK</span>';
    }

    if (modalContent) {
        modalContent.classList.remove('processing');
    }

    // Show the details and footer again
    if (modalDetails) {
        modalDetails.style.display = 'block';
    }

    if (modalFooter) {
        modalFooter.style.display = 'flex';
    }

    // Hide confirmation modal
    confirmModal.style.display = 'none';

    // Show error notification
    showErrorNotification('Error: Could not add to cart. Please try again.');
}

// Function to add product to cart
window.addToCartPreEvent = function() {
    console.log('Real addToCartPreEvent function called');

    const confirmModal = document.getElementById('preEventCartConfirmModal');

    // Use nights as quantity (no special calculation for double rooms)
    const quantity = preEventNights;

    // Get the variation radio button - use a more reliable selector
    const variationRadio = document.querySelector(`.accommodation-product-variations[data-product-id="${preEventProductId}"] input[type="radio"]:checked`);

    // Make sure we have a valid variation ID
    if (variationRadio && !preEventVariationId) {
        preEventVariationId = parseInt(variationRadio.value);
    }

    console.log('Starting add to cart with variation ID:', preEventVariationId);

    // Get product details for the success notification
    const productName = document.getElementById('preEventConfirmProductName').textContent;
    const roomType = document.getElementById('confirmRoomType').textContent;
    const nights = document.getElementById('preEventConfirmNights').textContent;
    const totalPrice = document.getElementById('confirmTotalPrice').textContent;

    // Get product image
    const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${preEventProductId}"]`).closest('.accommodation-product-card');
    const productImage = productCard.querySelector('.accommodation-product-image img').src;

    // Change modal title and intro text to "Adding to Cart"
    const modalTitle = document.getElementById('confirmModalTitle');
    const modalIntro = document.getElementById('confirmModalIntro');
    const bookButton = document.getElementById('confirmBookButton');
    const modalContent = document.querySelector('.accommodation-cart-confirm-content');
    const modalDetails = document.querySelector('.accommodation-cart-confirm-details');
    const modalFooter = document.querySelector('.accommodation-cart-confirm-footer');

    if (modalTitle) {
        modalTitle.innerHTML = '<i class="fas fa-shopping-cart me-2" style="color: white;"></i> Adding to Cart';
    }

    if (modalIntro) {
        // Create a loading animation container
        modalIntro.innerHTML = `
            <div style="text-align: center; padding: 20px 0;">
                <div class="cart-loading-animation" style="margin: 0 auto 20px auto; width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #36b1dc, #2980b9); display: flex; align-items: center; justify-content: center; box-shadow: 0 10px 20px rgba(54, 177, 220, 0.3);">
                    <i class="fas fa-shopping-cart" style="color: white; font-size: 32px;"></i>
                </div>
                <p style="color: #2c3e50; font-size: 18px; font-weight: 600; margin-bottom: 10px;">Processing your selection</p>
                <p style="color: #5d6d7e; font-size: 14px;">Please wait while we add this item to your cart...</p>
                <div class="loading-bar" style="height: 4px; background: rgba(54, 177, 220, 0.1); border-radius: 2px; margin: 20px auto; width: 80%; max-width: 300px; overflow: hidden; position: relative;">
                    <div class="loading-bar-progress" style="position: absolute; top: 0; left: 0; height: 100%; width: 30%; background: linear-gradient(to right, #36b1dc, #2980b9); border-radius: 2px; animation: loadingProgress 1.5s infinite ease-in-out;"></div>
                </div>
            </div>
            <style>
                @keyframes loadingProgress {
                    0% { left: -30%; width: 30%; }
                    50% { width: 50%; }
                    100% { left: 100%; width: 30%; }
                }
                .cart-loading-animation {
                    animation: pulse 1.5s infinite;
                }
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                    100% { transform: scale(1); }
                }
            </style>
        `;
    }

    // Hide the details and footer
    if (modalDetails) {
        modalDetails.style.display = 'none';
    }

    if (modalFooter) {
        modalFooter.style.display = 'none';
    }

    // Add loading animation to the modal content
    if (modalContent) {
        modalContent.classList.add('processing');
    }

    // Create a URLSearchParams object for the request
    const params = new URLSearchParams();
    params.append('action', 'lci-dashboard-add-to-cart');
    params.append('product_id', preEventProductId);
    params.append('quantity', quantity);
    params.append('security', lci_ajax.nonce);

    // Explicitly add variation ID if it exists - this is the key part!
    if (preEventVariationId) {
        params.append('variation_id', preEventVariationId);
        console.log('Added variation_id to params:', preEventVariationId);

        if (variationRadio) {
            // Get variation attributes
            const attributeName = variationRadio.getAttribute('data-attribute-name');
            const attributeValue = variationRadio.getAttribute('data-attribute-value');

            if (attributeName && attributeValue) {
                params.append(`attribute_${attributeName}`, attributeValue);
                console.log(`Added attribute_${attributeName}:`, attributeValue);
            }
        }
    }

    // Log the params as a string to see exactly what's being sent
    console.log('Request parameters:', params.toString());

    // Send AJAX request using jQuery to ensure compatibility with WooCommerce
    jQuery.ajax({
        type: 'POST',
        url: lci_ajax.ajax_url,
        data: params.toString(),
        contentType: 'application/x-www-form-urlencoded',
        success: function(response) {
            console.log('Cart response:', response);

            // Trigger WooCommerce's update_cart event
            jQuery(document.body).trigger('added_to_cart', [null, null, jQuery('.add-to-cart-btn')]);

            // Add a slight delay to show the animation
            setTimeout(() => {
                // Update modal to show success message
                if (modalTitle) {
                    modalTitle.innerHTML = '<i class="fas fa-check-circle me-2" style="color: white;"></i> Added to Cart';
                }

                if (modalContent) {
                    modalContent.classList.remove('processing');
                }

                // Create success content for the modal
                if (modalIntro) {
                    modalIntro.innerHTML = `
                        <div style="text-align: center; padding: 20px 0;">
                            <div class="success-animation" style="margin: 0 auto 20px auto; width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #28a745, #20883b); display: flex; align-items: center; justify-content: center; box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3); animation: successPulse 1s 1;">
                                <i class="fas fa-check" style="color: white; font-size: 32px;"></i>
                            </div>
                            <p style="color: #2c3e50; font-size: 18px; font-weight: 600; margin-bottom: 10px;">Successfully Added to Cart</p>

                            <div style="background: rgba(248, 249, 250, 0.7); border-radius: 10px; padding: 15px; margin: 20px auto; max-width: 400px; text-align: left;">
                                <div style="margin-bottom: 8px;">
                                    <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Hotel:</span>
                                    <span style="color: #2c3e50; font-weight: 600;">${productName}</span>
                                </div>

                                ${roomType ? `
                                <div style="margin-bottom: 8px;">
                                    <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Room Type:</span>
                                    <span style="color: #2c3e50; font-weight: 600;">${roomType}</span>
                                </div>
                                ` : ''}

                                <div style="margin-bottom: 8px;">
                                    <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Stay Duration:</span>
                                    <span style="color: #2c3e50; font-weight: 600;">${nights}</span>
                                </div>

                                <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.1), rgba(54, 177, 220, 0.05)); padding: 8px 12px; border-radius: 8px; margin-top: 10px;">
                                    <span style="color: #2c3e50; font-weight: 700; font-size: 14px;">Total: ${totalPrice}</span>
                                </div>
                            </div>
                        </div>
                        <style>
                            @keyframes successPulse {
                                0% { transform: scale(0.5); opacity: 0; }
                                50% { transform: scale(1.2); }
                                100% { transform: scale(1); opacity: 1; }
                            }
                        </style>
                    `;
                }

                // Update footer to show a single close button
                if (modalFooter) {
                    modalFooter.innerHTML = `
                        <button type="button" onclick="closePreEventConfirmModal()" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-primary" style="width: 100%; text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                            <i class="fas fa-check me-2" style="color: white !important;"></i> <span style="color: white !important;">DONE</span>
                        </button>
                    `;
                    modalFooter.style.display = 'flex';
                }

                // Show success message on the product card
                const addToCartBtn = productCard.querySelector('.add-to-cart-btn');

                // Replace button with success message
                addToCartBtn.innerHTML = '<i class="fas fa-check me-2"></i> ADDED TO CART';
                addToCartBtn.style.backgroundColor = '#28a745';
                addToCartBtn.disabled = true;

                // Hide settings button
                const settingsBtn = productCard.querySelector('.accommodation-settings-button');
                if (settingsBtn) {
                    settingsBtn.style.display = 'none';
                }

                // Immediately update the minicart button content
                const customMiniCartCount = document.querySelector('.custom-mini-cart-count');
                const customMiniCartTotal = document.querySelector('.custom-mini-cart-total');

                if (customMiniCartCount) {
                    const currentCount = parseInt(customMiniCartCount.textContent) || 0;
                    customMiniCartCount.textContent = currentCount + 1;

                    // Add pulse animation
                    customMiniCartCount.classList.remove('count-pulse');
                    void customMiniCartCount.offsetWidth; // Trigger reflow to restart animation
                    customMiniCartCount.classList.add('count-pulse');
                }

                if (customMiniCartTotal) {
                    // Calculate and update the total immediately
                    const productPrice = parseFloat(totalPrice.replace(/[^0-9.-]+/g, ''));
                    const currentTotal = parseFloat(customMiniCartTotal.textContent.replace(/[^0-9.-]+/g, '')) || 0;
                    const newTotal = (currentTotal + productPrice).toFixed(2);
                    customMiniCartTotal.textContent = '€' + newTotal;
                }

                // Also refresh our custom mini cart if it's open
                if (document.getElementById('custom-mini-cart-modal').style.display === 'block') {
                    fetchCustomMiniCartItems();
                }

                // Refresh the mini-cart contents in the background
                setTimeout(() => {
                    refreshMiniCart();
                }, 500);

                // Auto-close the modal after 5 seconds
                setTimeout(() => {
                    closePreEventConfirmModal(); // This function uses the correct modal ID 'preEventCartConfirmModal'

                    // Reset modal state for next time
                    if (modalTitle) {
                        modalTitle.innerHTML = '<i class="fas fa-check-circle me-2" style="color: white;"></i> Confirm Your Selection';
                    }

                    if (modalIntro) {
                        modalIntro.innerHTML = 'Please confirm your pre-event accommodation selection:';
                    }

                    if (bookButton) {
                        bookButton.disabled = false;
                        bookButton.innerHTML = '<i class="fas fa-bed me-2" style="color: white !important;"></i> <span style="color: white !important;">BOOK</span>';
                    }

                    // Show the details again for next time
                    if (modalDetails) {
                        modalDetails.style.display = 'block';
                    }

                    // Reset footer for next time
                    if (modalFooter) {
                        modalFooter.innerHTML = `
                            <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-secondary" onclick="closePreEventConfirmModal()" style="margin-right: 15px; text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                                <i class="fas fa-times me-2" style="color: white !important;"></i> <span style="color: white !important;">CANCEL</span>
                            </button>
                            <button type="button" id="confirmBookButton" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-primary" onclick="addToCartPreEvent()" style="text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                                <i class="fas fa-bed me-2" style="color: white !important;"></i> <span style="color: white !important;">BOOK</span>
                            </button>
                        `;
                    }
                }, 5000);
            }, 1500); // 1.5 second delay to show the animation
        },
        error: function(error) {
            console.error('Error adding to cart:', error);

            // Reset modal state
            if (modalTitle) {
                modalTitle.innerHTML = '<i class="fas fa-check-circle me-2" style="color: white;"></i> Confirm Your Selection';
            }

            if (modalIntro) {
                modalIntro.innerHTML = 'Please confirm your pre-event accommodation selection:';
            }

            if (bookButton) {
                bookButton.disabled = false;
                bookButton.innerHTML = '<i class="fas fa-bed me-2" style="color: white !important;"></i> <span style="color: white !important;">BOOK</span>';
            }

            if (modalContent) {
                modalContent.classList.remove('processing');
            }

            // Show the details and footer again
            if (modalDetails) {
                modalDetails.style.display = 'block';
            }

            if (modalFooter) {
                modalFooter.style.display = 'flex';
            }

            // Hide confirmation modal
            confirmModal.style.display = 'none';

            // Show error notification
            showErrorNotification('Error: Could not add to cart. Please try again.');
        }
    });
}

// Function to show a nice UX2025 styled notification when product is added to cart
window.showAddedToCartNotification = function(productName, roomType, nights, totalPrice, productImage) {
    // Create notification container if it doesn't exist
    let notificationContainer = document.getElementById('addedToCartNotification');

    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'addedToCartNotification';
        notificationContainer.style.position = 'fixed';
        notificationContainer.style.top = '20px';
        notificationContainer.style.right = '20px';
        notificationContainer.style.zIndex = '10000';
        notificationContainer.style.maxWidth = '400px';
        notificationContainer.style.width = '100%';
        document.body.appendChild(notificationContainer);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.style.background = 'rgba(255, 255, 255, 0.95)';
    notification.style.borderRadius = '16px';
    notification.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(54, 177, 220, 0.1)';
    notification.style.padding = '20px';
    notification.style.marginBottom = '15px';
    notification.style.animation = 'slideInRight 0.5s forwards';
    notification.style.position = 'relative';
    notification.style.overflow = 'hidden';
    notification.style.backdropFilter = 'blur(10px)';
    notification.style.webkitBackdropFilter = 'blur(10px)';
    notification.style.border = '1px solid rgba(255, 255, 255, 0.8)';

    // Add keyframes for animation if they don't exist
    if (!document.getElementById('notification-animations')) {
        const style = document.createElement('style');
        style.id = 'notification-animations';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    // Add success icon and gradient
    notification.innerHTML = `
        <div style="position: absolute; top: 0; left: 0; right: 0; height: 5px; background: linear-gradient(to right, #36b1dc, #2980b9);"></div>
        <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>

        <div style="display: flex; align-items: flex-start;">
            <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.3);">
                <i class="fas fa-check" style="color: white; font-size: 18px;"></i>
            </div>

            <div style="flex: 1;">
                <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 16px; font-weight: 700;">Added to Your Cart</h4>

                <div style="background: rgba(248, 249, 250, 0.7); border-radius: 10px; padding: 12px; margin-bottom: 15px;">
                    <div style="margin-bottom: 8px;">
                        <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Hotel:</span>
                        <span style="color: #2c3e50; font-weight: 600;">${productName}</span>
                    </div>

                    ${roomType ? `
                    <div style="margin-bottom: 8px;">
                        <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Room Type:</span>
                        <span style="color: #2c3e50; font-weight: 600;">${roomType}</span>
                    </div>
                    ` : ''}

                    <div style="margin-bottom: 8px;">
                        <span style="color: #5d6d7e; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; display: block; margin-bottom: 3px;">Stay Duration:</span>
                        <span style="color: #2c3e50; font-weight: 600;">${nights}</span>
                    </div>

                    <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.1), rgba(54, 177, 220, 0.05)); padding: 8px 12px; border-radius: 8px; margin-top: 10px;">
                        <span style="color: #2c3e50; font-weight: 700; font-size: 14px;">Total: ${totalPrice}</span>
                    </div>
                </div>

                <div style="display: flex; justify-content: flex-end;">
                    <button onclick="this.closest('#addedToCartNotification').removeChild(this.closest('div').parentNode.parentNode)" style="background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; border: none; font-size: 14px; cursor: pointer; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; display: inline-flex; align-items: center;">
                        <i class="fas fa-check" style="margin-right: 8px; color: white;"></i> OK
                    </button>
                </div>
            </div>
        </div>
    `;

    // Add notification to container
    notificationContainer.appendChild(notification);

    // Auto-remove notification after 8 seconds
    setTimeout(() => {
        if (notification.parentNode === notificationContainer) {
            notification.style.animation = 'slideOutRight 0.5s forwards';
            setTimeout(() => {
                if (notification.parentNode === notificationContainer) {
                    notificationContainer.removeChild(notification);
                }
            }, 500);
        }
    }, 8000);
}

// Function to show error notification
window.showErrorNotification = function(message) {
    // Create notification container if it doesn't exist
    let notificationContainer = document.getElementById('addedToCartNotification');

    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'addedToCartNotification';
        notificationContainer.style.position = 'fixed';
        notificationContainer.style.top = '20px';
        notificationContainer.style.right = '20px';
        notificationContainer.style.zIndex = '10000';
        notificationContainer.style.maxWidth = '400px';
        notificationContainer.style.width = '100%';
        document.body.appendChild(notificationContainer);
    }

    // Add keyframes for animation if they don't exist
    if (!document.getElementById('notification-animations')) {
        const style = document.createElement('style');
        style.id = 'notification-animations';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.style.background = 'rgba(255, 255, 255, 0.95)';
    notification.style.borderRadius = '16px';
    notification.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(220, 53, 69, 0.1)';
    notification.style.padding = '20px';
    notification.style.marginBottom = '15px';
    notification.style.animation = 'slideInRight 0.5s forwards';
    notification.style.position = 'relative';
    notification.style.overflow = 'hidden';
    notification.style.backdropFilter = 'blur(10px)';
    notification.style.webkitBackdropFilter = 'blur(10px)';
    notification.style.border = '1px solid rgba(255, 255, 255, 0.8)';

    // Add error icon and gradient
    notification.innerHTML = `
        <div style="position: absolute; top: 0; left: 0; right: 0; height: 5px; background: linear-gradient(to right, #dc3545, #c82333);"></div>
        <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(220, 53, 69, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>

        <div style="display: flex; align-items: flex-start;">
            <div style="background: linear-gradient(135deg, #dc3545, #c82333); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);">
                <i class="fas fa-exclamation-triangle" style="color: white; font-size: 18px;"></i>
            </div>

            <div style="flex: 1;">
                <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 16px; font-weight: 700;">Error</h4>
                <p style="margin: 0 0 15px 0; color: #495057;">${message}</p>

                <button onclick="this.closest('#addedToCartNotification').removeChild(this.closest('div').parentNode.parentNode)" style="background: none; border: none; color: #6c757d; font-size: 14px; cursor: pointer; padding: 8px 12px; border-radius: 6px; transition: all 0.3s ease;">
                    Close
                </button>
            </div>
        </div>
    `;

    // Add notification to container
    notificationContainer.appendChild(notification);

    // Auto-remove notification after 8 seconds
    setTimeout(() => {
        if (notification.parentNode === notificationContainer) {
            notification.style.animation = 'slideOutRight 0.5s forwards';
            setTimeout(() => {
                if (notification.parentNode === notificationContainer) {
                    notificationContainer.removeChild(notification);
                }
            }, 500);
        }
    }, 8000);
}

// Function to open the custom mini cart
window.openCustomMiniCart = function() {
    // Show the modal
    const modal = document.getElementById('custom-mini-cart-modal');
    if (modal) {
        modal.style.display = 'block';
        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';

        // Fetch cart items
        fetchCustomMiniCartItems();
    }
}

// Function to close the custom mini cart
window.closeCustomMiniCart = function() {
    // Hide the modal
    const modal = document.getElementById('custom-mini-cart-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('overflow');
    }
}

// Function to fetch cart items for the custom mini cart
window.fetchCustomMiniCartItems = function() {
    // Show loading state
    document.getElementById('custom-mini-cart-loading').style.display = 'flex';
    document.getElementById('custom-mini-cart-empty').style.display = 'none';
    document.getElementById('custom-mini-cart-items').style.display = 'none';
    document.getElementById('custom-mini-cart-fundraising').style.display = 'none';

    // Create a nonce for security (use the one from lci_ajax_object if available)
    const nonce = typeof lci_ajax_object !== 'undefined' ? lci_ajax_object.nonce : '';
    const ajaxUrl = typeof lci_ajax_object !== 'undefined' ? lci_ajax_object.ajax_url : '/wp-admin/admin-ajax.php';

    // Fetch cart contents via AJAX
    fetch(ajaxUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=lci_get_mini_cart_items&nonce=' + nonce
    })
    .then(response => response.json())
    .then(data => {
        // Hide loading state
        document.getElementById('custom-mini-cart-loading').style.display = 'none';

        if (data.success) {
            const cartItems = data.data.items || [];
            const cartTotal = data.data.total || '€0.00';
            const hasFundraising = data.data.has_category_22 || false;
            const fundraisingAmount = data.data.fundraising_amount || 0;

            // Update cart total
            document.getElementById('custom-mini-cart-total-value').innerHTML = cartTotal;

            // Update cart count on the button
            const cartCountElement = document.querySelector('.custom-mini-cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = cartItems.length;
            }

            // Update cart items
            if (cartItems.length > 0) {
                // Show items container
                const itemsContainer = document.getElementById('custom-mini-cart-items');
                itemsContainer.style.display = 'block';

                // Generate HTML for cart items
                let itemsHtml = '';
                cartItems.forEach(item => {
                    itemsHtml += `
                        <div class="custom-mini-cart-item">
                            <div class="custom-mini-cart-item-image">
                                <img src="${item.image || 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp'}" alt="${item.name}">
                            </div>
                            <div class="custom-mini-cart-item-details">
                                <div class="custom-mini-cart-item-name">${item.name}</div>
                                ${item.meta ? `<div class="custom-mini-cart-item-meta">${item.meta}</div>` : ''}
                                <div class="custom-mini-cart-item-price">
                                    <span class="custom-mini-cart-item-quantity">${item.quantity} ×</span>
                                    ${item.price}
                                </div>
                            </div>
                            <button type="button" class="custom-mini-cart-item-remove" onclick="removeCustomMiniCartItem('${item.key}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                });

                // Update items container
                itemsContainer.innerHTML = itemsHtml;

                // Show fundraising message if applicable
                if (hasFundraising && fundraisingAmount > 0) {
                    document.getElementById('custom-mini-cart-fundraising').style.display = 'flex';
                    document.getElementById('custom-mini-cart-fundraising-amount').textContent = '€' + fundraisingAmount.toFixed(2);
                }
            } else {
                // Show empty cart message
                document.getElementById('custom-mini-cart-empty').style.display = 'block';
            }
        } else {
            // Show error message
            document.getElementById('custom-mini-cart-empty').style.display = 'block';
            document.getElementById('custom-mini-cart-empty').innerHTML = `
                <div class="custom-mini-cart-empty-icon" style="background: rgba(220, 53, 69, 0.1);">
                    <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                </div>
                <h4>Error Loading Cart</h4>
                <p>There was a problem loading your cart. Please try again.</p>
            `;
        }
    })
    .catch(error => {
        console.error('Error fetching cart items:', error);

        // Hide loading state
        document.getElementById('custom-mini-cart-loading').style.display = 'none';

        // Show error message
        document.getElementById('custom-mini-cart-empty').style.display = 'block';
        document.getElementById('custom-mini-cart-empty').innerHTML = `
            <div class="custom-mini-cart-empty-icon" style="background: rgba(220, 53, 69, 0.1);">
                <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
            </div>
            <h4>Error Loading Cart</h4>
            <p>There was a problem loading your cart. Please try again.</p>
        `;
    });
}

// Function to remove an item from the cart
window.removeCustomMiniCartItem = function(key) {
    if (!key) return;

    // Show loading state
    document.getElementById('custom-mini-cart-loading').style.display = 'flex';
    document.getElementById('custom-mini-cart-items').style.display = 'none';

    // Create a nonce for security
    const nonce = typeof lci_ajax_object !== 'undefined' ? lci_ajax_object.nonce : '';
    const ajaxUrl = typeof lci_ajax_object !== 'undefined' ? lci_ajax_object.ajax_url : '/wp-admin/admin-ajax.php';

    // Remove item via AJAX
    fetch(ajaxUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=lci_remove_cart_item&nonce=' + nonce + '&cart_item_key=' + key
    })
    .then(response => response.json())
    .then(data => {
        // Refresh cart items
        fetchCustomMiniCartItems();

        // Update mini cart count on the button with animation
        if (data.success && data.data && data.data.count !== undefined) {
            const cartCountElement = document.querySelector('.custom-mini-cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = data.data.count;

                // Add pulse animation
                cartCountElement.classList.remove('count-pulse');
                void cartCountElement.offsetWidth; // Trigger reflow to restart animation
                cartCountElement.classList.add('count-pulse');
            }
        }
    })
    .catch(error => {
        console.error('Error removing cart item:', error);

        // Refresh cart items anyway
        fetchCustomMiniCartItems();
    });
}

// Function to refresh the mini-cart
window.refreshMiniCart = function() {
    // Update our custom mini cart
    const cartCountElement = document.querySelector('.custom-mini-cart-count');
    const cartTotalElement = document.querySelector('.custom-mini-cart-total');

    if (cartCountElement || cartTotalElement) {
        // Create a nonce for security
        const nonce = typeof lci_ajax_object !== 'undefined' ? lci_ajax_object.nonce : '';
        const ajaxUrl = typeof lci_ajax_object !== 'undefined' ? lci_ajax_object.ajax_url : '/wp-admin/admin-ajax.php';

        // Fetch cart data
        fetch(ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=lci_get_mini_cart_items&nonce=' + nonce
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update cart count with animation
                if (cartCountElement) {
                    const oldCount = parseInt(cartCountElement.textContent) || 0;
                    const newCount = data.data.count || 0;
                    cartCountElement.textContent = newCount;

                    // Only animate if the count has changed
                    if (oldCount !== newCount) {
                        cartCountElement.classList.remove('count-pulse');
                        void cartCountElement.offsetWidth; // Trigger reflow to restart animation
                        cartCountElement.classList.add('count-pulse');
                    }
                }

                // Update cart total
                if (cartTotalElement) {
                    cartTotalElement.innerHTML = data.data.total || '€0.00';
                }
            }
        })
        .catch(error => {
            console.error('Error refreshing mini-cart:', error);
        });
    }
}
</script>

<!-- Pre-Event Accommodation Modal - UX2025 Style -->
<div x-data="preEventAccommodation()" x-init="initModal()">
    <div class="pre-event-modal-overlay" id="preEventModalOverlay" style="display: none;">
        <div class="pre-event-modal">
            <div class="pre-event-modal-header">
                <h3><i class="fas fa-hotel"></i> Pre-Event Accommodation</h3>
                <button onclick="closePreEventModal()" class="btn-close btn-close-white" aria-label="Close" style="position: relative; z-index: 2;"></button>
            </div>
            <div class="pre-event-modal-body">
                <!-- Decorative elements -->
                <div class="position-absolute" style="top: 20px; right: 20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                <div class="position-absolute" style="bottom: 30px; left: 10px; width: 120px; height: 120px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.03), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                <p class="mb-4" style="color: #37474f; font-size: 15px; position: relative; z-index: 1;">Please select your accommodation preferences for your stay before the main event:</p>

                <!-- Nights Selector - Enhanced UX2025 Style -->
                <div class="pre-event-modal-form-group">
                    <label for="nights" class="nights-label-header" style="display: flex; align-items: center; margin-bottom: 18px; color: #2c3e50; font-weight: 700;">
                        <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2), inset 0 2px 3px rgba(255, 255, 255, 0.3);">
                            <i class="fas fa-moon" style="color: white; font-size: 18px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));"></i>
                        </div>
                        <span style="position: relative; font-size: 18px;">
                            How many nights will you stay?
                            <div style="position: absolute; bottom: -5px; left: 0; width: 40px; height: 3px; background: linear-gradient(to right, #36b1dc, transparent); border-radius: 3px;"></div>
                        </span>
                    </label>
                    <div class="nights-selector" style="max-width: 100%;">
                        <div class="nights-selector-container" style="background: rgba(255, 255, 255, 0.8); border: 2px solid rgba(54, 177, 220, 0.3); border-radius: 20px; padding: 15px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); position: relative; overflow: hidden; display: flex; align-items: center;">
                            <!-- Decorative elements -->
                            <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                            <div style="position: absolute; bottom: -30px; left: 10%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.08), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                            <button type="button" class="nights-btn nights-btn-minus" @click="decrementNights()" :disabled="nights <= 1" style="width: 50px; height: 50px; border-radius: 15px; background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; border: none; font-size: 20px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2); position: relative; z-index: 1;">
                                <i class="fas fa-minus" style="color: white !important;"></i>
                            </button>
                            <div class="nights-display" style="flex-grow: 1; text-align: center; padding: 0 20px; position: relative; z-index: 1;">
                                <div class="nights-value-container" style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                                    <span class="nights-count" x-text="nights" style="font-size: 36px; font-weight: 800; color: #2c3e50; margin-right: 10px; text-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);"></span>
                                    <span class="nights-label" x-text="nights === 1 ? 'Night' : 'Nights'" style="font-size: 20px; color: #5d6d7e; font-weight: 600;"></span>
                                </div>
                                <!-- Enhanced Progress Bar with Markers -->
                                <div class="nights-progress-container" style="margin-top: 15px; position: relative;">
                                    <!-- Progress Track -->
                                    <div style="height: 12px; background: rgba(54, 177, 220, 0.1); border-radius: 6px; overflow: hidden; box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); position: relative;">
                                        <!-- Progress Fill -->
                                        <div class="nights-progress-bar" :style="'width: ' + (nights * 20) + '%'" style="height: 100%; background: linear-gradient(to right, #36b1dc, #2980b9); border-radius: 6px; transition: width 0.3s ease; box-shadow: 0 1px 3px rgba(54, 177, 220, 0.3);">
                                            <!-- Animated Glow Effect -->
                                            <div style="position: absolute; top: 0; right: 0; bottom: 0; width: 15px; background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent); animation: progressGlow 1.5s infinite; transform: skewX(-20deg);"></div>
                                        </div>
                                    </div>

                                    <!-- Progress Markers -->
                                    <div style="position: relative; height: 30px; margin-top: 5px;">
                                        <!-- Marker 1 -->
                                        <div style="position: absolute; left: 0%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 1 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 1 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 1 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">1 Night</span>
                                        </div>

                                        <!-- Marker 2 -->
                                        <div style="position: absolute; left: 25%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 2 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 2 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 2 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">2 Nights</span>
                                        </div>

                                        <!-- Marker 3 -->
                                        <div style="position: absolute; left: 50%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 3 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 3 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 3 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">3 Nights</span>
                                        </div>

                                        <!-- Marker 4 -->
                                        <div style="position: absolute; left: 75%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 4 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 4 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 4 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">4 Nights</span>
                                        </div>

                                        <!-- Marker 5 -->
                                        <div style="position: absolute; left: 100%; top: 0; transform: translateX(-50%);">
                                            <div :class="{ 'active-marker': nights >= 5 }" style="width: 20px; height: 20px; border-radius: 50%; background: white; border: 2px solid #e0e0e0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center; margin-bottom: 5px; transition: all 0.3s ease;" x-bind:style="nights >= 5 ? 'background: #36b1dc; border-color: #2980b9; box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);' : ''">
                                                <i class="fas fa-check" style="font-size: 10px; color: white; opacity: 0; transition: opacity 0.3s ease;" x-bind:style="nights >= 5 ? 'opacity: 1;' : ''"></i>
                                            </div>
                                            <span style="font-size: 11px; color: #5d6d7e; font-weight: 600; position: absolute; left: 50%; transform: translateX(-50%); white-space: nowrap;">5 Nights</span>
                                        </div>
                                    </div>

                                    <style>
                                    @keyframes progressGlow {
                                        0% { left: -15px; }
                                        100% { left: 100%; }
                                    }

                                    .active-marker {
                                        transform: scale(1.1);
                                    }
                                    </style>
                                </div>
                            </div>
                            <button type="button" class="nights-btn nights-btn-plus" @click="incrementNights()" style="width: 50px; height: 50px; border-radius: 15px; background: linear-gradient(135deg, #36b1dc, #2980b9); color: white; border: none; font-size: 20px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2); position: relative; z-index: 1;">
                                <i class="fas fa-plus" style="color: white !important;"></i>
                            </button>
                        </div>
                    </div>

                    <style>
                    /* Hover and active states for night selector buttons */
                    .nights-btn:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 6px 15px rgba(54, 177, 220, 0.3);
                        background: linear-gradient(135deg, #2980b9, #1c6ea4);
                    }

                    .nights-btn:active {
                        transform: translateY(1px);
                        box-shadow: 0 2px 5px rgba(54, 177, 220, 0.2);
                    }

                    .nights-btn:hover i,
                    .nights-btn:active i {
                        color: white !important;
                    }

                    /* Disabled state */
                    .nights-btn:disabled {
                        background: linear-gradient(135deg, #a8d1e7, #a8d1e7);
                        cursor: not-allowed;
                        transform: none;
                        opacity: 0.7;
                    }

                    /* Add ripple effect on button click */
                    .nights-btn::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 5px;
                        height: 5px;
                        background: rgba(255, 255, 255, 0.5);
                        opacity: 0;
                        border-radius: 100%;
                        transform: scale(1, 1) translate(-50%, -50%);
                        transform-origin: 50% 50%;
                    }

                    .nights-btn:active::after {
                        animation: nightsBtnRipple 0.6s ease-out;
                    }

                    @keyframes nightsBtnRipple {
                        0% {
                            transform: scale(0, 0);
                            opacity: 0.5;
                        }
                        100% {
                            transform: scale(20, 20);
                            opacity: 0;
                        }
                    }
                    </style>
                </div>

                <!-- Room Type Selector - Enhanced UX2025 Style -->
                <div class="pre-event-modal-form-group" style="margin-top: 25px;">
                    <label for="roomType" style="display: flex; align-items: center; margin-bottom: 18px; color: #2c3e50; font-weight: 700;">
                        <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2), inset 0 2px 3px rgba(255, 255, 255, 0.3);">
                            <i class="fas fa-bed" style="color: white; font-size: 18px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));"></i>
                        </div>
                        <span style="position: relative; font-size: 18px;">
                            Room Type
                            <div style="position: absolute; bottom: -5px; left: 0; width: 40px; height: 3px; background: linear-gradient(to right, #36b1dc, transparent); border-radius: 3px;"></div>
                        </span>
                    </label>

                    <div style="position: relative;">
                        <select id="roomType" x-model="roomType" class="form-select" style="border-radius: 16px; border: 2px solid rgba(54, 177, 220, 0.3); padding: 15px 20px; background: rgba(255, 255, 255, 0.8); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); font-size: 16px; color: #2c3e50; transition: all 0.3s ease; backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); appearance: none; -webkit-appearance: none; font-weight: 600; width: 100%;">
                            <option value="single">Single Room (1 person)</option>
                            <option value="double">Double Room (sharing)</option>
                        </select>
                        <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                            <i class="fas fa-chevron-down" style="color: #36b1dc; font-size: 16px;"></i>
                        </div>
                    </div>

                    <style>
                    /* Custom styling for the select dropdown */
                    .form-select:focus {
                        outline: none;
                        border-color: #36b1dc;
                        box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);
                    }

                    .form-select:hover {
                        border-color: #36b1dc;
                        transform: translateY(-2px);
                        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.07);
                    }
                    </style>
                </div>

                <!-- Stay Dates Information - UX2025 Style -->
                <!-- Your Stay Dates Header - Now outside the dates container -->
                <div style="display: flex; align-items: center; margin: 25px 0 15px 0;">
                    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 15px; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2), inset 0 2px 3px rgba(255, 255, 255, 0.3);">
                        <i class="fas fa-calendar-alt" style="color: white; font-size: 18px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));"></i>
                    </div>
                    <span style="color: #2c3e50; font-weight: 700; font-size: 18px; position: relative;">
                        Your Stay Dates
                        <div style="position: absolute; bottom: -5px; left: 0; width: 40px; height: 3px; background: linear-gradient(to right, #36b1dc, transparent); border-radius: 3px;"></div>
                    </span>
                </div>

                <div class="pre-event-modal-dates" style="background: rgba(255, 255, 255, 0.8); border-radius: 20px; padding: 20px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); border: 1px solid rgba(54, 177, 220, 0.2); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); position: relative; overflow: hidden;">
                    <!-- Decorative elements -->
                    <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                    <div style="position: absolute; bottom: -30px; left: 10%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.08), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                    <div class="pre-event-modal-dates-row" style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border-bottom: 1px solid rgba(0, 0, 0, 0.05); background: rgba(255, 255, 255, 0.5); border-radius: 12px; margin-bottom: 10px; transition: all 0.3s ease;">
                        <span class="pre-event-modal-dates-label" style="color: #5d6d7e; font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">Check-in Date:</span>
                        <span class="pre-event-modal-dates-value" x-text="calculateCheckinDate()" style="color: #2c3e50; font-weight: 600; font-size: 15px; background: linear-gradient(135deg, rgba(54, 177, 220, 0.15), rgba(54, 177, 220, 0.05)); padding: 8px 15px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);"></span>
                    </div>
                    <div class="pre-event-modal-dates-row" style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background: rgba(255, 255, 255, 0.5); border-radius: 12px; transition: all 0.3s ease;">
                        <span class="pre-event-modal-dates-label" style="color: #5d6d7e; font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">Check-out Date:</span>
                        <span class="pre-event-modal-dates-value" style="color: #2c3e50; font-weight: 600; font-size: 15px; background: linear-gradient(135deg, rgba(54, 177, 220, 0.15), rgba(54, 177, 220, 0.05)); padding: 8px 15px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);">21/08/2025</span>
                    </div>
                </div>
            </div>
            <div class="pre-event-modal-footer" style="padding: 25px 30px;">
                <div class="pre-event-modal-footer-buttons" style="display: flex; flex-direction: row; gap: 15px;">
                    <button onclick="closePreEventModal()" class="pre-event-modal-btn pre-event-modal-btn-secondary" style="width: 30%; text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                        <i class="fas fa-times me-2" style="color: white !important;"></i> <span style="color: white !important;">CANCEL</span>
                    </button>
                    <button @click="savePreferences()" class="pre-event-modal-btn pre-event-modal-btn-primary" style="width: 70%; text-transform: uppercase; letter-spacing: 0.5px; font-size: 14px;">
                        <i class="fas fa-check me-2" style="color: white !important;"></i> <span style="color: white !important;">SAVE PREFERENCES</span>
                    </button>
                </div>
            </div>

            <style>
            /* Responsive styles for mobile */
            @media (max-width: 576px) {
                .pre-event-modal-footer-buttons {
                    flex-direction: column !important;
                }

                .pre-event-modal-footer-buttons .pre-event-modal-btn {
                    width: 100% !important;
                }
            }
            </style>

            <style>
            /* Ensure text and icons remain white on hover */
            .pre-event-modal-btn-primary:hover i,
            .pre-event-modal-btn-primary:hover span,
            .pre-event-modal-btn-primary:active i,
            .pre-event-modal-btn-primary:active span {
                color: white !important;
            }

            .pre-event-modal-btn-secondary:hover i,
            .pre-event-modal-btn-secondary:hover span,
            .pre-event-modal-btn-secondary:active i,
            .pre-event-modal-btn-secondary:active span {
                color: white !important;
            }

            /* Add ripple effect on button click */
            .pre-event-modal-btn {
                position: relative;
                overflow: hidden;
            }

            .pre-event-modal-btn::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 5px;
                height: 5px;
                background: rgba(255, 255, 255, 0.5);
                opacity: 0;
                border-radius: 100%;
                transform: scale(1, 1) translate(-50%, -50%);
                transform-origin: 50% 50%;
            }

            .pre-event-modal-btn:active::after {
                animation: preEventBtnRipple 0.6s ease-out;
            }

            @keyframes preEventBtnRipple {
                0% {
                    transform: scale(0, 0);
                    opacity: 0.5;
                }
                100% {
                    transform: scale(20, 20);
                    opacity: 0;
                }
            }
            </style>
        </div>
    </div>
</div>

<script>
function preEventAccommodation() {
    return {
        showModal: false,
        nights: 1,
        roomType: 'single',

        initModal() {
            console.log('Initializing Pre-Event modal');

            // Load saved preferences if available
            const savedNights = sessionStorage.getItem('preEventNights');
            const savedRoomType = sessionStorage.getItem('preEventRoomType');

            if (savedNights) this.nights = parseInt(savedNights);
            if (savedRoomType) this.roomType = savedRoomType;

            // Always apply preferences to buttons
            this.applyPreferencesToButtons();

            // Only show modal if preferences are not already set
            const preferencesAlreadySet = savedNights && savedRoomType;

            if (!preferencesAlreadySet) {
                // Show modal only if preferences are not set
                setTimeout(() => {
                    this.showModal = true;
                    console.log('Modal should be visible now - no preferences found');
                }, 500);
            } else {
                console.log('Preferences already set, not showing modal automatically');
            }

            // Listen for modal visibility changes
            this.$watch('showModal', value => {
                if (value) {
                    console.log('Modal is now visible');
                    document.querySelector('.pre-event-modal-overlay').style.display = 'flex';
                }
            });
        },

        closeModal() {
            this.showModal = false;
            document.querySelector('.pre-event-modal-overlay').style.display = 'none';
        },

        incrementNights() {
            console.log('Incrementing nights');
            this.nights++;
            document.querySelector('.nights-count').textContent = this.nights;
            this.updateNightsDisplay();
        },

        decrementNights() {
            console.log('Decrementing nights');
            if (this.nights > 1) {
                this.nights--;
                document.querySelector('.nights-count').textContent = this.nights;
                this.updateNightsDisplay();
            }
        },

        updateNightsDisplay() {
            console.log('Updating nights display');

            // Update nights label
            const nightsLabel = document.querySelector('.nights-label');
            if (nightsLabel) {
                nightsLabel.textContent = this.nights === 1 ? 'Night' : 'Nights';
            }

            // Update check-in date display
            const checkinDateElement = document.querySelector('.pre-event-modal-dates-value');
            if (checkinDateElement) {
                checkinDateElement.textContent = this.calculateCheckinDate();
                console.log('Updated check-in date to:', this.calculateCheckinDate());
            }
        },

        calculateCheckinDate() {
            // Base date is 21/08/2025 (check-out date)
            const baseDate = new Date(2025, 7, 21); // Month is 0-indexed, so 7 = August

            // Calculate check-in date by subtracting nights from base date
            // For 1 night: Check-in on 20/08, stay night of 20-21, check out on 21/08
            // For 2 nights: Check-in on 19/08, stay nights of 19-20 and 20-21, check out on 21/08
            const checkinDate = new Date(baseDate);
            checkinDate.setDate(baseDate.getDate() - this.nights);

            // Format the date as DD/MM/YYYY
            const day = String(checkinDate.getDate()).padStart(2, '0');
            const month = String(checkinDate.getMonth() + 1).padStart(2, '0');
            const year = checkinDate.getFullYear();

            return `${day}/${month}/${year}`;
        },

        savePreferences() {
            console.log('Alpine savePreferences called');

            try {
                // Save preferences to session storage
                sessionStorage.setItem('preEventNights', this.nights.toString());
                sessionStorage.setItem('preEventRoomType', this.roomType);
                console.log('Saved to session storage:', { nights: this.nights, roomType: this.roomType });

                // Apply preferences to buttons in Alpine component
                this.applyPreferencesToButtons();

                // Call global functions to ensure everything is updated
                if (typeof updatePreEventAddToCartButtons === 'function') {
                    updatePreEventAddToCartButtons(this.nights, this.roomType);
                }

                if (typeof applyPreEventRoomTypePreferences === 'function') {
                    applyPreEventRoomTypePreferences();
                }

                // Close the modal
                this.closeModal();
            } catch (error) {
                console.error('Error saving preferences:', error);
            }
        },

        applyPreferencesToButtons() {
            // Get all add to cart buttons
            const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

            // Update all product variations based on room type preference
            const productVariationContainers = document.querySelectorAll('.accommodation-product-variations');

            productVariationContainers.forEach(container => {
                const productId = container.getAttribute('data-product-id');
                const variations = container.querySelectorAll('.accommodation-product-variation');

                let singleRoomVariation = null;
                let doubleRoomVariation = null;

                // Find single and double room variations
                variations.forEach(variation => {
                    const radioInput = variation.querySelector('input[type=radio]');
                    const variationName = radioInput.getAttribute('data-variation-name');

                    if (variationName.toLowerCase().includes('single')) {
                        singleRoomVariation = variation;
                    } else if (variationName.toLowerCase().includes('double')) {
                        doubleRoomVariation = variation;
                    }
                });

                // Apply room type preference
                if (singleRoomVariation && doubleRoomVariation) {
                    const singleRadio = singleRoomVariation.querySelector('input[type=radio]');
                    const doubleRadio = doubleRoomVariation.querySelector('input[type=radio]');

                    if (this.roomType === 'double') {
                        // Select double room variation
                        doubleRadio.checked = true;

                        // Disable single room variation
                        singleRoomVariation.classList.add('variation-disabled');
                        singleRadio.disabled = true;

                        // Enable double room variation
                        doubleRoomVariation.classList.remove('variation-disabled');
                        doubleRadio.disabled = false;
                    } else {
                        // When single room is selected
                        // Enable single room variation
                        singleRoomVariation.classList.remove('variation-disabled');
                        singleRadio.disabled = false;

                        // Disable double room variation
                        doubleRoomVariation.classList.add('variation-disabled');
                        doubleRadio.disabled = true;

                        // Default to single room
                        singleRadio.checked = true;
                    }
                }
            });

            // Update button text to reflect preferences
            addToCartButtons.forEach(button => {
                const nightsText = this.nights === 1 ? 'night' : 'nights';
                const buttonText = `<i class="fas fa-bed me-2"></i> BOOK (${this.nights} ${nightsText})`;
                button.innerHTML = buttonText;

                // Update quantity attribute - always use nights as quantity
                button.setAttribute('data-quantity', this.nights);
            });
        }
    };
}
</script>

<!-- Script for modal functionality -->
<script>
// Function to show the Pre-Event modal
window.showPreEventModal = function(event) {
    if (event) {
        event.preventDefault();
    }
    const modalElement = document.querySelector('.pre-event-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'flex';
        console.log('Modal displayed via direct function call');
    }
}

// Function to close the Pre-Event modal
window.closePreEventModal = function() {
    const modalElement = document.querySelector('.pre-event-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'none';
        console.log('Modal hidden via direct function call');
    }
}
</script>

<!-- Additional custom styles -->
<style>
.accommodation-features-list {
    margin: 2px !important;
}
</style>

<!-- Script to ensure all functions are globally accessible -->
<script>
// Make functions available immediately
(function() {
    // Explicitly assign all functions to the window object
    window.addToCartPreEvent = typeof addToCartPreEvent !== 'undefined' ? addToCartPreEvent : window.addToCartPreEvent;
    window.closePreEventConfirmModal = typeof closePreEventConfirmModal !== 'undefined' ? closePreEventConfirmModal : window.closePreEventConfirmModal;
    window.showPreEventConfirmModal = typeof showPreEventConfirmModal !== 'undefined' ? showPreEventConfirmModal : window.showPreEventConfirmModal;
    window.showPreEventModal = typeof showPreEventModal !== 'undefined' ? showPreEventModal : window.showPreEventModal;
    window.closePreEventModal = typeof closePreEventModal !== 'undefined' ? closePreEventModal : window.closePreEventModal;
    window.showErrorNotification = typeof showErrorNotification !== 'undefined' ? showErrorNotification : window.showErrorNotification;
    window.showAddedToCartNotification = typeof showAddedToCartNotification !== 'undefined' ? showAddedToCartNotification : window.showAddedToCartNotification;
    window.calculatePreEventCheckinDate = typeof calculatePreEventCheckinDate !== 'undefined' ? calculatePreEventCheckinDate : window.calculatePreEventCheckinDate;
    window.refreshMiniCart = typeof refreshMiniCart !== 'undefined' ? refreshMiniCart : window.refreshMiniCart;
    window.fetchCustomMiniCartItems = typeof fetchCustomMiniCartItems !== 'undefined' ? fetchCustomMiniCartItems : window.fetchCustomMiniCartItems;
    window.removeCustomMiniCartItem = typeof removeCustomMiniCartItem !== 'undefined' ? removeCustomMiniCartItem : window.removeCustomMiniCartItem;
    window.openCustomMiniCart = typeof openCustomMiniCart !== 'undefined' ? openCustomMiniCart : window.openCustomMiniCart;
    window.closeCustomMiniCart = typeof closeCustomMiniCart !== 'undefined' ? closeCustomMiniCart : window.closeCustomMiniCart;
    window.updateCurrentSettingsDisplay = typeof updateCurrentSettingsDisplay !== 'undefined' ? updateCurrentSettingsDisplay : window.updateCurrentSettingsDisplay;

    console.log('All functions have been made globally accessible immediately');
})();

// Also make sure all functions are available after DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    // Explicitly assign all functions to the window object
    window.addToCartPreEvent = typeof addToCartPreEvent !== 'undefined' ? addToCartPreEvent : window.addToCartPreEvent;
    window.closePreEventConfirmModal = typeof closePreEventConfirmModal !== 'undefined' ? closePreEventConfirmModal : window.closePreEventConfirmModal;
    window.showPreEventConfirmModal = typeof showPreEventConfirmModal !== 'undefined' ? showPreEventConfirmModal : window.showPreEventConfirmModal;
    window.showPreEventModal = typeof showPreEventModal !== 'undefined' ? showPreEventModal : window.showPreEventModal;
    window.closePreEventModal = typeof closePreEventModal !== 'undefined' ? closePreEventModal : window.closePreEventModal;
    window.showErrorNotification = typeof showErrorNotification !== 'undefined' ? showErrorNotification : window.showErrorNotification;
    window.showAddedToCartNotification = typeof showAddedToCartNotification !== 'undefined' ? showAddedToCartNotification : window.showAddedToCartNotification;
    window.calculatePreEventCheckinDate = typeof calculatePreEventCheckinDate !== 'undefined' ? calculatePreEventCheckinDate : window.calculatePreEventCheckinDate;
    window.refreshMiniCart = typeof refreshMiniCart !== 'undefined' ? refreshMiniCart : window.refreshMiniCart;
    window.fetchCustomMiniCartItems = typeof fetchCustomMiniCartItems !== 'undefined' ? fetchCustomMiniCartItems : window.fetchCustomMiniCartItems;
    window.removeCustomMiniCartItem = typeof removeCustomMiniCartItem !== 'undefined' ? removeCustomMiniCartItem : window.removeCustomMiniCartItem;
    window.openCustomMiniCart = typeof openCustomMiniCart !== 'undefined' ? openCustomMiniCart : window.openCustomMiniCart;
    window.closeCustomMiniCart = typeof closeCustomMiniCart !== 'undefined' ? closeCustomMiniCart : window.closeCustomMiniCart;
    window.updateCurrentSettingsDisplay = typeof updateCurrentSettingsDisplay !== 'undefined' ? updateCurrentSettingsDisplay : window.updateCurrentSettingsDisplay;

    console.log('All functions have been made globally accessible');
});
</script>

<script>
// Function to update current settings display
window.updateCurrentSettingsDisplay = function() {
    try {
        const savedRoomType = sessionStorage.getItem('preEventRoomType') || 'single';
        const savedNights = parseInt(sessionStorage.getItem('preEventNights')) || 1;

        // Update nights display
        const currentNightsElement = document.getElementById('current-nights');
        if (currentNightsElement) {
            currentNightsElement.textContent = savedNights;
        }

        // Update nights label (singular/plural)
        const currentNightsLabelElement = document.getElementById('current-nights-label');
        if (currentNightsLabelElement) {
            currentNightsLabelElement.textContent = savedNights === 1 ? 'night' : 'nights';
        }

        // Update room type display
        const currentRoomTypeElement = document.getElementById('current-room-type');
        if (currentRoomTypeElement) {
            currentRoomTypeElement.textContent = savedRoomType === 'single' ? 'Single Room' : 'Double Room';
        }

        // Update check-in date display
        const currentCheckinDateElement = document.getElementById('current-checkin-date');
        if (currentCheckinDateElement) {
            // Base date is 21/08/2025 (check-out date)
            const baseDate = new Date(2025, 7, 21); // Month is 0-indexed, so 7 = August

            // Calculate check-in date by subtracting nights from base date
            const checkinDate = new Date(baseDate);
            checkinDate.setDate(baseDate.getDate() - savedNights);

            // Format the date as DD/MM/YYYY
            const day = String(checkinDate.getDate()).padStart(2, '0');
            const month = String(checkinDate.getMonth() + 1).padStart(2, '0');
            const year = checkinDate.getFullYear();

            currentCheckinDateElement.textContent = `${day}/${month}/${year}`;
        }
    } catch (error) {
        console.error('Error updating current settings display:', error);
    }
}

// Apply room type preferences - global function
function applyPreEventRoomTypePreferences() {
    try {
        console.log('Applying pre-event room type preferences');
        const savedRoomType = sessionStorage.getItem('preEventRoomType') || 'single';
        console.log('Saved room type:', savedRoomType);

        // Get saved nights
        const savedNights = parseInt(sessionStorage.getItem('preEventNights')) || 1;
        console.log('Saved nights:', savedNights);

        // Update Add to Cart buttons first
        updatePreEventAddToCartButtons(savedNights, savedRoomType);

        // Update current settings display
        updateCurrentSettingsDisplay();

        // Update all product variations based on room type preference
        const productVariationContainers = document.querySelectorAll('.accommodation-product-variations');

        productVariationContainers.forEach(container => {
            const productId = container.getAttribute('data-product-id');
            const variations = container.querySelectorAll('.accommodation-product-variation');

            let singleRoomVariation = null;
            let doubleRoomVariation = null;

            // Find single and double room variations
            variations.forEach(variation => {
                const radioInput = variation.querySelector('input[type=radio]');
                const variationName = radioInput.getAttribute('data-variation-name');

                if (variationName.toLowerCase().includes('single')) {
                    singleRoomVariation = variation;
                } else if (variationName.toLowerCase().includes('double')) {
                    doubleRoomVariation = variation;
                }
            });

            // Apply room type preference
            if (singleRoomVariation && doubleRoomVariation) {
                const singleRadio = singleRoomVariation.querySelector('input[type=radio]');
                const doubleRadio = doubleRoomVariation.querySelector('input[type=radio]');

                if (savedRoomType === 'double') {
                    // Select double room variation
                    doubleRadio.checked = true;

                    // Disable single room variation
                    singleRoomVariation.classList.add('variation-disabled');
                    singleRadio.disabled = true;

                    // Enable double room variation
                    doubleRoomVariation.classList.remove('variation-disabled');
                    doubleRadio.disabled = false;
                } else {
                    // When single room is selected
                    // Enable single room variation
                    singleRoomVariation.classList.remove('variation-disabled');
                    singleRadio.disabled = false;

                    // Disable double room variation
                    doubleRoomVariation.classList.add('variation-disabled');
                    doubleRadio.disabled = true;

                    // Default to single room
                    singleRadio.checked = true;
                }
            }
        });
    } catch (error) {
        console.error('Error in applyPreEventRoomTypePreferences:', error);
    }
}

// Alpine.js component for Pre-Event Accommodation
function preEventAccommodation() {
    return {
        showModal: false,
        nights: 1,
        roomType: 'single',

        initModal() {
            console.log('Initializing Pre-Event modal');

            // Load saved preferences if available
            const savedNights = sessionStorage.getItem('preEventNights');
            const savedRoomType = sessionStorage.getItem('preEventRoomType');

            if (savedNights) this.nights = parseInt(savedNights);
            if (savedRoomType) this.roomType = savedRoomType;

            // Always apply preferences to buttons
            this.applyPreferencesToButtons();
        },

        closeModal() {
            this.showModal = false;
            document.querySelector('.pre-event-modal-overlay').style.display = 'none';
        },

        incrementNights() {
            console.log('Incrementing nights');
            this.nights++;
            this.updateNightsDisplay();
        },

        decrementNights() {
            console.log('Decrementing nights');
            if (this.nights > 1) {
                this.nights--;
                this.updateNightsDisplay();
            }
        },

        updateNightsDisplay() {
            console.log('Updating nights display');
        },

        calculateCheckinDate() {
            // Base date is 21/08/2025 (check-out date)
            const baseDate = new Date(2025, 7, 21); // Month is 0-indexed, so 7 = August

            // Calculate check-in date by subtracting nights from base date
            const checkinDate = new Date(baseDate);
            checkinDate.setDate(baseDate.getDate() - this.nights);

            // Format the date as DD/MM/YYYY
            const day = String(checkinDate.getDate()).padStart(2, '0');
            const month = String(checkinDate.getMonth() + 1).padStart(2, '0');
            const year = checkinDate.getFullYear();

            return `${day}/${month}/${year}`;
        },

        savePreferences() {
            console.log('Alpine savePreferences called');

            try {
                // Save preferences to session storage
                sessionStorage.setItem('preEventNights', this.nights.toString());
                sessionStorage.setItem('preEventRoomType', this.roomType);
                console.log('Saved to session storage:', { nights: this.nights, roomType: this.roomType });

                // Apply preferences to buttons in Alpine component
                this.applyPreferencesToButtons();

                // Update current settings display
                if (typeof updateCurrentSettingsDisplay === 'function') {
                    updateCurrentSettingsDisplay();
                }

                // Call global functions to ensure everything is updated
                if (typeof updatePreEventAddToCartButtons === 'function') {
                    updatePreEventAddToCartButtons(this.nights, this.roomType);
                }

                if (typeof applyPreEventRoomTypePreferences === 'function') {
                    applyPreEventRoomTypePreferences();
                }

                // Close the modal
                this.closeModal();
            } catch (error) {
                console.error('Error saving preferences:', error);
            }
        },

        applyPreferencesToButtons() {
            // Get all add to cart buttons
            const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

            // Update all product variations based on room type preference
            const productVariationContainers = document.querySelectorAll('.accommodation-product-variations');

            productVariationContainers.forEach(container => {
                const productId = container.getAttribute('data-product-id');
                const variations = container.querySelectorAll('.accommodation-product-variation');

                let singleRoomVariation = null;
                let doubleRoomVariation = null;

                // Find single and double room variations
                variations.forEach(variation => {
                    const radioInput = variation.querySelector('input[type=radio]');
                    const variationName = radioInput.getAttribute('data-variation-name');

                    if (variationName.toLowerCase().includes('single')) {
                        singleRoomVariation = variation;
                    } else if (variationName.toLowerCase().includes('double')) {
                        doubleRoomVariation = variation;
                    }
                });

                // Apply room type preference
                if (singleRoomVariation && doubleRoomVariation) {
                    const singleRadio = singleRoomVariation.querySelector('input[type=radio]');
                    const doubleRadio = doubleRoomVariation.querySelector('input[type=radio]');

                    if (this.roomType === 'double') {
                        // Select double room variation
                        doubleRadio.checked = true;

                        // Disable single room variation
                        singleRoomVariation.classList.add('variation-disabled');
                        singleRadio.disabled = true;

                        // Enable double room variation
                        doubleRoomVariation.classList.remove('variation-disabled');
                        doubleRadio.disabled = false;
                    } else {
                        // When single room is selected
                        // Enable single room variation
                        singleRoomVariation.classList.remove('variation-disabled');
                        singleRadio.disabled = false;

                        // Disable double room variation
                        doubleRoomVariation.classList.add('variation-disabled');
                        doubleRadio.disabled = true;

                        // Default to single room
                        singleRadio.checked = true;
                    }
                }
            });

            // Update button text to reflect preferences
            addToCartButtons.forEach(button => {
                const buttonText = this.roomType === 'double'
                    ? `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${this.nights} nights × 2 people)`
                    : `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${this.nights} nights)`;

                button.innerHTML = buttonText;

                // Update quantity attribute
                const quantity = this.roomType === 'double' ? this.nights * 2 : this.nights;
                button.setAttribute('data-quantity', quantity);
            });
        }
    };
}

// Function to update Add to Cart buttons with new nights value
function updatePreEventAddToCartButtons(nights, roomType) {
    console.log('Updating Add to Cart buttons with:', { nights, roomType });
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    console.log('Found buttons:', addToCartButtons.length);

    addToCartButtons.forEach(button => {
        // Skip buttons that are already in "Added to Cart" state
        if (button.disabled) {
            console.log('Skipping disabled button');
            return;
        }

        // Use the same text format for all room types
        const nightsText = nights === 1 ? 'night' : 'nights';
        const buttonText = `<i class="fas fa-bed me-2"></i> BOOK (${nights} ${nightsText})`;
        console.log('Setting button text to:', buttonText);
        button.innerHTML = buttonText;

        // Update quantity attribute - always use nights as quantity
        button.setAttribute('data-quantity', nights);
    });
}

// Pre-Event Accommodation Modal
document.write(`
<div x-data="preEventAccommodation()" x-init="initModal()">
    <div class="pre-event-modal-overlay" id="preEventModalOverlay" style="display: none;">
        <div class="pre-event-modal">
            <div class="pre-event-modal-header">
                <h3><i class="fas fa-hotel me-2"></i> Pre-Event Accommodation</h3>
                <button onclick="closePreEventModal()" style="background: none; border: none; color: white; font-size: 20px; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="pre-event-modal-body">
                <p>Please select your accommodation preferences for your stay in Brasov before the main event:</p>

                <!-- Form Controls in a Row -->
                <div class="pre-event-modal-form-row">
                    <!-- Nights Selector -->
                    <div class="pre-event-modal-form-group">
                        <label for="nights">How many nights will you stay?</label>
                        <div class="nights-selector">
                            <div class="nights-selector-container">
                                <button type="button" class="nights-btn" @click="decrementNights()" :disabled="nights <= 1">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <div class="nights-display">
                                    <span class="nights-count" x-text="nights"></span>
                                    <span class="nights-label" x-text="nights === 1 ? 'Night' : 'Nights'"></span>
                                </div>
                                <button type="button" class="nights-btn" @click="incrementNights()">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Room Type Selector -->
                    <div class="pre-event-modal-form-group">
                        <label for="roomType">Room Type:</label>
                        <select id="roomType" x-model="roomType" class="form-select">
                            <option value="single">Single Room (1 person)</option>
                            <option value="double">Double Room (2 people)</option>
                        </select>
                    </div>
                </div>

                <!-- Stay Dates Information -->
                <div class="pre-event-modal-dates">
                    <div class="pre-event-modal-dates-row">
                        <span class="pre-event-modal-dates-label">Check-in Date:</span>
                        <span class="pre-event-modal-dates-value" x-text="calculateCheckinDate()"></span>
                    </div>
                    <div class="pre-event-modal-dates-row">
                        <span class="pre-event-modal-dates-label">Check-out Date:</span>
                        <span class="pre-event-modal-dates-value">21/08/2025</span>
                    </div>

                </div>


            </div>
            <div class="pre-event-modal-footer">
                <button onclick="closePreEventModal()" class="pre-event-modal-btn pre-event-modal-btn-secondary" style="width: 30%;">
                    CANCEL
                </button>
                <button @click="savePreferences()" class="pre-event-modal-btn pre-event-modal-btn-primary" style="width: 70%;">
                    <i class="fas fa-check me-2"></i> SAVE PREFERENCES
                </button>
            </div>
        </div>
    </div>
</div>
`);

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for Alpine.js');

    // Check if user already has preferences saved
    const hasPreferences = sessionStorage.getItem('preEventNights') && sessionStorage.getItem('preEventRoomType');
    console.log('User has saved preferences:', hasPreferences ? 'Yes' : 'No');

    // Check if Alpine.js is loaded
    if (typeof Alpine === 'undefined') {
        console.log('Alpine.js not loaded, loading it manually');

        // Load Alpine.js manually if it's not loaded
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js';
        script.defer = true;
        document.head.appendChild(script);

        script.onload = function() {
            console.log('Alpine.js loaded manually');

            // Initialize Alpine.js
            Alpine.start();

            // Only show modal if user doesn't have preferences saved
            if (!hasPreferences) {
                console.log('Showing modal because no preferences found');
                setTimeout(function() {
                    showPreEventModal();
                }, 500);
            } else {
                console.log('Not showing modal because preferences already exist');
            }
        };
    } else {
        // If Alpine.js is already loaded, only show modal if user doesn't have preferences
        if (!hasPreferences) {
            console.log('Showing modal because no preferences found');
            setTimeout(function() {
                showPreEventModal();
            }, 500);
        } else {
            console.log('Not showing modal because preferences already exist');
        }
    }

    // Update current settings display
    updateCurrentSettingsDisplay();

    // Apply preferences on page load
    applyPreEventRoomTypePreferences();

    // Apply animation delays to cards
    const cards = document.querySelectorAll('.accommodation-product-card');
    cards.forEach((card, index) => {
        card.style.setProperty('--card-index', index);
    });

    // Apply animation delays to stars
    const starContainers = document.querySelectorAll('.accommodation-product-stars');
    starContainers.forEach(container => {
        const stars = container.querySelectorAll('i');
        stars.forEach((star, index) => {
            star.style.setProperty('--i', index);
        });
    });

    // Add to Cart Confirmation Modal Functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    const confirmModal = document.getElementById('preEventCartConfirmModal');

    // Get saved preferences
    const getNights = function() {
        const savedNights = sessionStorage.getItem('preEventNights');
        return savedNights ? parseInt(savedNights) : 1;
    };

    const getRoomType = function() {
        const savedRoomType = sessionStorage.getItem('preEventRoomType');
        return savedRoomType || 'single';
    };

    // Add click event to all Add to Cart buttons
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');
            const productType = this.getAttribute('data-product-type');

            // Set hotel name in confirmation modal
            document.getElementById('preEventConfirmProductName').textContent = productName;

            // Get current nights value from session storage
            const nights = getNights();

            // Set nights in confirmation modal
            document.getElementById('preEventConfirmNights').textContent = nights + (nights === 1 ? ' night' : ' nights');

            // Calculate and display check-in date
            const calculateCheckinDate = function(nights) {
                // Base date is 21/08/2025 (check-out date)
                const baseDate = new Date(2025, 7, 21); // Month is 0-indexed, so 7 = August

                // Calculate check-in date by subtracting nights from base date
                const checkinDate = new Date(baseDate);
                checkinDate.setDate(baseDate.getDate() - nights);

                // Format the date as DD/MM/YYYY
                const day = String(checkinDate.getDate()).padStart(2, '0');
                const month = String(checkinDate.getMonth() + 1).padStart(2, '0');
                const year = checkinDate.getFullYear();

                return `${day}/${month}/${year}`;
            };

            // Set check-in date
            document.getElementById('preEventConfirmCheckinDate').textContent = calculateCheckinDate(nights);

            // Handle variation selection for variable products
            let variationId = null;
            let variationName = null;
            let isDoubleRoom = false;

            // Get user room type preference
            const userRoomType = getRoomType();
            const isUserDoubleRoom = userRoomType === 'double';

            if (productType === 'variable') {
                // Find the appropriate variation based on user preference
                let selectedVariation = null;

                if (isUserDoubleRoom) {
                    // If user prefers double room, find double room variation
                    selectedVariation = document.querySelector(`input[name="variation_${productId}"][data-is-double="true"]`);
                } else {
                    // Otherwise, get the selected variation
                    selectedVariation = document.querySelector(`input[name="variation_${productId}"]:checked`);
                }

                if (selectedVariation) {
                    variationId = selectedVariation.value;
                    variationName = selectedVariation.getAttribute('data-variation-name');
                    isDoubleRoom = selectedVariation.getAttribute('data-is-double') === 'true' || isUserDoubleRoom;

                    // Show room type in confirmation modal
                    document.getElementById('confirmRoomTypeRow').style.display = 'flex';
                    document.getElementById('confirmRoomType').textContent = variationName;
                } else {
                    // Hide room type if no variation selected
                    document.getElementById('confirmRoomTypeRow').style.display = 'none';
                }
            } else {
                // Hide room type for simple products
                document.getElementById('confirmRoomTypeRow').style.display = 'none';
            }

            // Hide double room notice (no longer needed)
            document.getElementById('confirmDoubleRoomRow').style.display = 'none';
            document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

            // Calculate and display the total price
            let productPrice = 0;

            if (productType === 'variable' && variationId) {
                // Get price from the selected variation
                const selectedVariation = document.querySelector(`input[value="${variationId}"]`);
                if (selectedVariation) {
                    productPrice = parseFloat(selectedVariation.getAttribute('data-price') || 0);
                }
            } else {
                // Get price from the button's data attribute
                productPrice = parseFloat(this.getAttribute('data-price') || 0);
            }

            // Calculate total price based on nights
            const totalPrice = productPrice * nights;

            // Format the price with currency symbol
            const formattedPrice = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'EUR',
                minimumFractionDigits: 2
            }).format(totalPrice);

            // Update the price display
            document.getElementById('confirmTotalPrice').textContent = formattedPrice;

            // Store data for add to cart action
            preEventProductId = productId;
            preEventVariationId = variationId;
            preEventIsDoubleRoom = false; // No special handling for double rooms
            preEventNights = nights;

            // Show confirmation modal
            confirmModal.style.display = 'block';
        });
    });
});
</script>

<style>
/* Pre-Event Accommodation Modal Styles - UX2025 */
.pre-event-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.pre-event-modal {
    background-color: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    display: flex;
    flex-direction: column;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pre-event-modal-header {
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    padding: 18px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Animated background elements for header */
.pre-event-modal-header::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

.pre-event-modal-header::after {
    content: '';
    position: absolute;
    bottom: -30px;
    left: 10%;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

.pre-event-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: white;
    letter-spacing: -0.3px;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
}

.pre-event-modal-header h3 i {
    color: white;
    margin-right: 10px;
    font-size: 20px;
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
}

@media (min-width: 768px) {
    .pre-event-modal-header {
        padding: 20px 25px;
    }

    .pre-event-modal-header h3 {
        font-size: 22px;
    }
}

.pre-event-modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 140px); /* Subtract header and footer height */
    scrollbar-width: thin;
    scrollbar-color: #36b1dc #f0f0f0;
}

.pre-event-modal-body::-webkit-scrollbar {
    width: 8px;
}

.pre-event-modal-body::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 10px;
}

.pre-event-modal-body::-webkit-scrollbar-thumb {
    background-color: #36b1dc;
    border-radius: 10px;
    border: 2px solid #f0f0f0;
}

@media (min-width: 768px) {
    .pre-event-modal-body {
        padding: 25px 30px;
    }
}

/* For smaller laptop screens */
@media (max-height: 768px) {
    .pre-event-modal {
        max-height: 95vh;
    }

    .pre-event-modal-body {
        max-height: calc(95vh - 130px);
        padding: 15px 20px;
    }

    .pre-event-modal-form-row {
        margin-bottom: 15px;
    }

    .pre-event-modal-form-group {
        margin-bottom: 15px;
    }
}

.pre-event-modal-form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.pre-event-modal-form-group {
    flex: 1;
    min-width: 250px;
    margin-bottom: 20px;
}

.pre-event-modal-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
}

.pre-event-modal-form-group label i {
    color: #36b1dc;
    margin-right: 6px;
}

.pre-event-modal-dates {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.pre-event-modal-dates-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

@media (min-width: 576px) {
    .pre-event-modal-dates {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
    }

    .pre-event-modal-dates-row {
        flex: 1;
        min-width: 200px;
        background-color: white;
        padding: 10px 15px;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        margin-bottom: 0;
    }

    .pre-event-modal-dates-note {
        flex-basis: 100%;
        margin-top: 10px !important;
    }
}

.pre-event-modal-dates-label {
    font-weight: 500;
    color: #555;
}

.pre-event-modal-dates-value {
    font-weight: 600;
    color: #333;
}

.pre-event-modal-dates-note {
    margin-top: 10px;
    font-size: 14px;
    color: #6c757d;
}

.pre-event-modal-notice {
    background: linear-gradient(135deg, #fffbeb 0%, #fff8e1 100%);
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    animation: notice-pulse 2s infinite alternate;
}

@keyframes notice-pulse {
    0% {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }
    100% {
        box-shadow: 0 8px 20px rgba(255, 193, 7, 0.2);
    }
}

.pre-event-modal-notice-content {
    width: 100%;
}

.pre-event-modal-notice-header {
    background-color: #ffc107;
    color: #fff;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 15px;
}

.pre-event-modal-notice-header i {
    color: white;
    font-size: 16px;
}

.pre-event-modal-notice-body {
    padding: 15px;
    font-size: 14px;
    color: #664d03;
}

.notice-primary {
    font-weight: 600;
    margin-bottom: 12px;
    font-size: 14px;
    color: #664d03;
}

.notice-calculation {
    background-color: white;
    border-radius: 8px;
    padding: 12px;
    margin: 12px 0;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.calculation-formula {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
    font-size: 16px;
}

.formula-item {
    background-color: #fff3cd;
    border-radius: 4px;
    padding: 3px 8px;
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.formula-operator {
    color: #664d03;
    font-weight: 400;
}

.formula-result {
    background-color: #ffc107;
    color: white;
    border-radius: 4px;
    padding: 3px 8px;
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.calculation-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.notice-warning {
    background-color: rgba(255, 255, 255, 0.7);
    border-left: 3px solid #ffc107;
    padding: 10px;
    margin-bottom: 0;
    font-size: 13px;
    color: #664d03;
    border-radius: 0 4px 4px 0;
}

@media (min-width: 768px) {
    .pre-event-modal-notice-header {
        padding: 15px 20px;
        font-size: 16px;
    }

    .pre-event-modal-notice-body {
        padding: 20px;
    }

    .notice-calculation {
        margin: 15px 0;
        padding: 15px;
    }

    .calculation-formula {
        font-size: 18px;
    }
}

.pre-event-modal-footer {
    padding: 15px 20px;
    background-color: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid #e9ecef;
    position: relative;
    z-index: 1;
}

/* Add subtle gradient to footer */
.pre-event-modal-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
    z-index: -1;
}

@media (min-width: 768px) {
    .pre-event-modal-footer {
        padding: 20px;
    }

    .pre-event-modal-btn {
        padding: 10px 20px;
        min-width: 120px;
    }
}

/* For smaller laptop screens */
@media (max-height: 768px) {
    .pre-event-modal-footer {
        padding: 12px 15px;
    }

    .pre-event-modal-btn {
        padding: 8px 12px;
    }
}

.pre-event-modal-btn {
    padding: 12px 16px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

/* Add ripple effect on button click */
.pre-event-modal-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.pre-event-modal-btn:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

.pre-event-modal-btn i {
    color: white !important;
    margin-right: 8px;
}

.pre-event-modal-btn-primary {
    background: linear-gradient(135deg, #36b1dc, #2980b9);
    color: white;
    box-shadow: 0 4px 10px rgba(54, 177, 220, 0.2);
}

.pre-event-modal-btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1c6ea4);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(54, 177, 220, 0.3);
}

.pre-event-modal-btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 10px rgba(108, 117, 125, 0.2);
}

.pre-event-modal-btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #343a40);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(108, 117, 125, 0.3);
}

.accommodation-settings-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    margin-top: 10px;
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
    transition: all 0.2s ease;
}

.accommodation-settings-button:hover {
    background-color: #e9ecef;
}

/* Variation radio buttons */
.accommodation-variations-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.accommodation-variation-radio-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
    width: 100%;
}

.accommodation-variation-radio-label:hover {
    background-color: #f8f9fa;
}

.accommodation-variation-radio-label input[type="radio"] {
    margin-right: 10px;
}

.accommodation-product-stars {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: #ffc107;
}

.accommodation-product-stars i {
    margin-right: 3px;
}

.accommodation-product-star-description {
    font-size: 14px;
    color: #6c757d;
    margin-left: 8px;
}

/* Features List with Checkmarks */
.accommodation-product-features {
    margin-bottom: 15px;
}

.accommodation-features-list {
    list-style: none;
    padding: 0;
    margin: 2px !important;
}

.accommodation-features-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    font-size: 14px;
    color: #495057;
    line-height: 1.4;
}

.accommodation-features-list li i {
    color: #36b1dc;
    margin-right: 8px;
    font-size: 16px;
    flex-shrink: 0;
    margin-top: 2px;
}

.accommodation-product-short-description {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
    max-height: 80px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

/* Disabled variation styles */
.variation-disabled {
    opacity: 0.5;
    pointer-events: none;
}

.variation-disabled input[type="radio"] {
    opacity: 0.5;
}

/* Modern Nights Selector - 2025UX Trends */
.nights-label-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 500;
    color: #333;
}

.nights-icon {
    color: #36b1dc;
    font-size: 16px;
}

.nights-selector {
    max-width: 383px;
    margin: 0px auto;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.nights-selector:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nights-selector-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    padding: 8px 12px;
}

.nights-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: white;
    color: #495057;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nights-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.nights-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nights-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.nights-btn-minus {
    background-color: #f8d7da;
    color: #dc3545;
}

.nights-btn-plus {
    background-color: #d1e7dd;
    color: #198754;
}

.nights-display {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.nights-display span:first-child {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.nights-value-container {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 5px;
}

.nights-count {
    font-size: 24px;
    font-weight: 700;
    color: #36b1dc;
    display: block;
    text-align: center;
}

.nights-label {
    font-size: 14px;
    color: #6c757d;
    display: block;
    text-align: center;
}

.nights-progress-container {
    height: 6px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
    overflow: hidden;
}

.nights-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #36b1dc, #90caf9);
    border-radius: 3px;
    transition: width 0.3s ease;
}

@media (min-width: 768px) {
    .nights-btn {
        width: 48px;
        height: 48px;
    }

    .nights-count {
        font-size: 28px;
    }
}

/* Loading indicator for mini-cart */
.dashboard-mini-cart.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

/* WOW Effects for Accommodation Cards */
.accommodation-product-card {
    transition: all 0.3s ease;
    transform: translateY(0);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.accommodation-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #36b1dc, #90caf9);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    z-index: 1;
}

.accommodation-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.accommodation-product-card:hover::before {
    transform: scaleX(1);
}

.accommodation-product-image {
    overflow: hidden;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.accommodation-product-image img {
    transition: transform 0.5s ease;
}

.accommodation-product-card:hover .accommodation-product-image img {
    transform: scale(1.05);
}

.accommodation-product-button {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.accommodation-product-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
    z-index: -1;
}

.accommodation-product-button:hover::after {
    left: 100%;
}

.accommodation-settings-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.accommodation-settings-button:hover {
    background-color: #f1f1f1;
    transform: translateY(-2px);
}

/* Pulse animation for stars */
@keyframes star-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.accommodation-product-stars i {
    transition: all 0.3s ease;
}

.accommodation-product-card:hover .accommodation-product-stars i {
    color: #ffc107;
    animation: star-pulse 1s infinite;
    animation-delay: calc(0.1s * var(--i));
}

/* Fade-in animation for cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.accommodation-product-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    animation-delay: calc(0.1s * var(--card-index, 0));
}

.dashboard-mini-cart.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #36b1dc;
    border-top-color: transparent;
    border-radius: 50%;
    animation: mini-cart-spinner 0.8s linear infinite;
}

@keyframes mini-cart-spinner {
    to {
        transform: rotate(360deg);
    }
}

/* UX2025 Animations and Responsive Styles */
@keyframes pulse {
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.05); opacity: 0.9; }
    100% { transform: scale(1); opacity: 0.7; }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes glow {
    0% { opacity: 0.3; }
    50% { opacity: 0.7; }
    100% { opacity: 0.3; }
}

/* Responsive styles for laptops with lower resolutions */
@media (max-height: 768px) {
    .pre-event-modal {
        max-height: 90vh;
        width: 85%;
    }

    .pre-event-modal-body {
        padding: 15px;
        max-height: calc(90vh - 120px);
    }

    .pre-event-modal-form-group {
        margin-bottom: 15px;
    }

    .nights-selector-container {
        padding: 5px !important;
    }

    .nights-btn {
        width: 38px !important;
        height: 38px !important;
    }

    .nights-count {
        font-size: 24px !important;
    }

    .nights-label {
        font-size: 16px !important;
    }

    .pre-event-modal-dates {
        padding: 15px !important;
        margin-top: 15px !important;
    }

    .pre-event-modal-footer {
        padding: 10px 15px;
    }

    .pre-event-modal-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
}
</style>
