/**
 * Fix for mini-cart modal on regalia shop page
 */
(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        // Fix for multiple modal backdrops
        const fixModalBackdrops = function() {
            // Remove duplicate modal backdrops
            const backdrops = document.querySelectorAll('.modal-backdrop');
            if (backdrops.length > 1) {
                for (let i = 1; i < backdrops.length; i++) {
                    backdrops[i].remove();
                }
            }
        };

        // Fix for Alpine.js modals
        const fixAlpineModals = function() {
            // Ensure Alpine.js modals have proper z-index
            const alpineModals = document.querySelectorAll('.lci-modal-backdrop');
            alpineModals.forEach(function(modal) {
                modal.style.zIndex = '9999';
            });
        };

        // Fix for mini-cart modal
        const fixMiniCartModal = function() {
            // Ensure mini-cart modal is properly initialized
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (miniCartContainer && !miniCartContainer.__x) {
                // If Alpine.js is loaded but not initialized on the mini-cart
                if (window.Alpine) {
                    try {
                        // Try to initialize Alpine.js on the mini-cart
                        window.Alpine.initTree(miniCartContainer);
                        console.log('Mini-cart Alpine.js component initialized');
                    } catch (error) {
                        console.error('Error initializing mini-cart Alpine.js component:', error);
                    }
                }
            }
        };

        // Fix for add-to-cart confirmation modal
        const fixAddToCartModal = function() {
            // Ensure add-to-cart confirmation modal is properly initialized
            const addToCartConfirm = document.getElementById('add-to-cart-confirm');
            if (addToCartConfirm && !addToCartConfirm.__x) {
                // If Alpine.js is loaded but not initialized on the add-to-cart confirmation
                if (window.Alpine) {
                    try {
                        // Try to initialize Alpine.js on the add-to-cart confirmation
                        window.Alpine.initTree(addToCartConfirm);
                        console.log('Add-to-cart confirmation Alpine.js component initialized');
                    } catch (error) {
                        console.error('Error initializing add-to-cart confirmation Alpine.js component:', error);
                    }
                }
            }
        };

        // Ensure lci_ajax_object is defined
        if (typeof lci_ajax_object === 'undefined') {
            window.lci_ajax_object = {
                ajax_url: window.ajaxurl || '/wp-admin/admin-ajax.php',
                nonce: ''
            };
            console.log('lci_ajax_object defined with fallback values');
        }

        // Apply fixes
        fixModalBackdrops();
        fixAlpineModals();
        fixMiniCartModal();
        fixAddToCartModal();

        // Apply fixes again after a short delay to catch any late initializations
        setTimeout(function() {
            fixModalBackdrops();
            fixAlpineModals();
            fixMiniCartModal();
            fixAddToCartModal();
        }, 1000);

        // Listen for modal events
        document.addEventListener('show.bs.modal', fixModalBackdrops);
        document.addEventListener('shown.bs.modal', fixModalBackdrops);
        document.addEventListener('hide.bs.modal', fixModalBackdrops);
        document.addEventListener('hidden.bs.modal', fixModalBackdrops);

        // Listen for Alpine.js events
        document.addEventListener('alpine:initialized', function() {
            fixMiniCartModal();
            fixAddToCartModal();
        });

        // Fix z-index issues with modals
        const style = document.createElement('style');
        style.textContent = `
            .modal-backdrop { z-index: 1050 !important; }
            .modal { z-index: 1055 !important; }
            .lci-modal-backdrop { z-index: 1060 !important; }
            body.modal-open { overflow: hidden !important; padding-right: 0 !important; }
            .modal-backdrop + .modal-backdrop { display: none !important; }
            body > .modal-backdrop:nth-child(n+2) { display: none !important; }
        `;
        document.head.appendChild(style);
    });
})();
