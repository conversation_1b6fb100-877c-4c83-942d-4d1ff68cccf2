<?php
/**
 * LCI Database Inspector
 *
 * Provides tools to inspect database tables used by the plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class LCI_DB_Inspector {
    /**
     * Initialize the class
     */
    public static function init() {
        add_action('admin_menu', array(__CLASS__, 'add_menu_page'), 30);
        add_action('wp_ajax_lci_get_table_data', array(__CLASS__, 'ajax_get_table_data'));
        add_action('wp_ajax_lci_get_table_structure', array(__CLASS__, 'ajax_get_table_structure'));
        add_action('wp_ajax_lci_get_tables_list', array(__CLASS__, 'ajax_get_tables_list'));
    }

    /**
     * Add menu page
     */
    public static function add_menu_page() {
        add_submenu_page(
            'lci-dashboard',
            'Database Inspector',
            'DB Inspector',
            'manage_options',
            'lci-db-inspector',
            array(__CLASS__, 'render_page')
        );
    }

    /**
     * Render the page
     */
    public static function render_page() {
        include_once LCI2025_PATH . 'templates/admin/db-inspector.php';
    }

    /**
     * Get list of plugin tables
     */
    public static function get_plugin_tables() {
        global $wpdb;

        $tables = array(
            array(
                'name' => $wpdb->prefix . 'lci2025_participants',
                'description' => 'Main participants table'
            ),
            array(
                'name' => $wpdb->prefix . 'lci_tour_main_pretour',
                'description' => 'Main Pretour participants'
            ),
            array(
                'name' => $wpdb->prefix . 'lci_tour_legends_wildlife',
                'description' => 'Legends & Wildlife participants'
            ),
            array(
                'name' => $wpdb->prefix . 'lci_tour_royal_elegance',
                'description' => 'Royal Elegance participants'
            ),
            array(
                'name' => $wpdb->prefix . 'lci_tour_brasov_highlights',
                'description' => 'Brasov Highlights participants'
            )
        );

        return $tables;
    }

    /**
     * AJAX handler for getting tables list
     */
    public static function ajax_get_tables_list() {
        // Check nonce
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to access this data'));
        }

        $tables = self::get_plugin_tables();

        wp_send_json_success(array(
            'tables' => $tables
        ));
    }

    /**
     * AJAX handler for getting table structure
     */
    public static function ajax_get_table_structure() {
        global $wpdb;

        // Check nonce
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to access this data'));
        }

        // Get table name
        if (!isset($_REQUEST['table']) || empty($_REQUEST['table'])) {
            wp_send_json_error(array('message' => 'Table name is required'));
        }

        $table_name = sanitize_text_field($_REQUEST['table']);

        // Validate table name (must be one of our plugin tables)
        $valid_tables = array_map(function($table) {
            return $table['name'];
        }, self::get_plugin_tables());

        if (!in_array($table_name, $valid_tables)) {
            wp_send_json_error(array('message' => 'Invalid table name'));
        }

        // Get table structure
        $columns = $wpdb->get_results("DESCRIBE $table_name");

        if ($columns === null) {
            wp_send_json_error(array('message' => 'Table does not exist'));
        }

        wp_send_json_success(array(
            'columns' => $columns
        ));
    }

    /**
     * AJAX handler for getting table data
     */
    public static function ajax_get_table_data() {
        global $wpdb;

        // Check nonce
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to access this data'));
        }

        // Get table name
        if (!isset($_REQUEST['table']) || empty($_REQUEST['table'])) {
            wp_send_json_error(array('message' => 'Table name is required'));
        }

        $table_name = sanitize_text_field($_REQUEST['table']);

        // Validate table name (must be one of our plugin tables)
        $valid_tables = array_map(function($table) {
            return $table['name'];
        }, self::get_plugin_tables());

        if (!in_array($table_name, $valid_tables)) {
            wp_send_json_error(array('message' => 'Invalid table name'));
        }

        // Get search parameters
        $search = isset($_REQUEST['search']) ? sanitize_text_field($_REQUEST['search']) : '';
        $page = isset($_REQUEST['page']) ? intval($_REQUEST['page']) : 1;
        $per_page = isset($_REQUEST['per_page']) ? intval($_REQUEST['per_page']) : 20;

        // Calculate offset
        $offset = ($page - 1) * $per_page;

        // Build query
        $query = "SELECT * FROM $table_name";
        $count_query = "SELECT COUNT(*) FROM $table_name";

        // Add search condition if provided
        if (!empty($search)) {
            // Get table columns
            $columns = $wpdb->get_results("DESCRIBE $table_name", ARRAY_A);
            $column_names = array_column($columns, 'Field');

            // Build search conditions
            $search_conditions = array();
            foreach ($column_names as $column) {
                $search_conditions[] = "$column LIKE '%$search%'";
            }

            $search_sql = implode(' OR ', $search_conditions);
            $query .= " WHERE $search_sql";
            $count_query .= " WHERE $search_sql";
        }

        // Add pagination
        $query .= " LIMIT $per_page OFFSET $offset";

        // Get data
        $data = $wpdb->get_results($query, ARRAY_A);
        $total = $wpdb->get_var($count_query);

        wp_send_json_success(array(
            'data' => $data,
            'total' => intval($total),
            'page' => $page,
            'per_page' => $per_page,
            'total_pages' => ceil($total / $per_page)
        ));
    }
}

// Initialize the class
LCI_DB_Inspector::init();
