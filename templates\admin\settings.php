<div class="wrap lci-admin-wrap">
    <h1 class="text-3xl font-bold mb-6">Settings</h1>

    <div class="bg-white rounded-xl shadow-neumorph p-6 mb-6">
        <h2 class="text-xl font-medium text-gray-800 mb-4">General Settings</h2>

        <form method="post" action="options.php">
            <?php settings_fields('lci2025_settings'); ?>
            <?php do_settings_sections('lci2025_settings'); ?>

            <div class="grid grid-cols-1 gap-6 max-w-xl">
                <div>
                    <label class="form-label">Plugin Version</label>
                    <input type="text" class="form-input bg-gray-100" value="<?php echo esc_attr(LCI2025_VERSION); ?>" readonly>
                </div>

                <div>
                    <label class="form-label">Database Version</label>
                    <input type="text" class="form-input bg-gray-100" value="<?php echo esc_attr(get_option('lci2025_db_version', '0')); ?>" readonly>
                </div>

                <div>
                    <label class="form-label">Registration ID Prefix</label>
                    <input type="text" name="lci2025_reg_id_prefix" class="form-input" value="<?php echo esc_attr(get_option('lci2025_reg_id_prefix', 'LCI')); ?>">
                    <p class="text-sm text-gray-500 mt-1">Prefix for unique registration IDs (e.g., LCI-25-00001)</p>
                </div>

                <div>
                    <label class="form-label">Enable Debug Mode</label>
                    <div class="mt-1">
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="lci2025_debug_mode" value="1" <?php checked(get_option('lci2025_debug_mode', '0'), '1'); ?> class="rounded text-primary focus:ring-primary">
                            <span class="ml-2">Enable debug logging</span>
                        </label>
                        <p class="text-sm text-gray-500 mt-1">Logs detailed information about plugin operations for troubleshooting</p>
                        <p class="text-sm text-blue-600 mt-1">
                            <a href="<?php echo admin_url('admin.php?page=lci-debug-logs'); ?>" class="underline hover:text-blue-800">
                                View Debug Logs
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <div class="mt-6">
                <?php submit_button('Save Settings', 'btn btn-primary', 'submit', false); ?>
            </div>
        </form>
    </div>

    <div class="bg-white rounded-xl shadow-neumorph p-6">
        <h2 class="text-xl font-medium text-gray-800 mb-4">Tools</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Export Participants</h3>
                <p class="text-gray-600 mb-4">Export all participant data to a CSV file for external use.</p>
                <a href="<?php echo admin_url('admin.php?page=lci-participants&action=export'); ?>" class="btn btn-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Export CSV
                </a>
            </div>

            <div class="p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Repair Database</h3>
                <p class="text-gray-600 mb-4">Repair the database tables if you encounter issues with data integrity.</p>
                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                    <a href="<?php echo admin_url('admin.php?page=lci-settings&action=repair'); ?>" class="btn btn-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Repair Database
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=lci-settings&action=fix-duplicates'); ?>" class="btn btn-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                        </svg>
                        Fix Duplicate IDs
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
