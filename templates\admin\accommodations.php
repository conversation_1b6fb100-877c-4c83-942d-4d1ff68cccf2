<?php
// Get accommodation statistics
$statistics = LCI_Accommodations::get_accommodation_statistics();
$statistics_json = json_encode($statistics);

// Get accommodation categories
$categories = LCI_Accommodations::get_accommodation_categories();
$categories_json = json_encode($categories);
?>
<script type="text/javascript">
    // Make statistics and categories available to Alpine.js
    var lciAccommodationStats = <?php echo $statistics_json; ?>;
    var lciAccommodationCategories = <?php echo $categories_json; ?>;
    // Accommodations template - Version 1.5
</script>

<div class="wrap lci-admin-wrap">
    <div x-data="accommodationsManager">
        <!-- Sync Modal -->
        <div x-show="showSyncModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center" x-transition>
            <div class="bg-white rounded-xl shadow-lg p-6 max-w-lg w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Accommodation Data Sync</h2>
                    <button @click="showSyncModal = false" class="text-gray-500 hover:text-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div class="mb-6">
                    <p class="text-gray-600 mb-4">This will synchronize accommodation data from WooCommerce orders. This process may take a few moments.</p>

                    <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-blue-700">
                                    You can sync all accommodation categories or just the currently selected category.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button @click="showSyncModal = false" class="btn btn-secondary">Cancel</button>
                    <button @click="syncCategory()" class="btn btn-primary flex items-center" :disabled="isSyncing">
                        <svg x-show="isSyncing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span x-text="isSyncing ? 'Syncing...' : 'Sync Current Category'"></span>
                    </button>
                    <button @click="syncAllCategories()" class="btn btn-primary flex items-center" :disabled="isSyncing">
                        <svg x-show="isSyncing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span x-text="isSyncing ? 'Syncing...' : 'Sync All Categories'"></span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Page Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">Accommodation Management</h1>
            <button @click="showSyncModal = true" class="btn btn-primary flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Sync Accommodations
            </button>
        </div>

        <!-- Category Tabs -->
        <div class="bg-white rounded-xl shadow-neumorph p-4 mb-6">
            <div class="flex flex-wrap gap-2">
                <template x-for="category in categories" :key="category.id">
                    <button
                        @click="setActiveCategory(category.id)"
                        class="px-4 py-2 rounded-lg transition-all duration-200"
                        :class="activeCategory === category.id ? 'bg-primary text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'"
                    >
                        <span x-text="category.name"></span>
                        <span class="ml-2 px-2 py-0.5 rounded-full text-xs"
                            :class="activeCategory === category.id ? 'bg-white text-primary' : 'bg-gray-200 text-gray-700'"
                            x-text="getCategoryCount(category.id)"
                        ></span>
                    </button>
                </template>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-xl shadow-neumorph p-4">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Total Accommodations</h3>
                <div class="flex justify-between items-center">
                    <span class="text-3xl font-bold text-primary" x-text="statistics.total"></span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-neumorph p-4">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Hotels</h3>
                <div class="flex justify-between items-center">
                    <span class="text-3xl font-bold text-primary" x-text="statistics.by_hotel ? statistics.by_hotel.length : 0"></span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-neumorph p-4">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Room Types</h3>
                <div class="flex justify-between items-center">
                    <span class="text-3xl font-bold text-primary" x-text="statistics.by_room_type ? statistics.by_room_type.length : 0"></span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                    </svg>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-neumorph p-4">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Data Source</h3>
                <div class="flex justify-between items-center">
                    <span
                        class="text-xl font-bold"
                        :class="{
                            'text-blue-600': dataSource === 'database',
                            'text-green-600': dataSource === 'woocommerce'
                        }"
                        x-text="dataSource === 'database' ? 'Database' : 'WooCommerce'"
                    ></span>
                    <button
                        @click="toggleDataSource()"
                        class="px-3 py-1 rounded text-sm font-medium bg-gray-100 hover:bg-gray-200 transition-colors"
                    >
                        Switch
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading state -->
        <div x-show="isLoading" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
            <div class="spinner"></div>
            <span class="loading-text">Loading accommodation data...</span>
        </div>

        <!-- Debug Panel -->
        <div x-show="accommodations.length === 0" class="bg-white rounded-xl shadow-neumorph p-6 mb-6">
            <h2 class="text-xl font-medium text-gray-800 mb-4">Debug Information</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="mb-2"><strong>Active Category:</strong> <span x-text="activeCategory"></span></p>
                <p class="mb-2"><strong>Data Source:</strong> <span x-text="dataSource"></span></p>
                <p class="mb-2"><strong>Accommodations Count:</strong> <span x-text="accommodations.length"></span></p>
                <p class="mb-2"><strong>Hotels Count:</strong> <span x-text="hotels.length"></span></p>
                <p class="mb-2"><strong>Room Types Count:</strong> <span x-text="roomTypes.length"></span></p>
                <p class="mb-2"><strong>Filtered Accommodations Count:</strong> <span x-text="filteredAccommodations.length"></span></p>
                <p class="mb-2"><strong>Statistics:</strong> <span x-text="JSON.stringify(statistics)"></span></p>
            </div>
            <div class="mt-4">
                <button @click="fetchAccommodations()" class="btn btn-primary">
                    Retry Fetch
                </button>
            </div>
        </div>

        <!-- Main content -->
        <div x-show="!isLoading" class="bg-white rounded-xl shadow-neumorph p-6">
            <div class="flex flex-col space-y-4 mb-6">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-medium text-gray-800" x-text="getActiveCategoryName()"></h2>

                    <!-- Data source indicator -->
                    <div class="flex items-center">
                        <span class="text-sm text-gray-600 mr-2">Data source:</span>
                        <span
                            class="px-2 py-1 rounded text-xs font-medium"
                            :class="{
                                'bg-blue-100 text-blue-800': dataSource === 'database',
                                'bg-green-100 text-green-800': dataSource === 'woocommerce'
                            }"
                            x-text="dataSource === 'database' ? 'Database' : 'WooCommerce'"
                        ></span>

                        <!-- Toggle data source button -->
                        <button
                            @click="toggleDataSource()"
                            class="ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                            :class="{'bg-blue-100 hover:bg-blue-200': dataSource === 'woocommerce'}"
                        >
                            <span x-text="dataSource === 'woocommerce' ? 'Show Database Data' : 'Show WooCommerce Data'"></span>
                        </button>
                    </div>
                </div>

                <!-- Data source notification for WooCommerce data -->
                <div x-show="dataSource === 'woocommerce'" class="bg-blue-50 border-l-4 border-blue-400 p-3">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                Showing data from WooCommerce orders. Click "Sync Now" to save this data to the database.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="flex flex-col md:flex-row md:items-end space-y-4 md:space-y-0 md:space-x-4">
                    <div class="flex-1">
                        <label class="form-label">Search</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input
                                type="text"
                                x-model="searchQuery"
                                @input="filterAccommodations()"
                                placeholder="Search accommodations..."
                                class="form-input pl-10 w-full"
                            >
                        </div>
                    </div>

                    <div class="flex-1">
                        <label class="form-label">Hotel</label>
                        <select x-model="selectedHotel" @change="filterAccommodations()" class="form-input w-full">
                            <option value="">All Hotels</option>
                            <template x-for="hotel in hotels" :key="hotel.id">
                                <option :value="hotel.id" x-text="hotel.name"></option>
                            </template>
                        </select>
                    </div>

                    <button @click="filterAccommodations()" class="btn btn-primary">
                        Apply Filters
                    </button>

                    <button @click="resetFilters()" class="btn btn-secondary">
                        Reset
                    </button>
                </div>
            </div>

            <!-- Accommodations Table -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="text-left bg-gray-50">
                            <th class="p-3 font-medium text-gray-600">Participant</th>
                            <th class="p-3 font-medium text-gray-600">Product</th>
                            <th class="p-3 font-medium text-gray-600">Variation</th>
                            <th class="p-3 font-medium text-gray-600">Order Date</th>
                            <th class="p-3 font-medium text-gray-600">Payment Status</th>
                            <th class="p-3 font-medium text-gray-600">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="(accommodation, index) in paginatedAccommodations" :key="index">
                            <tr class="border-t border-gray-200 hover:bg-gray-50">
                                <td class="p-3">
                                    <div class="font-medium" x-text="getParticipantName(accommodation)"></div>
                                    <div class="text-sm text-gray-500" x-text="accommodation.unique_reg_id || 'No ID'"></div>
                                </td>
                                <td class="p-3" x-text="accommodation.product_name"></td>
                                <td class="p-3" x-text="accommodation.variation_name || 'N/A'"></td>
                                <td class="p-3" x-text="formatDate(accommodation.order_date)"></td>
                                <td class="p-3">
                                    <span
                                        class="px-2 py-1 rounded text-xs font-medium"
                                        :class="{
                                            'bg-green-100 text-green-800': accommodation.payment_status === 'completed',
                                            'bg-yellow-100 text-yellow-800': accommodation.payment_status === 'processing',
                                            'bg-red-100 text-red-800': accommodation.payment_status === 'on-hold',
                                            'bg-gray-100 text-gray-800': !['completed', 'processing', 'on-hold'].includes(accommodation.payment_status)
                                        }"
                                        x-text="accommodation.payment_status"
                                    ></span>
                                </td>
                                <td class="p-3">
                                    <a :href="'admin.php?page=lci-participants&view=participant&id=' + accommodation.participant_id" class="btn btn-sm btn-secondary" x-show="accommodation.participant_id > 0">
                                        View Participant
                                    </a>
                                    <a :href="'post.php?post=' + accommodation.order_id + '&action=edit'" class="btn btn-sm btn-secondary ml-2">
                                        View Order
                                    </a>
                                </td>
                            </tr>
                        </template>
                        <tr x-show="filteredAccommodations.length === 0">
                            <td colspan="6" class="p-3 text-center text-gray-500">
                                No accommodations found.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div x-show="totalPages > 1" class="mt-4 flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    Showing <span x-text="(currentPage - 1) * perPage + 1"></span>
                    to <span x-text="Math.min(currentPage * perPage, filteredAccommodations.length)"></span>
                    of <span x-text="filteredAccommodations.length"></span> accommodations
                </div>

                <div class="flex space-x-2">
                    <button
                        @click="prevPage()"
                        :disabled="currentPage === 1"
                        class="btn btn-sm btn-secondary"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                    >
                        Previous
                    </button>

                    <button
                        @click="nextPage()"
                        :disabled="currentPage === totalPages"
                        class="btn btn-sm btn-secondary"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
                    >
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
