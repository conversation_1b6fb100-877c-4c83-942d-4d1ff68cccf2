<?php
/**
 * Councilor Warning Modal Template
 *
 * Displays a warning to councilors about potential schedule conflicts with tours
 */
?>

<style>
    .councilor-warning-animation {
        animation: pulse-scale 0.5s ease-out;
    }

    @keyframes pulse-scale {
        0% { transform: scale(0.5); opacity: 0; }
        50% { transform: scale(1.2); opacity: 1; }
        100% { transform: scale(1); opacity: 1; }
    }

    .councilor-warning-text {
        animation: fade-in-up 0.5s ease-out 0.2s both;
    }

    @keyframes fade-in-up {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
    }
</style>

<div
    x-data="{
        open: false,

        init() {
            // Check if the modal should be shown automatically on page load
            if (window.shouldShowCouncilorWarning) {
                this.showWarning();
            }

            // Listen for events to show the warning
            window.addEventListener('show-councilor-warning', () => {
                this.showWarning();
            });
        },

        showWarning() {
            this.open = true;

            // Don't add modal-open class to body to prevent scroll locking
            // document.body.classList.add('modal-open');

            // Store the current scroll position but don't lock scrolling
            this.scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

            // Don't add our own scroll management to prevent scroll locking
            // document.body.style.overflow = 'hidden';
            // document.body.style.position = 'fixed';
            // document.body.style.top = `-${this.scrollPosition}px`;
            // document.body.style.width = '100%';
        },

        closeWarning() {
            this.open = false;

            // Remove modal-open class from body (commented out to match showWarning)
            // document.body.classList.remove('modal-open');

            // No need to restore scroll position since we're not locking it anymore
            // const scrollY = document.body.style.top;
            // document.body.style.removeProperty('overflow');
            // document.body.style.removeProperty('position');
            // document.body.style.removeProperty('top');
            // document.body.style.removeProperty('width');
            // window.scrollTo(0, parseInt(scrollY || '0') * -1);

            // Just make sure any scroll-preventing styles are removed
            document.body.style.overflow = 'auto';
            document.body.style.paddingRight = '0';
        }
    }"
    x-cloak
    id="councilor-warning-modal"
>
    <div
        x-show="open"
        x-transition:enter="fade-in"
        x-transition:leave="fade-out"
        class="lci-modal-backdrop"
        @click.self="closeWarning"
        style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center; overflow-y: auto; padding: 20px 0; pointer-events: auto;"
    >
        <div
            class="lci-modal"
            x-transition:enter="slide-in"
            x-transition:leave="slide-out"
            style="background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.12); max-width: 500px; width: 95%; position: relative;"
            @click.away="closeWarning"
        >
            <button @click="closeWarning" class="lci-modal-close" style="position: absolute; right: 1rem; top: 1rem; background: none; border: none; font-size: 1.25rem; color: #36b1dc; transition: all 0.2s ease; z-index: 10;">
                <i class="fas fa-times"></i>
            </button>

            <div class="lci-modal-body text-center" style="padding: 2.5rem 1.5rem 1.5rem;">
                <div class="councilor-warning-animation" style="margin-bottom: 1.5rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #fab33a;"></i>
                </div>

                <div class="councilor-warning-text">
                    <h4 style="color: #36b1dc; font-weight: 600; margin-bottom: 0.75rem;">Important Notice for Councilors</h4>
                    <p style="color: #333; font-size: 1rem; margin-bottom: 1rem;">As a councilor, please be aware that some tours may conflict with scheduled council meetings.</p>
                    <p style="color: #333; font-size: 1rem; margin-bottom: 1rem;">Please check the meeting schedule before booking any tours to avoid conflicts.</p>
                </div>
            </div>

            <div class="lci-modal-footer" style="padding: 1rem 1.5rem 1.5rem; display: flex; justify-content: center;">
                <button @click="closeWarning" class="lci-btn" style="padding: 0.75rem 2rem; border-radius: 6px; border: none; background-color: #36b1dc; color: #fff !important; font-weight: 500; transition: all 0.2s ease;">
                    I Understand
                </button>
            </div>
        </div>
    </div>
</div>
