/**
 * LCI 2025 Transfers Admin JavaScript
 * Modern 2025 UX/UI with AlpineJS
 */

document.addEventListener('alpine:init', () => {
    Alpine.directive('init-transfers', (el) => {
        setTimeout(() => {
            const component = Alpine.getRoot(el);
            component.loadBookings();
        }, 100);
    });

    Alpine.magic('transfersAdmin', () => {
        return {
            loadBookings() {
                return window.loadBookings.apply(this);
            },
            updateStats(newStats) {
                return window.updateStats.apply(this, [newStats]);
            },
            applyFilters() {
                return window.applyFilters.apply(this);
            },
            resetFilters() {
                return window.resetFilters.apply(this);
            },
            updatePaginatedBookings() {
                return window.updatePaginatedBookings.apply(this);
            },
            prevPage() {
                return window.prevPage.apply(this);
            },
            nextPage() {
                return window.nextPage.apply(this);
            },
            goToPage(page) {
                return window.goToPage.apply(this, [page]);
            },
            openStatusModal(booking) {
                return window.openStatusModal.apply(this, [booking]);
            },
            closeStatusModal() {
                return window.closeStatusModal.apply(this);
            },
            openBookingDetails(booking) {
                return window.openBookingDetails.apply(this, [booking]);
            },
            closeDetailsModal() {
                return window.closeDetailsModal.apply(this);
            },
            openDeleteModal(booking) {
                return window.openDeleteModal.apply(this, [booking]);
            },
            closeDeleteModal() {
                return window.closeDeleteModal.apply(this);
            },
            updateBookingStatus() {
                return window.updateBookingStatus.apply(this);
            },
            deleteBooking() {
                return window.deleteBooking.apply(this);
            },
            formatDate(dateString) {
                return window.formatDate.apply(this, [dateString]);
            },
            getTransferTypeLabel(type) {
                return window.getTransferTypeLabel.apply(this, [type]);
            },
            showNotification(message, type) {
                return window.showNotification.apply(this, [message, type]);
            }
        };
    });
});

// Define all methods on the window object
window.loadBookings = function() {
    const component = this;
    component.isLoading = true;

    // AJAX call to get bookings
    fetch(window.lciAdmin.ajaxUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'lci_get_transfer_bookings',
            nonce: window.lciAdmin.nonce
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            component.bookings = data.data.bookings;
            component.applyFilters();
            component.updateStats(data.data.stats);
        } else {
            console.error('Error loading bookings:', data.data.message);
        }
    })
    .catch(error => {
        console.error('Error loading bookings:', error);
    })
    .finally(() => {
        component.isLoading = false;
    });
};

// Update stats
window.updateStats = function(newStats) {
    if (newStats) {
        this.stats = newStats;
    } else {
        // Calculate stats from filtered bookings
        this.stats.total = this.bookings.length;
        this.stats.pending = this.bookings.filter(b => b.status === 'pending').length;
        this.stats.confirmed = this.bookings.filter(b => b.status === 'confirmed').length;
        this.stats.completed = this.bookings.filter(b => b.status === 'completed').length;
        this.stats.cancelled = this.bookings.filter(b => b.status === 'cancelled').length;
    }
};

// Apply filters
window.applyFilters = function() {
    this.filteredBookings = this.bookings.filter(booking => {
        // Status filter
        if (this.filters.status && booking.status !== this.filters.status) {
            return false;
        }

        // Type filter
        if (this.filters.type && booking.transfer_type !== this.filters.type) {
            return false;
        }

        // Date filter
        if (this.filters.date) {
            const filterDate = new Date(this.filters.date).toISOString().split('T')[0];
            const arrivalDate = new Date(booking.arrival_date).toISOString().split('T')[0];
            const departureDate = booking.departure_date ? new Date(booking.departure_date).toISOString().split('T')[0] : null;

            if (arrivalDate !== filterDate && departureDate !== filterDate) {
                return false;
            }
        }

        // Search filter
        if (this.filters.search) {
            const search = this.filters.search.toLowerCase();

            // Get product names from JSON string if available
            let productNames = [];
            if (booking.product_names) {
                try {
                    productNames = JSON.parse(booking.product_names);
                } catch (e) {
                    console.error('Error parsing product names:', e);
                }
            }

            const searchFields = [
                booking.user_name,
                booking.email,
                booking.arrival_flight,
                booking.departure_flight,
                ...productNames
            ].filter(Boolean); // Remove null/undefined values

            return searchFields.some(field => field.toLowerCase().includes(search));
        }

        return true;
    });

    // Update pagination
    this.totalPages = Math.ceil(this.filteredBookings.length / this.perPage);
    this.currentPage = 1; // Reset to first page when filters change
    this.updatePaginatedBookings();
};
// Reset filters
window.resetFilters = function() {
    this.filters = {
        status: '',
        type: '',
        date: '',
        search: ''
    };
    this.applyFilters();
};

// Update paginated bookings
window.updatePaginatedBookings = function() {
    const start = (this.currentPage - 1) * this.perPage;
    const end = start + this.perPage;
    this.paginatedBookings = this.filteredBookings.slice(start, end);
};

// Pagination methods
window.prevPage = function() {
    if (this.currentPage > 1) {
        this.currentPage--;
        this.updatePaginatedBookings();
    }
};

window.nextPage = function() {
    if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.updatePaginatedBookings();
    }
};

window.goToPage = function(page) {
    this.currentPage = page;
    this.updatePaginatedBookings();
};

// Modal methods
window.openStatusModal = function(booking) {
    this.selectedBooking = booking;
    this.newStatus = booking.status;
    this.showStatusModal = true;
};

window.closeStatusModal = function() {
    this.showStatusModal = false;
    this.selectedBooking = null;
    this.newStatus = '';
};

window.openBookingDetails = function(booking) {
    this.selectedBooking = booking;
    this.showDetailsModal = true;
};

window.closeDetailsModal = function() {
    this.showDetailsModal = false;
    this.selectedBooking = null;
};

window.openDeleteModal = function(booking) {
    this.selectedBooking = booking;
    this.showDeleteModal = true;
    // If the details modal is open, close it
    if (this.showDetailsModal) {
        this.showDetailsModal = false;
    }
};

window.closeDeleteModal = function() {
    this.showDeleteModal = false;
    this.selectedBooking = null;
};
// Update booking status
window.updateBookingStatus = function() {
    if (!this.selectedBooking || !this.newStatus) {
        return;
    }

    this.isLoading = true;

    // AJAX call to update status
    fetch(window.lciAdmin.ajaxUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'lci_update_transfer_status',
            nonce: window.lciAdmin.nonce,
            booking_id: this.selectedBooking.id,
            status: this.newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update booking in the local array
            const index = this.bookings.findIndex(b => b.id === this.selectedBooking.id);
            if (index !== -1) {
                this.bookings[index].status = this.newStatus;
            }

            // Re-apply filters and update pagination
            this.applyFilters();

            // Show success message
            this.showNotification('Status updated successfully', 'success');
        } else {
            console.error('Error updating status:', data.data.message);
            this.showNotification('Error updating status', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating status:', error);
        this.showNotification('Error updating status', 'error');
    })
    .finally(() => {
        this.isLoading = false;
        this.closeStatusModal();
    });
};

// Delete booking
window.deleteBooking = function() {
    if (!this.selectedBooking) {
        return;
    }

    this.isLoading = true;

    // AJAX call to delete booking
    fetch(window.lciAdmin.ajaxUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'lci_delete_transfer_booking',
            nonce: window.lciAdmin.nonce,
            booking_id: this.selectedBooking.id
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove booking from the local array
            this.bookings = this.bookings.filter(b => b.id !== this.selectedBooking.id);

            // Re-apply filters and update pagination
            this.applyFilters();

            // Update stats
            this.stats.total--;
            if (this.selectedBooking.status === 'pending') this.stats.pending--;
            if (this.selectedBooking.status === 'confirmed') this.stats.confirmed--;
            if (this.selectedBooking.status === 'completed') this.stats.completed--;
            if (this.selectedBooking.status === 'cancelled') this.stats.cancelled--;

            // Show success message
            this.showNotification('Booking deleted successfully', 'success');
        } else {
            console.error('Error deleting booking:', data.data.message);
            this.showNotification('Error deleting booking: ' + data.data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting booking:', error);
        this.showNotification('Error deleting booking', 'error');
    })
    .finally(() => {
        this.isLoading = false;
        this.closeDeleteModal();
    });
};
// Helper methods
window.formatDate = function(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
};

window.getTransferTypeLabel = function(type) {
    return this.transferTypes[type] || type;
};

window.showNotification = function(message, type = 'info') {
    // Simple notification using alert for now
    // In a real implementation, use a proper notification system
    alert(message);
};
// Initialize Alpine.js when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, waiting for Alpine.js to initialize');

    // Check if Alpine.js is loaded
    if (typeof window.Alpine === 'undefined') {
        console.error('Alpine.js is not loaded. Make sure it is included in the page.');
    }
});
















        };
        });

        console.log('Alpine.js components initialized successfully');
    }

    // Start waiting for Alpine to load
    waitForAlpine();
});
