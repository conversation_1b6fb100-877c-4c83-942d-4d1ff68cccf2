<div class="wrap lci-admin-wrap">
    <div x-data="dashboard">
        <h1 class="text-3xl font-bold mb-6">LCI 2025 Dashboard</h1>

        <!-- Loading state -->
        <div x-show="isLoading" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
            <div class="spinner"></div>
            <span class="loading-text">Loading dashboard data...</span>
        </div>

        <!-- Dashboard content -->
        <div x-show="!isLoading" class="space-y-8">
            <!-- Stats overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Total participants -->
                <div class="bg-white rounded-xl shadow-neumorph p-6 transition-all duration-300 hover:shadow-lg">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-700">Total Participants</h3>
                        <span class="p-2 bg-blue-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </span>
                    </div>
                    <div class="mt-4">
                        <span class="text-4xl font-bold text-gray-800" x-text="stats.total"></span>
                    </div>
                </div>

                <!-- Completed payments -->
                <div class="bg-white rounded-xl shadow-neumorph p-6 transition-all duration-300 hover:shadow-lg">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-700">Completed Payments</h3>
                        <span class="p-2 bg-green-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </span>
                    </div>
                    <div class="mt-4">
                        <span class="text-4xl font-bold text-gray-800" x-text="stats.payment_status?.completed || 0"></span>
                    </div>
                </div>

                <!-- Pending payments -->
                <div class="bg-white rounded-xl shadow-neumorph p-6 transition-all duration-300 hover:shadow-lg">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-700">Pending Payments</h3>
                        <span class="p-2 bg-yellow-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </span>
                    </div>
                    <div class="mt-4">
                        <span class="text-4xl font-bold text-gray-800" x-text="stats.payment_status?.pending || 0"></span>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Registration types chart -->
                <div class="bg-white rounded-xl shadow-neumorph p-6 relative">
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="regTypesChart"></canvas>
                    </div>
                </div>

                <!-- Payment status chart -->
                <div class="bg-white rounded-xl shadow-neumorph p-6 relative">
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="paymentStatusChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent participants -->
            <div class="bg-white rounded-xl shadow-neumorph p-6">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Recent Participants</h3>

                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="text-left bg-gray-50">
                                <th class="p-3 font-medium text-gray-600">ID</th>
                                <th class="p-3 font-medium text-gray-600">Name</th>
                                <th class="p-3 font-medium text-gray-600">Email</th>
                                <th class="p-3 font-medium text-gray-600">Registration Type</th>
                                <th class="p-3 font-medium text-gray-600">Payment Status</th>
                                <th class="p-3 font-medium text-gray-600">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_participants as $participant): ?>
                                <tr class="border-t border-gray-200 hover:bg-gray-50">
                                    <td class="p-3">
                                        <div class="font-medium text-primary"><?php echo esc_html($participant->unique_reg_id); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo esc_html($participant->original_reg_id); ?></div>
                                    </td>
                                    <td class="p-3"><?php echo esc_html($participant->first_name . ' ' . $participant->last_name); ?></td>
                                    <td class="p-3"><?php echo esc_html($participant->email); ?></td>
                                    <td class="p-3"><?php echo esc_html($participant->registration_type); ?></td>
                                    <td class="p-3">
                                        <span class="status-badge <?php echo strtolower($participant->payment_status); ?>">
                                            <?php echo esc_html($participant->payment_status); ?>
                                        </span>
                                    </td>
                                    <td class="p-3">
                                        <a href="<?php echo admin_url('admin.php?page=lci-participant-detail&id=' . $participant->id); ?>" class="btn btn-primary">
                                            View
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>

                            <?php if (empty($recent_participants)): ?>
                                <tr>
                                    <td colspan="6" class="p-3 text-center text-gray-500">
                                        No participants found.
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-4 text-right">
                    <a href="<?php echo admin_url('admin.php?page=lci-participants'); ?>" class="btn btn-secondary">
                        View All Participants
                    </a>
                </div>
            </div>

            <!-- Quick actions -->
            <div class="bg-white rounded-xl shadow-neumorph p-6">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Quick Actions</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="<?php echo admin_url('admin.php?page=lci-sync-tool'); ?>" class="btn btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Sync Participants
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=lci-participants'); ?>" class="btn btn-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        Manage Participants
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=lci-settings'); ?>" class="btn btn-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
