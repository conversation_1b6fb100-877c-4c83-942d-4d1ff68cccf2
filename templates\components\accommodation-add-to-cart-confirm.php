<?php
/**
 * Accommodation Add to Cart Confirmation Template
 */
?>

<script>
// Define the function globally before Alpine initializes
window.showAccommodationAddToCartConfirmation = function(name, image) {
    // Store the data for Alpine to use when it initializes
    window.pendingAccommodationConfirmation = {
        productName: name,
        productImage: image
    };

    // Try to find the Alpine component
    const confirmEl = document.getElementById('accommodation-add-to-cart-confirm');
    if (confirmEl && confirmEl.__x) {
        // If Alpine is already initialized, call the method directly
        try {
            confirmEl.__x.getUnobservedData().showConfirmation(name, image);
        } catch (error) {
            // Try direct DOM manipulation as a fallback
            showAccommodationConfirmationFallback(name, image);
        }
    } else {
        // Otherwise dispatch an event that will be handled when Alpine initializes
        window.dispatchEvent(new CustomEvent('accommodation-show-confirmation-pending', {
            detail: {
                productName: name,
                productImage: image
            }
        }));

        // Also try direct DOM manipulation as a fallback
        showAccommodationConfirmationFallback(name, image);
    }
};

// Fallback function for browsers without Alpine.js
function showAccommodationConfirmationFallback(name, image) {
    // Check if a fallback modal already exists
    let fallbackModal = document.getElementById('accommodation-fallback-modal');
    
    // If it exists, remove it first
    if (fallbackModal) {
        document.body.removeChild(fallbackModal);
    }
    
    // Create a new fallback modal
    fallbackModal = document.createElement('div');
    fallbackModal.id = 'accommodation-fallback-modal';
    fallbackModal.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center; padding: 20px;';
    
    document.body.appendChild(fallbackModal);
    
    // Set the modal content
    fallbackModal.innerHTML = `
        <div style="background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.12); max-width: 450px; width: 95%; position: relative;">
            <button onclick="document.getElementById('accommodation-fallback-modal').style.display='none';" style="position: absolute; right: 1rem; top: 1rem; background: none; border: none; font-size: 1.25rem; color: #36b1dc; z-index: 10; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
            
            <div style="padding: 2.5rem 1.5rem 1.5rem; text-align: center;">
                <div style="margin-bottom: 1.5rem;">
                    <i class="fas fa-check-circle" style="font-size: 3rem; color: #36b1dc;"></i>
                </div>
                
                <div>
                    <h4 style="color: #36b1dc; font-weight: 600; margin-bottom: 0.75rem;">${name}</h4>
                    <p style="color: #36b1dc; font-size: 1.1rem;">has been added to your LCI Accommodation Booking</p>
                </div>
            </div>
            <div style="padding: 1rem 1.5rem 1.5rem; display: flex; justify-content: space-between; gap: 1rem;">
                <button onclick="document.getElementById('accommodation-fallback-modal').style.display='none';" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #36b1dc; color: #fff; font-weight: 500; transition: all 0.2s ease; cursor: pointer;">Continue Booking</button>
                <button onclick="document.getElementById('accommodation-fallback-modal').style.display='none'; window.location.href='/cart/';" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #36b1dc; color: #fff; font-weight: 500; transition: all 0.2s ease; cursor: pointer;">
                    <i class="fas fa-shopping-bag me-2" style="color: #fff;"></i> View Bookings
                </button>
            </div>
            <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 4px; background-color: rgba(54, 177, 220, 0.1); overflow: hidden;">
                <div id="accommodation-fallback-progress" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: #36b1dc; transform-origin: left center; transition: transform 3s linear; transform: scaleX(0);"></div>
            </div>
        </div>
    `;

    // Start the progress bar animation
    setTimeout(() => {
        const progressBar = document.getElementById('accommodation-fallback-progress');
        if (progressBar) {
            progressBar.style.transform = 'scaleX(1)';
        }
    }, 50);

    // Auto-close after 3 seconds
    setTimeout(() => {
        if (fallbackModal) {
            fallbackModal.style.display = 'none';
        }
    }, 3000);
}

// Make sure the showAddToCartConfirmation function also works
if (typeof window.showAddToCartConfirmation === 'undefined') {
    window.showAddToCartConfirmation = window.showAccommodationAddToCartConfirmation;
}
</script>

<div x-data="{
    open: false,
    productName: '',
    productImage: '',
    progress: 0,
    progressInterval: null,
    scrollPosition: 0,
    
    init() {
        // Store a reference to the original function
        const originalFunction = window.showAccommodationAddToCartConfirmation;

        // Update the global reference to use this instance
        const self = this;
        window.showAccommodationAddToCartConfirmation = function(name, image) {
            self.showConfirmation(name, image);
        };

        // Listen for custom event
        this.$el.addEventListener('accommodation-show-confirmation', (event) => {
            if (event.detail) {
                this.showConfirmation(event.detail.productName, event.detail.productImage);
            }
        });

        // Listen for window event
        window.addEventListener('accommodation-show-modal', (event) => {
            if (event.detail) {
                this.showConfirmation(event.detail.productName, event.detail.productImage);
            }
        });

        // Check for pending confirmation
        if (window.pendingAccommodationConfirmation) {
            this.showConfirmation(
                window.pendingAccommodationConfirmation.productName,
                window.pendingAccommodationConfirmation.productImage
            );
            window.pendingAccommodationConfirmation = null;
        }

        // Listen for pending confirmation event
        window.addEventListener('accommodation-show-confirmation-pending', (event) => {
            if (event.detail) {
                this.showConfirmation(event.detail.productName, event.detail.productImage);
            }
        });
    },

    showConfirmation(name, image) {
        this.productName = name;
        this.productImage = image;
        this.open = true;
        this.startProgress();

        // Add modal-open class to body
        document.body.classList.add('modal-open');

        // Store the current scroll position
        this.scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

        // Add our own scroll management
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.top = `-${this.scrollPosition}px`;
        document.body.style.width = '100%';
    },

    closeConfirmation() {
        this.open = false;
        this.stopProgress();

        // Remove modal-open class from body
        document.body.classList.remove('modal-open');

        // Restore scroll position
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        window.scrollTo(0, this.scrollPosition);
    },

    startProgress() {
        this.progress = 0;
        this.stopProgress();
        
        const duration = 3000; // 3 seconds
        const interval = 30; // Update every 30ms
        const steps = duration / interval;
        const increment = 100 / steps;
        
        this.progressInterval = setInterval(() => {
            this.progress += increment;
            if (this.progress >= 100) {
                this.stopProgress();
                this.closeConfirmation();
            }
        }, interval);
    },

    stopProgress() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    },

    viewCart() {
        window.location.href = '/cart/';
    }
}" x-cloak id="accommodation-add-to-cart-confirm">
    <div
        x-show="open"
        x-transition:enter="fade-in"
        x-transition:leave="fade-out"
        class="lci-modal-backdrop"
        @click.self="closeConfirmation"
        style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center; overflow-y: auto; padding: 20px 0;"
    >
        <div
            class="lci-modal"
            x-transition:enter="slide-in"
            x-transition:leave="slide-out"
            style="background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15), 0 5px 10px rgba(0,0,0,0.12); max-width: 450px; width: 95%; position: relative;"
        >
            <button @click="closeConfirmation" class="lci-modal-close" style="position: absolute; right: 1rem; top: 1rem; background: none; border: none; font-size: 1.25rem; color: #36b1dc; transition: all 0.2s ease; z-index: 10; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>

            <div class="lci-modal-body text-center" style="padding: 2.5rem 1.5rem 1.5rem;">
                <div class="accommodation-confirmation-animation" style="margin-bottom: 1.5rem;">
                    <i class="fas fa-check-circle" style="font-size: 3rem; color: #36b1dc;"></i>
                </div>

                <div class="accommodation-confirmation-text">
                    <h4 style="color: #36b1dc; font-weight: 600; margin-bottom: 0.75rem;" x-text="productName"></h4>
                    <p style="color: #36b1dc; font-size: 1.1rem;">has been added to your LCI Accommodation Booking</p>
                </div>
            </div>

            <div class="lci-modal-footer" style="padding: 1rem 1.5rem 1.5rem; display: flex; justify-content: space-between; gap: 1rem;">
                <button @click="closeConfirmation" class="lci-btn" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #36b1dc; color: #fff !important; font-weight: 500; transition: all 0.2s ease; cursor: pointer;">Continue Booking</button>
                <button @click="viewCart" class="lci-btn" style="flex: 1; padding: 0.75rem 1rem; border-radius: 6px; border: none; background-color: #36b1dc; color: #fff !important; font-weight: 500; transition: all 0.2s ease; cursor: pointer;">
                    <i class="fas fa-shopping-bag me-2" style="color: #fff !important;"></i> View Bookings
                </button>
            </div>

            <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 4px; background-color: rgba(54, 177, 220, 0.1); overflow: hidden;">
                <div 
                    style="position: absolute; top: 0; left: 0; bottom: 0; background-color: #36b1dc; transform-origin: left center;"
                    :style="{ width: progress + '%' }"
                ></div>
            </div>
        </div>
    </div>
</div>

<style>
.fade-in {
    animation: fadeIn 0.3s ease-out forwards;
}

.fade-out {
    animation: fadeOut 0.2s ease-in forwards;
}

.slide-in {
    animation: slideIn 0.3s ease-out forwards;
}

.slide-out {
    animation: slideOut 0.2s ease-in forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(20px); opacity: 0; }
}

[x-cloak] { display: none !important; }
</style>
