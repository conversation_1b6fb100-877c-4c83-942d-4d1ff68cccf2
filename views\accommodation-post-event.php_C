<?php
/**
 * Post-Event Brasov Accommodation View
 *
 * Displays Post-Event accommodation options in Brasov
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Enqueue the accommodation CSS
wp_enqueue_style('accommodation-css', LCI2025_URL . 'assets/css/accommodation.css', array(), LCI2025_VERSION);

// Add inline CSS for the accommodation products
$accommodation_products_css = "
.accommodation-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.accommodation-product-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.accommodation-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accommodation-product-image {
    height: 350px;
    overflow: hidden;
    position: relative;
}

.accommodation-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.accommodation-product-card:hover .accommodation-product-image img {
    transform: scale(1.05);
}

.accommodation-product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.accommodation-product-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.accommodation-product-title {
    color: #00b2e3;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.accommodation-product-description {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
    flex-grow: 1;
}

.accommodation-product-price {
    font-weight: bold;
    font-size: 18px;
    color: #343a40;
    margin-bottom: 15px;
}

.accommodation-product-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #36b1dc;
    color: white !important;
    border: none;
    width: 100%;
    box-shadow: 0 4px 6px rgba(54, 177, 220, 0.2);
}

.accommodation-product-button i {
    color: white;
}

.accommodation-product-button:hover {
    background-color: #36b1dc;
    color: white !important;
    box-shadow: 0 6px 12px rgba(54, 177, 220, 0.3);
    transform: translateY(-2px);
}

.accommodation-product-variations {
    margin-top: 15px;
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.accommodation-product-variation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.accommodation-product-variation-name {
    font-size: 14px;
    color: #495057;
}

.accommodation-product-variation-price {
    font-weight: bold;
    color: #00b2e3;
}

.accommodation-product-stars {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: #ffc107;
}

/* Features List with Checkmarks */
.accommodation-product-features {
    margin-bottom: 15px;
}

.accommodation-features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.accommodation-features-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    font-size: 14px;
    color: #495057;
    line-height: 1.4;
}

.accommodation-features-list li i {
    color: #36b1dc;
    margin-right: 8px;
    font-size: 16px;
    flex-shrink: 0;
    margin-top: 2px;
}

.accommodation-product-soldout {
    opacity: 0.7;
    position: relative;
}

.accommodation-product-soldout::after {
    content: 'SOLD OUT';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-15deg);
    background-color: rgba(220, 53, 69, 0.8);
    color: white;
    padding: 10px 20px;
    font-weight: bold;
    font-size: 24px;
    border-radius: 5px;
    z-index: 10;
}

.accommodation-product-variation.soldout {
    opacity: 0.5;
    text-decoration: line-through;
}

.nights-selector {
    max-width: 300px;
    margin: 30px auto;
}

.nights-selector-container {
    background: white;
    border: 2px solid #00b2e3;
    border-radius: 50px;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nights-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #00b2e3;
    color: white;
    border: none;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nights-btn:disabled {
    background: #e0e0e0;
    cursor: not-allowed;
}

.nights-display {
    flex-grow: 1;
    text-align: center;
}

.nights-count {
    font-size: 24px;
    font-weight: bold;
    color: #00b2e3;
}

.nights-label {
    font-size: 16px;
    color: #555;
    margin-left: 5px;
}

@media (max-width: 768px) {
    .accommodation-products-grid {
        grid-template-columns: 1fr;
    }
}
";

wp_add_inline_style('accommodation-css', $accommodation_products_css);

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Get Post-Event accommodation products (category ID 37)
$post_event_products = [];
$args = [
    'post_type' => 'product',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'tax_query' => array(
        array(
            'taxonomy' => 'product_cat',
            'field' => 'id',
            'terms' => 37, // Pre/Post Event accommodation category ID
        ),
    ),
];

$posts = get_posts($args);
foreach ($posts as $post) {
    $product = wc_get_product($post->ID);
    if ($product) {
        $post_event_products[] = $product;
    }
}

// Check if any products are in the cart
$products_in_cart = [];
if (WC()->cart) {
    foreach (WC()->cart->get_cart() as $cart_item) {
        $products_in_cart[] = $cart_item['product_id'];
    }
}

// Initialize nights value
$nights = isset($_POST['nights']) ? intval($_POST['nights']) : 1;
?>

<div class="accommodation-container">
    <!-- Header with title and mini cart -->
    <div class="dashboard-header mb-4">
        <div class="dashboard-mini-cart-container d-flex justify-content-between align-items-center">
            <div class="dashboard-title">
                <h2 class="text-primary mb-0"><i class="fas fa-hotel me-2"></i> Post-Event Accommodation</h2>
            </div>

            <!-- Mini Cart Button -->
            <?php echo lci_get_mini_cart_html(); ?>
        </div>
    </div>

    <!-- Back to Accommodation Button -->
    <div class="mb-3">
        <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation', remove_query_arg('category_id'))); ?>" class="btn btn-sm btn-outline-secondary d-inline-flex align-items-center">
            <i class="fas fa-arrow-left me-2"></i> Back to All Accommodations
        </a>
    </div>

    <!-- Introduction Section -->
    <div class="mb-5">

        <div class="mb-4" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); overflow: hidden;">
            <div style="background-color: #36b1dc; padding: 15px 20px; color: white;">
                <h4 style="margin: 0; font-weight: 600; font-size: 20px; display: flex; align-items: center; color: white;">
                    <i class="fas fa-hotel me-2" style="color: white;"></i> Staying Longer in Brasov?
                </h4>
            </div>
            <div style="padding: 20px;">
                <p style="margin-bottom: 20px; color: #37474f; font-size: 15px; line-height: 1.5;">
                    Book your accommodation in Brasov after the main event ends (after August 24, 2025).
                    Select the number of nights you need and choose from our recommended hotels.
                </p>

                <div id="current-settings-display" style="background-color: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); margin-top: 10px;">
                    <div style="margin-bottom: 12px;">
                        <div style="font-weight: 600; color: #36b1dc; font-size: 16px;">YOUR STAY PREFERENCES</div>
                    </div>

                    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px;">
                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">DURATION</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-moon me-1" style="color: #36b1dc;"></i>
                                <span id="current-nights"><?php echo $nights; ?></span> night(s)
                            </div>
                        </div>

                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">ROOM TYPE</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-bed me-1" style="color: #36b1dc;"></i>
                                <span id="current-room-type">Single Room</span>
                            </div>
                        </div>
                    </div>

                    <div style="height: 1px; background-color: #e0e0e0; margin: 15px 0;"></div>

                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">CHECK-IN</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-calendar-check me-1" style="color: #36b1dc;"></i>
                                24/08/2025
                            </div>
                        </div>

                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">CHECK-OUT</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-calendar-times me-1" style="color: #36b1dc;"></i>
                                <span id="current-checkout-date">25/08/2025</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (empty($post_event_products)): ?>
        <!-- No products available message -->
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span>No accommodation options are currently available for post-event. Please check back later.</span>
        </div>
        <?php else: ?>
        <!-- Products Grid -->
        <div class="accommodation-products-grid">
            <?php foreach ($post_event_products as $product):
                $product_id = $product->get_id();
                $in_cart = in_array($product_id, $products_in_cart);
                $image_url = wp_get_attachment_url($product->get_image_id());
                $price_html = $product->get_price_html();
                $description = $product->get_short_description();
                $is_in_stock = $product->is_in_stock();

                // Get hotel stars from product meta
                $hotel_stars = get_post_meta($product_id, '_number_of_stars', true);
                if (!$hotel_stars) $hotel_stars = 3; // Default to 3 stars

                // Get hotel features from product meta
                $hotel_features = get_post_meta($product_id, '_hotel_features', true);
                if ($hotel_features) {
                    $features = explode("\n", $hotel_features);
                } else {
                    $features = [];
                }

                // Get hotel website from product meta
                $hotel_website = get_post_meta($product_id, '_hotel_website', true);

                // Add soldout class if product is out of stock
                $soldout_class = !$is_in_stock ? 'accommodation-product-soldout' : '';
            ?>
            <div class="accommodation-product-card">
                <div class="accommodation-product-image">
                    <?php if ($in_cart): ?>
                    <div class="accommodation-product-badge">In Cart</div>
                    <?php endif; ?>
                    <img src="<?php echo esc_url($image_url ? $image_url : 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121215/post-event.jpg'); ?>" alt="<?php echo esc_attr($product->get_name()); ?>">
                </div>
                <div class="accommodation-product-content">
                    <h4 class="accommodation-product-title"><?php echo esc_html($product->get_name()); ?></h4>

                    <!-- Hotel Stars -->
                    <div class="accommodation-product-stars">
                        <?php for ($i = 0; $i < $hotel_stars; $i++): ?>
                            <i class="fas fa-star"></i>
                        <?php endfor; ?>
                        <span class="accommodation-product-star-description">
                            <?php echo $hotel_stars; ?>-star hotel
                        </span>
                    </div>

                    <!-- Description List with Checkmarks -->
                    <div class="accommodation-product-features">
                        <ul class="accommodation-features-list">
                            <?php
                            // Get description fields from product meta
                            $descriere = get_post_meta($product_id, '_descriere', true);
                            $descriere1 = get_post_meta($product_id, '_descriere1', true);
                            $descriere2 = get_post_meta($product_id, '_descriere2', true);
                            $descriere3 = get_post_meta($product_id, '_descriere3', true);

                            // Display description fields if they exist
                            if ($descriere) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere) . '</li>';
                            if ($descriere1) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere1) . '</li>';
                            if ($descriere2) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere2) . '</li>';
                            if ($descriere3) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere3) . '</li>';
                            ?>
                        </ul>
                    </div>

                    <div class="accommodation-product-description">
                        <?php if (!empty($features)): ?>
                        <ul class="mt-2 mb-0" style="padding-left: 20px;">
                            <?php foreach ($features as $feature): ?>
                            <li><?php echo esc_html(trim($feature)); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>

                        <?php if ($hotel_website): ?>
                        <div class="mt-2">
                            <a href="<?php echo esc_url($hotel_website); ?>" target="_blank" style="color: #00b2e3; text-decoration: none; display: inline-flex; align-items: center;">
                                <i class="fas fa-globe me-1"></i> Visit hotel website
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($product->is_type('variable')):
                        $variations = $product->get_available_variations();
                    ?>
                    <div class="accommodation-product-variations" data-product-id="<?php echo esc_attr($product_id); ?>">
                        <h5 class="accommodation-variations-title">Available Room Types:</h5>
                        <?php
                        $first_variation = true;
                        foreach ($variations as $variation):
                            $variation_obj = wc_get_product($variation['variation_id']);
                            if (!$variation_obj || !$variation_obj->is_in_stock()) continue;

                            $variation_id = $variation['variation_id'];

                            // Get attribute names instead of values
                            $attribute_names = [];
                            foreach ($variation['attributes'] as $key => $value) {
                                $taxonomy = str_replace('attribute_', '', $key);
                                $term = get_term_by('slug', $value, $taxonomy);
                                if ($term) {
                                    $attribute_names[] = $term->name;
                                } else {
                                    // For custom product attributes
                                    $attribute_names[] = $value;
                                }
                            }

                            $variation_name = implode(' - ', $attribute_names);
                            $variation_price_html = $variation_obj->get_price_html();

                            // Check if this is a double room variation
                            $is_double_room = stripos($variation_name, 'double') !== false;

                            // Get variation price
                            $variation_price = $variation_obj->get_price();
                        ?>
                        <div class="accommodation-product-variation">
                            <label class="accommodation-variation-radio-label">
                                <input type="radio" name="variation_<?php echo esc_attr($product_id); ?>"
                                       value="<?php echo esc_attr($variation_id); ?>"
                                       data-variation-name="<?php echo esc_attr($variation_name); ?>"
                                       data-is-double="<?php echo $is_double_room ? 'true' : 'false'; ?>"
                                       data-price="<?php echo esc_attr($variation_price); ?>"
                                       <?php echo $first_variation ? 'checked' : ''; ?>>
                                <div class="accommodation-product-variation-name"><?php echo esc_html($variation_name); ?></div>
                                <div class="accommodation-product-variation-price"><?php echo $variation_price_html; ?></div>
                            </label>
                        </div>
                        <?php
                        $first_variation = false;
                        endforeach;
                        ?>
                    </div>
                    <?php else: ?>
                    <div class="accommodation-product-single-price">
                        <?php echo $price_html; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ($in_cart): ?>
                    <button class="accommodation-product-button" disabled style="background: #28a745;">
                        <i class="fas fa-check me-2"></i> Added to Cart
                    </button>
                    <?php elseif (!$is_in_stock): ?>
                    <button class="accommodation-product-button" disabled style="background: #6c757d;">
                        <i class="fas fa-times me-2"></i> Sold Out
                    </button>
                    <?php else: ?>
                    <button class="accommodation-product-button add-to-cart-btn"
                            data-product-id="<?php echo esc_attr($product_id); ?>"
                            data-product-name="<?php echo esc_attr($product->get_name()); ?>"
                            data-product-type="<?php echo esc_attr($product->get_type()); ?>"
                            data-price="<?php echo esc_attr($product->get_price()); ?>">
                        <i class="fas fa-shopping-cart me-2"></i> Add to Cart
                    </button>
                    <button onclick="showPostEventModal(event)" class="accommodation-settings-button">
                        <i class="fas fa-cog me-2"></i> Change Stay Settings
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add to Cart Confirmation Modal -->
<div class="accommodation-cart-confirm-modal" id="postEventCartConfirmModal" style="display: none;">
    <div class="accommodation-cart-confirm-overlay"></div>
    <div class="accommodation-cart-confirm-content">
        <div class="accommodation-cart-confirm-header">
            <h3><i class="fas fa-check-circle me-2"></i> Confirm Your Selection</h3>
            <button type="button" class="accommodation-cart-confirm-close" onclick="closePostEventConfirmModal()">&times;</button>
        </div>
        <div class="accommodation-cart-confirm-body">
            <p>Please confirm your post-event accommodation selection:</p>

            <div class="accommodation-cart-confirm-details">
                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Hotel:</span>
                    <span class="accommodation-cart-confirm-value" id="postEventConfirmProductName"></span>
                </div>

                <div class="accommodation-cart-confirm-row" id="confirmRoomTypeRow" style="display: none;">
                    <span class="accommodation-cart-confirm-label">Room Type:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmRoomType"></span>
                </div>

                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Number of Nights:</span>
                    <span class="accommodation-cart-confirm-value" id="postEventConfirmNights"></span>
                </div>

                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Check-in Date:</span>
                    <span class="accommodation-cart-confirm-value">24/08/2025</span>
                </div>

                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Check-out Date:</span>
                    <span class="accommodation-cart-confirm-value" id="postEventConfirmCheckoutDate"></span>
                </div>

                <!-- Removed note about August 24 -->

                <div class="accommodation-cart-confirm-row" id="confirmDoubleRoomRow" style="display: none;">
                    <span class="accommodation-cart-confirm-label">Double Room:</span>
                    <span class="accommodation-cart-confirm-value">Yes (quantity will be doubled)</span>
                </div>

                <div class="accommodation-cart-confirm-row accommodation-cart-confirm-total">
                    <span class="accommodation-cart-confirm-label">Total Price:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmTotalPrice">Calculating...</span>
                </div>
            </div>

            <div class="accommodation-cart-confirm-notice" id="confirmDoubleRoomNotice" style="display: none;">
                <i class="fas fa-info-circle"></i>
                <div>
                    <strong>Important:</strong> When booking a Double Room, the price is calculated per person.
                    <p class="mt-2 mb-0">Since this room is for 2 people, the system will automatically charge for both guests (<span id="confirmDoubleRoomCalc"></span> total).</p>
                    <p class="mt-2 mb-0">If you are booking alone, please make sure you will be sharing the room with someone, or choose a Single Room instead.</p>
                </div>
            </div>
        </div>
        <div class="accommodation-cart-confirm-footer">
            <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-secondary" onclick="closePostEventConfirmModal()">
                Cancel
            </button>
            <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-primary" onclick="addToCartPostEvent()">
                <i class="fas fa-shopping-cart me-2"></i> Add to Cart
            </button>
        </div>
    </div>
</div>

<style>
/* Accommodation Cart Confirm Modal Styles */
.accommodation-cart-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: none;
}

.accommodation-cart-confirm-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

.accommodation-cart-confirm-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 95%;
    max-width: 700px;
    overflow: hidden;
}

.accommodation-cart-confirm-header {
    background-color: #36b1dc;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.accommodation-cart-confirm-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.accommodation-cart-confirm-header h3 i {
    color: white;
    margin-right: 8px;
}

.accommodation-cart-confirm-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.accommodation-cart-confirm-body {
    padding: 20px;
}

@media (min-width: 768px) {
    .accommodation-cart-confirm-header {
        padding: 20px 25px;
    }

    .accommodation-cart-confirm-header h3 {
        font-size: 22px;
    }

    .accommodation-cart-confirm-body {
        padding: 25px 30px;
    }

    .accommodation-cart-confirm-details {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .accommodation-cart-confirm-row {
        flex-direction: column;
        background-color: #f8f9fa;
        padding: 12px 15px;
        border-radius: 6px;
        border-bottom: none;
        margin-bottom: 0;
    }

    .accommodation-cart-confirm-label {
        margin-bottom: 5px;
        color: #6c757d;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .accommodation-cart-confirm-value {
        font-size: 16px;
        font-weight: 500;
    }

    .accommodation-cart-confirm-total {
        grid-column: 1 / -1;
        margin-top: 15px;
        background-color: #e3f2fd;
        border-top: none;
        padding-top: 0;
    }
}

.accommodation-cart-confirm-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.accommodation-cart-confirm-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.accommodation-cart-confirm-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.accommodation-cart-confirm-total {
    margin-top: 15px;
    border-top: 2px solid #e9ecef;
    padding-top: 15px;
    font-weight: bold;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-label {
    font-size: 16px;
    color: #343a40;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-value {
    font-size: 16px;
    color: #36b1dc;
}

.accommodation-cart-confirm-label {
    font-weight: 600;
    color: #495057;
}

.accommodation-cart-confirm-value {
    color: #343a40;
}

.accommodation-cart-confirm-notice {
    background-color: #e8f4fd;
    color: #0c5460;
    padding: 16px;
    border-radius: 8px;
    margin-top: 15px;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid #36b1dc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.accommodation-cart-confirm-notice i {
    margin-right: 12px;
    font-size: 18px;
    margin-top: 2px;
    color: #36b1dc;
}

.accommodation-cart-confirm-notice p {
    line-height: 1.5;
}

.accommodation-cart-confirm-notice strong {
    color: #0c5460;
    font-weight: 600;
}

.accommodation-cart-confirm-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
}

@media (min-width: 768px) {
    .accommodation-cart-confirm-footer {
        padding: 20px 25px;
    }

    .accommodation-cart-confirm-btn {
        min-width: 150px;
        padding: 12px 20px;
    }
}

.accommodation-cart-confirm-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    width: 48%;
    color: white;
}

.accommodation-cart-confirm-btn i {
    color: white;
    margin-right: 8px;
}

.accommodation-cart-confirm-btn-primary {
    background-color: #36b1dc;
    color: white;
}

.accommodation-cart-confirm-btn-primary:hover {
    background-color: #2d9cc3;
    color: white;
}

.accommodation-cart-confirm-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.accommodation-cart-confirm-btn-secondary:hover {
    background-color: #5a6268;
    color: white;
}

.accommodation-cart-confirm-dates-note {
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
}

.accommodation-cart-confirm-dates-note i {
    color: #36b1dc;
    margin-right: 6px;
    font-size: 14px;
}
</style>

<script>
// Variables to store product information for the confirmation modal
let postEventProductId = 0;
let postEventVariationId = 0;
let postEventNights = 1;
let postEventIsDoubleRoom = false;

// Function to show the confirmation modal
function showPostEventConfirmModal(productId, variationId, isDoubleRoom) {
    postEventProductId = productId;
    postEventVariationId = variationId;
    postEventIsDoubleRoom = isDoubleRoom;

    // Get saved nights from session storage
    postEventNights = parseInt(sessionStorage.getItem('postEventNights')) || 1;

    // Get product name
    const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${productId}"]`).closest('.accommodation-product-card');
    const productName = productCard.querySelector('.accommodation-product-title').textContent;

    // Get variation name if applicable
    let roomType = '';
    if (variationId) {
        const variationRadio = document.querySelector(`input[value="${variationId}"]`);
        if (variationRadio) {
            roomType = variationRadio.getAttribute('data-variation-name');
        }
    }

    // Calculate check-out date
    const checkoutDate = calculatePostEventCheckoutDate(postEventNights);

    // Update modal content
    document.getElementById('postEventConfirmProductName').textContent = productName;

    if (roomType) {
        document.getElementById('confirmRoomTypeRow').style.display = 'flex';
        document.getElementById('confirmRoomType').textContent = roomType;
    } else {
        document.getElementById('confirmRoomTypeRow').style.display = 'none';
    }

    document.getElementById('postEventConfirmCheckoutDate').textContent = checkoutDate;
    document.getElementById('postEventConfirmNights').textContent = `${postEventNights} ${postEventNights === 1 ? 'night' : 'nights'}`;

    // Hide double room notice (no longer needed)
    document.getElementById('confirmDoubleRoomRow').style.display = 'none';
    document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

    // Calculate and display the total price
    let productPrice = 0;

    if (variationId) {
        // Get price from the selected variation
        const selectedVariation = document.querySelector(`input[value="${variationId}"]`);
        if (selectedVariation) {
            productPrice = parseFloat(selectedVariation.getAttribute('data-price') || 0);
        }
    } else {
        // Get price from the button's data attribute
        const addToCartBtn = document.querySelector(`.add-to-cart-btn[data-product-id="${productId}"]`);
        if (addToCartBtn) {
            productPrice = parseFloat(addToCartBtn.getAttribute('data-price') || 0);
        }
    }

    // Calculate total price based on nights
    const totalPrice = productPrice * postEventNights;

    // Format the price with currency symbol
    const formattedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(totalPrice);

    // Update the price display
    document.getElementById('confirmTotalPrice').textContent = formattedPrice;

    // Show modal
    const confirmModal = document.getElementById('postEventCartConfirmModal');
    confirmModal.style.display = 'block';
}

// Function to close the confirmation modal
function closePostEventConfirmModal() {
    const confirmModal = document.getElementById('postEventCartConfirmModal');
    confirmModal.style.display = 'none';
}

// Function to calculate check-out date
function calculatePostEventCheckoutDate(nights) {
    // Base date is 24/08/2025 (check-in date)
    const baseDate = new Date(2025, 7, 24); // Month is 0-indexed, so 7 = August

    // Calculate check-out date by adding nights to base date
    const checkoutDate = new Date(baseDate);
    checkoutDate.setDate(baseDate.getDate() + nights);

    // Format the date as DD/MM/YYYY
    const day = String(checkoutDate.getDate()).padStart(2, '0');
    const month = String(checkoutDate.getMonth() + 1).padStart(2, '0');
    const year = checkoutDate.getFullYear();

    return `${day}/${month}/${year}`;
}

// Function to add product to cart
function addToCartPostEvent() {
    const confirmModal = document.getElementById('postEventCartConfirmModal');

    // Calculate quantity based on room type
    const quantity = postEventIsDoubleRoom ? postEventNights * 2 : postEventNights;

    // Create a direct add-to-cart URL
    let addToCartUrl = '?add-to-cart=' + postEventProductId + '&quantity=' + quantity;

    // Add variation ID if applicable
    if (postEventVariationId) {
        addToCartUrl += '&variation_id=' + postEventVariationId;
    }

    // Send AJAX request using jQuery to ensure compatibility with WooCommerce
    jQuery.ajax({
        type: 'GET',
        url: addToCartUrl,
        success: function(response) {
            // Trigger WooCommerce's update_cart event
            jQuery(document.body).trigger('added_to_cart', [null, null, jQuery('.add-to-cart-btn')]);

            // Hide confirmation modal
            confirmModal.style.display = 'none';

            // Show success message instead of reloading
            const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${postEventProductId}"]`).closest('.accommodation-product-card');
            const addToCartBtn = productCard.querySelector('.add-to-cart-btn');

            // Replace button with success message
            addToCartBtn.innerHTML = '<i class="fas fa-check me-2"></i> Added to Cart';
            addToCartBtn.style.backgroundColor = '#28a745';
            addToCartBtn.disabled = true;

            // Hide settings button
            const settingsBtn = productCard.querySelector('.accommodation-settings-button');
            if (settingsBtn) {
                settingsBtn.style.display = 'none';
            }

            // Update mini cart count (if available)
            const miniCartCount = document.querySelector('.mini-cart-count');
            if (miniCartCount) {
                const currentCount = parseInt(miniCartCount.textContent) || 0;
                miniCartCount.textContent = currentCount + 1;
            }

            // Refresh the mini-cart contents
            refreshMiniCart();
        },
        error: function(error) {
            console.error('Error adding to cart:', error);

            // Hide confirmation modal
            confirmModal.style.display = 'none';
        }
    });
}

// Function to refresh the mini-cart
function refreshMiniCart() {
    // Get the mini-cart container
    const miniCartContainer = document.querySelector('.dashboard-mini-cart');
    if (!miniCartContainer) {
        console.warn('Mini-cart container not found');
        return;
    }

    console.log('Refreshing mini-cart...');

    // Add loading indicator
    miniCartContainer.classList.add('loading');

    // Fetch updated mini-cart HTML via AJAX
    fetch('?wc-ajax=get_refreshed_fragments', {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update fragments (including mini-cart)
        if (data.fragments) {
            for (let id in data.fragments) {
                const element = document.querySelector(id);
                if (element) {
                    element.innerHTML = data.fragments[id];
                }
            }
        }

        // Remove loading indicator
        miniCartContainer.classList.remove('loading');

        console.log('Mini-cart refreshed successfully');
    })
    .catch(error => {
        console.error('Error refreshing mini-cart:', error);
        miniCartContainer.classList.remove('loading');
    });
}
</script>

<!-- Post-Event Accommodation Modal -->
<div x-data="postEventAccommodation()" x-init="initModal()">
    <div class="post-event-modal-overlay" id="postEventModalOverlay" style="display: none;">
        <div class="post-event-modal">
            <div class="post-event-modal-header">
                <h3><i class="fas fa-hotel me-2"></i> Post-Event Accommodation</h3>
                <button onclick="closePostEventModal()" style="background: none; border: none; color: white; font-size: 20px; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="post-event-modal-body">
                <p>Please select your accommodation preferences for your stay after the main event:</p>

                <!-- Nights Selector -->
                <div class="post-event-modal-form-group">
                    <label for="nights">How many nights will you stay?</label>
                    <div class="nights-selector">
                        <div class="nights-selector-container">
                            <button type="button" class="nights-btn" @click="decrementNights()" :disabled="nights <= 1">
                                <i class="fas fa-minus"></i>
                            </button>
                            <div class="nights-display">
                                <span class="nights-count" x-text="nights"></span>
                                <span class="nights-label" x-text="nights === 1 ? 'Night' : 'Nights'"></span>
                            </div>
                            <button type="button" class="nights-btn" @click="incrementNights()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Room Type Selector -->
                <div class="post-event-modal-form-group">
                    <label for="roomType">Room Type:</label>
                    <select id="roomType" x-model="roomType" class="form-select">
                        <option value="single">Single Room (1 person)</option>
                        <option value="double">Double Room (2 people)</option>
                    </select>
                </div>

                <!-- Stay Dates Information -->
                <div class="post-event-modal-dates">
                    <div class="post-event-modal-dates-row">
                        <span class="post-event-modal-dates-label">Check-in Date:</span>
                        <span class="post-event-modal-dates-value">24/08/2025</span>
                    </div>
                    <div class="post-event-modal-dates-row">
                        <span class="post-event-modal-dates-label">Check-out Date:</span>
                        <span class="post-event-modal-dates-value" x-text="calculateCheckoutDate()"></span>
                    </div>
                    <!-- Removed note about August 24 -->
                </div>

                <!-- Notice for Double Room -->
                <div class="post-event-modal-notice" x-show="roomType === 'double'">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Important:</strong> When booking a Double Room, the price is calculated per person.
                        <p class="mt-2 mb-0">Since this room is for 2 people, the system will automatically charge for both guests (<strong x-text="nights"></strong> nights × 2 persons = <strong x-text="nights * 2"></strong> nights total).</p>
                        <p class="mt-2 mb-0">If you are booking alone, please make sure you will be sharing the room with someone, or choose a Single Room instead.</p>
                    </div>
                </div>
            </div>
            <div class="post-event-modal-footer">
                <button onclick="closePostEventModal()" class="post-event-modal-btn post-event-modal-btn-secondary">
                    Cancel
                </button>
                <button @click="savePreferences()" class="post-event-modal-btn post-event-modal-btn-primary">
                    <i class="fas fa-check me-2"></i> Save Preferences
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function postEventAccommodation() {
    return {
        showModal: false,
        nights: 1,
        roomType: 'single',

        initModal() {
            console.log('Initializing Post-Event modal');

            // Load saved preferences if available
            const savedNights = sessionStorage.getItem('postEventNights');
            const savedRoomType = sessionStorage.getItem('postEventRoomType');

            if (savedNights) this.nights = parseInt(savedNights);
            if (savedRoomType) this.roomType = savedRoomType;

            // Always apply preferences to buttons
            this.applyPreferencesToButtons();

            // Don't force modal to show on page load
            // We'll let the user click the "Change Stay Settings" button instead

            // Listen for modal visibility changes
            this.$watch('showModal', value => {
                if (value) {
                    console.log('Modal is now visible');
                    document.querySelector('.post-event-modal-overlay').style.display = 'flex';
                }
            });
        },

        closeModal() {
            this.showModal = false;
            document.querySelector('.post-event-modal-overlay').style.display = 'none';
        },

        incrementNights() {
            console.log('Incrementing nights');
            this.nights++;
            document.querySelector('.nights-count').textContent = this.nights;
            this.updateNightsDisplay();
        },

        decrementNights() {
            console.log('Decrementing nights');
            if (this.nights > 1) {
                this.nights--;
                document.querySelector('.nights-count').textContent = this.nights;
                this.updateNightsDisplay();
            }
        },

        updateNightsDisplay() {
            console.log('Updating nights display');

            // Update nights label
            const nightsLabel = document.querySelector('.nights-label');
            if (nightsLabel) {
                nightsLabel.textContent = this.nights === 1 ? 'Night' : 'Nights';
            }

            // Update checkout date display
            const checkoutDateElement = document.querySelector('.post-event-modal-dates-value:nth-child(2)');
            if (checkoutDateElement) {
                checkoutDateElement.textContent = this.calculateCheckoutDate();
                console.log('Updated checkout date to:', this.calculateCheckoutDate());
            }

            // Update double room calculation if visible
            if (this.roomType === 'double') {
                const doubleRoomElements = document.querySelectorAll('strong[x-text="nights"]');
                doubleRoomElements.forEach(el => {
                    el.textContent = this.nights;
                });

                const totalNightsElements = document.querySelectorAll('strong[x-text="nights * 2"]');
                totalNightsElements.forEach(el => {
                    el.textContent = this.nights * 2;
                });

                console.log('Updated double room calculations');
            }
        },

        calculateCheckoutDate() {
            // Base date is 24/08/2025 (check-in date)
            const baseDate = new Date(2025, 7, 24); // Month is 0-indexed, so 7 = August

            // Calculate checkout date by adding nights to base date
            // For 1 night: Check-in on 24/08, stay night of 24-25, check out on 25/08
            // For 2 nights: Check-in on 24/08, stay nights of 24-25 and 25-26, check out on 26/08
            const checkoutDate = new Date(baseDate);
            checkoutDate.setDate(baseDate.getDate() + this.nights);

            // Format the date as DD/MM/YYYY
            const day = String(checkoutDate.getDate()).padStart(2, '0');
            const month = String(checkoutDate.getMonth() + 1).padStart(2, '0');
            const year = checkoutDate.getFullYear();

            return `${day}/${month}/${year}`;
        },

        savePreferences() {
            console.log('Alpine savePreferences called');

            try {
                // Save preferences to session storage
                sessionStorage.setItem('postEventNights', this.nights.toString());
                sessionStorage.setItem('postEventRoomType', this.roomType);
                console.log('Saved to session storage:', { nights: this.nights, roomType: this.roomType });

                // Apply preferences to buttons in Alpine component
                this.applyPreferencesToButtons();

                // Call global functions to ensure everything is updated
                if (typeof updatePostEventAddToCartButtons === 'function') {
                    updatePostEventAddToCartButtons(this.nights, this.roomType);
                }

                if (typeof applyPostEventRoomTypePreferences === 'function') {
                    applyPostEventRoomTypePreferences();
                }

                // Close the modal
                this.closeModal();
            } catch (error) {
                console.error('Error saving preferences:', error);
            }
        },

        applyPreferencesToButtons() {
            // Get all add to cart buttons
            const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

            // Update all product variations based on room type preference
            const productVariationContainers = document.querySelectorAll('.accommodation-product-variations');

            productVariationContainers.forEach(container => {
                const productId = container.getAttribute('data-product-id');
                const variations = container.querySelectorAll('.accommodation-product-variation');

                let singleRoomVariation = null;
                let doubleRoomVariation = null;

                // Find single and double room variations
                variations.forEach(variation => {
                    const radioInput = variation.querySelector('input[type=radio]');
                    const variationName = radioInput.getAttribute('data-variation-name');

                    if (variationName.toLowerCase().includes('single')) {
                        singleRoomVariation = variation;
                    } else if (variationName.toLowerCase().includes('double')) {
                        doubleRoomVariation = variation;
                    }
                });

                // Apply room type preference
                if (singleRoomVariation && doubleRoomVariation) {
                    const singleRadio = singleRoomVariation.querySelector('input[type=radio]');
                    const doubleRadio = doubleRoomVariation.querySelector('input[type=radio]');

                    if (this.roomType === 'double') {
                        // Select double room variation
                        doubleRadio.checked = true;

                        // Disable single room variation
                        singleRoomVariation.classList.add('variation-disabled');
                        singleRadio.disabled = true;

                        // Enable double room variation
                        doubleRoomVariation.classList.remove('variation-disabled');
                        doubleRadio.disabled = false;
                    } else {
                        // When single room is selected
                        // Enable single room variation
                        singleRoomVariation.classList.remove('variation-disabled');
                        singleRadio.disabled = false;

                        // Disable double room variation
                        doubleRoomVariation.classList.add('variation-disabled');
                        doubleRadio.disabled = true;

                        // Default to single room
                        singleRadio.checked = true;
                    }
                }
            });

            // Update button text to reflect preferences
            addToCartButtons.forEach(button => {
                const buttonText = this.roomType === 'double'
                    ? `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${this.nights} nights × 2 people)`
                    : `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${this.nights} nights)`;

                button.innerHTML = buttonText;

                // Update quantity attribute
                const quantity = this.roomType === 'double' ? this.nights * 2 : this.nights;
                button.setAttribute('data-quantity', quantity);
            });
        }
    };
}
</script>

<!-- Script for modal functionality -->
<script>
// Function to show the Post-Event modal
function showPostEventModal(event) {
    event.preventDefault();
    const modalElement = document.querySelector('.post-event-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'flex';
        console.log('Modal displayed via direct function call');
    }
}

// Function to close the Post-Event modal
function closePostEventModal() {
    const modalElement = document.querySelector('.post-event-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'none';
        console.log('Modal hidden via direct function call');
    }
}

// Apply room type preferences - global function
function applyPostEventRoomTypePreferences() {
    try {
        console.log('Applying post-event room type preferences');
        const savedRoomType = sessionStorage.getItem('postEventRoomType') || 'single';
        console.log('Saved room type:', savedRoomType);

        // Get saved nights
        const savedNights = parseInt(sessionStorage.getItem('postEventNights')) || 1;
        console.log('Saved nights:', savedNights);

        // Update Add to Cart buttons first
        updatePostEventAddToCartButtons(savedNights, savedRoomType);

        // Update all product variations based on room type preference
        const productVariationContainers = document.querySelectorAll('.accommodation-product-variations');

        productVariationContainers.forEach(container => {
            const productId = container.getAttribute('data-product-id');
            const variations = container.querySelectorAll('.accommodation-product-variation');

            let singleRoomVariation = null;
            let doubleRoomVariation = null;

            // Find single and double room variations
            variations.forEach(variation => {
                const radioInput = variation.querySelector('input[type=radio]');
                const variationName = radioInput.getAttribute('data-variation-name');

                if (variationName.toLowerCase().includes('single')) {
                    singleRoomVariation = variation;
                } else if (variationName.toLowerCase().includes('double')) {
                    doubleRoomVariation = variation;
                }
            });

            // Apply room type preference
            if (singleRoomVariation && doubleRoomVariation) {
                const singleRadio = singleRoomVariation.querySelector('input[type=radio]');
                const doubleRadio = doubleRoomVariation.querySelector('input[type=radio]');

                if (savedRoomType === 'double') {
                    // Select double room variation
                    doubleRadio.checked = true;

                    // Disable single room variation
                    singleRoomVariation.classList.add('variation-disabled');
                    singleRadio.disabled = true;

                    // Enable double room variation
                    doubleRoomVariation.classList.remove('variation-disabled');
                    doubleRadio.disabled = false;
                } else {
                    // When single room is selected
                    // Enable single room variation
                    singleRoomVariation.classList.remove('variation-disabled');
                    singleRadio.disabled = false;

                    // Disable double room variation
                    doubleRoomVariation.classList.add('variation-disabled');
                    doubleRadio.disabled = true;

                    // Default to single room
                    singleRadio.checked = true;
                }
            }
        });
    } catch (error) {
        console.error('Error in applyPostEventRoomTypePreferences:', error);
    }
}

// Function to update Add to Cart buttons with new nights value
function updatePostEventAddToCartButtons(nights, roomType) {
    console.log('Updating Add to Cart buttons with:', { nights, roomType });
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    console.log('Found buttons:', addToCartButtons.length);

    addToCartButtons.forEach(button => {
        // Skip buttons that are already in "Added to Cart" state
        if (button.disabled) {
            console.log('Skipping disabled button');
            return;
        }

        const buttonText = roomType === 'double'
            ? `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${nights} nights × 2 people)`
            : `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${nights} nights)`;

        console.log('Setting button text to:', buttonText);
        button.innerHTML = buttonText;

        // Update quantity attribute
        const quantity = roomType === 'double' ? nights * 2 : nights;
        button.setAttribute('data-quantity', quantity);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for Alpine.js');

    // Check if Alpine.js is loaded
    if (typeof Alpine === 'undefined') {
        console.log('Alpine.js not loaded, loading it manually');

        // Load Alpine.js manually if it's not loaded
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js';
        script.defer = true;
        document.head.appendChild(script);

        script.onload = function() {
            console.log('Alpine.js loaded manually');

            // Initialize Alpine.js
            Alpine.start();
        };
    }

    // Don't force modal to show on page load
    // We'll let the user click the "Change Stay Settings" button instead

    // Apply preferences on page load
    applyPostEventRoomTypePreferences();

    // Add to Cart Confirmation Modal Functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    const confirmModal = document.getElementById('postEventCartConfirmModal');

    // Get saved preferences
    const getNights = function() {
        const savedNights = sessionStorage.getItem('postEventNights');
        return savedNights ? parseInt(savedNights) : 1;
    };

    const getRoomType = function() {
        const savedRoomType = sessionStorage.getItem('postEventRoomType');
        return savedRoomType || 'single';
    };

    // Add click event to all Add to Cart buttons
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');
            const productType = this.getAttribute('data-product-type');

            // Set hotel name in confirmation modal
            document.getElementById('postEventConfirmProductName').textContent = productName;

            // Get current nights value from session storage
            const nights = getNights();

            // Set nights in confirmation modal
            document.getElementById('postEventConfirmNights').textContent = nights + (nights === 1 ? ' night' : ' nights');

            // Calculate and display check-out date
            const calculateCheckoutDate = function(nights) {
                // Base date is 24/08/2025 (check-in date)
                const baseDate = new Date(2025, 7, 24); // Month is 0-indexed, so 7 = August

                // Calculate check-out date by adding nights to base date
                const checkoutDate = new Date(baseDate);
                checkoutDate.setDate(baseDate.getDate() + nights);

                // Format the date as DD/MM/YYYY
                const day = String(checkoutDate.getDate()).padStart(2, '0');
                const month = String(checkoutDate.getMonth() + 1).padStart(2, '0');
                const year = checkoutDate.getFullYear();

                return `${day}/${month}/${year}`;
            };

            // Set check-out date
            document.getElementById('postEventConfirmCheckoutDate').textContent = calculateCheckoutDate(nights);

            // Handle variation selection for variable products
            let variationId = null;
            let variationName = null;
            let isDoubleRoom = false;

            // Get user room type preference
            const userRoomType = getRoomType();
            const isUserDoubleRoom = userRoomType === 'double';

            if (productType === 'variable') {
                // Find the appropriate variation based on user preference
                let selectedVariation = null;

                if (isUserDoubleRoom) {
                    // If user prefers double room, find double room variation
                    selectedVariation = document.querySelector(`input[name="variation_${productId}"][data-is-double="true"]`);
                } else {
                    // Otherwise, get the selected variation
                    selectedVariation = document.querySelector(`input[name="variation_${productId}"]:checked`);
                }

                if (selectedVariation) {
                    variationId = selectedVariation.value;
                    variationName = selectedVariation.getAttribute('data-variation-name');
                    isDoubleRoom = selectedVariation.getAttribute('data-is-double') === 'true' || isUserDoubleRoom;

                    // Show room type in confirmation modal
                    document.getElementById('confirmRoomTypeRow').style.display = 'flex';
                    document.getElementById('confirmRoomType').textContent = variationName;
                } else {
                    // Hide room type if no variation selected
                    document.getElementById('confirmRoomTypeRow').style.display = 'none';
                }
            } else {
                // Hide room type for simple products
                document.getElementById('confirmRoomTypeRow').style.display = 'none';
            }

            // Hide double room notice (no longer needed)
            document.getElementById('confirmDoubleRoomRow').style.display = 'none';
            document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

            // Calculate and display the total price
            let productPrice = 0;

            if (productType === 'variable' && variationId) {
                // Get price from the selected variation
                const selectedVariation = document.querySelector(`input[value="${variationId}"]`);
                if (selectedVariation) {
                    productPrice = parseFloat(selectedVariation.getAttribute('data-price') || 0);
                }
            } else {
                // Get price from the button's data attribute
                productPrice = parseFloat(this.getAttribute('data-price') || 0);
            }

            // Calculate total price based on nights
            const totalPrice = productPrice * nights;

            // Format the price with currency symbol
            const formattedPrice = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'EUR',
                minimumFractionDigits: 2
            }).format(totalPrice);

            // Update the price display
            document.getElementById('confirmTotalPrice').textContent = formattedPrice;

            // Store data for add to cart action
            postEventProductId = productId;
            postEventVariationId = variationId;
            postEventIsDoubleRoom = isDoubleRoom || isUserDoubleRoom;
            postEventNights = nights;

            // Show confirmation modal
            confirmModal.style.display = 'block';
        });
    });
});
</script>

<style>
/* Post-Event Accommodation Modal Styles */
.post-event-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.post-event-modal {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 95%;
    max-width: 700px;
    overflow: hidden;
}

.post-event-modal-header {
    background-color: #36b1dc;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-event-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.post-event-modal-header h3 i {
    color: white;
    margin-right: 8px;
}

.post-event-modal-body {
    padding: 20px;
}

@media (min-width: 768px) {
    .post-event-modal-header {
        padding: 20px 25px;
    }

    .post-event-modal-header h3 {
        font-size: 22px;
    }

    .post-event-modal-body {
        padding: 25px 30px;
    }
}

.post-event-modal-form-group {
    margin-bottom: 20px;
}

.post-event-modal-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.post-event-modal-dates {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.post-event-modal-dates-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.post-event-modal-dates-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.post-event-modal-dates-label {
    font-weight: 600;
    color: #495057;
}

.post-event-modal-dates-value {
    color: #343a40;
    font-weight: 500;
}

.post-event-modal-dates-note {
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
}

.post-event-modal-dates-note i {
    color: #36b1dc;
    margin-right: 6px;
    font-size: 14px;
}

.post-event-modal-notice {
    background-color: #e8f4fd;
    color: #0c5460;
    padding: 16px;
    border-radius: 8px;
    margin-top: 15px;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid #36b1dc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.post-event-modal-notice i {
    margin-right: 12px;
    font-size: 18px;
    margin-top: 2px;
    color: #36b1dc;
}

.post-event-modal-notice p {
    line-height: 1.5;
}

.post-event-modal-notice strong {
    color: #0c5460;
    font-weight: 600;
}

.post-event-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
}

@media (min-width: 768px) {
    .post-event-modal-footer {
        padding: 20px 25px;
    }

    .post-event-modal-btn {
        min-width: 150px;
        padding: 12px 20px;
    }
}

.post-event-modal-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    width: 48%;
    color: white;
}

.post-event-modal-btn i {
    color: white;
    margin-right: 8px;
}

.post-event-modal-btn-primary {
    background-color: #36b1dc;
    color: white;
}

.post-event-modal-btn-primary:hover {
    background-color: #2d9cc3;
    color: white;
}

.post-event-modal-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.post-event-modal-btn-secondary:hover {
    background-color: #5a6268;
    color: white;
}

.accommodation-settings-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    margin-top: 10px;
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
    transition: all 0.2s ease;
}

.accommodation-settings-button:hover {
    background-color: #e9ecef;
}

/* Variation radio buttons */
.accommodation-variations-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.accommodation-variation-radio-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
    width: 100%;
}

.accommodation-variation-radio-label:hover {
    background-color: #f8f9fa;
}

.accommodation-variation-radio-label input[type="radio"] {
    margin-right: 10px;
}

.accommodation-product-star-description {
    font-size: 14px;
    color: #6c757d;
    margin-left: 8px;
}

.accommodation-product-short-description {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
    max-height: 80px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

/* Disabled variation styles */
.variation-disabled {
    opacity: 0.5;
    pointer-events: none;
}

.variation-disabled input[type="radio"] {
    opacity: 0.5;
}

/* Loading indicator for mini-cart */
.dashboard-mini-cart.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.dashboard-mini-cart.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #36b1dc;
    border-top-color: transparent;
    border-radius: 50%;
    animation: mini-cart-spinner 0.8s linear infinite;
}

@keyframes mini-cart-spinner {
    to {
        transform: rotate(360deg);
    }
}
</style>
