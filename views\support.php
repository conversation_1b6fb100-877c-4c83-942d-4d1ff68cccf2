<?php
/**
 * Support Tickets view for LCI 2025 Dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_email = $current_user->user_email;

// Define ticket categories
$ticket_categories = [
    'registration' => 'Registration Issues',
    'payment' => 'Payment & Invoices',
    'accommodation' => 'Accommodation',
    'tours' => 'Tours & Activities',
    'visa' => 'Visa & Travel',
    'other' => 'Other Questions'
];

// Get user tickets using our new support class
if (class_exists('LCI_Support')) {
    $tickets = LCI_Support::get_user_tickets($user_id);
} else {
    $tickets = [];
}
?>

<script>
// Immediately hide all elements with x-show attribute
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('[x-show]').forEach(function(el) {
        el.style.display = 'none';
    });
});
</script>

<div class="support-container" x-data="supportTickets">
    <!-- Header Section - 2025 UX/UI Style -->
    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 40px 20px; position: relative; overflow: hidden; border-radius: 0 0 30px 30px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2); margin-bottom: 40px;">
        <!-- Animated background elements -->
        <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%); border-radius: 50%; animation: pulse 8s infinite alternate;"></div>
        <div class="position-absolute" style="bottom: -30px; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%); border-radius: 50%; animation: float 12s infinite alternate;"></div>
        <div class="position-absolute" style="top: 20%; right: 20%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%); border-radius: 50%; animation: float 7s infinite alternate-reverse;"></div>

        <!-- Glassmorphism overlay -->
        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>

        <div class="container position-relative" style="z-index: 2;">
            <div class="d-flex align-items-center justify-content-center flex-column text-center">
                <!-- Modern 3D icon with shadow and glow -->
                <div style="background: rgba(255, 255, 255, 0.2); width: 80px; height: 80px; border-radius: 24px; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); transform: perspective(500px) rotateX(10deg); border: 1px solid rgba(255, 255, 255, 0.3);">
                    <div style="position: relative;">
                        <i class="fas fa-headset" style="color: white; font-size: 36px; filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));"></i>
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.8), transparent 70%); filter: blur(8px); opacity: 0.5; animation: glow 3s infinite alternate;"></div>
                    </div>
                </div>

                <!-- Typography with modern styling -->
                <h1 style="color: white; font-size: 32px; font-weight: 800; margin: 0; letter-spacing: -0.5px; text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">Support Center</h1>
                <p style="color: rgba(255, 255, 255, 0.9); font-size: 18px; margin: 0; margin-top: 8px; max-width: 600px; font-weight: 300; letter-spacing: 0.5px;">Get help with your LCI 2025 AGM registration, accommodation, or any other questions</p>

                <!-- Animated indicator -->
                <div class="mt-4" style="animation: bounce 2s infinite;">
                    <i class="fas fa-chevron-down" style="color: rgba(255, 255, 255, 0.7); font-size: 20px;"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div x-show="formSuccess" x-transition class="container mb-4">
        <div style="background: rgba(76, 175, 80, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px; border: 1px solid rgba(76, 175, 80, 0.2); display: flex; align-items: center;">
            <div style="background: rgba(76, 175, 80, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                <i class="fas fa-check-circle" style="color: #4caf50; font-size: 20px;"></i>
            </div>
            <div style="flex: 1;">
                <p style="color: #2c3e50; margin: 0;" x-text="successMessage"></p>
            </div>
            <button @click="formSuccess = false" style="background: none; border: none; color: #4caf50; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div x-show="formError" x-transition class="container mb-4">
        <div style="background: rgba(244, 67, 54, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px; border: 1px solid rgba(244, 67, 54, 0.2); display: flex; align-items: center;">
            <div style="background: rgba(244, 67, 54, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                <i class="fas fa-exclamation-circle" style="color: #f44336; font-size: 20px;"></i>
            </div>
            <div style="flex: 1;">
                <p style="color: #2c3e50; margin: 0;" x-text="errorMessage"></p>
            </div>
            <button @click="formError = false" style="background: none; border: none; color: #f44336; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <!-- My Tickets - 2025 UX/UI Style -->
                <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden;">
                    <!-- Decorative elements -->
                    <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                    <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                    <!-- Section header with 3D effect and Create New Ticket button -->
                    <div class="d-flex align-items-center justify-content-between mb-4 position-relative" style="z-index: 1;">
                        <div class="d-flex align-items-center">
                            <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                                <i class="fas fa-ticket-alt" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                            </div>
                            <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">My Tickets</h3>
                        </div>

                        <a href="<?php echo add_query_arg('tab', 'support-new-ticket'); ?>" class="btn btn-primary d-flex align-items-center create-ticket-btn" style="background: linear-gradient(135deg, #36b1dc, #2980b9); border: none; border-radius: 12px; padding: 10px 20px; color: white; font-weight: 600; text-decoration: none; box-shadow: 0 4px 10px rgba(54, 177, 220, 0.3);">
                            <i class="fas fa-plus-circle me-2"></i> Create New Ticket
                        </a>
                    </div>

                    <!-- No loading indicator -->

                    <!-- Empty state -->
                    <div x-show="tickets.length === 0" style="display: none;" class="text-center py-5 position-relative" style="z-index: 1;">
                        <div style="background: rgba(255, 255, 255, 0.5); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);">
                            <i class="fas fa-inbox" style="color: #a0aec0; font-size: 32px;"></i>
                        </div>
                        <p style="color: #64748b; font-size: 16px; margin: 0;">You don't have any support tickets yet.</p>
                    </div>

                    <!-- Ticket list -->
                    <div x-show="tickets.length > 0" style="display: none;" class="position-relative" style="z-index: 1;">
                        <div class="mb-3">
                            <p style="color: #64748b; font-size: 14px; margin: 0;">Select a ticket to view details and conversation</p>
                        </div>

                        <div class="ticket-list">
                            <template x-for="ticket in tickets" :key="ticket.id">
                                <a :href="'<?php echo add_query_arg('tab', 'support-ticket-details'); ?>&ticket_id=' + ticket.id" class="ticket-item" style="display: block; text-decoration: none; background: rgba(255, 255, 255, 0.5); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px; margin-bottom: 12px; border: 1px solid rgba(255, 255, 255, 0.8); cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02); transform: translateY(0);"
                                    onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 10px 20px rgba(0, 0, 0, 0.05)';"
                                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.02)';">

                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h4 style="color: #2c3e50; font-size: 16px; font-weight: 600; margin: 0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 70%;" x-text="ticket.subject"></h4>
                                        <span class="ticket-status" :class="getStatusClass(ticket.status)" x-text="getStatusText(ticket.status)" style="font-size: 12px; padding: 4px 8px; border-radius: 12px; font-weight: 500;"></span>
                                    </div>

                                    <p style="color: #64748b; font-size: 14px; margin: 0 0 8px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" x-text="ticket.message.substring(0, 100) + (ticket.message.length > 100 ? '...' : '')"></p>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <span style="color: #94a3b8; font-size: 12px;" x-text="'#' + ticket.ticket_id"></span>
                                        <span style="color: #94a3b8; font-size: 12px;" x-text="formatDate(ticket.created_at)"></span>
                                    </div>
                                </a>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

            <!-- FAQ Section - 2025 UX/UI Style -->
            <div class="mt-4" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden;">
                <!-- Decorative elements -->
                <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
                <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

                <!-- Section header with 3D effect -->
                <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
                    <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                        <i class="fas fa-question-circle" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                    </div>
                    <h3 style="color: #36b1dc; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Frequently Asked Questions</h3>
                </div>

                <!-- FAQ Accordion - Modern Style -->
                <div class="position-relative" style="z-index: 1;">
                    <!-- FAQ Item 1 -->
                    <div x-data="{ open: false }" class="mb-3">
                        <div @click="open = !open" class="faq-question" style="background: rgba(255, 255, 255, 0.5); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px 20px; border: 1px solid rgba(255, 255, 255, 0.8); cursor: pointer; transition: all 0.3s ease; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);"
                            :style="open ? 'background: rgba(54, 177, 220, 0.05); border-color: rgba(54, 177, 220, 0.2);' : ''">
                            <div class="d-flex align-items-center justify-content-between w-100">
                                <h4 class="faq-title" style="color: #2c3e50; font-size: 16px; font-weight: 600; margin: 0;">How do I update my registration details?</h4>
                                <div style="width: 24px; height: 24px; border-radius: 50%; background: rgba(54, 177, 220, 0.1); display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; flex-shrink: 0; margin-left: 10px;" :style="open ? 'transform: rotate(180deg);' : ''">
                                    <i class="fas fa-chevron-down" style="color: #36b1dc; font-size: 12px;"></i>
                                </div>
                            </div>
                        </div>
                        <div x-show="open" x-transition style="background: rgba(255, 255, 255, 0.5); border-radius: 0 0 16px 16px; padding: 20px; margin-top: -5px; border: 1px solid rgba(54, 177, 220, 0.1); border-top: none; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);">
                            <p style="color: #64748b; font-size: 15px; line-height: 1.6; margin: 0; text-align: justify;">
                                You can update most of your registration details in the <a href="<?php echo add_query_arg('tab', 'profile'); ?>" style="color: #36b1dc; text-decoration: none; font-weight: 500; border-bottom: 1px dashed #36b1dc;">Profile</a> section. For specific changes to your registration type or other details, please create a support ticket.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div x-data="{ open: false }" class="mb-3">
                        <div @click="open = !open" class="faq-question" style="background: rgba(255, 255, 255, 0.5); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px 20px; border: 1px solid rgba(255, 255, 255, 0.8); cursor: pointer; transition: all 0.3s ease; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);"
                            :style="open ? 'background: rgba(54, 177, 220, 0.05); border-color: rgba(54, 177, 220, 0.2);' : ''">
                            <div class="d-flex align-items-center justify-content-between w-100">
                                <h4 class="faq-title" style="color: #2c3e50; font-size: 16px; font-weight: 600; margin: 0;">How can I get an invoice for my payment?</h4>
                                <div style="width: 24px; height: 24px; border-radius: 50%; background: rgba(54, 177, 220, 0.1); display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; flex-shrink: 0; margin-left: 10px;" :style="open ? 'transform: rotate(180deg);' : ''">
                                    <i class="fas fa-chevron-down" style="color: #36b1dc; font-size: 12px;"></i>
                                </div>
                            </div>
                        </div>
                        <div x-show="open" x-transition style="background: rgba(255, 255, 255, 0.5); border-radius: 0 0 16px 16px; padding: 20px; margin-top: -5px; border: 1px solid rgba(54, 177, 220, 0.1); border-top: none; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);">
                            <p style="color: #64748b; font-size: 15px; line-height: 1.6; margin: 0; text-align: justify;">
                                Invoices are automatically generated after your payment is processed. You can find all your invoices in the <a href="<?php echo add_query_arg('tab', 'my-invoices'); ?>" style="color: #36b1dc; text-decoration: none; font-weight: 500; border-bottom: 1px dashed #36b1dc;">My Invoices</a> section. If you need a specific invoice format, please create a support ticket.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div x-data="{ open: false }" class="mb-3">
                        <div @click="open = !open" class="faq-question" style="background: rgba(255, 255, 255, 0.5); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px 20px; border: 1px solid rgba(255, 255, 255, 0.8); cursor: pointer; transition: all 0.3s ease; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);"
                            :style="open ? 'background: rgba(54, 177, 220, 0.05); border-color: rgba(54, 177, 220, 0.2);' : ''">
                            <div class="d-flex align-items-center justify-content-between w-100">
                                <h4 class="faq-title" style="color: #2c3e50; font-size: 16px; font-weight: 600; margin: 0;">What should I do if I need to cancel my registration?</h4>
                                <div style="width: 24px; height: 24px; border-radius: 50%; background: rgba(54, 177, 220, 0.1); display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; flex-shrink: 0; margin-left: 10px;" :style="open ? 'transform: rotate(180deg);' : ''">
                                    <i class="fas fa-chevron-down" style="color: #36b1dc; font-size: 12px;"></i>
                                </div>
                            </div>
                        </div>
                        <div x-show="open" x-transition style="background: rgba(255, 255, 255, 0.5); border-radius: 0 0 16px 16px; padding: 20px; margin-top: -5px; border: 1px solid rgba(54, 177, 220, 0.1); border-top: none; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);">
                            <p style="color: #64748b; font-size: 15px; line-height: 1.6; margin: 0; text-align: justify;">
                                If you need to cancel your registration, please create a support ticket as soon as possible. Refund policies vary depending on the date of cancellation and the type of registration.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div x-data="{ open: false }" class="mb-3">
                        <div @click="open = !open" class="faq-question" style="background: rgba(255, 255, 255, 0.5); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 16px 20px; border: 1px solid rgba(255, 255, 255, 0.8); cursor: pointer; transition: all 0.3s ease; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);"
                            :style="open ? 'background: rgba(54, 177, 220, 0.05); border-color: rgba(54, 177, 220, 0.2);' : ''">
                            <div class="d-flex align-items-center justify-content-between w-100">
                                <h4 class="faq-title" style="color: #2c3e50; font-size: 16px; font-weight: 600; margin: 0;">How do I book accommodation for the event?</h4>
                                <div style="width: 24px; height: 24px; border-radius: 50%; background: rgba(54, 177, 220, 0.1); display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; flex-shrink: 0; margin-left: 10px;" :style="open ? 'transform: rotate(180deg);' : ''">
                                    <i class="fas fa-chevron-down" style="color: #36b1dc; font-size: 12px;"></i>
                                </div>
                            </div>
                        </div>
                        <div x-show="open" x-transition style="background: rgba(255, 255, 255, 0.5); border-radius: 0 0 16px 16px; padding: 20px; margin-top: -5px; border: 1px solid rgba(54, 177, 220, 0.1); border-top: none; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);">
                            <p style="color: #64748b; font-size: 15px; line-height: 1.6; margin: 0; text-align: justify;">
                                You can book accommodation through the <a href="<?php echo add_query_arg('tab', 'accommodation'); ?>" style="color: #36b1dc; text-decoration: none; font-weight: 500; border-bottom: 1px dashed #36b1dc;">Accommodation</a> section. Various options are available for both the main event and pre/post-event stays. If you have specific requirements, please create a support ticket.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>

<!-- Add animations -->
<style>
    @keyframes pulse {
        0% { transform: scale(1); opacity: 0.5; }
        100% { transform: scale(1.2); opacity: 0.2; }
    }
    @keyframes float {
        0% { transform: translateY(0) translateX(0); }
        50% { transform: translateY(-20px) translateX(10px); }
        100% { transform: translateY(10px) translateX(-10px); }
    }
    @keyframes glow {
        0% { opacity: 0.3; }
        100% { opacity: 0.7; }
    }
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    /* Mobile Responsive Styles */
    @media (max-width: 767px) {
        /* Reduce padding on all containers */
        .container {
            padding-left: 10px !important;
            padding-right: 10px !important;
        }

        /* Make cards take full width with minimal padding */
        [style*="border-radius: 30px"],
        [style*="border-radius: 24px"] {
            padding: 20px !important;
            border-radius: 20px !important;
            margin-left: -5px !important;
            margin-right: -5px !important;
            width: calc(100% + 10px) !important;
        }

        /* Make form inputs full width */
        input[type="text"],
        input[type="email"],
        input[type="file"],
        select,
        textarea {
            width: 100% !important;
            max-width: 100% !important;
            box-sizing: border-box !important;
        }

        /* Adjust button width */
        button[type="submit"] {
            width: 100% !important;
            min-width: 100% !important;
        }

        /* Adjust spacing */
        .mb-5 {
            margin-bottom: 15px !important;
        }

        .py-5 {
            padding-top: 15px !important;
            padding-bottom: 15px !important;
        }

        /* Make header more compact */
        h1 {
            font-size: 24px !important;
        }

        h2 {
            font-size: 20px !important;
        }

        h3 {
            font-size: 18px !important;
        }

        p {
            font-size: 14px !important;
        }

        /* Adjust form field spacing */
        .position-relative {
            margin-bottom: 15px !important;
        }

        /* Center align text on mobile except for FAQ content */
        h4:not(.faq-title), p:not(.faq-question + div p) {
            text-align: center !important;
        }

        /* Fix FAQ question alignment on mobile */
        .faq-question {
            align-items: center !important;
        }

        .faq-question .d-flex {
            align-items: center !important;
        }

        .faq-title {
            text-align: left !important;
            font-size: 14px !important;
        }

        /* Remove margin for admin replies on mobile */
        .ms-5 {
            margin-left: 0 !important;
        }

        /* Make create ticket button full width on mobile */
        .create-ticket-btn {
            width: auto;
        }

        /* Adjust header layout on mobile */
        .d-flex.align-items-center.justify-content-between {
            flex-direction: column !important;
            gap: 15px;
        }

        .create-ticket-btn {
            width: 100% !important;
            justify-content: center !important;
        }
    }
</style>

<style>
    [x-cloak] {
        display: none !important;
    }

    .support-container h2 {
        color: var(--primary-color, #36b1dc);
        font-weight: 600;
    }

    .support-container .card-header {
        font-weight: 600;
    }

    .support-container .btn-primary {
        background-color: var(--primary-color, #36b1dc);
        border-color: var(--primary-color, #36b1dc);
    }

    .support-container .btn-primary:hover {
        background-color: #2a8fb3;
        border-color: #2a8fb3;
    }

    .support-container .accordion-button:not(.collapsed) {
        background-color: rgba(54, 177, 220, 0.1);
        color: var(--primary-color, #36b1dc);
    }

    .support-container .accordion-button:focus {
        border-color: rgba(54, 177, 220, 0.5);
        box-shadow: 0 0 0 0.25rem rgba(54, 177, 220, 0.25);
    }

    .text-primary {
        color: var(--primary-color, #36b1dc) !important;
    }
</style>

<script>
// Define the Alpine.js component
document.addEventListener('alpine:init', function() {
    // Make sure lciSupport is available
    if (typeof window.lciSupport === 'undefined') {
        console.error('lciSupport is not defined');
        window.lciSupport = {
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('lci_support_nonce'); ?>',
            messages: {
                ticketSubmitted: '<?php echo esc_js(__('Your ticket has been submitted successfully.', 'lci-2025-dashboard')); ?>',
                ticketError: '<?php echo esc_js(__('There was an error submitting your ticket. Please try again.', 'lci-2025-dashboard')); ?>',
                replySubmitted: '<?php echo esc_js(__('Your reply has been submitted successfully.', 'lci-2025-dashboard')); ?>',
                replyError: '<?php echo esc_js(__('There was an error submitting your reply. Please try again.', 'lci-2025-dashboard')); ?>'
            }
        };
    }

    Alpine.data('supportTickets', function() {
        return {
            tickets: [],
            isSubmitting: false,
            formSuccess: false,
            formError: false,
            errorMessage: '',
            successMessage: '',

            init() {
                // Load tickets immediately
                this.loadTickets();
            },

            loadTickets() {

                fetch(window.lciSupport.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'lci_get_tickets',
                        nonce: window.lciSupport.nonce
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.tickets = data.data.tickets;
                    } else {
                        this.showError(data.data.message || 'Error loading tickets');
                    }
                })
                .catch(error => {
                    this.showError('Error loading tickets. Please try again.');
                });
            },





            submitNewTicket() {
                const form = document.getElementById('support-ticket-form');
                if (!form) return;

                // Form validation
                const subject = document.getElementById('ticket_subject').value;
                const category = document.getElementById('ticket_category').value;
                const message = document.getElementById('ticket_message').value;

                if (!subject || !category || !message) {
                    this.showError('Please fill in all required fields.');
                    return;
                }

                // File size validation
                const fileInput = document.getElementById('ticket_attachment');
                if (fileInput && fileInput.files.length > 0) {
                    const fileSize = fileInput.files[0].size; // in bytes
                    const maxSize = 5 * 1024 * 1024; // 5MB

                    if (fileSize > maxSize) {
                        this.showError('File size exceeds the maximum limit of 5MB.');
                        return;
                    }
                }

                this.isSubmitting = true;

                const formData = new FormData(form);
                formData.append('action', 'lci_submit_ticket');
                formData.append('nonce', window.lciSupport.nonce);

                fetch(window.lciSupport.ajaxUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.showSuccess(window.lciSupport.messages.ticketSubmitted || 'Ticket submitted successfully');
                        form.reset();
                        this.loadTickets();
                    } else {
                        this.showError(data.data.message || window.lciSupport.messages.ticketError || 'Error submitting ticket');
                    }
                    this.isSubmitting = false;
                })
                .catch(error => {
                    this.showError(window.lciSupport.messages.ticketError || 'Error submitting ticket. Please try again.');
                    this.isSubmitting = false;
                });
            },

            showSuccess(message) {
                this.successMessage = message;
                this.formSuccess = true;
                this.formError = false;

                setTimeout(() => {
                    this.formSuccess = false;
                }, 5000);
            },

            showError(message) {
                this.errorMessage = message;
                this.formError = true;
                this.formSuccess = false;

                setTimeout(() => {
                    this.formError = false;
                }, 5000);
            },

            getStatusClass(status) {
                if (!status) return 'bg-gray-100 text-gray-800';

                switch (status) {
                    case 'open': return 'bg-yellow-100 text-yellow-800';
                    case 'in-progress': return 'bg-blue-100 text-blue-800';
                    case 'resolved': return 'bg-green-100 text-green-800';
                    case 'closed': return 'bg-gray-100 text-gray-800';
                    default: return 'bg-gray-100 text-gray-800';
                }
            },

            getStatusText(status) {
                if (!status) return '';

                switch (status) {
                    case 'open': return 'Open';
                    case 'in-progress': return 'In Progress';
                    case 'resolved': return 'Resolved';
                    case 'closed': return 'Closed';
                    default: return status.charAt(0).toUpperCase() + status.slice(1);
                }
            },

            formatDate(dateString) {
                if (!dateString) return '';

                try {
                    const date = new Date(dateString);
                    return date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch (error) {
                    console.error('Error formatting date:', error);
                    return '';
                }
            }
        };
    });
});
</script>
