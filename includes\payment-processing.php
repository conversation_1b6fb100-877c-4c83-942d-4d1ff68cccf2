<?php
/**
 * Payment processing functions for LCI 2025 Dashboard
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get available WooCommerce payment gateways
 *
 * @return array Available payment gateways
 */
function lci_get_available_payment_gateways() {
    // Ensure WooCommerce is active
    if (!function_exists('WC')) {
        return array();
    }

    // Get available payment gateways
    $available_gateways = WC()->payment_gateways->get_available_payment_gateways();

    return $available_gateways;
}

/**
 * Create a custom order programmatically
 *
 * @param int $user_id User ID
 * @param array $products Products to add to the order
 * @param array $billing_data Billing data
 * @param array $shipping_data Shipping data
 * @return WC_Order Created order
 */
function lci_create_custom_order($user_id, $products, $billing_data = array(), $shipping_data = array()) {
    // Create a new order
    $order = wc_create_order(array('customer_id' => $user_id));

    // Add products to the order
    foreach ($products as $product) {
        $product_obj = wc_get_product($product['product_id']);
        if ($product_obj) {
            $order->add_product(
                $product_obj,
                $product['quantity'],
                array(
                    'subtotal' => $product['subtotal'],
                    'total' => $product['total']
                )
            );
        }
    }

    // Set billing info
    if (!empty($billing_data)) {
        $order->set_address($billing_data, 'billing');
    }

    // Set shipping info
    if (!empty($shipping_data)) {
        $order->set_address($shipping_data, 'shipping');
    }

    // Calculate totals
    $order->calculate_totals();

    // Save the order
    $order->save();

    return $order;
}

/**
 * Process payment through WooCommerce gateway
 *
 * @param int $order_id Order ID
 * @param string $payment_method Payment method ID
 * @param array $additional_data Additional data to pass to the payment gateway
 * @return array Payment result
 */
function lci_process_custom_payment($order_id, $payment_method, $additional_data = array()) {
    // Get the order
    $order = wc_get_order($order_id);
    if (!$order) {
        return array(
            'result' => 'failure',
            'message' => 'Invalid order'
        );
    }

    // Get available payment gateways
    $available_gateways = WC()->payment_gateways->get_available_payment_gateways();

    // Check if the selected payment method is available
    if (isset($available_gateways[$payment_method])) {
        // Get the payment gateway
        $gateway = $available_gateways[$payment_method];

        // Save the order
        $order->save();

        // Allow plugins to modify payment arguments
        $payment_args = apply_filters('lci_process_payment_args', [], $order_id, $payment_method);

        // Allow plugins to prepare for payment processing
        do_action('lci_before_process_payment', $order, $payment_method, $payment_args);

        // Allow for special handling of payment gateways
        do_action('lci_before_gateway_process_payment', $gateway, $order, $payment_method, $additional_data);

        // Process the payment
        $result = $gateway->process_payment($order_id);

        // Log the result
        error_log('Payment processing result for order ' . $order_id . ': ' . print_r($result, true));

        // Allow plugins to handle the payment response
        $result = apply_filters('lci_process_payment_result', $result, $order, $payment_method);

        // Allow plugins to perform actions after payment processing
        do_action('lci_after_process_payment', $result, $order, $payment_method);

        // Return the result
        return $result;
    }

    return array(
        'result' => 'failure',
        'message' => 'Payment method not available'
    );
}

/**
 * Get user billing and shipping information
 *
 * @param int $user_id User ID
 * @return array User data
 */
function lci_get_user_data($user_id) {
    $user = get_userdata($user_id);
    if (!$user) {
        return array();
    }

    // Get billing data
    $billing_data = array(
        'first_name' => get_user_meta($user_id, 'billing_first_name', true),
        'last_name' => get_user_meta($user_id, 'billing_last_name', true),
        'company' => get_user_meta($user_id, 'billing_company', true),
        'address_1' => get_user_meta($user_id, 'billing_address_1', true),
        'address_2' => get_user_meta($user_id, 'billing_address_2', true),
        'city' => get_user_meta($user_id, 'billing_city', true),
        'state' => get_user_meta($user_id, 'billing_state', true),
        'postcode' => get_user_meta($user_id, 'billing_postcode', true),
        'country' => get_user_meta($user_id, 'billing_country', true),
        'email' => $user->user_email,
        'phone' => get_user_meta($user_id, 'billing_phone', true),
    );

    // Get shipping data
    $shipping_data = array(
        'first_name' => get_user_meta($user_id, 'shipping_first_name', true),
        'last_name' => get_user_meta($user_id, 'shipping_last_name', true),
        'company' => get_user_meta($user_id, 'shipping_company', true),
        'address_1' => get_user_meta($user_id, 'shipping_address_1', true),
        'address_2' => get_user_meta($user_id, 'shipping_address_2', true),
        'city' => get_user_meta($user_id, 'shipping_city', true),
        'state' => get_user_meta($user_id, 'shipping_state', true),
        'postcode' => get_user_meta($user_id, 'shipping_postcode', true),
        'country' => get_user_meta($user_id, 'shipping_country', true),
    );

    // If shipping fields are empty, use billing data
    foreach ($shipping_data as $key => $value) {
        if (empty($value) && !empty($billing_data[$key])) {
            $shipping_data[$key] = $billing_data[$key];
        }
    }

    return array(
        'billing' => $billing_data,
        'shipping' => $shipping_data
    );
}

/**
 * AJAX handler for processing payments
 */
function lci_ajax_process_payment() {
    // Verify nonce
    check_ajax_referer('lci_payment_nonce', 'security');

    // Get the current user
    $user_id = get_current_user_id();

    if (!$user_id) {
        wp_send_json_error(array('message' => 'User not logged in'));
        return;
    }

    // Get form data
    $payment_method = isset($_POST['payment_method']) ? sanitize_text_field($_POST['payment_method']) : '';

    // Get additional parameters from the request
    $additional_data = apply_filters('lci_payment_additional_data', [], $_POST);

    // Check if we're processing a single product or the cart
    if (isset($_POST['product_id']) && !empty($_POST['product_id'])) {
        // Single product checkout
        $product_id = intval($_POST['product_id']);
        $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;

        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(array('message' => 'Invalid product'));
            return;
        }

        // Create products array
        $products = array(
            array(
                'product_id' => $product_id,
                'quantity' => $quantity,
                'subtotal' => $product->get_price() * $quantity,
                'total' => $product->get_price() * $quantity,
            )
        );
    } else {
        // Cart checkout
        if (!function_exists('WC') || WC()->cart->is_empty()) {
            wp_send_json_error(array('message' => 'Cart is empty'));
            return;
        }

        // Get products from cart
        $products = array();
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $products[] = array(
                'product_id' => $product->get_id(),
                'quantity' => $cart_item['quantity'],
                'subtotal' => $cart_item['line_subtotal'],
                'total' => $cart_item['line_total'],
            );
        }
    }

    // Get user data
    $user_data = lci_get_user_data($user_id);

    // Create the order
    $order = lci_create_custom_order($user_id, $products, $user_data['billing'], $user_data['shipping']);

    // Allow plugins to add additional data for payment processing
    $additional_data = apply_filters('lci_payment_process_data', $additional_data, $order, $payment_method);

    // Process the payment
    $result = lci_process_custom_payment($order->get_id(), $payment_method, $additional_data);

    // Return the result
    if ($result['result'] === 'success') {
        // Clear cart if we're checking out the cart
        if (!isset($_POST['product_id']) || empty($_POST['product_id'])) {
            WC()->cart->empty_cart();
        }

        // Prepare response data
        $response_data = array(
            'redirect' => $result['redirect'],
            'order_id' => $order->get_id(),
            'message' => 'Payment processing initiated'
        );

        // Allow plugins to modify the response
        $response_data = apply_filters('lci_payment_success_response', $response_data, $result, $order, $payment_method);

        wp_send_json_success($response_data);
    } else {
        // Log the error
        error_log('Payment failed for order ' . $order->get_id() . ': ' . print_r($result, true));

        wp_send_json_error(array(
            'message' => isset($result['message']) ? $result['message'] : 'Payment failed'
        ));
    }
}
add_action('wp_ajax_lci_process_payment', 'lci_ajax_process_payment');
