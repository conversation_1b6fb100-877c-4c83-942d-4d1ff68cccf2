<?php
$current_user = wp_get_current_user();
$first_name = $current_user->first_name ?: explode(' ', $current_user->display_name)[0];
$tabs = [
    'my-registration' => 'My Registration',
    'accommodation'   => 'Accommodation',
    'tours'           => 'Tours',
    'transfers'       => 'Transfers',
    'flights'         => 'Flights',
    'regalia-shop'    => 'Regalia Shop',
    'payment'         => 'Payment',
    'my-invoices'     => 'My Invoices',
    'my-visa'         => 'My Visa',
    'support'         => 'Support',
    'profile'         => 'Profile',
    'logout'          => 'Log out',
];

$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'my-registration';
?>

<div class="container-fluid py-5">
  <div class="row">
    <!-- Sidebar -->
  <div class="col-md-3 mb-4">
  <div class="card shadow rounded-4 border-0">
    <div class="card-body">
      <!-- User Avatar and Welcome Message with UX2025 Styling -->
      <div class="d-flex flex-column align-items-center text-center mb-4">
        <?php
        $avatar_id = get_user_meta($current_user->ID, 'wp_user_avatar', true);
        $avatar_url = $avatar_id ? wp_get_attachment_url($avatar_id) : get_avatar_url($current_user->ID);
        $profile_url = add_query_arg('tab', 'profile');
        ?>
        <!-- Clickable avatar with UX2025 styling that redirects to profile tab -->
        <a href="<?php echo esc_url($profile_url); ?>" class="avatar-link position-relative" title="View Profile">
          <div class="avatar-container position-relative ux2025-avatar dashboard-avatar">
            <!-- Avatar image with border and shadow -->
            <div class="avatar-image-wrapper">
              <img src="<?php echo esc_url($avatar_url); ?>" class="rounded-circle mb-3" width="90" height="90" alt="User Avatar">
            </div>
            <!-- Decorative elements for UX2025 style -->
            <div class="avatar-decoration-circle"></div>
            <div class="avatar-decoration-dot"></div>
            <!-- Hover overlay with profile icon -->
            <div class="avatar-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center rounded-circle">
              <i class="fas fa-user text-white" style="font-size: 24px;"></i>
            </div>
          </div>
        </a>
        <h5 class="fw-bold" style="color: #36b1dc;">Welcome, <?php echo esc_html($first_name); ?></h5>
      </div>

      <!-- Toggle for mobile with UX2025 styling -->
      <button class="btn d-md-none mb-3 w-100 mobile-menu-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#lciSidebar" aria-expanded="false" aria-controls="lciSidebar" style="background-color: #36b1dc; color: white; border: none; border-radius: 10px; padding: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 1px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); transition: all 0.3s ease;">
        <span class="menu-icon-container d-inline-flex align-items-center">
          <i class="fas fa-bars me-2" style="color: white; transition: transform 0.3s ease;"></i>
          <span>Menu</span>
        </span>
        <span class="menu-close-container d-none align-items-center">
          <i class="fas fa-times me-2" style="color: white; transition: transform 0.3s ease;"></i>
          <span>Close Menu</span>
        </span>
      </button>

      <!-- Add JavaScript to toggle menu icon -->
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          const menuButton = document.querySelector('.mobile-menu-toggle');
          const menuIcon = document.querySelector('.menu-icon-container');
          const closeIcon = document.querySelector('.menu-close-container');
          const sidebarMenu = document.getElementById('lciSidebar');

          // Set initial state
          menuIcon.classList.add('d-inline-flex');
          menuIcon.classList.remove('d-none');
          closeIcon.classList.add('d-none');
          closeIcon.classList.remove('d-inline-flex');

          // Listen for Bootstrap collapse events
          sidebarMenu.addEventListener('show.bs.collapse', function() {
            // When menu is about to open
            menuIcon.classList.add('d-none');
            menuIcon.classList.remove('d-inline-flex');
            closeIcon.classList.add('d-inline-flex');
            closeIcon.classList.remove('d-none');
          });

          sidebarMenu.addEventListener('hide.bs.collapse', function() {
            // When menu is about to close
            menuIcon.classList.add('d-inline-flex');
            menuIcon.classList.remove('d-none');
            closeIcon.classList.add('d-none');
            closeIcon.classList.remove('d-inline-flex');
          });
        });
      </script>

      <div id="lciSidebar" class="collapse d-md-block">
        <ul class="nav flex-column gap-2 list-unstyled lci-menu">
          <?php
          $icon_map = [
            'my-registration' => 'fa-ticket-alt',
            'accommodation'   => 'fa-bed',
            'tours'           => 'fa-map-marked-alt',
            'transfers'       => 'fa-shuttle-van',
            'flights'         => 'fa-plane',
            'regalia-shop'    => 'fa-shopping-bag',
            'payment'         => 'fa-credit-card',
            'my-invoices'     => 'fa-file-invoice',
            'my-visa'         => 'fa-passport',
            'support'         => 'fa-headset',
            'profile'         => 'fa-user-circle',
            'logout'          => 'fa-sign-out-alt',
          ];

          foreach ($tabs as $slug => $label):
            $is_active = ($active_tab === $slug);
            $icon = isset($icon_map[$slug]) ? $icon_map[$slug] : 'fa-circle';
          ?>
            <li class="nav-item">
              <a class="nav-link d-flex align-items-center gap-2 <?php echo $is_active ? 'active text-white bg-ux2025-primary' : 'text-dark'; ?> rounded px-3 py-2 gap-2 list-unstyled"
                 href="<?php echo add_query_arg('tab', $slug); ?>" style="<?php echo $is_active ? 'background-color: #36b1dc; font-weight: 600;' : ''; ?>">
                <i class="fas <?php echo esc_attr($icon); ?>" style="<?php echo $is_active ? 'color: white;' : ''; ?>"></i>
                <span><?php echo esc_html($label); ?></span>
              </a>
            </li>
          <?php endforeach; ?>
        </ul>
      </div>
    </div>
  </div>
</div>
    <!-- Main Content -->
    <div class="col-md-9">
      <div class="card shadow-lg rounded-4 border-0">
        <div class="card-body">
          <?php do_action('lci_dashboard_before_content'); ?>
          <?php
          switch ($active_tab) {
            case 'accommodation-wizard-php':
              include LCI2025_PATH . 'views/accommodation-wizard-php.php';
              break;
            case 'accommodation-wizard':
              include LCI2025_PATH . 'views/accommodation-wizard.php';
              break;
            case 'accommodation':
              include LCI2025_PATH . 'views/accommodation.php';
              break;
            case 'accommodation-bucharest':
              include LCI2025_PATH . 'views/accommodation-bucharest.php';
              break;
            case 'accommodation-main-event':
              include LCI2025_PATH . 'views/accommodation-main-event.php';
              break;
            case 'accommodation-pre-event':
              include LCI2025_PATH . 'views/accommodation-pre-event.php';
              break;
            case 'accommodation-post-event':
              include LCI2025_PATH . 'views/accommodation-post-event.php';
              break;
            case 'tours':
              include LCI2025_PATH . 'views/tours.php';
              break;
            case 'transfers':
              include LCI2025_PATH . 'views/transfers.php';
              break;
            case 'flights':
              include LCI2025_PATH . 'views/flights.php';
              break;
            case 'regalia-shop':
              include LCI2025_PATH . 'views/regalia-shop.php';
              break;
            case 'payment':
              include LCI2025_PATH . 'views/payment.php';
              break;
            case 'my-invoices':
              include LCI2025_PATH . 'views/my-invoices.php';
              break;
            case 'my-visa':
              include LCI2025_PATH . 'views/my-visa.php';
              break;
            case 'generate-invitation':
              include LCI2025_PATH . 'views/generate-invitation.php';
              break;
            case 'support':
              include LCI2025_PATH . 'views/support.php';
              break;
            case 'support-new-ticket':
              include LCI2025_PATH . 'views/support-new-ticket.php';
              break;
            case 'support-ticket-details':
              include LCI2025_PATH . 'views/support-ticket-details.php';
              break;
            case 'profile':
              include LCI2025_PATH . 'views/profile.php';
              break;
            case 'logout':
              wp_logout();
              wp_redirect(home_url('/'));
              exit;
              break;
            case 'my-registration':
            default:
              include LCI2025_PATH . 'views/my-registration.php';
              break;
          }
          ?>
        </div> <!-- End of card-body -->
      </div> <!-- End of card -->
    </div> <!-- End of col-md-9 -->
  </div> <!-- End of row -->
</div> <!-- End of container-fluid -->