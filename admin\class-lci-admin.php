<?php
/**
 * LCI 2025 Dashboard Admin
 *
 * Handles admin functionality for the LCI 2025 Dashboard plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class LCI_Admin {
    /**
     * Initialize the admin functionality
     */
    public static function init() {
        // Add admin menu
        add_action('admin_menu', [__CLASS__, 'add_admin_menu']);

        // Register admin assets
        add_action('admin_enqueue_scripts', [__CLASS__, 'register_admin_assets']);

        // Add AJAX handlers
        add_action('wp_ajax_lci_sync_participant', [__CLASS__, 'ajax_sync_participant']);
        add_action('wp_ajax_lci_sync_all_participants', [__CLASS__, 'ajax_sync_all_participants']);
        add_action('wp_ajax_lci_update_participant', [__CLASS__, 'ajax_update_participant']);
        add_action('wp_ajax_lci_get_participants', [__CLASS__, 'ajax_get_participants']);
        add_action('wp_ajax_lci_get_tour_participants', [__CLASS__, 'ajax_get_tour_participants']);
        add_action('wp_ajax_lci_sync_tour_participants', [__CLASS__, 'ajax_sync_tour_participants']);
        add_action('wp_ajax_lci_clear_tour_table', [__CLASS__, 'ajax_clear_tour_table']);
        add_action('wp_ajax_lci_get_email_comparison', [__CLASS__, 'ajax_get_email_comparison']);

        // Add WooCommerce order hooks
        add_action('woocommerce_checkout_order_processed', [__CLASS__, 'sync_new_order'], 10, 3);
        add_action('woocommerce_order_status_changed', [__CLASS__, 'sync_order_status_changed'], 10, 4);
    }

    /**
     * Add admin menu items
     */
    public static function add_admin_menu() {
        // Main menu
        add_menu_page(
            'LCI 2025 Dashboard',
            'LCI 2025',
            'manage_options',
            'lci-dashboard',
            [__CLASS__, 'render_dashboard_page'],
            'dashicons-groups',
            30
        );

        // Dashboard submenu
        add_submenu_page(
            'lci-dashboard',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'lci-dashboard',
            [__CLASS__, 'render_dashboard_page']
        );

        // Participants submenu
        add_submenu_page(
            'lci-dashboard',
            'Participants',
            'Participants',
            'manage_options',
            'lci-participants',
            [__CLASS__, 'render_participants_page']
        );

        // Tours submenu
        add_submenu_page(
            'lci-dashboard',
            'Tours',
            'Tours',
            'manage_options',
            'lci-tours',
            [__CLASS__, 'render_tours_page']
        );

        // Tours Unified submenu
        add_submenu_page(
            'lci-dashboard',
            'Tours (Unified)',
            'Tours (Unified)',
            'manage_options',
            'lci-tours-unified',
            [__CLASS__, 'render_tours_unified_page']
        );

        // Participant detail submenu (hidden)
        add_submenu_page(
            null,
            'Participant Detail',
            'Participant Detail',
            'manage_options',
            'lci-participant-detail',
            [__CLASS__, 'render_participant_detail_page']
        );

        // Allergies & Diet submenu
        add_submenu_page(
            'lci-dashboard',
            'Allergies & Diet',
            'Allergies & Diet',
            'manage_options',
            'lci-allergies-diet',
            [__CLASS__, 'render_allergies_diet_page']
        );

        // Accommodations submenu
        add_submenu_page(
            'lci-dashboard',
            'Accommodations',
            'Accommodations',
            'manage_options',
            'lci-accommodations',
            [__CLASS__, 'render_accommodations_page']
        );

        // Main Event submenu
        add_submenu_page(
            'lci-dashboard',
            'Main Event',
            'Main Event',
            'manage_options',
            'lci-main-event',
            [__CLASS__, 'render_main_event_page']
        );

        // Sync tool submenu
        add_submenu_page(
            'lci-dashboard',
            'Sync Tool',
            'Sync Tool',
            'manage_options',
            'lci-sync-tool',
            [__CLASS__, 'render_sync_tool_page']
        );

        // Settings submenu
        add_submenu_page(
            'lci-dashboard',
            'Settings',
            'Settings',
            'manage_options',
            'lci-settings',
            [__CLASS__, 'render_settings_page']
        );

        // Debug logs submenu
        add_submenu_page(
            'lci-dashboard',
            'Debug Logs',
            'Debug Logs',
            'manage_options',
            'lci-debug-logs',
            [__CLASS__, 'render_debug_logs_page']
        );

        // Email Comparison submenu
        add_submenu_page(
            'lci-dashboard',
            'Email Comparison',
            'Email Comparison',
            'manage_options',
            'lci-email-comparison',
            [__CLASS__, 'render_email_comparison_page']
        );

        // DB Inspector submenu
        add_submenu_page(
            'lci-dashboard',
            'Database Inspector',
            'DB Inspector',
            'manage_options',
            'lci-db-inspector',
            [__CLASS__, 'render_db_inspector_page']
        );
    }

    /**
     * Register admin assets
     */
    public static function register_admin_assets($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'lci-') === false) {
            return;
        }

        // Tailwind CSS
        wp_enqueue_style(
            'lci-tailwind',
            'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            [],
            LCI2025_VERSION
        );

        // Alpine.js - load with defer attribute
        wp_enqueue_script(
            'alpine-js',
            'https://cdn.jsdelivr.net/npm/alpinejs@3.12.0/dist/cdn.min.js',
            [],
            '3.12.0',
            true // Load in footer
        );

        // Add defer attribute to Alpine.js
        add_filter('script_loader_tag', function($tag, $handle) {
            if ('alpine-js' === $handle) {
                return str_replace(' src', ' defer src', $tag);
            }
            return $tag;
        }, 10, 2);

        // Chart.js
        wp_enqueue_script(
            'chart-js',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            [],
            '3.9.1',
            true
        );

        // Admin CSS
        wp_enqueue_style(
            'lci-admin-css',
            LCI2025_URL . 'admin/css/admin.css',
            ['lci-tailwind'],
            LCI2025_VERSION
        );

        // Admin JS
        wp_enqueue_script(
            'lci-admin-js',
            LCI2025_URL . 'admin/js/admin.js',
            ['jquery', 'alpine-js', 'chart-js'],
            LCI2025_VERSION,
            true
        );

        // No need to load additional JS for DB Inspector page as it uses inline JavaScript

        // Localize script
        wp_localize_script('lci-admin-js', 'lciAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lci_admin_nonce'),
            'strings' => array(
                'confirmDelete' => __('Are you sure you want to delete this participant?', 'lci-2025-dashboard'),
                'confirmSync' => __('Are you sure you want to sync all participants? This may take a while.', 'lci-2025-dashboard'),
                'syncSuccess' => __('Participants synced successfully.', 'lci-2025-dashboard'),
                'syncError' => __('Error syncing participants.', 'lci-2025-dashboard'),
                'updateSuccess' => __('Participant updated successfully.', 'lci-2025-dashboard'),
                'updateError' => __('Error updating participant.', 'lci-2025-dashboard'),
                'deleteSuccess' => __('Participant deleted successfully.', 'lci-2025-dashboard'),
                'deleteError' => __('Error deleting participant.', 'lci-2025-dashboard'),
            )
        ));
    }

    /**
     * Render dashboard page
     */
    public static function render_dashboard_page() {
        // Get statistics
        $stats = LCI_Participant::get_statistics();

        // Get recent participants
        $recent_participants = LCI_Participant::get_participants([
            'orderby' => 'created_at',
            'order' => 'DESC',
            'limit' => 5
        ]);

        // Include template
        include LCI2025_PATH . 'templates/admin/dashboard.php';
    }

    /**
     * Render participants page
     */
    public static function render_participants_page() {
        // Include template
        include LCI2025_PATH . 'templates/admin/participants-list.php';
    }

    /**
     * Render participant detail page
     */
    public static function render_participant_detail_page() {
        // Get participant ID
        $participant_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        // Get participant
        $participant = LCI_Participant::get_participant($participant_id);

        if (!$participant) {
            wp_die(__('Participant not found.', 'lci-2025-dashboard'));
        }

        // Include template
        include LCI2025_PATH . 'templates/admin/participant-detail.php';
    }

    /**
     * Render allergies diet page
     */
    public static function render_allergies_diet_page() {
        // Include template
        include LCI2025_PATH . 'templates/admin/allergies-diet.php';
    }

    /**
     * Render sync tool page
     */
    public static function render_sync_tool_page() {
        // Include template
        include LCI2025_PATH . 'templates/admin/sync-tool.php';
    }

    /**
     * Render tours page
     */
    public static function render_tours_page() {
        // Get missing participants counts for each tour
        $missing_counts = [
            'main_pretour' => LCI_Tours::get_missing_participants_count(LCI_Tours::MAIN_PRETOUR_ID, 'main_pretour'),
            'legends_wildlife' => LCI_Tours::get_missing_participants_count(LCI_Tours::LEGENDS_WILDLIFE_ID, 'legends_wildlife'),
            'royal_elegance' => LCI_Tours::get_missing_participants_count(LCI_Tours::ROYAL_ELEGANCE_ID, 'royal_elegance'),
            'brasov_highlights' => LCI_Tours::get_missing_participants_count(LCI_Tours::BRASOV_HIGHLIGHTS_ID, 'brasov_highlights')
        ];

        // Include template
        include LCI2025_PATH . 'templates/admin/tours.php';
    }

    /**
     * Render tours unified page
     */
    public static function render_tours_unified_page() {
        // Include template
        include LCI2025_PATH . 'templates/admin/tours-unified.php';
    }

    /**
     * Render settings page
     */
    public static function render_settings_page() {
        // Handle repair actions
        if (isset($_GET['action'])) {
            if ($_GET['action'] === 'repair') {
                // Repair database tables
                $result = LCI_Database_Repair::repair_tables();

                if ($result['success']) {
                    add_settings_error('lci_settings', 'repair_success', $result['message'], 'success');
                } else {
                    add_settings_error('lci_settings', 'repair_error', $result['message'], 'error');
                }
            } elseif ($_GET['action'] === 'fix-duplicates') {
                // Fix duplicate registration IDs
                $result = LCI_Database_Repair::fix_duplicate_reg_ids();

                if ($result['success']) {
                    add_settings_error('lci_settings', 'fix_duplicates_success', $result['message'], 'success');
                } else {
                    add_settings_error('lci_settings', 'fix_duplicates_error', $result['message'], 'error');

                    if (!empty($result['errors'])) {
                        foreach ($result['errors'] as $error) {
                            add_settings_error('lci_settings', 'fix_duplicates_error_detail', $error, 'error');
                        }
                    }
                }
            }
        }

        // Display settings errors
        settings_errors('lci_settings');

        // Include template
        include LCI2025_PATH . 'templates/admin/settings.php';
    }

    /**
     * Render debug logs page
     */
    public static function render_debug_logs_page() {
        // Handle log clearing actions
        if (isset($_GET['action']) && isset($_GET['nonce']) && wp_verify_nonce($_GET['nonce'], 'lci_clear_logs')) {
            if ($_GET['action'] === 'clear-error-logs') {
                $error_log_file = LCI2025_PATH . 'logs/sync_errors.log';
                if (file_exists($error_log_file)) {
                    file_put_contents($error_log_file, '');
                    add_settings_error('lci_logs', 'logs_cleared', 'Error logs cleared successfully.', 'success');
                }
            } elseif ($_GET['action'] === 'clear-debug-logs') {
                $debug_log_file = LCI2025_PATH . 'logs/sync_debug.log';
                if (file_exists($debug_log_file)) {
                    file_put_contents($debug_log_file, '');
                    add_settings_error('lci_logs', 'logs_cleared', 'Debug logs cleared successfully.', 'success');
                }
            } elseif ($_GET['action'] === 'clear-summary-logs') {
                $summary_log_file = LCI2025_PATH . 'logs/sync_summary.log';
                if (file_exists($summary_log_file)) {
                    file_put_contents($summary_log_file, '');
                    add_settings_error('lci_logs', 'logs_cleared', 'Summary logs cleared successfully.', 'success');
                }
            }
        }

        // Display settings errors
        settings_errors('lci_logs');

        // Include template
        include LCI2025_PATH . 'templates/admin/debug-logs.php';
    }

    /**
     * Render email comparison page
     */
    public static function render_email_comparison_page() {
        // Include template
        include LCI2025_PATH . 'templates/admin/email-comparison.php';
    }

    /**
     * AJAX: Sync participant
     */
    public static function ajax_sync_participant() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get order ID
        $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;

        if (!$order_id) {
            wp_send_json_error(['message' => 'Invalid order ID.']);
        }

        // Sync participant
        $result = LCI_Participant::sync_from_order($order_id);

        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        }

        wp_send_json_success(['message' => 'Participant synced successfully.']);
    }

    /**
     * AJAX: Sync all participants
     */
    public static function ajax_sync_all_participants() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Enable debug mode for this sync operation
        $debug_mode = get_option('lci2025_debug_mode', '0');

        // Get all WooCommerce orders
        $orders = wc_get_orders([
            'limit' => -1,
            'type' => 'shop_order',
            'status' => ['processing', 'completed', 'on-hold']
        ]);

        $success_count = 0;
        $error_count = 0;
        $errors = [];

        foreach ($orders as $order) {
            $order_id = $order->get_id();
            $result = LCI_Participant::sync_from_order($order_id);

            if (is_wp_error($result)) {
                $error_count++;

                // Store error details for logging
                $errors[] = [
                    'order_id' => $order_id,
                    'error_code' => $result->get_error_code(),
                    'error_message' => $result->get_error_message()
                ];

                // Log detailed error if debug mode is not enabled (since it would already be logged)
                if ($debug_mode !== '1') {
                    $log_file = LCI2025_PATH . 'logs/sync_errors.log';
                    $log_dir = dirname($log_file);

                    // Create logs directory if it doesn't exist
                    if (!file_exists($log_dir)) {
                        mkdir($log_dir, 0755, true);
                    }

                    $timestamp = current_time('mysql');
                    $log_entry = "[{$timestamp}] [Order: {$order_id}] ERROR: {$result->get_error_message()}\n";

                    file_put_contents($log_file, $log_entry, FILE_APPEND);
                }
            } else {
                $success_count++;
            }
        }

        // Log summary
        $log_file = LCI2025_PATH . 'logs/sync_summary.log';
        $log_dir = dirname($log_file);

        // Create logs directory if it doesn't exist
        if (!file_exists($log_dir)) {
            mkdir($log_dir, 0755, true);
        }

        $timestamp = current_time('mysql');
        $log_entry = "[{$timestamp}] Sync completed. {$success_count} participants synced successfully, {$error_count} errors.\n";

        if (!empty($errors)) {
            $log_entry .= "Error summary:\n";
            foreach ($errors as $error) {
                $log_entry .= "  - Order {$error['order_id']}: {$error['error_message']}\n";
            }
        }

        file_put_contents($log_file, $log_entry, FILE_APPEND);

        wp_send_json_success([
            'message' => sprintf(
                'Sync completed. %d participants synced successfully, %d errors. Check Debug Logs for details.',
                $success_count,
                $error_count
            ),
            'success_count' => $success_count,
            'error_count' => $error_count
        ]);
    }

    /**
     * AJAX: Update participant
     */
    public static function ajax_update_participant() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get participant ID
        $participant_id = isset($_POST['id']) ? intval($_POST['id']) : 0;

        if (!$participant_id) {
            wp_send_json_error(['message' => 'Invalid participant ID.']);
        }

        // Get participant data
        $data = isset($_POST['data']) ? $_POST['data'] : [];

        if (empty($data)) {
            wp_send_json_error(['message' => 'No data provided.']);
        }

        // Update participant
        $result = LCI_Participant::update_participant($participant_id, $data);

        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        }

        wp_send_json_success(['message' => 'Participant updated successfully.']);
    }

    /**
     * AJAX: Get participants
     */
    public static function ajax_get_participants() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get query args
        $args = [
            'orderby' => isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'id',
            'order' => isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'DESC',
            'search' => isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '',
        ];

        // Get participants
        $participants = LCI_Participant::get_participants($args);
        $total = LCI_Participant::count_participants($args);

        wp_send_json_success([
            'participants' => $participants,
            'total' => $total
        ]);
    }

    /**
     * Sync new order
     */
    public static function sync_new_order($order_id, $posted_data, $order) {
        // Only sync if order is complete or processing
        if (!in_array($order->get_status(), ['processing', 'completed'])) {
            return;
        }

        // Sync participant
        LCI_Participant::sync_from_order($order_id);
    }

    /**
     * Sync order when status changes
     */
    public static function sync_order_status_changed($order_id, $from_status, $to_status, $order) {
        // Only sync if order is complete or processing
        if (!in_array($to_status, ['processing', 'completed'])) {
            return;
        }

        // Sync participant
        LCI_Participant::sync_from_order($order_id);
    }

    /**
     * AJAX: Get tour participants
     */
    public static function ajax_get_tour_participants() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Check required parameters
        if (!isset($_GET['tour_type'])) {
            wp_send_json_error(['message' => 'Missing required parameters.']);
        }

        $tour_type = sanitize_text_field($_GET['tour_type']);

        // Validate tour type
        $valid_tour_types = ['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'];
        if (!in_array($tour_type, $valid_tour_types)) {
            wp_send_json_error(['message' => 'Invalid tour type.']);
        }

        // Check if we should force WooCommerce data
        $force_woocommerce = isset($_GET['force_woocommerce']) && $_GET['force_woocommerce'] === 'true';

        // Get tour participants
        $result = LCI_Tours::get_tour_participants($tour_type, $force_woocommerce);

        wp_send_json_success([
            'participants' => $result['participants'],
            'source' => $result['source'],
            'table_exists' => $result['table_exists'],
            'table_has_data' => $result['table_has_data']
        ]);
    }

    /**
     * AJAX: Sync tour participants
     */
    public static function ajax_sync_tour_participants() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Check required parameters
        if (!isset($_POST['tour_type']) || !isset($_POST['product_id'])) {
            wp_send_json_error(['message' => 'Missing required parameters.']);
        }

        $tour_type = sanitize_text_field($_POST['tour_type']);
        $product_id = intval($_POST['product_id']);

        // Validate tour type
        $valid_tour_types = ['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'];
        if (!in_array($tour_type, $valid_tour_types)) {
            wp_send_json_error(['message' => 'Invalid tour type.']);
        }

        // Sync tour participants
        $result = LCI_Tours::sync_tour_participants($product_id, $tour_type);

        wp_send_json_success([
            'message' => sprintf(
                'Sync completed. %d participants synced successfully, %d errors.',
                $result['success_count'],
                $result['error_count']
            ),
            'success_count' => $result['success_count'],
            'error_count' => $result['error_count'],
            'total_count' => $result['total_count']
        ]);
    }

    /**
     * AJAX: Clear tour table
     */
    public static function ajax_clear_tour_table() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get tour type
        $tour_type = isset($_POST['tour_type']) ? sanitize_text_field($_POST['tour_type']) : '';

        if (!$tour_type) {
            wp_send_json_error(['message' => 'Invalid tour type.']);
        }

        // Clear tour table
        $result = LCI_Tours::clear_tour_table($tour_type);

        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        }

        wp_send_json_success([
            'message' => 'Tour table cleared successfully.'
        ]);
    }

    /**
     * AJAX: Get email comparison data
     */
    public static function ajax_get_email_comparison() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get all WooCommerce orders
        $orders = wc_get_orders([
            'limit' => -1,
            'type' => 'shop_order',
            'status' => ['processing', 'completed', 'on-hold']
        ]);

        $comparison_data = [];

        foreach ($orders as $order) {
            $order_id = $order->get_id();
            $user_id = $order->get_user_id();
            $billing_email = $order->get_billing_email();
            $first_name = $order->get_billing_first_name();
            $last_name = $order->get_billing_last_name();
            $order_date = $order->get_date_created() ? $order->get_date_created()->date('Y-m-d H:i:s') : '';

            // Get customer email (user account email)
            $customer_email = '';
            $has_user_account = false;

            if ($user_id > 0) {
                $user = get_user_by('id', $user_id);
                if ($user) {
                    $customer_email = $user->user_email;
                    $has_user_account = true;
                }
            }

            // Determine email match status
            $email_match = false;
            if ($has_user_account && $billing_email && $customer_email) {
                $email_match = (strtolower($billing_email) === strtolower($customer_email));
            }

            // Determine status
            $status = 'no_user';
            if ($has_user_account) {
                $status = $email_match ? 'match' : 'mismatch';
            }

            $comparison_data[] = [
                'order_id' => $order_id,
                'user_id' => $user_id,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'billing_email' => $billing_email,
                'customer_email' => $customer_email,
                'has_user_account' => $has_user_account,
                'email_match' => $email_match,
                'status' => $status,
                'order_date' => $order_date
            ];
        }

        wp_send_json_success([
            'comparison_data' => $comparison_data,
            'total' => count($comparison_data)
        ]);
    }

    /**
     * Render DB Inspector page
     */
    public static function render_db_inspector_page() {
        // Include the DB Inspector template
        include_once LCI2025_PATH . 'templates/admin/db-inspector.php';
    }

    /**
     * Render Accommodations page
     */
    public static function render_accommodations_page() {
        // Include the Accommodations template
        include_once LCI2025_PATH . 'templates/admin/accommodation-unified.php';
    }

    /**
     * Render Main Event page
     */
    public static function render_main_event_page() {
        // Ensure the main event table exists
        $table_status = LCI_Main_Event::check_main_event_table();
        if (!$table_status['exists']) {
            // Create the table if it doesn't exist
            LCI_Main_Event::create_tables();

            // Refresh the table status
            $table_status = LCI_Main_Event::check_main_event_table();
        }

        // Include the Main Event template
        include_once LCI2025_PATH . 'templates/admin/main-event.php';
    }
}

// Initialize the admin functionality
LCI_Admin::init();
