/**
 * LCI 2025 Dashboard Mini Cart
 */

// Ensure lci_ajax_object is defined
if (typeof lci_ajax_object === 'undefined') {
    window.lci_ajax_object = {
        ajax_url: '/wp-admin/admin-ajax.php',
        nonce: ''
    };
    console.warn('lci_ajax_object was not defined, using fallback');
}

// Make sure Alpine.js is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Create a global object to store references
    window.lciComponents = {};

    // Wait a bit for Alpine to initialize components
    setTimeout(() => {
        // Store reference to confirmation component
        const confirmEl = document.getElementById('add-to-cart-confirm');
        if (confirmEl && confirmEl.__x) {
            // Store reference to confirmation component
            window.lciComponents.confirmModal = confirmEl.__x;
        }

        // Store reference to mini cart component
        const miniCartEl = document.getElementById('mini-cart-container');
        if (miniCartEl && miniCartEl.__x) {
            // Store reference to mini cart component
            window.lciComponents.miniCart = miniCartEl.__x;

            // Create a global function to open the mini cart
            if (!window.openMiniCart) {
                window.openMiniCart = function() {
                    // Global openMiniCart called
                    if (window.lciComponents.miniCart) {
                        try {
                            const data = window.lciComponents.miniCart.getUnobservedData();
                            // Prevent auto-opening on first load
                            data.autoOpenCart = false;
                            data.openCart();
                        } catch (error) {
                            // Error occurred, use fallback
                            // Fallback: dispatch event
                            window.dispatchEvent(new CustomEvent('open-mini-cart'));
                        }
                    } else {
                        // Fallback: dispatch event
                        window.dispatchEvent(new CustomEvent('open-mini-cart'));
                    }
                };
            }
        }
    }, 500);

    // Add click handler to any mini cart button
    const miniCartButtons = document.querySelectorAll('[id^="mini-cart-button"]');
    miniCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            if (typeof window.openMiniCart === 'function') {
                window.openMiniCart();
            } else {
                // openMiniCart function not found, use fallback
                window.dispatchEvent(new CustomEvent('open-mini-cart'));
            }
        });
    });
});

// Global function to show add to cart confirmation
function showAddToCartConfirmation(productName, productImage, callback) {
    // Show add to cart confirmation

    // Store the callback globally so it can be accessed by the confirmation modal
    window.addToCartCloseCallback = callback;

    // Try using the direct global function first
    if (typeof window.showAddToCartConfirmationDirect === 'function') {
        // Try using the direct global function
        try {
            window.showAddToCartConfirmationDirect(productName, productImage);
            return;
        } catch (error) {
            // Error occurred, try next method
        }
    }

    // Try dispatching a window event
    try {
        window.dispatchEvent(new CustomEvent('show-modal', {
            detail: { productName, productImage }
        }));
    } catch (error) {
        // Error occurred, try next method
    }

    // Try using the stored reference
    if (window.lciComponents && window.lciComponents.confirmModal) {
        // Try using the stored reference
        try {
            const data = window.lciComponents.confirmModal.getUnobservedData();
            data.showConfirmation(productName, productImage);
            return;
        } catch (error) {
            // Error occurred, try next method
        }
    }

    // Fallback to finding the element directly
    const confirmEl = document.getElementById('add-to-cart-confirm');

    if (confirmEl) {
        // Try multiple approaches to show the modal

        // 1. Try using Alpine.js instance if available
        if (confirmEl.__x) {
            // Try using Alpine.js instance if available
            try {
                const data = confirmEl.__x.getUnobservedData();

                data.showConfirmation(productName, productImage);
                return;
            } catch (error) {
                // Error occurred, try next method
            }
        }

        // 2. Try dispatching a custom event
        confirmEl.dispatchEvent(new CustomEvent('show-confirmation', {
            detail: { productName, productImage }
        }));
    } else {
        // Confirmation element not found, try alternative

        // Try to find by any means necessary
        const altConfirmEl = document.querySelector('[x-data]');
        if (altConfirmEl) {
            // Try with alternative element
            altConfirmEl.dispatchEvent(new CustomEvent('show-confirmation', {
                detail: { productName, productImage }
            }));
        }
    }
}
