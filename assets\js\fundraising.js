/**
 * LCI 2025 Dashboard Fundraising
 *
 * Handles automatic updating of the fundraising message when cart changes
 */

(function($) {
    'use strict';

    // Function to refresh the fundraising message
    // Make it globally available
    window.refreshFundraisingMessage = function() {
        // Only proceed if we have the AJAX object
        if (typeof lci_ajax_object === 'undefined') {
            // AJAX object not available
            return;
        }

        // Show loading state
        const fundraisingMessage = $('#lci-fundraising-message');
        if (fundraisingMessage.length) {
            fundraisingMessage.css('opacity', '0.7');
        }

        // Make AJAX request to get updated message
        $.ajax({
            url: lci_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'lci_get_fundraising_message',
                nonce: lci_ajax_object.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    // If response data is empty, remove the message (cart is empty or no category 22 products)
                    if (!response.data || response.data.trim() === '') {
                        if (fundraisingMessage.length) {
                            // Remove the message
                            fundraisingMessage.remove();
                        }
                    } else {
                        // Replace the message with the updated one
                        if (fundraisingMessage.length) {
                            // Replace existing message
                            fundraisingMessage.replaceWith(response.data);
                        } else {
                            // If message doesn't exist yet, add it before the footer
                            // Add new message
                            $('.lci-modal-footer').before(response.data);
                        }
                    }
                }
            },
            error: function(xhr, status, error) {
                // Error handling
            },
            complete: function() {
                // Reset opacity
                $('#lci-fundraising-message').css('opacity', '1');
            }
        });
    }

    // Listen for cart update events
    $(document.body).on('added_to_cart removed_from_cart updated_cart_totals', function() {
        // Cart updated, refresh message
        refreshFundraisingMessage();
    });

    // Listen for our custom cart item removed event
    $(document).on('lci:cart-item-removed', function(e) {
        // Item removed, refresh message
        refreshFundraisingMessage();
    });

    // Also refresh when mini cart is opened
    $(document).on('click', '#mini-cart-button', function() {
        setTimeout(refreshFundraisingMessage, 500); // Slight delay to ensure cart is open
    });

})(jQuery);
