<?php
// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(home_url('/login'));
    exit;
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Enqueue the accommodation wizard CSS
wp_enqueue_style('accommodation-wizard-css', LCI2025_URL . 'assets/css/accommodation-wizard.css', array(), LCI2025_VERSION);

// Initialize wizard session variables if not set
if (!isset($_SESSION['accommodation_wizard'])) {
    $_SESSION['accommodation_wizard'] = [
        'current_step' => 1,
        'total_steps' => 4,
        'has_main_pretour' => false,
        'bucharest' => [
            'selected' => null, // 'yes' or 'no'
            'nights' => 1
        ],
        'brasov' => [
            'selected' => null, // 'yes', 'no', or 'type'
            'type' => null, // 'pre', 'main', or 'post'
            'nights' => 1
        ],
        'products' => [],
        'selected_product' => null
    ];
}

// Get current wizard state
$wizard = &$_SESSION['accommodation_wizard'];

// Check if user has Main Pretour (product ID 743) if not already checked
if (!isset($wizard['has_main_pretour_checked']) || $wizard['has_main_pretour_checked'] === false) {
    $user_id = get_current_user_id();
    $has_main_pretour = false;

    // Query for orders containing product ID 743
    $orders = wc_get_orders([
        'customer_id' => $user_id,
        'status' => ['completed', 'processing', 'on-hold'],
    ]);

    foreach ($orders as $order) {
        foreach ($order->get_items() as $item) {
            if ($item->get_product_id() == 743) {
                $has_main_pretour = true;
                break 2;
            }
        }
    }

    $wizard['has_main_pretour'] = $has_main_pretour;
    $wizard['has_main_pretour_checked'] = true;

    // Set total steps based on whether user has Main Pretour
    $wizard['total_steps'] = $has_main_pretour ? 4 : 3;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['wizard_action'])) {
        switch ($_POST['wizard_action']) {
            case 'next':
                if ($wizard['current_step'] < $wizard['total_steps']) {
                    $wizard['current_step']++;
                }
                break;

            case 'prev':
                if ($wizard['current_step'] > 1) {
                    $wizard['current_step']--;
                }
                break;

            case 'reset':
                // Reset wizard state
                unset($_SESSION['accommodation_wizard']);
                // Use the correct dashboard URL
                $redirect_url = add_query_arg('tab', 'accommodation-wizard-php', home_url('/lci-dashboard/'));
                wp_redirect($redirect_url);
                exit;

            case 'complete':
                // Redirect to accommodation page
                wp_redirect(add_query_arg(['tab' => 'accommodation', 'wizard-completed' => '1'], home_url('/lci-dashboard/')));
                exit;
        }
    }

    // COMPLETELY REWRITTEN LOGIC
    // First, handle special actions
    if (isset($_POST['bucharest_selected']) && $_POST['bucharest_selected'] === 'yes') {
        // User selected Yes for Bucharest accommodation
        $wizard['bucharest']['selected'] = 'yes';
        $wizard['in_bucharest_flow'] = true;
        $wizard['current_step'] = 2; // Force to step 2 (nights selection)

        // Log this action
        error_log('User selected YES for Bucharest accommodation. Setting step to 2.');
    }
    else if (isset($_POST['bucharest_selected']) && $_POST['bucharest_selected'] === 'no') {
        // User selected No for Bucharest accommodation
        $wizard['bucharest']['selected'] = 'no';
        $wizard['in_bucharest_flow'] = false;
        $wizard['current_step'] = 2; // Force to step 2 (Brasov type selection)

        // Log this action
        error_log('User selected NO for Bucharest accommodation. Setting step to 2.');
    }
    else if (isset($_POST['brasov_selected']) && $_POST['brasov_selected'] === 'yes') {
        // User selected Yes for Brasov accommodation
        $wizard['brasov']['selected'] = 'yes';
        $wizard['current_step'] = 'brasov_period'; // Special step for Brasov period selection

        // Log this action
        error_log('User selected YES for Brasov accommodation. Setting step to brasov_period.');
    }
    else if (isset($_POST['brasov_period'])) {
        // User selected a period for Brasov accommodation
        $wizard['brasov_period'] = $_POST['brasov_period'];

        // Set the brasov type based on the period
        $wizard['brasov']['type'] = $wizard['brasov_period'];

        // Set the category ID based on the period
        if ($wizard['brasov_period'] === 'pre') {
            $wizard['brasov']['category_id'] = 37; // Pre-event accommodation
            // Don't set meta query for now, we'll handle this in the product query
            $wizard['brasov']['period_type'] = 'pre';
        } else if ($wizard['brasov_period'] === 'main') {
            $wizard['brasov']['category_id'] = 1; // Main event accommodation
            $wizard['brasov']['period_type'] = 'main';
        } else if ($wizard['brasov_period'] === 'post') {
            $wizard['brasov']['category_id'] = 37; // Post-event accommodation
            // Don't set meta query for now, we'll handle this in the product query
            $wizard['brasov']['period_type'] = 'post';
        }

        // Move to the appropriate next step
        if ($wizard['brasov_period'] === 'pre' || $wizard['brasov_period'] === 'post') {
            $wizard['current_step'] = 'brasov_nights'; // Go to dedicated nights selection step
        } else {
            $wizard['current_step'] = 4; // Go to product selection for main event
        }

        // Log this action
        error_log('User selected ' . $wizard['brasov_period'] . ' period for Brasov with category ID ' . $wizard['brasov']['category_id'] . '. Moving to next step.');
    }

    // Handle nights selection for Bucharest
    if (isset($_POST['bucharest_nights'])) {
        $wizard['bucharest']['nights'] = intval($_POST['bucharest_nights']);

        // If we're in step 2 and submitting nights, go to step 3
        if ($wizard['current_step'] == 2 && $wizard['in_bucharest_flow']) {
            $wizard['current_step'] = 3; // Force to step 3 (Bucharest products)

            // Log this action
            error_log('User selected ' . $wizard['bucharest']['nights'] . ' nights for Bucharest. Setting step to 3.');
        }
    }

    // Handle Brasov accommodation type
    if (isset($_POST['brasov_type'])) {
        $wizard['brasov']['type'] = $_POST['brasov_type'];

        // If we're in step 2 and submitting Brasov type, go to step 3
        if ($wizard['current_step'] == 2 && !$wizard['in_bucharest_flow']) {
            $wizard['current_step'] = 3; // Force to step 3 (Brasov products)

            // Log this action
            error_log('User selected ' . $wizard['brasov']['type'] . ' for Brasov. Setting step to 3.');
        }
    }

    // Handle nights selection for Brasov
    if (isset($_POST['brasov_nights'])) {
        $wizard['brasov']['nights'] = intval($_POST['brasov_nights']);

        // If we're in the brasov_nights step and submitting nights, go to product selection
        if ($wizard['current_step'] === 'brasov_nights') {
            $wizard['current_step'] = 4; // Go to product selection

            // Log this action
            error_log('User selected ' . $wizard['brasov']['nights'] . ' nights for Brasov ' . $wizard['brasov']['type'] . '. Moving to product selection.');
        }
    }

    // Handle adding Bucharest product to cart
    if (isset($_POST['add_bucharest_to_cart']) && isset($_POST['bucharest_product_id'])) {
        // Special handling for Bucharest accommodation
        $product_id = intval($_POST['bucharest_product_id']);
        $quantity = 1;

        // Check if we have a variation ID
        $variation_id = 0;
        $variation_attributes = array();

        if (isset($_POST['room_variation_id']) && !empty($_POST['room_variation_id'])) {
            $variation_id = intval($_POST['room_variation_id']);

            // Get the product
            $product = wc_get_product($product_id);

            // If it's a variable product, get the variation attributes
            if ($product && $product->is_type('variable')) {
                $variation = wc_get_product($variation_id);

                if ($variation) {
                    // Get variation attributes
                    $variation_data = $variation->get_variation_attributes();

                    // Format attributes for WC()->cart->add_to_cart()
                    foreach ($variation_data as $key => $value) {
                        $attribute_key = 'attribute_' . sanitize_title($key);
                        $variation_attributes[$attribute_key] = $value;
                    }
                }
            }
        }

        // Get the number of nights from the wizard
        $nights = isset($wizard['bucharest']['nights']) ? intval($wizard['bucharest']['nights']) : 1;

        // Add product to cart multiple times based on number of nights
        $success = false;
        $cart_item_keys = [];

        // Add to cart once for each night
        for ($i = 0; $i < $nights; $i++) {
            $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $variation_attributes);
            if ($cart_item_key) {
                $cart_item_keys[] = $cart_item_key;
                $success = true;
            }
        }

        // Debug log
        error_log('Adding to cart: product_id=' . $product_id . ', variation_id=' . $variation_id .
                  ', attributes=' . print_r($variation_attributes, true) .
                  ', nights=' . $nights .
                  ', results=' . print_r($cart_item_keys, true));

        // Store selected product
        $wizard['selected_product'] = $product_id;

        // Show success message
        if ($success) {
            if ($nights > 1) {
                wc_add_notice('Bucharest accommodation for ' . $nights . ' nights added to cart successfully!', 'success');
            } else {
                wc_add_notice('Bucharest accommodation added to cart successfully!', 'success');
            }
        } else {
            wc_add_notice('Failed to add accommodation to cart. Please try again.', 'error');
        }

        // Log this action
        error_log('Added Bucharest product ID ' . $product_id . ' to cart with variation ID ' . $variation_id . '. Result: ' . ($cart_item_key ? 'success' : 'failed'));
    }

    // Handle adding Brasov product to cart
    if (isset($_POST['add_to_cart']) && isset($_POST['product_id'])) {
        // Regular handling for other accommodation
        $product_id = intval($_POST['product_id']);

        // Get the number of nights from the wizard
        $nights = isset($wizard['brasov']['nights']) ? intval($wizard['brasov']['nights']) : 1;
        $quantity = $nights; // Multiply by number of nights

        // Check if we have a variation ID
        $variation_id = 0;
        $variation_attributes = array();

        if (isset($_POST['variation_id']) && !empty($_POST['variation_id'])) {
            $variation_id = intval($_POST['variation_id']);

            // Get the product
            $product = wc_get_product($product_id);

            // If it's a variable product, get the variation attributes
            if ($product && $product->is_type('variable')) {
                $variation = wc_get_product($variation_id);

                if ($variation) {
                    // Get variation attributes
                    $variation_data = $variation->get_variation_attributes();

                    // Format attributes for WC()->cart->add_to_cart()
                    foreach ($variation_data as $key => $value) {
                        $attribute_key = 'attribute_' . sanitize_title($key);
                        $variation_attributes[$attribute_key] = $value;
                    }
                }
            }
        }

        // Add product to cart
        $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $variation_attributes);

        // Debug log
        error_log('Adding to cart: product_id=' . $product_id . ', variation_id=' . $variation_id .
                  ', attributes=' . print_r($variation_attributes, true) .
                  ', nights=' . $nights .
                  ', result=' . ($cart_item_key ? 'success' : 'failed'));

        // Store selected product
        $wizard['selected_product'] = $product_id;

        // Show success message
        if ($cart_item_key) {
            if ($nights > 1) {
                wc_add_notice('Brasov accommodation for ' . $nights . ' nights added to cart successfully!', 'success');
            } else {
                wc_add_notice('Brasov accommodation added to cart successfully!', 'success');
            }
        } else {
            wc_add_notice('Failed to add accommodation to cart. Please try again.', 'error');
        }

        // Advance to the next step for non-Bucharest accommodation
        if ($wizard['current_step'] < $wizard['total_steps']) {
            $wizard['current_step']++;
        }

        // Log this action
        error_log('Added Brasov product ID ' . $product_id . ' to cart with variation ID ' . $variation_id . '. Moving to next step.');
    }

    // Redirect to prevent form resubmission
    // Use the correct dashboard URL
    $redirect_url = add_query_arg('tab', 'accommodation-wizard-php', home_url('/lci-dashboard/'));
    wp_redirect($redirect_url);
    exit;
}

// Calculate progress percentage - simple approach
$progress = 50; // Default to 50%
?>

<div class="wizard-container">
    <!-- Simplified Progress Bar -->
    <div class="wizard-progress" style="margin: 20px auto; max-width: 600px;">
        <div class="wizard-progress-bar" style="height: 8px; background-color: #e9ecef; border-radius: 4px; overflow: hidden;">
            <div class="wizard-progress-bar-inner" style="height: 100%; background: linear-gradient(to right, #00b2e3, #0099cc); width: <?php echo $progress; ?>%; transition: width 0.3s ease;"></div>
        </div>
    </div>

    <!-- Wizard Content -->
    <div class="wizard-content">
        <form method="post" action="<?php echo esc_url(add_query_arg('tab', 'accommodation-wizard-php', home_url('/lci-dashboard/'))); ?>">
            <div style="max-width: 600px; margin: 0 auto; background-color: #f8f9fa; border-radius: 10px; padding: 30px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                <h3 class="text-center mb-4">Do you need accommodation in Brasov?</h3>

                <div class="info-box" style="background-color: #fff3cd; border-color: #ffeeba; margin-bottom: 20px; padding: 15px; border-radius: 8px;">
                    <i class="fas fa-info-circle me-2" style="color: #856404;"></i>
                    <span>Please select if you need accommodation in Brasov.</span>
                </div>

                <div style="display: flex; gap: 20px; margin-top: 30px;">
                    <label style="flex: 1; border: 2px solid #e9ecef; border-radius: 10px; padding: 20px; cursor: pointer; transition: all 0.2s ease; text-align: center;">
                        <input type="radio" name="brasov_selected" value="yes" style="margin-bottom: 10px;">
                        <div style="font-weight: bold; margin-bottom: 5px; color: #00b2e3;">Yes</div>
                        <div style="font-size: 14px; color: #6c757d;">I need accommodation in Brasov</div>
                    </label>

                    <label style="flex: 1; border: 2px solid #e9ecef; border-radius: 10px; padding: 20px; cursor: pointer; transition: all 0.2s ease; text-align: center;">
                        <input type="radio" name="brasov_selected" value="no" style="margin-bottom: 10px;">
                        <div style="font-weight: bold; margin-bottom: 5px; color: #6c757d;">No</div>
                        <div style="font-size: 14px; color: #6c757d;">I don't need accommodation</div>
                    </label>
                </div>

                <div class="text-center mt-4">
                    <button type="submit" class="wizard-btn wizard-btn-primary" style="background: linear-gradient(to right, #00b2e3, #0099cc); color: white; border: none; padding: 12px 24px; border-radius: 6px; font-weight: bold; cursor: pointer; box-shadow: 0 2px 5px rgba(0,178,227,0.3); transition: all 0.2s ease;">
                        Continue <i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>

                <div class="text-center mt-3">
                    <button type="submit" name="wizard_action" value="reset" style="background: none; border: none; color: #6c757d; text-decoration: underline; cursor: pointer;">
                        Reset Wizard
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Add to Cart Confirmation Modal -->
<?php echo lci_get_add_to_cart_confirm_html(); ?>

<!-- External CSS is now loaded via wp_enqueue_style -->
