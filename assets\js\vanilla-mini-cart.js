/**
 * LCI 2025 Dashboard Mini Cart - Vanilla JS Implementation
 */

document.addEventListener('DOMContentLoaded', function() {
    // Mini Cart Elements
    const miniCartButton = document.getElementById('mini-cart-button');
    const miniCartModal = document.getElementById('mini-cart-modal');
    const miniCartItems = document.getElementById('mini-cart-items');
    const miniCartTotal = document.getElementById('mini-cart-total');
    const miniCartCount = document.getElementById('mini-cart-count');
    const miniCartClose = document.getElementById('mini-cart-close');
    const miniCartBackdrop = document.getElementById('mini-cart-backdrop');
    
    // Add to Cart Confirmation Elements
    const addToCartModal = document.getElementById('add-to-cart-modal');
    const addToCartBackdrop = document.getElementById('add-to-cart-backdrop');
    const addToCartClose = document.getElementById('add-to-cart-close');
    const addToCartImage = document.getElementById('added-product-image');
    const addToCartName = document.getElementById('added-product-name');
    const addToCartProgress = document.getElementById('add-to-cart-progress');
    const viewCartButton = document.getElementById('view-cart-btn');
    const continueShoppingButton = document.getElementById('continue-shopping-btn');
    
    // Event Listeners
    if (miniCartButton) {
        miniCartButton.addEventListener('click', openMiniCart);
    }
    
    if (miniCartClose) {
        miniCartClose.addEventListener('click', closeMiniCart);
    }
    
    if (miniCartBackdrop) {
        miniCartBackdrop.addEventListener('click', function(e) {
            if (e.target === miniCartBackdrop) {
                closeMiniCart();
            }
        });
    }
    
    if (addToCartClose) {
        addToCartClose.addEventListener('click', closeAddToCartModal);
    }
    
    if (addToCartBackdrop) {
        addToCartBackdrop.addEventListener('click', function(e) {
            if (e.target === addToCartBackdrop) {
                closeAddToCartModal();
            }
        });
    }
    
    if (viewCartButton) {
        viewCartButton.addEventListener('click', function() {
            closeAddToCartModal();
            openMiniCart();
        });
    }
    
    if (continueShoppingButton) {
        continueShoppingButton.addEventListener('click', closeAddToCartModal);
    }
    
    // Functions
    function openMiniCart() {
        fetchMiniCartItems();
        if (miniCartModal) {
            miniCartModal.style.display = 'block';
            document.body.classList.add('modal-open');
        }
    }
    
    function closeMiniCart() {
        if (miniCartModal) {
            miniCartModal.style.display = 'none';
            document.body.classList.remove('modal-open');
        }
    }
    
    function showAddToCartModal(productName, productImage) {
        if (addToCartModal) {
            if (addToCartImage) {
                addToCartImage.src = productImage || '';
            }
            
            if (addToCartName) {
                addToCartName.textContent = productName || 'Product';
            }
            
            addToCartModal.style.display = 'block';
            document.body.classList.add('modal-open');
            
            // Start progress bar
            startProgressBar();
            
            // Auto close after 3 seconds
            setTimeout(closeAddToCartModal, 3000);
        }
    }
    
    function closeAddToCartModal() {
        if (addToCartModal) {
            addToCartModal.style.display = 'none';
            document.body.classList.remove('modal-open');
        }
    }
    
    function startProgressBar() {
        if (addToCartProgress) {
            addToCartProgress.style.width = '0%';
            
            // Small delay to ensure initial state is rendered
            setTimeout(() => {
                addToCartProgress.style.transition = 'width 3s linear';
                addToCartProgress.style.width = '100%';
            }, 50);
        }
    }
    
    function fetchMiniCartItems() {
        if (!miniCartItems) return;
        
        // Show loading state
        miniCartItems.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading your items...</p>
            </div>
        `;
        
        // Create a nonce for security
        const nonce = lci_ajax_object.nonce;
        
        // Fetch cart contents via our custom AJAX endpoint
        fetch(lci_ajax_object.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=lci_get_mini_cart_items&nonce=' + nonce
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.items && data.data.items.length > 0) {
                const cartItems = data.data.items;
                let cartItemsHtml = '<div class="cart-items-list">';
                
                cartItems.forEach(item => {
                    cartItemsHtml += `
                        <div class="mini-cart-item d-flex align-items-center p-3 border-bottom">
                            <!-- Product Image -->
                            <div class="mini-cart-item-image me-2">
                                <img src="${item.image}" alt="${item.name}" class="img-fluid rounded" style="width: 50px; height: 50px; object-fit: contain;">
                            </div>
                            
                            <!-- Quantity Badge -->
                            <div class="mini-cart-item-quantity-container me-2">
                                <span class="mini-cart-item-quantity badge bg-light text-dark">${item.quantity} ×</span>
                            </div>
                            
                            <!-- Product Details -->
                            <div class="mini-cart-item-details flex-grow-1 d-flex align-items-center justify-content-between">
                                <div class="mini-cart-item-name-container">
                                    <span class="mini-cart-item-name">${item.name}</span>
                                </div>
                                <div class="mini-cart-item-price-container">
                                    <span class="mini-cart-item-price text-primary fw-bold">${item.price}</span>
                                </div>
                            </div>
                            
                            <!-- Remove Button -->
                            <div class="mini-cart-item-remove ms-2">
                                <a href="#" class="text-danger remove-cart-item" data-cart-item-key="${item.key}">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    `;
                });
                
                cartItemsHtml += '</div>';
                miniCartItems.innerHTML = cartItemsHtml;
                
                // Update the total
                if (miniCartTotal) {
                    miniCartTotal.innerHTML = data.data.total;
                }
                
                // Update the count
                if (miniCartCount) {
                    miniCartCount.textContent = data.data.count;
                    miniCartCount.style.display = 'inline-block';
                }
                
                // Update the button text
                if (miniCartButton) {
                    const buttonText = miniCartButton.querySelector('.mini-cart-button-text');
                    if (buttonText) {
                        buttonText.innerHTML = data.data.total;
                    }
                }
                
                // Add event listeners to remove buttons
                document.querySelectorAll('.remove-cart-item').forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const cartItemKey = this.getAttribute('data-cart-item-key');
                        if (cartItemKey) {
                            removeCartItem(cartItemKey);
                        }
                    });
                });
            } else {
                // Show empty cart message
                miniCartItems.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-bag text-muted" style="font-size: 3rem;"></i>
                        <p class="mt-3">Your goodies bag is empty</p>
                    </div>
                `;
                
                // Update the total
                if (miniCartTotal) {
                    miniCartTotal.innerHTML = '0.00 €';
                }
                
                // Update the count
                if (miniCartCount) {
                    miniCartCount.textContent = '0';
                    miniCartCount.style.display = 'none';
                }
                
                // Update the button text
                if (miniCartButton) {
                    const buttonText = miniCartButton.querySelector('.mini-cart-button-text');
                    if (buttonText) {
                        buttonText.innerHTML = '0.00 €';
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error loading cart items:', error);
            miniCartItems.innerHTML = `
                <div class="p-4">
                    <div class="alert alert-danger mb-0">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Error loading cart items. Please try again.
                    </div>
                </div>
            `;
        });
    }
    
    function removeCartItem(cartItemKey) {
        // Create a nonce for security
        const nonce = lci_ajax_object.nonce;
        
        fetch(lci_ajax_object.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=lci_remove_cart_item&nonce=' + nonce + '&cart_item_key=' + cartItemKey
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload mini cart items
                fetchMiniCartItems();
            }
        })
        .catch(error => {
            console.error('Error removing cart item:', error);
        });
    }
    
    // Expose functions globally
    window.lciMiniCart = {
        open: openMiniCart,
        close: closeMiniCart,
        refresh: fetchMiniCartItems,
        showAddToCartConfirmation: showAddToCartModal
    };
});

// Global function to show add to cart confirmation
function showAddToCartConfirmation(productName, productImage) {
    if (window.lciMiniCart && window.lciMiniCart.showAddToCartConfirmation) {
        window.lciMiniCart.showAddToCartConfirmation(productName, productImage);
    }
}
