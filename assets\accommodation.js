/**
 * Accommodation JavaScript
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        // Dispatch a custom event to notify that accommodation.js is loaded
        document.dispatchEvent(new CustomEvent('accommodation:loaded'));

        // Alpine.js handles the accommodation wizard functionality
        console.log('Accommodation.js loaded - Alpine.js will handle the wizard functionality');

        // Check for products already in cart
        checkProductsInCart();

        // Prevent mini cart from auto-opening on page load
        const miniCartContainer = document.getElementById('mini-cart-container');
        if (miniCartContainer && miniCartContainer.__x) {
            try {
                const data = miniCartContainer.__x.getUnobservedData();
                data.autoOpenCart = true; // Set to true to prevent auto-opening
            } catch (error) {
                console.error('Error setting autoOpenCart flag:', error);
            }
        }

        // Show variations button functionality for variable products
        $('.accommodation-product-card .show-variations-btn').on('click', function(e) {
            e.preventDefault();

            // Get the product card and variation card
            const productCard = $(this).closest('.accommodation-product-card')[0];

            if (!productCard) {
                console.error('Product card not found');
                return;
            }

            // Find the variation card that's a direct child of the product card
            const variationCard = $(productCard).children('.accommodation-variation-card')[0];

            // Log the product card and its children for debugging
            console.log('Product card:', productCard);
            console.log('Product card children:', $(productCard).children());

            if (!variationCard) {
                console.error('Variation card not found');
                return;
            }

            // Show the variation card with animation
            $(variationCard).addClass('active');

            // Debug information
            console.log('Variation card activated:', variationCard);
            console.log('Variation options:', $(variationCard).find('.accommodation-variation-option').length);
            console.log('Variation card options:', $(variationCard).find('.accommodation-variation-card-option').length);

            // Add event listener to close button if not already added
            const closeButton = $(variationCard).find('.close-variation-card')[0];
            if (closeButton && !closeButton._hasClickListener) {
                $(closeButton).on('click', function() {
                    // Add a closing class for animation
                    $(variationCard).addClass('closing');

                    // Remove the active class after animation completes
                    setTimeout(() => {
                        $(variationCard).removeClass('active closing');
                    }, 400); // Match the animation duration
                });
                closeButton._hasClickListener = true;
            }

            // Add index to variation options for staggered animation
            $(variationCard).find('.accommodation-variation-option').each(function(index) {
                $(this).css('--option-index', index);
            });

            // Add click handlers for variation card options
            $(variationCard).find('.accommodation-variation-card-option').on('click', function() {
                // Get attribute and value from data attributes
                const attributeName = $(this).data('attribute');
                const value = $(this).data('value');

                // Debug log
                console.log('Selected variation:', attributeName, value);

                // Remove selected class and checkmark from all options in this attribute group
                $(this).parent().find('.accommodation-variation-card-option').removeClass('selected').find('.checkmark').remove();

                // Add selected class to this option
                $(this).addClass('selected');

                // Add checkmark icon if not already present
                if ($(this).find('.checkmark').length === 0) {
                    $(this).append('<span class="checkmark"><i class="fas fa-check"></i></span>');
                }

                // Update the hidden input value
                $(this).closest('.accommodation-variation-option').find('input.variation-dropdown').val(value).removeClass('error');
            });
        });

        // Show quantity button functionality for simple products
        $('.accommodation-product-card .show-quantity-btn').on('click', function(e) {
            e.preventDefault();

            // Get the product card and quantity card
            const productCard = $(this).closest('.accommodation-product-card')[0];

            if (!productCard) {
                console.error('Product card not found');
                return;
            }

            const quantityCard = $(productCard).children('.accommodation-quantity-card')[0];

            if (!quantityCard) {
                console.error('Quantity card not found');
                return;
            }

            // Show the quantity card with animation
            $(quantityCard).addClass('active');

            // Add event listener to close button if not already added
            const closeButton = $(quantityCard).find('.close-quantity-card')[0];
            if (closeButton && !closeButton._hasClickListener) {
                $(closeButton).on('click', function() {
                    // Add a closing class for animation
                    $(quantityCard).addClass('closing');

                    // Remove the active class after animation completes
                    setTimeout(() => {
                        $(quantityCard).removeClass('active closing');
                    }, 400); // Match the animation duration
                });
                closeButton._hasClickListener = true;
            }
        });

        // Quantity plus/minus buttons
        $('.quantity-plus').on('click', function() {
            var input = $(this).siblings('.quantity-input-field');
            var value = parseInt(input.val());
            input.val(value + 1);
        });

        $('.quantity-minus').on('click', function() {
            var input = $(this).siblings('.quantity-input-field');
            var value = parseInt(input.val());
            if (value > 1) {
                input.val(value - 1);
            }
        });

        // Variable product - Add to cart button
        $('.add-variable-to-cart-btn').on('click', function(e) {
            e.preventDefault();

            const productId = $(this).data('product-id');
            const productCard = $(this).closest('.accommodation-product-card');
            const variationCard = $(this).closest('.accommodation-variation-card');

            if (!productCard.length) {
                console.error('Product card not found');
                return;
            }

            const productName = productCard.find('.accommodation-product-title').text().trim();
            const productImage = productCard.find('.accommodation-product-image img').attr('src');
            const originalText = $(this).html();

            // Get selected variations
            const variationSelects = variationCard.find('input.variation-dropdown');
            const variationData = {};
            let allVariationsSelected = true;

            variationSelects.each(function() {
                const attributeName = $(this).data('attribute');
                const attributeValue = $(this).val();

                if (!attributeValue) {
                    allVariationsSelected = false;
                    $(this).addClass('error');

                    // Highlight the variation cards section
                    const cardsContainer = $(this).siblings('.accommodation-variation-cards');
                    cardsContainer.addClass('error-highlight');

                    // Scroll to the error
                    variationCard.find('.accommodation-variation-options').animate({
                        scrollTop: $(this).closest('.accommodation-variation-option').position().top
                    }, 300);
                } else {
                    $(this).removeClass('error');
                    $(this).siblings('.accommodation-variation-cards').removeClass('error-highlight');
                    variationData[`attribute_${attributeName}`] = attributeValue;
                }
            });

            if (!allVariationsSelected) {
                alert('Please select all accommodation options before booking.');
                return;
            }

            // Use fixed quantity of 1 for accommodations
            const quantity = 1;

            // Show loading state
            $(this).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
            $(this).prop('disabled', true);

            // Add to cart via AJAX
            const data = {
                action: 'lci-dashboard-add-to-cart',
                nonce: lci_ajax.nonce,
                security: lci_ajax.nonce,
                product_id: productId,
                quantity: quantity,
                variation_id: 0, // Will be determined by the server
                ...variationData
            };

            $.ajax({
                url: lci_ajax.ajax_url,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Reset button state
                    $('.add-variable-to-cart-btn').html(originalText);
                    $('.add-variable-to-cart-btn').prop('disabled', false);

                    // Close variation card
                    variationCard.removeClass('active');

                    if (response.success) {
                        // Show confirmation modal
                        if (typeof window.showAccommodationAddToCartConfirmation === 'function') {
                            window.showAccommodationAddToCartConfirmation(productName, productImage);
                        } else if (typeof window.showAddToCartConfirmation === 'function') {
                            window.showAddToCartConfirmation(productName, productImage);
                        }

                        // Update mini cart
                        updateMiniCart(response.data.cart_count, response.data.cart_total);

                        // Replace the price and button with "Added to your cart" message
                        var productActions = productCard.find('.accommodation-product-actions');
                        productActions.html('<div class="accommodation-added-to-cart"><i class="fas fa-check-circle"></i> Added to your cart</div>');

                        // Add a class to the product card to mark it as added to cart
                        productCard.addClass('product-in-cart');
                    } else {
                        alert(response.data.message || 'Error adding product to cart');
                    }
                },
                error: function() {
                    // Reset button state
                    $('.add-variable-to-cart-btn').html(originalText);
                    $('.add-variable-to-cart-btn').prop('disabled', false);

                    alert('Error adding product to cart. Please try again.');
                }
            });
        });

        // Simple product - Add to cart button
        $('.add-to-cart-btn').on('click', function(e) {
            e.preventDefault();

            const productId = $(this).data('product-id');
            const productCard = $(this).closest('.accommodation-product-card');
            const quantityCard = $(this).closest('.accommodation-quantity-card');

            if (!productCard.length) {
                console.error('Product card not found');
                return;
            }

            const productName = productCard.find('.accommodation-product-title').text().trim();
            const productImage = productCard.find('.accommodation-product-image img').attr('src');
            const originalText = $(this).html();

            // Get quantity
            const quantityInput = quantityCard.find('.quantity-input-field');
            const quantity = quantityInput.length ? parseInt(quantityInput.val()) : 1;

            // Show loading state
            $(this).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
            $(this).prop('disabled', true);

            // Add to cart via AJAX
            addToCart(productId, quantity, productName, productImage, originalText, productCard, quantityCard);
        });

        // Function to add product to cart
        function addToCart(productId, quantity, productName, productImage, originalText, productCard, quantityCard) {
            // If parameters are not provided, try to get them from the DOM
            if (!productCard) {
                productCard = $('.accommodation-product-card').has('[data-product-id="' + productId + '"]');
            }

            if (!productName) {
                productName = productCard.find('.accommodation-product-title').text();
            }

            if (!productImage) {
                productImage = productCard.find('.accommodation-product-image img').attr('src');
            }

            // Show loading state
            var addButton;
            if (!originalText) {
                addButton = $('.accommodation-product-card [data-product-id="' + productId + '"]');
                originalText = addButton.html();
                addButton.html('<i class="fas fa-spinner fa-spin"></i> Adding...');
                addButton.prop('disabled', true);
            }

            // Prepare data - simplified to always use quantity=1 and no variations
            var data = {
                action: 'lci-dashboard-add-to-cart',
                nonce: lci_ajax.nonce,
                security: lci_ajax.nonce, // Add security parameter as fallback
                product_id: productId,
                quantity: quantity
            };

            // Make AJAX request
            $.ajax({
                url: lci_ajax.ajax_url,
                type: 'POST',
                data: data,
                success: function(response) {
                    // Reset button state
                    if (addButton) {
                        addButton.html(originalText);
                        addButton.prop('disabled', false);
                    }

                    // Close quantity card if it exists
                    if (quantityCard && quantityCard.length) {
                        quantityCard.removeClass('active');
                    }

                    if (response.success) {
                        // Show accommodation confirmation modal
                        if (typeof window.showAccommodationAddToCartConfirmation === 'function') {
                            window.showAccommodationAddToCartConfirmation(productName, productImage);
                        } else if (typeof window.showAddToCartConfirmation === 'function') {
                            window.showAddToCartConfirmation(productName, productImage);
                        }

                        // Update mini cart
                        updateMiniCart(response.data.cart_count, response.data.cart_total);

                        // Replace the price and button with "Added to your cart" message
                        var productActions = productCard.find('.accommodation-product-actions');
                        productActions.html('<div class="accommodation-added-to-cart"><i class="fas fa-check-circle"></i> Added to your cart</div>');

                        // Add a class to the product card to mark it as added to cart
                        productCard.addClass('product-in-cart');
                    } else {
                        alert(response.data.message || 'Error adding product to cart');
                    }
                },
                error: function() {
                    // Reset button state
                    if (addButton) {
                        addButton.html(originalText);
                        addButton.prop('disabled', false);
                    }

                    // Close quantity card if it exists
                    if (quantityCard && quantityCard.length) {
                        quantityCard.removeClass('active');
                    }

                    alert('Error adding product to cart. Please try again.');
                }
            });
        }

        // Function to update mini cart
        function updateMiniCart(count, total) {
            var miniCartButton = $('#mini-cart-button');
            var countBadge = miniCartButton.find('.badge');
            var totalText = miniCartButton.find('.mini-cart-button-text');

            // Update count
            countBadge.text(count);
            countBadge.toggle(count > 0);

            // Update total
            if (totalText.length) {
                totalText.html(total);
            }

            // Update mini cart items
            loadMiniCartItems({
                cart_count: count,
                cart_total: total
            });
        }

        // Function to load mini cart items via AJAX
        function loadMiniCartItems(cartData) {
            // Dispatch event to update Alpine.js mini cart component
            window.dispatchEvent(new CustomEvent('lci:cartUpdated', {
                detail: cartData || {}
            }));
        }

        // Function to check if products are already in cart
        function checkProductsInCart() {
            // Make AJAX request to get cart contents
            $.ajax({
                url: lci_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'lci_get_cart_contents',
                    nonce: lci_ajax.nonce,
                    security: lci_ajax.nonce // Add security parameter as fallback
                },
                success: function(response) {
                    if (response.success && response.data && response.data.cart_items) {
                        // Get product IDs in cart
                        var productsInCart = response.data.cart_items.map(function(item) {
                            return item.product_id.toString();
                        });

                        // Loop through product cards and update UI for products in cart
                        $('.accommodation-product-card').each(function() {
                            var card = $(this);
                            var productBtn = card.find('[data-product-id]');

                            if (productBtn.length) {
                                var productId = productBtn.data('product-id').toString();

                                if (productsInCart.includes(productId)) {
                                    // Replace the price and button with "Added to your cart" message
                                    var productActions = card.find('.accommodation-product-actions');
                                    productActions.html('<div class="accommodation-added-to-cart"><i class="fas fa-check-circle"></i> Added to your cart</div>');

                                    // Add a class to the product card to mark it as added to cart
                                    card.addClass('product-in-cart');
                                }
                            }
                        });
                    }
                }
            });
        }
    });
})(jQuery);
