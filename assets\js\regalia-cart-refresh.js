/**
 * Regalia Shop Cart Refresh
 * Forces a cart refresh when the regalia shop page loads
 */
(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Regalia cart refresh script loaded');
        
        // Check if we're on the regalia shop page
        if (window.location.href.includes('tab=regalia-shop')) {
            console.log('On regalia shop page - forcing cart refresh');
            
            // Force a cart refresh
            setTimeout(function() {
                // First, deduplicate the cart on the server
                fetch(lci_ajax.ajax_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=lci_deduplicate_cart&security=' + lci_ajax.nonce
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Cart deduplication response:', data);
                    
                    // Then refresh the mini-cart
                    if (typeof window.openMiniCart === 'function') {
                        // First open the mini-cart to ensure it's initialized
                        window.openMiniCart();
                        
                        // Then close it after a short delay
                        setTimeout(function() {
                            // Find the close button and click it
                            const closeButton = document.querySelector('.lci-modal-close');
                            if (closeButton) {
                                closeButton.click();
                            }
                        }, 100);
                    }
                })
                .catch(error => {
                    console.error('Error deduplicating cart:', error);
                });
            }, 1000);
        }
    });
})();
