<?php
/**
 * LCI 2025 Dashboard Database Handler
 *
 * Manages database operations for the LCI 2025 Dashboard plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class LCI_Database {
    /**
     * Initialize the database handler
     */
    public static function init() {
        // Register activation hook for table creation
        register_activation_hook(LCI2025_PLUGIN_FILE, [__CLASS__, 'create_tables']);

        // Add upgrade check
        add_action('plugins_loaded', [__CLASS__, 'check_for_db_upgrade']);
    }

    /**
     * Create the necessary database tables
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Participants table
        $table_name = $wpdb->prefix . 'lci2025_participants';

        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            unique_reg_id VARCHAR(20) NOT NULL,
            original_reg_id VARCHAR(50) NOT NULL,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            order_id BIGINT(20) UNSIGNED NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(50),
            country VARCHAR(100),
            order_date DATETIME,
            association VARCHAR(100),
            club_no VARCHAR(50),
            club_position VARCHAR(100),
            registration_type VARCHAR(50) NOT NULL,
            payment_status VARCHAR(50) NOT NULL,
            badge_status VARCHAR(50) DEFAULT 'Pending',
            pub_party_number VARCHAR(50) DEFAULT 'Pending',
            gala_dinner_table VARCHAR(50) DEFAULT 'Pending',
            diet VARCHAR(255),
            allergies TEXT,
            room_share VARCHAR(50),
            share_with VARCHAR(100),
            accommodation TEXT,
            tours TEXT,
            admin_notes TEXT,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            last_sync DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY unique_reg_id (unique_reg_id),
            KEY original_reg_id (original_reg_id),
            KEY user_id (user_id),
            KEY order_id (order_id),
            KEY email (email),
            KEY registration_type (registration_type),
            KEY payment_status (payment_status),
            KEY badge_status (badge_status)
        ) $charset_collate;";

        // Accommodations table
        $table_name = $wpdb->prefix . 'lci2025_accommodations';

        $sql_accommodations = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            participant_id BIGINT(20) UNSIGNED NOT NULL,
            unique_reg_id VARCHAR(12) NOT NULL,
            category_id BIGINT(20) UNSIGNED NOT NULL,
            category_name VARCHAR(100) NOT NULL,
            hotel_id BIGINT(20) UNSIGNED NOT NULL,
            hotel_name VARCHAR(255) NOT NULL,
            room_type_id BIGINT(20) UNSIGNED,
            room_type_name VARCHAR(255),
            check_in_date DATE,
            check_out_date DATE,
            nights INT UNSIGNED DEFAULT 1,
            order_id BIGINT(20) UNSIGNED NOT NULL,
            order_date DATETIME,
            payment_status VARCHAR(50) NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            KEY participant_id (participant_id),
            KEY hotel_id (hotel_id),
            KEY category_id (category_id),
            KEY order_id (order_id)
        ) $charset_collate;";

        // Hotel inventory table
        $table_name = $wpdb->prefix . 'lci2025_hotel_inventory';

        $sql_hotel_inventory = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            hotel_id BIGINT(20) UNSIGNED NOT NULL,
            hotel_name VARCHAR(255) NOT NULL,
            room_type_id BIGINT(20) UNSIGNED,
            room_type_name VARCHAR(255) NOT NULL,
            category_id BIGINT(20) UNSIGNED NOT NULL,
            total_rooms INT UNSIGNED DEFAULT 0,
            allocated_rooms INT UNSIGNED DEFAULT 0,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY hotel_room_type (hotel_id, room_type_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        dbDelta($sql_accommodations);
        dbDelta($sql_hotel_inventory);

        // Create tour tables
        LCI_Tours::create_tables();

        // Store the database version
        update_option('lci2025_db_version', LCI2025_DB_VERSION);
    }

    /**
     * Check if database needs upgrading
     */
    public static function check_for_db_upgrade() {
        $current_db_version = get_option('lci2025_db_version', '0');

        if (version_compare($current_db_version, LCI2025_DB_VERSION, '<')) {
            self::upgrade_database($current_db_version, LCI2025_DB_VERSION);
        }
    }

    /**
     * Upgrade database
     *
     * @param string $old_version Old database version
     * @param string $new_version New database version
     */
    public static function upgrade_database($old_version, $new_version) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        // Perform upgrade tasks based on version
        if (version_compare($old_version, '1.0', '<')) {
            // Initial database setup
            self::create_tables();
        }

        // Check if room_share column needs to be updated
        if (version_compare($old_version, '1.1', '<')) {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if ($table_exists) {
                // Get room_share column info
                $column_info = $wpdb->get_row("SHOW COLUMNS FROM $table_name LIKE 'room_share'");

                // If room_share is VARCHAR(10), update it to VARCHAR(50)
                if ($column_info && strpos($column_info->Type, 'varchar(10)') !== false) {
                    $wpdb->query("ALTER TABLE $table_name MODIFY room_share VARCHAR(50)");
                }
            }
        }

        // Add country and order_date columns if they don't exist
        if (version_compare($old_version, '1.2', '<')) {
            // Check if table exists
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if ($table_exists) {
                // Check if country column exists
                $country_column = $wpdb->get_row("SHOW COLUMNS FROM $table_name LIKE 'country'");
                if (!$country_column) {
                    $wpdb->query("ALTER TABLE $table_name ADD COLUMN country VARCHAR(100) AFTER phone");
                }

                // Check if order_date column exists
                $order_date_column = $wpdb->get_row("SHOW COLUMNS FROM $table_name LIKE 'order_date'");
                if (!$order_date_column) {
                    $wpdb->query("ALTER TABLE $table_name ADD COLUMN order_date DATETIME AFTER country");
                }
            }
        }

        // Update database version
        update_option('lci2025_db_version', $new_version);
    }

    /**
     * Generate a unique registration ID
     *
     * Format: LCI{YEAR}{6-CHAR-RANDOM-ALPHANUMERIC}
     * Example: LCI25AB12CD
     * Total length: 12 characters
     *
     * @return string Unique registration ID
     */
    public static function generate_unique_reg_id() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'lci2025_participants';

        // Get current year (2-digit format)
        $year = date('y');

        // Get prefix from settings or use default
        $prefix = get_option('lci2025_reg_id_prefix', 'LCI');

        // Calculate how many random characters we need
        // Total length should be 12 characters
        $prefix_length = strlen($prefix);
        $year_length = 2; // Always 2 digits for year
        $random_length = 12 - $prefix_length - $year_length;

        // Ensure we have at least 4 random characters
        if ($random_length < 4) {
            $random_length = 4;
        }

        // Try up to 10 times to generate a unique ID
        $max_attempts = 10;
        $attempt = 0;
        $unique_id = null;

        while ($attempt < $max_attempts) {
            // Generate random alphanumeric string (excluding similar-looking characters)
            $characters = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'; // Removed 0,1,I,O to avoid confusion
            $random_string = '';

            for ($i = 0; $i < $random_length; $i++) {
                $random_string .= $characters[rand(0, strlen($characters) - 1)];
            }

            // Create the new unique ID
            $candidate_id = $prefix . $year . $random_string;

            // Check if this ID already exists
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE unique_reg_id = %s",
                $candidate_id
            ));

            if ($exists == 0) {
                $unique_id = $candidate_id;
                break;
            }

            $attempt++;
        }

        // If we couldn't generate a unique ID after max attempts, add timestamp to ensure uniqueness
        if ($unique_id === null) {
            $timestamp = substr(time(), -4);
            $random_string = substr($random_string, 0, $random_length - 4) . $timestamp;
            $unique_id = $prefix . $year . $random_string;
        }

        return $unique_id;
    }
}

// Initialize the database handler
LCI_Database::init();
