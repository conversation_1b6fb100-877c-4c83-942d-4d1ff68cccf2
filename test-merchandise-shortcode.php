<?php
/**
 * Template Name: Test Merchandise Shortcode
 */

get_header();
?>

<div class="container mt-5 mb-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">LCI 2025 Merchandise Shop</h1>

            <?php
            // Get category ID from URL parameter, default to 22
            $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 22;
            $title = isset($_GET['title']) ? sanitize_text_field($_GET['title']) : 'LCI 2025 Merchandise';
            $columns = isset($_GET['columns']) ? intval($_GET['columns']) : 3;

            // Validate columns (1-4)
            $columns = max(1, min(4, $columns));

            // Display the shortcode
            echo do_shortcode('[lci_merchandise_shop category_id="' . $category_id . '" title="' . $title . '" columns="' . $columns . '"]');
            ?>

            <div class="card mt-4 mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Try Different Category ID</h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="category_id" class="form-label">Category ID</label>
                            <input type="number" class="form-control" id="category_id" name="category_id" value="<?php echo $category_id; ?>" min="1" step="1">
                        </div>
                        <div class="col-md-4">
                            <label for="title" class="form-label">Title</label>
                            <input type="text" class="form-control" id="title" name="title" value="<?php echo esc_attr($title); ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="columns" class="form-label">Columns</label>
                            <select class="form-select" id="columns" name="columns">
                                <?php for ($i = 1; $i <= 4; $i++): ?>
                                <option value="<?php echo $i; ?>" <?php selected($columns, $i); ?>><?php echo $i; ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">Update</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="mt-5">
                <h2>How to Use This Shortcode</h2>
                <p>The <code>[lci_merchandise_shop]</code> shortcode allows you to display merchandise products from a WooCommerce category on any page, making them available for purchase by both authenticated and unauthenticated users.</p>

                <h3>Basic Usage</h3>
                <pre><code>[lci_merchandise_shop]</code></pre>

                <h3>With Parameters</h3>
                <pre><code>[lci_merchandise_shop category_id="22" title="LCI 2025 Merchandise" columns="3"]</code></pre>

                <h3>Available Parameters</h3>
                <ul>
                    <li><strong>category_id</strong> - The WooCommerce category ID to display products from (default: 22)</li>
                    <li><strong>title</strong> - The title to display above the products (default: "LCI 2025 Merchandise Shop")</li>
                    <li><strong>columns</strong> - The number of columns to display products in (1-4, default: 3)</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php
get_footer();
?>
