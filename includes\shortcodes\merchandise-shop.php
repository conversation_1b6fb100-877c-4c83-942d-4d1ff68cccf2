<?php
/**
 * LCI 2025 Merchandise Shop Shortcode
 *
 * Allows unauthenticated users to buy merchandise using a shortcode
 * Usage: [lci_merchandise_shop category_id="22"]
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

/**
 * Register the merchandise shop shortcode
 */
function lci_register_merchandise_shop_shortcode() {
    add_shortcode('lci_merchandise_shop', 'lci_merchandise_shop_shortcode');
}
add_action('init', 'lci_register_merchandise_shop_shortcode');

/**
 * Merchandise shop shortcode callback
 *
 * @param array $atts Shortcode attributes
 * @return string Shortcode output
 */
function lci_merchandise_shop_shortcode($atts) {
    // Extract shortcode attributes
    $atts = shortcode_atts(
        array(
            'category_id' => 22, // Default to regalia category
            'title' => 'LCI 2025 Merchandise Shop',
            'columns' => 3,
        ),
        $atts,
        'lci_merchandise_shop'
    );

    // Convert attributes to variables
    $category_id = intval($atts['category_id']);
    $title = sanitize_text_field($atts['title']);
    $columns = intval($atts['columns']);

    // Validate columns (1-4)
    $columns = max(1, min(4, $columns));

    // Enqueue necessary scripts and styles
    wp_enqueue_style('merchandise-shop-css', LCI2025_URL . 'assets/css/regalia-shop.css', array(), LCI2025_VERSION);
    wp_enqueue_script('merchandise-shop-js', LCI2025_URL . 'assets/js/merchandise-shop.js', array('jquery', 'alpine-js'), LCI2025_VERSION, true);
    wp_enqueue_script('fix-alpine', LCI2025_URL . 'assets/js/fix-alpine.js', array('jquery', 'alpine-js'), LCI2025_VERSION, true);
    wp_enqueue_script('fix-quantity-buttons', LCI2025_URL . 'assets/js/fix-quantity-buttons.js', array('jquery'), LCI2025_VERSION, true);
    wp_enqueue_script('fundraising-js', LCI2025_URL . 'assets/js/fundraising.js', array('jquery'), LCI2025_VERSION, true);

    // Get products from the specified category
    $merchandise_products = lci_get_merchandise_products($category_id);

    // Start output buffering
    ob_start();

    // Get current user (will be empty for unauthenticated users)
    $current_user = wp_get_current_user();
    $user_id = $current_user->ID;

    // Create nonce for AJAX requests
    $nonce = wp_create_nonce('lci-merchandise-add-to-cart-nonce');

    // Get cart contents count
    $cart_count = WC()->cart ? WC()->cart->get_cart_contents_count() : 0;

    // Get cart total
    $cart_total = WC()->cart ? WC()->cart->get_cart_total() : 0;
    ?>
    <div class="regalia-shop-container">
        <!-- Header with title and mini cart -->
        <div class="mb-4">
            <!-- Title - centered on mobile -->
            <div class="text-center text-md-start mb-3">
                <h2 class="text-primary mb-0 h3"><i class="fas fa-tshirt me-2"></i> <?php echo esc_html($title); ?></h2>
            </div>

            <!-- Mini Cart Button - below title on mobile -->
            <div class="d-flex justify-content-center justify-content-md-end">
                <?php echo lci_get_mini_cart_html(); ?>
            </div>
        </div>

        <!-- Products Section -->
        <div class="mb-5">
            <?php if (empty($merchandise_products)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No merchandise products are currently available. Please check back later.
                </div>

                <!-- Debug information -->
                <div class="alert alert-warning mt-3">
                    <h5><i class="fas fa-bug me-2"></i> Debug Information</h5>
                    <p>Attempting to retrieve products from category ID: <?php echo $category_id; ?></p>

                    <?php
                    // Check if the category exists
                    $term = get_term_by('id', $category_id, 'product_cat');
                    if ($term) {
                        echo '<p>Category found: ' . esc_html($term->name) . ' (ID: ' . esc_html($term->term_id) . ')</p>';

                        // Get all product categories for comparison
                        $product_categories = get_terms(array(
                            'taxonomy' => 'product_cat',
                            'hide_empty' => false,
                        ));

                        if (!empty($product_categories) && !is_wp_error($product_categories)) {
                            echo '<p>Available product categories:</p>';
                            echo '<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-2 mb-3">';
                            foreach ($product_categories as $category) {
                                echo '<div class="col">';
                                echo '<div class="card p-2 mb-2">';
                                echo esc_html($category->name) . ' <span class="badge bg-secondary float-end">' . esc_html($category->term_id) . '</span>';
                                echo '<div class="small text-muted">Products: ' . esc_html($category->count) . '</div>';
                                echo '</div>';
                                echo '</div>';
                            }
                            echo '</div>';

                            // Try to get products using different methods
                            echo '<h6>Trying different methods to get products:</h6>';

                            // Method 1: Using WP_Query
                            $args = array(
                                'post_type'      => 'product',
                                'posts_per_page' => -1,
                                'post_status'    => 'publish',
                                'tax_query'      => array(
                                    array(
                                        'taxonomy' => 'product_cat',
                                        'field'    => 'term_id',
                                        'terms'    => $category_id,
                                    ),
                                ),
                            );

                            $products_query = new WP_Query($args);
                            $method1_count = $products_query->post_count;
                            echo '<p>Method 1 (WP_Query): Found ' . $method1_count . ' products</p>';

                            // Method 2: Using wc_get_products
                            $args = array(
                                'status' => 'publish',
                                'limit'  => -1,
                                'category' => array($category_id),
                            );

                            $method2_products = wc_get_products($args);
                            $method2_count = count($method2_products);
                            echo '<p>Method 2 (wc_get_products): Found ' . $method2_count . ' products</p>';

                            // Method 3: Using get_posts
                            $args = array(
                                'post_type'      => 'product',
                                'posts_per_page' => -1,
                                'post_status'    => 'publish',
                                'tax_query'      => array(
                                    array(
                                        'taxonomy' => 'product_cat',
                                        'field'    => 'slug',
                                        'terms'    => $term->slug,
                                    ),
                                ),
                            );

                            $posts = get_posts($args);
                            $method3_count = count($posts);
                            echo '<p>Method 3 (get_posts): Found ' . $method3_count . ' products</p>';
                        } else {
                            echo '<p>No product categories found or error retrieving categories.</p>';
                        }
                    } else {
                        echo '<p>Category with ID ' . esc_html($category_id) . ' not found. Please check the category ID.</p>';
                    }
                    ?>
                </div>
            <?php else: ?>
                <?php
                // Get fundraising progress data from options
                $fundraising_goal = get_option('lci_regalia_fundraising_goal', 1500); // Default €1500
                $fundraising_current = get_option('lci_regalia_fundraising_current', 0); // Default €0
                $fundraising_percentage = min(100, ($fundraising_current / $fundraising_goal) * 100);
                $participants_funded = floor($fundraising_current / $fundraising_goal);
                $additional_progress = ($fundraising_current % $fundraising_goal) / $fundraising_goal * 100;

                // Get the stored contributor count from the options
                $contributor_count = get_option('lci_regalia_contributor_count', 0);

                // Display the CTA and progress bar
                echo '<div class="regalia-fundraising-cta mb-4">';
                echo '<div class="regalia-cta-message">';
                echo '<p class="regalia-cta-headline"><i class="fas fa-heart me-2" style="color: #fab33a;"></i> Your Purchase Makes a Difference!</p>';
                echo '<p class="regalia-cta-text">All profits from Regalia sales until May will be used to offer a <strong>free event participation to a young Circler</strong>. If the funds raised exceed the cost of one participation, we will open additional spots to support even more young members!</p>';
                echo '</div>';

                echo '<div class="regalia-progress-container">';
                echo '<div class="regalia-progress-stats">';
                echo '<div class="regalia-progress-raised">€' . number_format($fundraising_current, 0, '.', ',') . ' <span>raised</span></div>';
                echo '<div class="regalia-progress-goal">€' . number_format($fundraising_goal, 0, '.', ',') . ' <span>goal per participant</span></div>';
                echo '</div>';

                echo '<div class="regalia-progress-bar-container">';
                echo '<div class="regalia-progress-bar" style="width: ' . $fundraising_percentage . '%"></div>';

                // Add milestone markers for each full participant funded
                if ($participants_funded > 0) {
                    for ($i = 1; $i <= $participants_funded; $i++) {
                        $milestone_position = min(100, ($i * 100) / $participants_funded);
                        echo '<div class="regalia-milestone" style="left: ' . $milestone_position . '%"><i class="fas fa-user-graduate"></i></div>';
                    }
                }

                echo '</div>';

                echo '<div class="regalia-impact-message">';

                if ($participants_funded > 0) {
                    echo '<p><strong>' . $participants_funded . ' young ' . ($participants_funded == 1 ? 'Circler' : 'Circlers/ ') . '</strong> will attend thanks to your generosity! ' .
                         ($additional_progress > 0 ? 'We\'re ' . round($additional_progress) . '% of the way to sponsoring another!' : '') . '</p>';
                } else {
                    if ($contributor_count > 0) {
                        echo '<p class="regalia-contributor-count"><i class="fas fa-users text-primary me-1"></i> Join the <strong>' . $contributor_count . ' ' . ($contributor_count == 1 ? 'Circler' : 'Circlers / Partners') . '</strong> who ' . ($contributor_count == 1 ? 'has' : 'have') . ' already contributed and help a young Circler attend LCI 2025 AGM in Brasov!</p>';
                    } else {
                        echo '<p class="regalia-contributor-count"><i class="fas fa-heart text-danger me-1"></i> Be the first to help a young Circler attend this transformative event!</p>';
                    }
                }

                // Only show additional contributor info if we have funded participants
                if ($participants_funded > 0 && $contributor_count > 0) {
                    echo '<p class="regalia-contributor-count"><i class="fas fa-users text-primary me-1"></i> <strong>' . $contributor_count . ' ' . ($contributor_count == 1 ? 'Circler has' : 'Circlers have') . '</strong> contributed so far!</p>';
                }
                echo '</div>';

                echo '</div>';
                echo '</div>'; // Close the main regalia-fundraising-cta div
                ?>

                <div class="regalia-products-container">
                    <div class="regalia-products-grid">
                    <?php foreach ($merchandise_products as $product): ?>
                        <div class="regalia-product-card">
                            <div class="regalia-product-image">
                                <img src="<?php echo esc_url(wp_get_attachment_url($product->get_image_id())); ?>" alt="<?php echo esc_attr($product->get_name()); ?>">

                                <!-- Badges -->
                                <div class="regalia-badges-left">
                                    <?php if ($product->is_on_sale()): ?>
                                    <div class="regalia-badge regalia-badge-sale">
                                        <i class="fas fa-percentage mr-1"></i>Sale!
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($product->is_featured()): ?>
                                    <div class="regalia-badge regalia-badge-featured">
                                        <i class="fas fa-star mr-1"></i>Featured
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="regalia-product-content">
                                <h3 class="regalia-product-title"><?php echo esc_html($product->get_name()); ?></h3>

                                <div class="regalia-product-description">
                                    <?php echo wp_kses_post($product->get_description()); ?>
                                </div>

                                <?php if ($product->is_type('variable')): ?>
                                <div class="regalia-product-variants">
                                    <i class="fas fa-layer-group mr-1"></i>
                                    <span>
                                    <?php
                                        $variations = $product->get_available_variations();
                                        $count = count($variations);
                                        echo $count . ' ' . ($count == 1 ? 'variant' : 'variants') . ' available';
                                    ?>
                                    </span>
                                </div>
                                <?php endif; ?>

                                <div class="regalia-product-actions">
                                    <div class="regalia-product-price">
                                        <?php if ($product->is_on_sale()): ?>
                                        <div class="regalia-price-sale">
                                            <span class="regalia-price-new"><?php echo wp_kses_post(wc_price($product->get_sale_price())); ?></span>
                                            <span class="regalia-price-old"><?php echo wp_kses_post(wc_price($product->get_regular_price())); ?></span>
                                        </div>
                                        <?php else: ?>
                                        <span class="regalia-price-regular"><?php echo wp_kses_post($product->get_price_html()); ?></span>
                                        <?php endif; ?>
                                    </div>

                                    <?php if ($product->is_in_stock()): ?>
                                        <?php if ($product->is_type('variable')): ?>
                                        <button type="button" class="regalia-btn regalia-btn-primary merchandise-show-variations" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                                            <i class="fas fa-shopping-cart"></i>
                                            <span>Order</span>
                                        </button>
                                        <?php else: ?>
                                        <button type="button" class="regalia-btn regalia-btn-primary merchandise-show-quantity" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                                            <i class="fas fa-shopping-cart"></i>
                                            <span>Order</span>
                                        </button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <button class="regalia-btn regalia-btn-disabled" disabled>
                                            <i class="fas fa-times-circle"></i>
                                            <span>Out of Stock</span>
                                        </button>
                                    <?php endif; ?>
                                </div>

                                <!-- Variation/Quantity Card -->
                                <?php if ($product->is_type('variable')): ?>
                                <!-- Variable Product Card -->
                                <div class="merchandise-variation-card" id="variation-card-<?php echo esc_attr($product->get_id()); ?>">
                                    <div class="merchandise-variation-header">
                                        <h4>Select Options</h4>
                                        <button type="button" class="merchandise-close-variation-card" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>

                                    <div class="merchandise-variation-options">
                                        <?php
                                        $variation_attributes = $product->get_variation_attributes();
                                        foreach ($variation_attributes as $attribute_name => $attribute_values) {
                                            $attribute_label = wc_attribute_label($attribute_name);
                                            echo '<div class="merchandise-variation-option">';
                                            echo '<label>' . esc_html($attribute_label) . '</label>';
                                            echo '<select class="merchandise-variation-dropdown" data-attribute="' . esc_attr(sanitize_title($attribute_name)) . '">';
                                            echo '<option value="">Choose ' . esc_html($attribute_label) . '</option>';

                                            foreach ($attribute_values as $attribute_value) {
                                                echo '<option value="' . esc_attr($attribute_value) . '">' . esc_html($attribute_value) . '</option>';
                                            }

                                            echo '</select>';
                                            echo '</div>';
                                        }
                                        ?>

                                        <div class="merchandise-variation-option">
                                            <label>Quantity</label>
                                            <div class="merchandise-quantity">
                                                <button type="button" class="merchandise-quantity-minus">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <input type="number" value="1" min="1" class="merchandise-quantity-input">
                                                <button type="button" class="merchandise-quantity-plus">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="merchandise-variation-actions">
                                        <button type="button" class="regalia-btn regalia-btn-primary merchandise-add-variable-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>" style="width: 100%; color: #fff;">
                                            <i class="fas fa-shopping-cart" style="color: #fff;"></i>
                                            <span>Confirm</span>
                                        </button>
                                    </div>
                                </div>
                                <?php else: ?>
                                <!-- Simple Product Quantity Card -->
                                <div class="merchandise-variation-card merchandise-quantity-card" id="quantity-card-<?php echo esc_attr($product->get_id()); ?>">
                                    <div class="merchandise-variation-header">
                                        <h4>Select Quantity</h4>
                                        <button type="button" class="merchandise-close-variation-card" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>

                                    <div class="merchandise-variation-options">
                                        <div class="merchandise-variation-option">
                                            <label>Quantity</label>
                                            <div class="merchandise-quantity">
                                                <button type="button" class="merchandise-quantity-minus">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <input type="number" value="1" min="1" class="merchandise-quantity-input">
                                                <button type="button" class="merchandise-quantity-plus">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="merchandise-variation-actions">
                                        <button type="button" class="regalia-btn regalia-btn-primary merchandise-add-to-cart" data-product-id="<?php echo esc_attr($product->get_id()); ?>" style="width: 100%; color: #fff;">
                                            <i class="fas fa-shopping-cart" style="color: #fff;"></i>
                                            <span>Confirm</span>
                                        </button>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    </div>
                    <div id="sentinel" class="h-20"></div>
                </div>
            <?php endif; ?>
        </div>

        <?php
        // Only show previous orders section for logged-in users
        if (is_user_logged_in()):
            // Get user's orders containing products from the merchandise category
            $user_orders = [];

            // Check if WooCommerce is active
            if (class_exists('WooCommerce')) {
                // Get all customer orders
                $customer_orders = wc_get_orders([
                    'customer' => $user_id,
                    'limit' => -1,
                    'status' => ['processing', 'completed', 'on-hold'],
                    'orderby' => 'date',
                    'order' => 'DESC',
                ]);

                // Filter orders to only include those with merchandise products
                foreach ($customer_orders as $order) {
                    $has_merchandise = false;
                    $merchandise_items = [];

                    foreach ($order->get_items() as $item) {
                        $product_id = $item->get_product_id();
                        $product = wc_get_product($product_id);

                        if ($product) {
                            $categories = $product->get_category_ids();

                            if (in_array($category_id, $categories)) {
                                $has_merchandise = true;
                                $merchandise_items[] = [
                                    'id' => $product_id,
                                    'name' => $item->get_name(),
                                    'quantity' => $item->get_quantity(),
                                    'total' => $item->get_total(),
                                    'image' => wp_get_attachment_url($product->get_image_id()),
                                ];
                            }
                        }
                    }

                    if ($has_merchandise) {
                        $user_orders[] = [
                            'id' => $order->get_id(),
                            'date' => $order->get_date_created()->date('Y-m-d H:i:s'),
                            'status' => $order->get_status(),
                            'total' => $order->get_total(),
                            'items' => $merchandise_items,
                        ];
                    }
                }
            }
        ?>

        <!-- Previous Orders Section -->
        <?php if (!empty($user_orders)): ?>
        <div class="previous-orders-section mt-5">
            <h3 class="h5 mb-4 text-primary"><i class="fas fa-shopping-bag me-2"></i> My Previous Orders</h3>
            <div class="accordion" id="ordersAccordion">
                <?php foreach ($user_orders as $index => $order): ?>
                    <div class="accordion-item border-0 mb-3 shadow-sm rounded-4 overflow-hidden">
                        <h2 class="accordion-header" id="heading<?php echo $index; ?>">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $index; ?>" aria-expanded="false" aria-controls="collapse<?php echo $index; ?>">
                                <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                    <div>
                                        <span class="fw-bold">Order #<?php echo esc_html($order['id']); ?></span>
                                        <span class="ms-3 text-muted"><?php echo date('F j, Y', strtotime($order['date'])); ?></span>
                                    </div>
                                    <div>
                                        <span class="badge bg-<?php echo esc_attr(lci_get_order_status_color($order['status'])); ?>">
                                            <?php echo esc_html(wc_get_order_status_name($order['status'])); ?>
                                        </span>
                                        <span class="ms-3 fw-bold"><?php echo wc_price($order['total']); ?></span>
                                    </div>
                                </div>
                            </button>
                        </h2>
                        <div id="collapse<?php echo $index; ?>" class="accordion-collapse collapse" aria-labelledby="heading<?php echo $index; ?>" data-bs-parent="#ordersAccordion">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th style="width: 80px">Image</th>
                                                <th>Product</th>
                                                <th class="text-center">Quantity</th>
                                                <th class="text-end">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($order['items'] as $item): ?>
                                                <tr>
                                                    <td>
                                                        <?php if ($item['image']): ?>
                                                            <img src="<?php echo esc_url($item['image']); ?>" alt="<?php echo esc_attr($item['name']); ?>" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                                                <i class="fas fa-image text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo esc_html($item['name']); ?></td>
                                                    <td class="text-center"><?php echo esc_html($item['quantity']); ?></td>
                                                    <td class="text-end"><?php echo wc_price($item['total']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php else: ?>
        <div class="previous-orders-section mt-5">
            <h3 class="h5 mb-4 text-primary"><i class="fas fa-shopping-bag me-2"></i> My Previous Orders</h3>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> You haven't purchased any merchandise items yet.
            </div>
        </div>
        <?php endif; ?>

        <?php endif; // End of is_user_logged_in check ?>
    </div>

    <!-- Add to Cart Confirmation Modal -->
    <?php echo lci_get_add_to_cart_confirm_html(); ?>

    <!-- Add inline script to ensure variation cards work properly -->
    <script>
    jQuery(document).ready(function($) {
        console.log('Merchandise shop inline script loaded');

        // Function to show alert modal with custom message
        function showAlertModal(message, isHTML = false) {
            if (isHTML) {
                $('#merchandiseAlertMessage').html(message);
            } else {
                $('#merchandiseAlertMessage').text(message);
            }
            const alertModal = new bootstrap.Modal(document.getElementById('merchandiseAlertModal'));
            alertModal.show();
        }

        // Function to show add to cart confirmation modal
        function showAddToCartConfirmation(productName, variations = null, quantity = 1) {
            // Set product details in modal
            $('#added-product-name').text(productName);

            // Clear previous variations
            $('#added-product-variations').empty();

            // Add variations if provided
            if (variations && Object.keys(variations).length > 0) {
                let variationHtml = '';
                for (const attribute in variations) {
                    const attributeName = attribute.replace('attribute_pa_', '').replace('attribute_', '').replace(/-/g, ' ');
                    const formattedAttribute = attributeName.charAt(0).toUpperCase() + attributeName.slice(1);
                    variationHtml += '<span class="variation-item">' + formattedAttribute + ': ' + variations[attribute] + '</span>';
                }
                $('#added-product-variations').html(variationHtml);
            }

            // Get fundraising message for the modal
            $.ajax({
                url: lci_ajax_object.ajax_url,
                type: 'POST',
                data: {
                    action: 'lci_get_fundraising_message',
                    nonce: lci_ajax_object.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        $('#modal-fundraising-message').html(response.data);
                    } else {
                        $('#modal-fundraising-message').empty();
                    }
                }
            });

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('merchandiseAddToCartModal'));
            modal.show();

            // Animate progress bar
            $('#autoCloseProgress').css('width', '0%');
            setTimeout(function() {
                $('#autoCloseProgress').css('width', '100%');
            }, 100);

            // Auto-close modal after 3 seconds
            setTimeout(function() {
                modal.hide();
            }, 3000);

            // Refresh mini cart
            $(document.body).trigger('wc_fragment_refresh');
        }

        // Show variation card for variable products
        $('.merchandise-show-variations').on('click', function() {
            const productId = $(this).data('product-id');
            const variationCard = $('#variation-card-' + productId);
            const button = $(this);

            console.log('Show variations clicked for product ID:', productId);
            console.log('Variation card found:', variationCard.length);

            // Toggle the card
            if (variationCard.hasClass('active')) {
                variationCard.removeClass('active');
                button.html('<i class="fas fa-shopping-cart"></i><span>Order</span>');
            } else {
                // Hide all other cards
                $('.merchandise-variation-card.active').removeClass('active');
                $('.merchandise-show-variations, .merchandise-show-quantity').each(function() {
                    $(this).html('<i class="fas fa-shopping-cart"></i><span>Order</span>');
                });

                // Show this card
                variationCard.addClass('active');
                button.html('<i class="fas fa-times"></i><span>Cancel</span>');
            }

        });

        // Show quantity card for simple products
        $('.merchandise-show-quantity').on('click', function() {
            const productId = $(this).data('product-id');
            const quantityCard = $('#quantity-card-' + productId);
            const button = $(this);

            console.log('Show quantity clicked for product ID:', productId);
            console.log('Quantity card found:', quantityCard.length);

            // Toggle the card
            if (quantityCard.hasClass('active')) {
                quantityCard.removeClass('active');
                button.html('<i class="fas fa-shopping-cart"></i><span>Order</span>');
            } else {
                // Hide all other cards
                $('.merchandise-variation-card.active').removeClass('active');
                $('.merchandise-show-variations, .merchandise-show-quantity').each(function() {
                    $(this).html('<i class="fas fa-shopping-cart"></i><span>Order</span>');
                });

                // Show this card
                quantityCard.addClass('active');
                button.html('<i class="fas fa-times"></i><span>Cancel</span>');
            }


        });

        // Close variation card
        $('.merchandise-close-variation-card').on('click', function() {
            const productId = $(this).data('product-id');
            const card = $(this).closest('.merchandise-variation-card');
            const button = $('.merchandise-show-variations[data-product-id="' + productId + '"], .merchandise-show-quantity[data-product-id="' + productId + '"]');

            // Add closing class for animation
            card.addClass('closing');

            // Reset button text
            button.html('<i class="fas fa-shopping-cart"></i><span>Order</span>');

            // Remove active class after animation completes
            setTimeout(function() {
                card.removeClass('active closing');
            }, 500);
        });



        // Quantity buttons
        $('.merchandise-quantity-plus').on('click', function() {
            const input = $(this).siblings('input');
            const value = parseInt(input.val(), 10);
            input.val(value + 1);
        });

        $('.merchandise-quantity-minus').on('click', function() {
            const input = $(this).siblings('input');
            const value = parseInt(input.val(), 10);
            if (value > 1) {
                input.val(value - 1);
            }
        });

        // Add to cart for simple products
        $('.merchandise-add-to-cart').on('click', function() {
            const productId = $(this).data('product-id');
            const quantity = $(this).closest('.merchandise-variation-card').find('.merchandise-quantity-input').val();

            console.log('Adding to cart:', productId, 'Quantity:', quantity);

            // Implement AJAX add to cart
            $.ajax({
                url: lci_ajax_object.ajax_url,
                type: 'POST',
                data: {
                    action: 'lci_merchandise_add_to_cart',
                    product_id: productId,
                    quantity: quantity,
                    security: lci_ajax_object.add_to_cart_nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Get product title
                        const productCard = $('[data-product-id="' + productId + '"]').closest('.regalia-product-card');
                        const productTitle = productCard.find('.regalia-product-title').text().trim();

                        // Show confirmation modal
                        showAddToCartConfirmation(productTitle, null, quantity);

                        // Hide card with animation
                        const card = $('.merchandise-variation-card.active');
                        card.addClass('closing');

                        // Remove active class after animation completes
                        setTimeout(function() {
                            card.removeClass('active closing');
                        }, 500);

                        // Update button text
                        const button = $('.merchandise-show-quantity[data-product-id="' + productId + '"]');
                        button.html('<i class="fas fa-check me-2"></i><span>Added!</span>');

                        // Reset button text after 2 seconds
                        setTimeout(function() {
                            button.html('<i class="fas fa-shopping-cart"></i><span>Order</span>');
                        }, 2000);

                        // Refresh fundraising message in mini cart
                        if (typeof window.refreshFundraisingMessage === 'function') {
                            window.refreshFundraisingMessage();
                        }
                    } else {
                        // Show error message
                        showAlertModal(response.data.message || 'Failed to add product to cart.');
                    }
                },
                error: function() {
                    // Show error message
                    showAlertModal('An error occurred. Please try again.');
                }
            });



        });

        // Add to cart for variable products
        $('.merchandise-add-variable-to-cart').on('click', function() {
            const productId = $(this).data('product-id');
            const quantity = $(this).closest('.merchandise-variation-card').find('.merchandise-quantity-input').val();

            // Get selected variations
            const variations = {};
            $(this).closest('.merchandise-variation-card').find('.merchandise-variation-dropdown').each(function() {
                const attribute = $(this).data('attribute');
                const value = $(this).val();

                if (!value) {
                    // Show the modal alert with a more user-friendly attribute name
                    const attributeName = attribute.replace('pa_', '').replace(/-/g, ' ');
                    const formattedAttribute = attributeName.charAt(0).toUpperCase() + attributeName.slice(1);
                    showAlertModal('Please select ' + formattedAttribute + ' before adding to cart.');
                    return false;
                }

                variations[attribute] = value;
            });

            console.log('Adding to cart:', productId, 'Quantity:', quantity, 'Variations:', variations);

            // Implement AJAX add to cart
            $.ajax({
                url: lci_ajax_object.ajax_url,
                type: 'POST',
                data: {
                    action: 'lci_merchandise_add_variable_to_cart',
                    product_id: productId,
                    quantity: quantity,
                    variation_id: 0, // Will be determined by the server based on attributes
                    variation: variations,
                    security: lci_ajax_object.add_to_cart_nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Get product title
                        const productCard = $('[data-product-id="' + productId + '"]').closest('.regalia-product-card');
                        const productTitle = productCard.find('.regalia-product-title').text().trim();

                        // Format variations for display in the modal
                        const formattedVariations = {};
                        for (const attribute in variations) {
                            // Format attribute name for display
                            const attributeKey = 'attribute_' + attribute;
                            formattedVariations[attributeKey] = variations[attribute];
                        }

                        // Show confirmation modal with variations
                        showAddToCartConfirmation(productTitle, formattedVariations, quantity);

                        // Hide card with animation
                        const card = $('.merchandise-variation-card.active');
                        card.addClass('closing');

                        // Remove active class after animation completes
                        setTimeout(function() {
                            card.removeClass('active closing');
                        }, 500);

                        // Update button text
                        const button = $('.merchandise-show-variations[data-product-id="' + productId + '"]');
                        button.html('<i class="fas fa-check me-2"></i><span>Added!</span>');

                        // Reset button text after 2 seconds
                        setTimeout(function() {
                            button.html('<i class="fas fa-shopping-cart"></i><span>Order</span>');
                        }, 2000);

                        // Refresh fundraising message in mini cart
                        if (typeof window.refreshFundraisingMessage === 'function') {
                            window.refreshFundraisingMessage();
                        }
                    } else {
                        // Show error message
                        showAlertModal(response.data.message || 'Failed to add product to cart.');
                    }
                },
                error: function() {
                    // Show error message
                    showAlertModal('An error occurred. Please try again.');
                }
            });




        });
    });
    </script>

    <style>
    /* Import Open Sans font */
    @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap');

    /* Modal Backdrop Fix - Inline Critical CSS */
    .modal-backdrop + .modal-backdrop { display: none !important; }
    body > .modal-backdrop:nth-child(n+2) { display: none !important; }
    body.modal-open { overflow: hidden !important; padding-right: 0 !important; }

    /* Regalia Shop Styles - 2025 UX */
    .regalia-shop-container {
        font-family: 'Open Sans', sans-serif;
    }

    /* Mobile Responsive Fixes */
    @media (max-width: 767.98px) {
        .regalia-shop-container h2 {
            font-size: 1.5rem;
        }

        /* Mini cart button styling for mobile */
        #mini-cart-button {
            margin-bottom: 1rem;
            width: auto;
            min-width: 200px;
        }

        /* Contributor count text justified on mobile */
        .regalia-contributor-count {
            text-align: justify;
            display: block;
            width: 100%;
        }
    }

    /* Fundraising CTA */
    .regalia-fundraising-cta {
        background: linear-gradient(135deg, #f8fbfd 0%, #eef7fc 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.025);
        margin-bottom: 2rem;
    }

    .regalia-cta-message {
        margin-bottom: 1.5rem;
    }

    .regalia-cta-headline {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .regalia-cta-text {
        font-size: 0.95rem;
        color: #555;
        line-height: 1.6;
        margin-bottom: 0;
    }

    .regalia-progress-container {
        margin-top: 1rem;
    }

    .regalia-progress-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .regalia-progress-raised {
        font-weight: 600;
        color: #36b1dc;
    }

    .regalia-progress-raised span,
    .regalia-progress-goal span {
        font-weight: 400;
        font-size: 0.85rem;
        color: #777;
    }

    .regalia-progress-goal {
        text-align: right;
        color: #555;
    }

    /* Progress Bar Container */
    .regalia-progress-bar-container {
        height: 1rem;
        background-color: #e2e8f0;
        border-radius: 0.75rem;
        position: relative;
        margin-bottom: 0.75rem;
        overflow: hidden;
    }

    .regalia-progress-bar {
        height: 100%;
        background: linear-gradient(to right, #36b1dc, #2d93b7);
        border-radius: 0.75rem;
        transition: width 1s ease;
    }

    .regalia-milestone {
        position: absolute;
        top: -8px;
        transform: translateX(-50%);
        color: #fab33a;
        font-size: 1.25rem;
        z-index: 2;
    }

    .regalia-impact-message {
        font-size: 0.95rem;
        color: #555;
        margin-top: 1rem;
        text-align: center;
    }

    .regalia-contributor-count {
        margin-bottom: 0;
    }

    /* Products Grid */
    .regalia-products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    @media (max-width: 767.98px) {
        .regalia-products-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Product Card */
    .regalia-product-card {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        overflow: visible;
        position: relative;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        z-index: 1;
    }

    .regalia-product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .regalia-product-image {
        height: 250px;
        position: relative;
        overflow: hidden;
        background-color: #f8f9fa;
    }

    .regalia-product-image img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        transition: transform 0.5s ease;
        padding: 0;
    }

    .regalia-product-card:hover .regalia-product-image img {
        transform: scale(1.05);
    }

    .regalia-badges-left {
        position: absolute;
        top: 1rem;
        left: 1rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .regalia-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .regalia-badge-sale {
        background-color: #f56565;
        color: white;
    }

    .regalia-badge-featured {
        background-color: #ecc94b;
        color: #744210;
    }

    .regalia-product-content {
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        position: relative;
        z-index: 2;
        background-color: white;
        border-radius: 0 0 1rem 1rem;
    }

    .regalia-product-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.75rem;
    }

    .regalia-product-description {
        color: #4a5568;
        font-size: 0.875rem;
        margin-bottom: 1rem;
        flex-grow: 1;
    }

    .regalia-product-variants {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #718096;
        margin-bottom: 1rem;
    }

    .regalia-product-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
    }

    .regalia-product-price {
        font-weight: 600;
    }

    .regalia-price-regular {
        color: #36b1dc;
        font-size: 1.25rem;
    }

    .regalia-price-sale {
        display: flex;
        flex-direction: column;
    }

    .regalia-price-new {
        color: #f56565;
        font-size: 1.25rem;
    }

    .regalia-price-old {
        color: #a0aec0;
        text-decoration: line-through;
        font-size: 0.875rem;
        font-weight: 400;
    }

    .regalia-btn {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        border: none;
        transition: all 0.3s ease;
    }

    .regalia-btn-primary {
        background-color: #36b1dc;
        color: white;
    }

    .regalia-btn-primary:hover {
        background-color: #2d93b7;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(45, 147, 183, 0.2);
    }

    .regalia-btn-disabled {
        background-color: #e2e8f0;
        color: #a0aec0;
        cursor: not-allowed;
    }

    /* Merchandise Variation Card */
    .merchandise-variation-card {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        border-radius: 0.5rem;
        border: 1px solid #e2e8f0;
        box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.1);
        padding: 1rem;
        max-height: 0;
        overflow: hidden;
        opacity: 0;
        transform: translateY(100%);
        transition: max-height 0.5s ease,
                    opacity 0.4s ease,
                    transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
                    padding 0.5s ease;
        padding-top: 0;
        padding-bottom: 0;
        z-index: 10;
    }

    .merchandise-variation-card.active {
        max-height: 500px;
        opacity: 1;
        transform: translateY(0);
        padding: 1rem;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }

    .merchandise-variation-card.closing {
        max-height: 0;
        opacity: 0;
        transform: translateY(100%);
        padding-top: 0;
        padding-bottom: 0;
        transition: max-height 0.5s ease,
                    opacity 0.4s ease,
                    transform 0.5s cubic-bezier(0.68, -0.6, 0.32, 1.6),
                    padding 0.5s ease;
    }

    /* Fix for variation cards on mobile */
    @media (max-width: 767.98px) {
        .regalia-product-card {
            margin-bottom: 2rem;
        }

        .merchandise-variation-card {
            border-radius: 1rem 1rem 0 0;
        }

        .merchandise-variation-card.active {
            padding: 0.75rem;
            border-radius: 1rem 1rem 0 0;
        }


    }

    /* Merchandise Variation Header */
    .merchandise-variation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .merchandise-variation-header h4 {
        font-size: 0.9rem;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
        text-transform: uppercase;
    }

    .merchandise-close-variation-card {
        background: none;
        border: none;
        color: #a0aec0;
        cursor: pointer;
        font-size: 1.25rem;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .merchandise-close-variation-card:hover {
        background-color: #f7fafc;
        color: #4a5568;
    }

    /* Merchandise Variation Options */
    .merchandise-variation-options {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .merchandise-variation-option {
        margin-bottom: 1rem;
    }

    .merchandise-variation-option label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #4a5568;
        margin-bottom: 0.5rem;
    }

    .merchandise-variation-option select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        color: #2d3748;
        background-color: white;
    }

    /* Merchandise Quantity */
    .merchandise-quantity {
        display: flex;
        align-items: center;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .merchandise-quantity button {
        background: none;
        border: none;
        color: #4a5568;
        cursor: pointer;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        transition: background-color 0.2s ease;
    }

    .merchandise-quantity button:hover {
        background-color: #f7fafc;
    }

    .merchandise-quantity input {
        width: 11rem;
        text-align: center;
        border: none;
        border-left: 1px solid #e2e8f0;
        border-right: 1px solid #e2e8f0;
        padding: 0.5rem 0;
        font-size: 0.875rem;
        color: #2d3748;
        -moz-appearance: textfield;
    }

    .merchandise-quantity input::-webkit-outer-spin-button,
    .merchandise-quantity input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Merchandise Variation Actions */
    .merchandise-variation-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid #e2e8f0;
    }

    .regalia-variation-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 0 !important;
        padding: 1rem !important;
        border-bottom: 1px solid #e2e8f0 !important;
    }

    .regalia-variation-header h4 {
        font-size: 0.8rem !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 !important;
        text-transform: uppercase !important;
    }

    .close-variation-card {
        background: none !important;
        border: none !important;
        color: #a0aec0 !important;
        cursor: pointer;
        font-size: 1.25rem;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .close-variation-card:hover {
        background-color: #f7fafc;
        color: #4a5568;
    }

    .regalia-variation-options {
        padding: 1rem !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 1rem !important;
    }

    .regalia-variation-option {
        margin-bottom: 1rem;
    }

    .regalia-variation-option label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #4a5568;
        margin-bottom: 0.5rem;
    }

    .regalia-variation-option select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        color: #2d3748;
        background-color: white;
    }

    .regalia-quantity {
        display: flex;
        align-items: center;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .regalia-quantity button {
        background: none;
        border: none;
        color: #4a5568;
        cursor: pointer;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        transition: background-color 0.2s ease;
    }

    .regalia-quantity button:hover {
        background-color: #f7fafc;
    }

    .regalia-quantity input {
        width: 3rem;
        text-align: center;
        border: none;
        border-left: 1px solid #e2e8f0;
        border-right: 1px solid #e2e8f0;
        padding: 0.5rem 0;
        font-size: 0.875rem;
        color: #2d3748;
        -moz-appearance: textfield;
    }

    .regalia-quantity input::-webkit-outer-spin-button,
    .regalia-quantity input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .regalia-variation-actions {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
        padding: 1rem !important;
        border-top: 1px solid #e2e8f0 !important;
    }

    /* Previous Orders Section */
    .previous-orders-section {
        margin-top: 3rem;
    }

    .accordion-item {
        margin-bottom: 1rem;
        border: none;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .accordion-button {
        padding: 1rem 1.5rem;
        font-weight: 500;
        color: #2d3748;
        background-color: white;
    }

    .accordion-button:not(.collapsed) {
        color: #36b1dc;
        background-color: #ebf8ff;
    }

    .accordion-button:focus {
        box-shadow: none;
        border-color: #e2e8f0;
    }

    .accordion-body {
        padding: 1.5rem;
        background-color: white;
    }

    /* Alert Modal Styles */
    #merchandiseAlertModal .modal-content {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    #merchandiseAlertModal .modal-header {
        border-bottom: 1px solid #e2e8f0;
        padding: 1rem 1.5rem;
    }

    #merchandiseAlertModal .modal-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
    }

    #merchandiseAlertModal .modal-title i {
        color: #f6ad55;
    }

    #merchandiseAlertModal .modal-body {
        padding: 2rem 1.5rem;
    }

    #merchandiseAlertModal #merchandiseAlertMessage {
        color: #4a5568;
        font-weight: 500;
    }

    #merchandiseAlertModal .modal-footer {
        border-top: 1px solid #e2e8f0;
        padding: 1rem 1.5rem;
    }

    #merchandiseAlertModal .btn-primary {
        background-color: #36b1dc;
        border-color: #36b1dc;
        padding: 0.5rem 1.5rem;
        font-weight: 500;
    }

    #merchandiseAlertModal .btn-primary:hover {
        background-color: #2d93b7;
        border-color: #2d93b7;
    }

    /* Cart Notification Styles */
    .cart-notification {
        display: flex;
        align-items: flex-start;
        padding: 0.5rem;
    }

    .cart-notification-icon {
        font-size: 2rem;
        margin-right: 1rem;
        color: #48bb78;
    }

    .cart-notification-content {
        flex: 1;
        text-align: left;
    }

    .cart-notification-content h5 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .cart-notification-content p {
        margin-bottom: 0.25rem;
        color: #4a5568;
    }

    .cart-notification-id {
        font-size: 0.8rem;
        color: #a0aec0;
        margin-top: 0.5rem;
    }

    .variation-details {
        margin: 0.5rem 0;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .variation-item {
        background-color: #edf2f7;
        color: #4a5568;
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        display: inline-block;
    }

    /* Confirmation Animation */
    @keyframes pulse-scale {
        0% { transform: scale(0.5); opacity: 0; }
        50% { transform: scale(1.2); opacity: 1; }
        100% { transform: scale(1); opacity: 1; }
    }

    @keyframes fade-in-up {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    .confirmation-animation i {
        display: inline-block;
        animation: pulse-scale 0.5s ease-out;
    }

    .confirmation-text {
        animation: fade-in-up 0.5s ease-out 0.2s both;
    }

    /* Add to Cart Modal */
    #merchandiseAddToCartModal .modal-content {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.12);
    }

    #merchandiseAddToCartModal .btn-close {
        z-index: 10;
        opacity: 0.7;
        transition: opacity 0.2s ease;
    }

    #merchandiseAddToCartModal .btn-close:hover {
        opacity: 1;
    }

    #merchandiseAddToCartModal .modal-body {
        padding: 2.5rem 1.5rem 1.5rem;
    }

    #merchandiseAddToCartModal .modal-footer {
        padding: 1rem 1.5rem 1.5rem;
        border-top: 1px solid rgba(54, 177, 220, 0.2);
    }

    #merchandiseAddToCartModal .btn-primary {
        background-color: #36b1dc;
        border-color: #36b1dc;
    }

    #merchandiseAddToCartModal .btn-primary:hover {
        background-color: #2d93b7;
        border-color: #2d93b7;
    }

    #merchandiseAddToCartModal .progress {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 4px;
        border-radius: 0;
    }

    #merchandiseAddToCartModal .progress-bar {
        transition: width 3s linear;
        background-color: #36b1dc;
    }

    /* Merchandise Quantity Styling */
    .merchandise-quantity {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }

    .merchandise-quantity-input {
        width: 11rem;
        text-align: center;
        border: 1px solid #e2e8f0;
        border-radius: 0.25rem;
        padding: 0.5rem;
        margin: 0 0.25rem;
        -moz-appearance: textfield; /* Remove spinner for Firefox */
    }

    /* Remove spinner for Chrome, Safari, Edge, Opera */
    .merchandise-quantity-input::-webkit-outer-spin-button,
    .merchandise-quantity-input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .merchandise-quantity-minus,
    .merchandise-quantity-plus {
        width: 20%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: 0.25rem;
        padding: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #4a5568;
    }

    .merchandise-quantity-minus:hover,
    .merchandise-quantity-plus:hover {
        background-color: #36b1dc;
        color: white;
        border-color: #36b1dc;
    }

    .merchandise-quantity-minus:focus,
    .merchandise-quantity-plus:focus,
    .merchandise-quantity-input:focus {
        outline: none;
        border-color: #36b1dc;
        box-shadow: 0 0 0 3px rgba(54, 177, 220, 0.2);
    }

    /* Mobile Responsive Styles for Quantity */
    @media (max-width: 767.98px) {
        .merchandise-quantity {
            max-width: 100%;
        }

        .merchandise-quantity-input {
            width: 60%;
            padding: 0.5rem 0.25rem;
        }

        .merchandise-quantity-minus,
        .merchandise-quantity-plus {
            width: 20%;
            padding: 0.5rem 0.25rem;
        }

        .merchandise-variation-option {
            margin-bottom: 1rem;
        }
    }
    </style>

    <!-- Add to Cart Confirmation Modal -->
    <div class="modal fade" id="addToCartConfirmModal" tabindex="-1" aria-labelledby="addToCartConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addToCartConfirmModalLabel">
                        <i class="fas fa-check-circle text-success me-2"></i>Added to Cart
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="added-product-image" src="" alt="Product" class="mb-3">
                    <p class="mb-1">You've added</p>
                    <h4 id="added-product-name" class="mb-3"></h4>
                    <div id="autoCloseProgress" class="mt-3">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Continue Shopping</button>
                    <a href="<?php echo esc_url(wc_get_cart_url()); ?>" class="btn btn-primary">
                        <i class="fas fa-shopping-cart me-1"></i> View Cart
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Variations Modal -->
    <div class="modal fade" id="productVariationsModal" tabindex="-1" aria-labelledby="productVariationsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productVariationsModalLabel">Select Options</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="product-variations-container">
                        <!-- Variations will be loaded here via AJAX -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading product options...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="add-variation-to-cart-btn" disabled>
                        <i class="fas fa-shopping-cart me-1"></i> Add to Cart
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Define ajaxurl if it's not already defined
    var ajaxurl = ajaxurl || '<?php echo admin_url('admin-ajax.php'); ?>';
    var lci_nonce = '<?php echo $nonce; ?>';
    var lci_ajax_object = {
        ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
        nonce: '<?php echo wp_create_nonce('lci_mini_cart_nonce'); ?>',
        add_to_cart_nonce: '<?php echo $nonce; ?>'
    };
    </script>

    <!-- Alert Modal -->
    <div class="modal fade" id="merchandiseAlertModal" tabindex="-1" aria-labelledby="merchandiseAlertModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="merchandiseAlertModalLabel">
                        <i class="fas fa-exclamation-circle text-warning me-2"></i>Attention Required
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body py-4">
                    <div id="merchandiseAlertMessage" class="mb-0">Please select all options before adding to cart.</div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-1"></i> Okay
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Fundraising Message Container for Mini Cart -->
    <div id="merchandise-fundraising-container" style="display: none;">
        <?php
        // The fundraising message will be loaded via AJAX when needed
        ?>
    </div>

    <!-- Add to Cart Confirmation Modal -->
    <div class="modal fade" id="merchandiseAddToCartModal" tabindex="-1" aria-labelledby="merchandiseAddToCartModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button type="button" class="btn-close position-absolute" style="right: 1rem; top: 1rem; z-index: 10;" data-bs-dismiss="modal" aria-label="Close"></button>

                <div class="modal-body text-center py-4">
                    <div class="confirmation-animation mb-4">
                        <i class="fas fa-check-circle" style="font-size: 3rem; color: #36b1dc;"></i>
                    </div>
                    <div class="confirmation-text">
                        <h4 id="added-product-name" class="text-primary fw-bold mb-2"></h4>
                        <p class="text-primary fs-5">has been added to your cart</p>
                        <div id="added-product-variations" class="variation-details mt-3"></div>
                    </div>
                </div>

                <!-- Fundraising Message in Modal -->
                <div id="modal-fundraising-message" class="px-4 pb-2"></div>

                <div class="modal-footer border-top d-flex justify-content-between">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal" style="flex: 1;">
                        Continue Shopping
                    </button>
                    <a href="<?php echo esc_url(wc_get_checkout_url()); ?>" class="btn btn-primary" style="flex: 1;">
                        <i class="fas fa-credit-card me-2"></i> Checkout
                    </a>
                </div>

                <div class="progress" style="height: 4px; border-radius: 0;">
                    <div id="autoCloseProgress" class="progress-bar bg-primary" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        </div>
    </div>
            </div>
        </div>
    </div>
    <?php

    // Return the buffered output
    return ob_get_clean();
}

/**
 * Get merchandise products
 *
 * @param int $category_id Category ID
 * @return array Array of products
 */
function lci_get_merchandise_products($category_id = 22) {
    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        return [];
    }

    // Try multiple methods to get products
    $merchandise_products = array();

    // Method 1: Using WP_Query with tax_query
    $args = array(
        'post_type'      => 'product',
        'posts_per_page' => -1,
        'post_status'    => 'publish',
        'tax_query'      => array(
            array(
                'taxonomy' => 'product_cat',
                'field'    => 'term_id',
                'terms'    => $category_id,
            ),
        ),
    );

    $products_query = new WP_Query($args);
    if ($products_query->have_posts()) {
        while ($products_query->have_posts()) {
            $products_query->the_post();
            $product_id = get_the_ID();
            $product = wc_get_product($product_id);
            if ($product) {
                $merchandise_products[] = $product;
            }
        }
        wp_reset_postdata();
    }

    // If no products found, try Method 2: Using wc_get_products
    if (empty($merchandise_products)) {
        $args = array(
            'status' => 'publish',
            'limit'  => -1,
            'category' => array($category_id),
        );

        $method2_products = wc_get_products($args);
        if (!empty($method2_products)) {
            $merchandise_products = $method2_products;
        }
    }

    // If still no products found, try Method 3: Using get_posts
    if (empty($merchandise_products)) {
        // Get the category slug
        $term = get_term_by('id', $category_id, 'product_cat');
        if ($term) {
            $args = array(
                'post_type'      => 'product',
                'posts_per_page' => -1,
                'post_status'    => 'publish',
                'tax_query'      => array(
                    array(
                        'taxonomy' => 'product_cat',
                        'field'    => 'slug',
                        'terms'    => $term->slug,
                    ),
                ),
            );

            $posts = get_posts($args);
            foreach ($posts as $post) {
                $product = wc_get_product($post->ID);
                if ($product) {
                    $merchandise_products[] = $product;
                }
            }
        }
    }

    // Method 4: Last resort - try to get all products and filter by category name
    if (empty($merchandise_products)) {
        // Get all product categories
        $product_categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
        ));

        // Look for a category that might be the merchandise category
        $possible_merchandise_categories = array();
        foreach ($product_categories as $category) {
            if (stripos($category->name, 'regalia') !== false ||
                stripos($category->name, 'merchandise') !== false ||
                stripos($category->name, 'shop') !== false) {
                $possible_merchandise_categories[] = $category->term_id;
            }
        }

        // Try each possible category
        foreach ($possible_merchandise_categories as $cat_id) {
            $args = array(
                'post_type'      => 'product',
                'posts_per_page' => -1,
                'post_status'    => 'publish',
                'tax_query'      => array(
                    array(
                        'taxonomy' => 'product_cat',
                        'field'    => 'term_id',
                        'terms'    => $cat_id,
                    ),
                ),
            );

            $products_query = new WP_Query($args);

            if ($products_query->have_posts()) {
                while ($products_query->have_posts()) {
                    $products_query->the_post();
                    $product_id = get_the_ID();
                    $product = wc_get_product($product_id);
                    if ($product) {
                        $merchandise_products[] = $product;
                    }
                }
                wp_reset_postdata();
            }
        }
    }

    return $merchandise_products;
}

/**
 * AJAX handler for getting product variations
 */
function lci_get_merchandise_variations() {
    // Verify nonce
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci-merchandise-add-to-cart-nonce')) {
        wp_send_json_error(['message' => 'Security check failed.']);
        return;
    }

    // Get product ID
    $product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;

    // Validate product ID
    if (!$product_id) {
        wp_send_json_error(['message' => 'Invalid product ID.']);
        return;
    }

    // Get product
    $product = wc_get_product($product_id);
    if (!$product || !$product->is_type('variable')) {
        wp_send_json_error(['message' => 'Product not found or not a variable product.']);
        return;
    }

    // Get available variations
    $available_variations = $product->get_available_variations();
    $variation_attributes = $product->get_variation_attributes();

    // Build HTML for variations
    ob_start();
    ?>
    <div class="product-variations">
        <h4 class="mb-3"><?php echo esc_html($product->get_name()); ?></h4>

        <?php if ($product->get_image_id()): ?>
        <div class="text-center mb-3">
            <img src="<?php echo esc_url(wp_get_attachment_url($product->get_image_id())); ?>" alt="<?php echo esc_attr($product->get_name()); ?>" style="max-height: 150px; width: auto;">
        </div>
        <?php endif; ?>

        <div class="variation-price mb-3">
            <span class="price-label">Price:</span>
            <span class="price-value"><?php echo wp_kses_post($product->get_price_html()); ?></span>
        </div>

        <form class="variations-form" data-product-id="<?php echo esc_attr($product_id); ?>">
            <?php foreach ($variation_attributes as $attribute_name => $options): ?>
            <div class="variation-attribute mb-3">
                <label for="<?php echo esc_attr(sanitize_title($attribute_name)); ?>" class="form-label">
                    <?php echo esc_html(wc_attribute_label($attribute_name)); ?>
                </label>
                <select name="attribute_<?php echo esc_attr(sanitize_title($attribute_name)); ?>" id="<?php echo esc_attr(sanitize_title($attribute_name)); ?>" class="form-select variation-select">
                    <option value="">Choose an option</option>
                    <?php foreach ($options as $option): ?>
                    <option value="<?php echo esc_attr($option); ?>"><?php echo esc_html($option); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php endforeach; ?>

            <div class="quantity-input mb-3">
                <label for="quantity" class="form-label">Quantity</label>
                <div class="input-group">
                    <button type="button" class="btn btn-outline-secondary quantity-minus">-</button>
                    <input type="number" name="quantity" id="quantity" class="form-control text-center" value="1" min="1" max="<?php echo esc_attr($product->get_max_purchase_quantity()); ?>">
                    <button type="button" class="btn btn-outline-secondary quantity-plus">+</button>
                </div>
            </div>
        </form>
    </div>
    <?php
    $html = ob_get_clean();

    wp_send_json_success([
        'html' => $html,
        'variations' => $available_variations,
        'price_html' => $product->get_price_html(),
    ]);
}
add_action('wp_ajax_lci_get_merchandise_variations', 'lci_get_merchandise_variations');
add_action('wp_ajax_nopriv_lci_get_merchandise_variations', 'lci_get_merchandise_variations');

/**
 * AJAX handler for adding merchandise to cart
 */
function lci_merchandise_add_to_cart_handler() {
    // Verify nonce
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci-merchandise-add-to-cart-nonce')) {
        wp_send_json_error(['message' => 'Security check failed.']);
        return;
    }

    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        wp_send_json_error(['message' => 'WooCommerce is not active.']);
        return;
    }

    // Get product ID and quantity
    $product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;
    $quantity = isset($_POST['quantity']) ? absint($_POST['quantity']) : 1;

    // Validate product ID
    if (!$product_id) {
        wp_send_json_error(['message' => 'Invalid product ID.']);
        return;
    }

    // Check if product exists
    $product = wc_get_product($product_id);
    if (!$product) {
        wp_send_json_error(['message' => 'Product not found.']);
        return;
    }

    // Check if product is in stock
    if (!$product->is_in_stock()) {
        wp_send_json_error(['message' => 'Product is out of stock.']);
        return;
    }

    // Check if it's a variable product
    $variation_id = 0;
    $variation_attributes = array();

    if ($product->is_type('variable')) {
        // Extract variation attributes from POST data
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'attribute_') === 0) {
                $variation_attributes[$key] = $value;
            }
        }

        // Find matching variation ID
        if (!empty($variation_attributes)) {
            $data_store = WC_Data_Store::load('product');
            $variation_id = $data_store->find_matching_product_variation($product, $variation_attributes);

            if (!$variation_id) {
                wp_send_json_error(['message' => 'No matching variation found.']);
                return;
            }

            // Check if variation is in stock
            $variation = wc_get_product($variation_id);
            if (!$variation || !$variation->is_in_stock()) {
                wp_send_json_error(['message' => 'Selected variation is out of stock.']);
                return;
            }
        }
    }

    // For simple products, check if already in cart
    if (!$variation_id) {
        $cart_item_key_to_update = null;
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            if ($cart_item['product_id'] == $product_id && $cart_item['variation_id'] == 0) {
                $cart_item_key_to_update = $cart_item_key;
                break;
            }
        }

        // If simple product is already in cart, update its quantity
        if ($cart_item_key_to_update) {
            // Get current quantity
            $current_quantity = WC()->cart->get_cart_item($cart_item_key_to_update)['quantity'];

            // Set new quantity (current + new)
            WC()->cart->set_quantity($cart_item_key_to_update, $current_quantity + $quantity);

            // Get updated cart info
            $cart_count = WC()->cart->get_cart_contents_count();
            $cart_total = WC()->cart->get_cart_total();

            wp_send_json_success([
                'message' => 'Product quantity updated in cart.',
                'cart_count' => $cart_count,
                'cart_total' => $cart_total,
            ]);
            return;
        }
    }

    // Add to cart
    if ($variation_id > 0) {
        // Add variable product to cart
        $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $variation_attributes);
    } else {
        // Add simple product to cart
        $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity);
    }

    // Check if product was added successfully
    if (!$cart_item_key) {
        wp_send_json_error(['message' => 'Failed to add product to cart.']);
        return;
    }

    // Get updated cart info
    $cart_count = WC()->cart->get_cart_contents_count();
    $cart_total = WC()->cart->get_cart_total();

    wp_send_json_success([
        'message' => 'Product added to cart.',
        'cart_count' => $cart_count,
        'cart_total' => $cart_total,
    ]);
}
add_action('wp_ajax_lci_merchandise_add_to_cart', 'lci_merchandise_add_to_cart_handler');
add_action('wp_ajax_nopriv_lci_merchandise_add_to_cart', 'lci_merchandise_add_to_cart_handler');

/**
 * AJAX handler for adding variable merchandise to cart
 */
function lci_merchandise_add_variable_to_cart_handler() {
    // Verify nonce
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci-merchandise-add-to-cart-nonce')) {
        wp_send_json_error(['message' => 'Security check failed.']);
        return;
    }

    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        wp_send_json_error(['message' => 'WooCommerce is not active.']);
        return;
    }

    // Get product ID and quantity
    $product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;
    $quantity = isset($_POST['quantity']) ? absint($_POST['quantity']) : 1;
    $variation = isset($_POST['variation']) ? (array) $_POST['variation'] : [];

    // Validate product ID
    if (!$product_id) {
        wp_send_json_error(['message' => 'Invalid product ID.']);
        return;
    }

    // Check if product exists
    $product = wc_get_product($product_id);
    if (!$product || !$product->is_type('variable')) {
        wp_send_json_error(['message' => 'Product not found or not a variable product.']);
        return;
    }

    // Check if product is in stock
    if (!$product->is_in_stock()) {
        wp_send_json_error(['message' => 'Product is out of stock.']);
        return;
    }

    // Format variation attributes
    $variation_attributes = [];
    foreach ($variation as $attribute => $value) {
        $variation_attributes['attribute_' . $attribute] = $value;
    }

    // Find matching variation ID
    $data_store = WC_Data_Store::load('product');
    $variation_id = $data_store->find_matching_product_variation($product, $variation_attributes);

    if (!$variation_id) {
        wp_send_json_error(['message' => 'No matching variation found.']);
        return;
    }

    // Check if variation is in stock
    $variation_product = wc_get_product($variation_id);
    if (!$variation_product || !$variation_product->is_in_stock()) {
        wp_send_json_error(['message' => 'Selected variation is out of stock.']);
        return;
    }

    // Check if variation is already in cart
    $cart_item_key_to_update = null;
    foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
        if ($cart_item['product_id'] == $product_id && $cart_item['variation_id'] == $variation_id) {
            $cart_item_key_to_update = $cart_item_key;
            break;
        }
    }

    // If variation is already in cart, update its quantity
    if ($cart_item_key_to_update) {
        // Get current quantity
        $current_quantity = WC()->cart->get_cart_item($cart_item_key_to_update)['quantity'];

        // Set new quantity (current + new)
        WC()->cart->set_quantity($cart_item_key_to_update, $current_quantity + $quantity);

        // Get updated cart info
        $cart_count = WC()->cart->get_cart_contents_count();
        $cart_total = WC()->cart->get_cart_total();

        wp_send_json_success([
            'message' => 'Product quantity updated in cart.',
            'cart_count' => $cart_count,
            'cart_total' => $cart_total,
        ]);
        return;
    }

    // Add to cart
    $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $variation_attributes);

    // Check if product was added successfully
    if (!$cart_item_key) {
        wp_send_json_error(['message' => 'Failed to add product to cart.']);
        return;
    }

    // Get updated cart info
    $cart_count = WC()->cart->get_cart_contents_count();
    $cart_total = WC()->cart->get_cart_total();

    wp_send_json_success([
        'message' => 'Product added to cart.',
        'cart_count' => $cart_count,
        'cart_total' => $cart_total,
    ]);
}
add_action('wp_ajax_lci_merchandise_add_variable_to_cart', 'lci_merchandise_add_variable_to_cart_handler');
add_action('wp_ajax_nopriv_lci_merchandise_add_variable_to_cart', 'lci_merchandise_add_variable_to_cart_handler');
