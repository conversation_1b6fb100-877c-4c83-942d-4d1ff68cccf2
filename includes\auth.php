<?php
// ? Confirm the file loads
file_put_contents(__DIR__ . '/debug-auth-check.txt', "auth.php LOADED\n", FILE_APPEND);

// ? AJAX login hook
add_action('wp_ajax_nopriv_logintoLCIAGM', 'logintoLCIAGM_handler');

// Forgot password hook
add_action('wp_ajax_nopriv_lci_ajax_forgot_password', 'lci_ajax_forgot_password_handler');

// Magic link hook
add_action('wp_ajax_nopriv_lci_ajax_magic_link', 'lci_ajax_magic_link_handler');

// Change password hook
add_action('wp_ajax_nopriv_lci_ajax_change_password', 'lci_ajax_change_password_handler');

// Generate reset key hook
add_action('wp_ajax_lci_generate_reset_key', 'lci_generate_reset_key_handler');
add_action('wp_ajax_nopriv_lci_generate_reset_key', 'lci_generate_reset_key_handler');

// Magic link login hook
add_action('template_redirect', 'lci_process_magic_link_login');

// Password reset redirect hook - must run before other hooks
add_action('template_redirect', 'lci_handle_password_reset_redirect', 5);

// Add a direct handler for password reset on the dashboard page
add_action('wp_footer', 'lci_direct_password_reset_handler');

// Enqueue password reset handler script
add_action('wp_enqueue_scripts', 'lci_enqueue_password_reset_handler');

/**
 * Enqueue password reset handler script
 */
function lci_enqueue_password_reset_handler() {
    // Enqueue on the dashboard page and any page that might handle password reset
    if (is_page('lci-dashboard') ||
        (isset($_GET['key']) && isset($_GET['login'])) ||
        strpos($_SERVER['REQUEST_URI'], 'lost-password') !== false) {

        // Add debug info
        error_log('Enqueuing password reset handler script on: ' . $_SERVER['REQUEST_URI']);

        // Enqueue the script
        wp_enqueue_script('password-reset-handler', LCI2025_URL . 'assets/js/password-reset-handler.js', array('jquery'), LCI2025_VERSION, true);
        wp_enqueue_script('force-password-reset-form', LCI2025_URL . 'assets/js/force-password-reset-form.js', array('jquery'), LCI2025_VERSION, true);

        // Add inline script to force form display only when needed
        wp_add_inline_script('password-reset-handler', '
            document.addEventListener("DOMContentLoaded", function() {
                console.log("Inline script loaded");

                // Check if this is a password reset URL
                var urlParams = new URLSearchParams(window.location.search);
                var key = urlParams.get("key");
                var login = urlParams.get("login");
                var action = urlParams.get("action");
                var showResetForm = urlParams.get("show-reset-form");

                // Only proceed if this is a password reset URL
                var isLostPasswordPage = window.location.href.indexOf("lost-password") !== -1;
                var isResetUrl = false;

                if (isLostPasswordPage && (action === "reset_password" || showResetForm === "true")) {
                    isResetUrl = true;
                } else if (key && login && (action === "reset_password" || showResetForm === "true")) {
                    isResetUrl = true;
                }

                if (!isResetUrl) {
                    console.log("Not a password reset URL, inline script will not modify forms");
                    return; // Exit the script if not a reset URL
                }

                console.log("Inline script running to force form display");

                // Force display of change password form after a short delay
                setTimeout(function() {
                    var changeForm = document.getElementById("lci-change-password-form");
                    var loginForm = document.getElementById("lci-login-form");

                    if (changeForm && loginForm) {
                        console.log("Forcing display of change password form");
                        loginForm.classList.add("d-none");
                        changeForm.classList.remove("d-none");
                        changeForm.style.display = "block";
                        changeForm.style.opacity = "1";
                        changeForm.style.transform = "translateY(0)";
                    }
                }, 500);
            });
        ');
    }
}

function logintoLCIAGM_handler() {
    $debug_file = __DIR__ . '/debug-login.txt';
    $log = "==== LOGIN ATTEMPT ====\n";
    $log .= print_r($_POST, true);
    file_put_contents($debug_file, $log, FILE_APPEND);

    // 1. Check nonce
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'logintoLCIAGM_nonce')) {
        file_put_contents($debug_file, "? Nonce failed\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Security check failed.']);
    }

    // 2. Check required fields
    if (empty($_POST['email']) || empty($_POST['password'])) {
        file_put_contents($debug_file, "? Missing email or password\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Missing required fields.']);
    }

    // 3. Prepare credentials
    $creds = [
        'user_login'    => sanitize_text_field($_POST['email']),
        'user_password' => $_POST['password'],
        'remember'      => true,
    ];

    // 4. Try login
    $user = wp_signon($creds, false);

    if (is_wp_error($user)) {
        file_put_contents($debug_file, "? Login failed: " . $user->get_error_message() . "\n", FILE_APPEND);
        wp_send_json_error(['message' => $user->get_error_message()]);
    }

    // 5. Success
    file_put_contents($debug_file, "? Login success for user ID: {$user->ID}\n", FILE_APPEND);
    wp_send_json_success(['message' => 'Login successful']);
}

/**
 * Handle forgot password requests
 */
function lci_ajax_forgot_password_handler() {
    $debug_file = __DIR__ . '/debug-forgot-password.txt';
    $log = "==== FORGOT PASSWORD ATTEMPT ====\n";
    $log .= print_r($_POST, true);
    file_put_contents($debug_file, $log, FILE_APPEND);

    // 1. Check nonce
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci_forgot_password_nonce')) {
        file_put_contents($debug_file, "? Nonce failed\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Security check failed.']);
    }

    // 2. Check required fields
    if (empty($_POST['email'])) {
        file_put_contents($debug_file, "? Missing email\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Please enter your email address.']);
    }

    $email = sanitize_email($_POST['email']);

    // 3. Check if user exists
    $user = get_user_by('email', $email);
    if (!$user) {
        file_put_contents($debug_file, "? User not found for email: {$email}\n", FILE_APPEND);
        wp_send_json_error(['message' => 'No account found with that email address.']);
    }

    // 4. Generate password reset key
    $key = get_password_reset_key($user);
    if (is_wp_error($key)) {
        file_put_contents($debug_file, "? Error generating reset key: {$key->get_error_message()}\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Error generating password reset link. Please try again.']);
    }

    // 5. Create reset link
    $reset_link = add_query_arg([
        'key' => $key,
        'login' => rawurlencode($user->user_login)
    ], wp_login_url());

    // For our custom page, we'll use the dashboard page with parameters
    $custom_reset_link = add_query_arg([
        'key' => $key,
        'login' => rawurlencode($user->user_login),
        'action' => 'reset_password' // Add action parameter for clarity
    ], home_url('/lci-dashboard/'));

    // 6. Send email with HTML template
    $subject = 'LCI 2025 - Password Reset Request';

    // Get site logo URL
    $logo_url = 'https://lci2025brasov.com/wp-content/uploads/2023/10/LCI-2025-Logo-1.png';

    // Create HTML email template
    $message = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .email-header {
            background-color: #36b1dc;
            padding: 20px;
            text-align: center;
        }
        .email-header img {
            max-width: 200px;
            height: auto;
        }
        .email-body {
            padding: 30px;
        }
        .email-footer {
            background-color: #f5f5f5;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666666;
        }
        h1 {
            color: #36b1dc;
            margin-top: 0;
        }
        .button {
            display: inline-block;
            background-color: #36b1dc;
            color: #ffffff !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            margin: 20px 0;
            font-weight: bold;
            text-align: center;
        }
        .button:hover {
            background-color: #2d93b7;
        }
        .reset-link {
            word-break: break-all;
            color: #36b1dc;
            margin-top: 15px;
            font-size: 12px;
        }
        .note {
            background-color: #f9f9f9;
            padding: 15px;
            border-left: 4px solid #36b1dc;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <img src="' . $logo_url . '" alt="LCI 2025 Logo">
        </div>
        <div class="email-body">
            <h1>Password Reset</h1>
            <p>Hello ' . $user->display_name . ',</p>
            <p>We received a request to reset your password for your LCI 2025 account. If you didn\'t make this request, you can safely ignore this email.</p>
            <p>To reset your password, please click the button below:</p>
            <div style="text-align: center;">
                <a href="' . $custom_reset_link . '" class="button">Reset Password</a>
            </div>
            <p class="note">This link will expire in 24 hours for security reasons.</p>
            <p>If the button above doesn\'t work, copy and paste the following link into your browser:</p>
            <p class="reset-link">' . $custom_reset_link . '</p>
        </div>
        <div class="email-footer">
            <p>&copy; ' . date('Y') . ' LCI 2025 Brasov. All rights reserved.</p>
            <p>This email was sent to you because a password reset was requested for your account.</p>
        </div>
    </div>
</body>
</html>';

    $headers = [
        'Content-Type: text/html; charset=UTF-8'
    ];

    file_put_contents($debug_file, "? Attempting to send email to: {$email}\n", FILE_APPEND);

    $email_sent = wp_mail($email, $subject, $message, $headers);

    if (!$email_sent) {
        file_put_contents($debug_file, "? Error sending email to: {$email}\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Error sending password reset email. Please try again.']);
    }

    // 7. Success
    file_put_contents($debug_file, "? Reset email sent to: {$email} at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    wp_send_json_success(['message' => 'Password reset link has been sent to your email address.']);
}

/**
 * Handle password change requests
 */
function lci_ajax_change_password_handler() {
    // Prevent WordPress from doing a redirect after the response
    add_filter('wp_redirect', '__return_false', 999);

    // Set headers to prevent caching
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Cache-Control: post-check=0, pre-check=0', false);
    header('Pragma: no-cache');
    $debug_file = __DIR__ . '/debug-change-password.txt';
    $log = "==== CHANGE PASSWORD ATTEMPT ====\n";
    $log .= "POST data: " . print_r($_POST, true) . "\n";
    $log .= "GET data: " . print_r($_GET, true) . "\n";
    $log .= "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
    file_put_contents($debug_file, $log, FILE_APPEND);

    // Log all request data for debugging
    file_put_contents($debug_file, "? POST data: " . print_r($_POST, true) . "\n", FILE_APPEND);
    file_put_contents($debug_file, "? FILES data: " . print_r($_FILES, true) . "\n", FILE_APPEND);
    file_put_contents($debug_file, "? REQUEST data: " . print_r($_REQUEST, true) . "\n", FILE_APPEND);

    // 1. Check required fields
    if ((empty($_POST['email']) && empty($_POST['login'])) || empty($_POST['password'])) {
        $missing = [];
        if (empty($_POST['email']) && empty($_POST['login'])) $missing[] = 'email or login';
        if (empty($_POST['password'])) $missing[] = 'password';

        $error_message = 'Missing required information: ' . implode(', ', $missing);
        file_put_contents($debug_file, "? {$error_message}\n", FILE_APPEND);

        // Send a detailed error response
        wp_send_json_error([
            'message' => $error_message,
            'status' => 'error',
            'fields' => $missing
        ]);
    }

    // Check for key in various places
    $key = null;
    if (!empty($_POST['key'])) {
        $key = $_POST['key'];
        file_put_contents($debug_file, "? Key found in POST data: {$key}\n", FILE_APPEND);
    } elseif (!empty($_REQUEST['key'])) {
        $key = $_REQUEST['key'];
        file_put_contents($debug_file, "? Key found in REQUEST data: {$key}\n", FILE_APPEND);
    } elseif (!empty($_GET['key'])) {
        $key = $_GET['key'];
        file_put_contents($debug_file, "? Key found in GET data: {$key}\n", FILE_APPEND);
    } else {
        $error_message = 'Missing required information: key';
        file_put_contents($debug_file, "? {$error_message}\n", FILE_APPEND);

        // Send a detailed error response
        wp_send_json_error([
            'message' => $error_message,
            'status' => 'error',
            'fields' => ['key']
        ]);
    }

    $password = $_POST['password'];
    // Key is already set above

    // Get user by login (username) or email
    $user = null;
    if (!empty($_POST['login'])) {
        $login = sanitize_text_field($_POST['login']);
        file_put_contents($debug_file, "? Looking up user by login: {$login}\n", FILE_APPEND);
        $user = get_user_by('login', $login);

        if (!$user) {
            // Try to URL decode the login parameter and try again
            $decoded_login = urldecode($login);
            file_put_contents($debug_file, "? Trying with decoded login: {$decoded_login}\n", FILE_APPEND);
            $user = get_user_by('login', $decoded_login);
        }
    } else if (!empty($_POST['email'])) {
        $email = sanitize_email($_POST['email']);
        file_put_contents($debug_file, "? Looking up user by email: {$email}\n", FILE_APPEND);
        $user = get_user_by('email', $email);
    }

    // Log the lookup attempt
    if ($user) {
        file_put_contents($debug_file, "? User found: ID={$user->ID}, login={$user->user_login}, email={$user->user_email}\n", FILE_APPEND);
    }

    // 2. Check if user exists
    if (!$user) {
        $error_message = 'Invalid user. Please check your email or username.';
        file_put_contents($debug_file, "? User not found\n", FILE_APPEND);

        // Send a detailed error response
        wp_send_json_error([
            'message' => $error_message,
            'status' => 'error',
            'code' => 'invalid_user'
        ]);
    }

    // 3. Check reset key
    file_put_contents($debug_file, "? Checking reset key: {$key} for user: {$user->user_login}\n", FILE_APPEND);
    $check = check_password_reset_key($key, $user->user_login);
    if (is_wp_error($check)) {
        $error_message = $check->get_error_message();
        $error_code = $check->get_error_code();
        file_put_contents($debug_file, "? Invalid or expired key: {$error_message} (Code: {$error_code})\n", FILE_APPEND);

        // Send a detailed error response
        wp_send_json_error([
            'message' => 'The password reset link has expired or is invalid. Please request a new password reset link.',
            'status' => 'error',
            'code' => $error_code,
            'error_details' => $error_message
        ]);
    } else {
        file_put_contents($debug_file, "? Key validation successful\n", FILE_APPEND);
    }

    // 4. Reset password
    file_put_contents($debug_file, "? Resetting password for user ID: {$user->ID}\n", FILE_APPEND);

    try {
        reset_password($user, $password);
        file_put_contents($debug_file, "? Password reset successful\n", FILE_APPEND);

        // 5. Success
        file_put_contents($debug_file, "? Password changed for user ID: {$user->ID}\n", FILE_APPEND);

        // Send a detailed success response
        wp_send_json_success([
            'message' => 'Your password has been successfully changed. You can now log in with your new password.',
            'status' => 'success',
            'user' => $user->user_login
        ]);
    } catch (Exception $e) {
        $error_message = 'Error resetting password: ' . $e->getMessage();
        file_put_contents($debug_file, "? {$error_message}\n", FILE_APPEND);

        // Send a detailed error response
        wp_send_json_error([
            'message' => $error_message,
            'status' => 'error',
            'code' => 'reset_error',
            'error_details' => $e->getMessage()
        ]);
    }
}

/**
 * Generate a reset key for a user
 */
function lci_generate_reset_key_handler() {
    $debug_file = __DIR__ . '/debug-reset-key.txt';
    $log = "==== GENERATE RESET KEY REQUEST ====\n";
    $log .= "POST data: " . print_r($_POST, true) . "\n";
    file_put_contents($debug_file, $log, FILE_APPEND);

    // Check required fields
    if (empty($_POST['login'])) {
        file_put_contents($debug_file, "? Missing login parameter\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Missing login parameter']);
        return;
    }

    $login = sanitize_text_field($_POST['login']);
    file_put_contents($debug_file, "? Generating key for login: {$login}\n", FILE_APPEND);

    // Get user by login
    $user = get_user_by('login', $login);

    // If not found by login, try email
    if (!$user) {
        file_put_contents($debug_file, "? User not found by login, trying email\n", FILE_APPEND);
        $user = get_user_by('email', $login);
    }

    // Log the user lookup result
    if ($user) {
        file_put_contents($debug_file, "? User found: ID={$user->ID}, login={$user->user_login}, email={$user->user_email}\n", FILE_APPEND);
    } else {
        file_put_contents($debug_file, "? User not found for login: {$login}\n", FILE_APPEND);
    }

    // If still not found, try URL decoding
    if (!$user) {
        $decoded_login = urldecode($login);
        $user = get_user_by('login', $decoded_login);

        if (!$user) {
            $user = get_user_by('email', $decoded_login);
        }
    }

    if (!$user) {
        file_put_contents($debug_file, "? User not found for login: {$login}\n", FILE_APPEND);
        wp_send_json_error(['message' => 'User not found']);
        return;
    }

    // Generate reset key
    $key = get_password_reset_key($user);

    if (is_wp_error($key)) {
        file_put_contents($debug_file, "? Error generating key: " . $key->get_error_message() . "\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Error generating key: ' . $key->get_error_message()]);
        return;
    }

    file_put_contents($debug_file, "? Generated key for user: {$user->user_login}, key: {$key}\n", FILE_APPEND);
    wp_send_json_success(['key' => $key, 'message' => 'Key generated successfully']);
}

/**
 * Handle magic link requests
 */
function lci_ajax_magic_link_handler() {
    $debug_file = __DIR__ . '/debug-magic-link.txt';
    $log = "==== MAGIC LINK REQUEST ====\n";
    $log .= print_r($_POST, true);
    file_put_contents($debug_file, $log, FILE_APPEND);

    // 1. Check nonce
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci_magic_link_nonce')) {
        file_put_contents($debug_file, "? Nonce failed\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Security check failed.']);
    }

    // 2. Check required fields
    if (empty($_POST['email'])) {
        file_put_contents($debug_file, "? Missing email\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Please enter your email address.']);
    }

    $email = sanitize_email($_POST['email']);

    // 3. Check if user exists
    $user = get_user_by('email', $email);
    if (!$user) {
        file_put_contents($debug_file, "? User not found for email: {$email}\n", FILE_APPEND);
        wp_send_json_error(['message' => 'No account found with that email address.']);
    }

    // 4. Generate magic link token
    $token = wp_generate_password(32, false);
    $expiration = time() + (30 * MINUTE_IN_SECONDS); // 30 minutes expiration

    // 5. Store token in user meta
    update_user_meta($user->ID, 'lci_magic_link_token', $token);
    update_user_meta($user->ID, 'lci_magic_link_expiration', $expiration);

    // 6. Create magic link
    $magic_link = add_query_arg([
        'lci_magic_token' => $token,
        'lci_magic_user' => $user->ID,
        'action' => 'magic_login'
    ], home_url('/lci-dashboard/'));

    // 7. Send email with HTML template
    $subject = 'LCI 2025 - Magic Login Link';

    // Get site logo URL
    $logo_url = 'https://lci2025brasov.com/wp-content/uploads/2023/10/LCI-2025-Logo-1.png';

    // Create HTML email template
    $message = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Magic Login Link</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .email-header {
            background-color: #36b1dc;
            padding: 20px;
            text-align: center;
        }
        .email-header img {
            max-width: 200px;
            height: auto;
        }
        .email-body {
            padding: 30px;
        }
        .email-footer {
            background-color: #f5f5f5;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666666;
        }
        h1 {
            color: #36b1dc;
            margin-top: 0;
        }
        .button {
            display: inline-block;
            background-color: #36b1dc;
            color: #ffffff !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            margin: 20px 0;
            font-weight: bold;
            text-align: center;
        }
        .button:hover {
            background-color: #2d93b7;
        }
        .magic-link {
            word-break: break-all;
            color: #36b1dc;
            margin-top: 15px;
            font-size: 12px;
        }
        .note {
            background-color: #f9f9f9;
            padding: 15px;
            border-left: 4px solid #36b1dc;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <img src="' . $logo_url . '" alt="LCI 2025 Logo">
        </div>
        <div class="email-body">
            <h1>Magic Login Link</h1>
            <p>Hello ' . $user->display_name . ',</p>
            <p>You requested a magic login link for your LCI 2025 account. Click the button below to log in instantly - no password needed!</p>
            <div style="text-align: center;">
                <a href="' . $magic_link . '" class="button">Log In Now</a>
            </div>
            <p class="note">This link will expire in 30 minutes for security reasons.</p>
            <p>If the button above doesn\'t work, copy and paste the following link into your browser:</p>
            <p class="magic-link">' . $magic_link . '</p>
        </div>
        <div class="email-footer">
            <p>&copy; ' . date('Y') . ' LCI 2025 Brasov. All rights reserved.</p>
            <p>This email was sent to you because a magic login link was requested for your account.</p>
        </div>
    </div>
</body>
</html>';

    $headers = [
        'Content-Type: text/html; charset=UTF-8'
    ];

    $email_sent = wp_mail($email, $subject, $message, $headers);

    if (!$email_sent) {
        file_put_contents($debug_file, "? Error sending email to: {$email}\n", FILE_APPEND);
        wp_send_json_error(['message' => 'Error sending magic link email. Please try again.']);
    }

    // 8. Success
    file_put_contents($debug_file, "? Magic link email sent to: {$email}\n", FILE_APPEND);
    wp_send_json_success(['message' => 'Magic login link has been sent to your email address. Please check your inbox.']);
}

/**
 * Process password reset URL
 */
function lci_process_password_reset() {
    $debug_file = __DIR__ . '/debug-password-reset.txt';

    // Check if this is a password reset request with any format
    $is_reset_url = false;

    // Log the request for debugging
    $log = "==== PASSWORD RESET CHECK ====\n";
    $log .= "GET data: " . print_r($_GET, true) . "\n";
    $log .= "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
    file_put_contents($debug_file, $log, FILE_APPEND);

    // Standard format with key and login
    if (isset($_GET['key']) && isset($_GET['login']) &&
        (isset($_GET['action']) && $_GET['action'] === 'reset_password' ||
         isset($_GET['show-reset-form']) && $_GET['show-reset-form'] === 'true')) {
        $is_reset_url = true;
        file_put_contents($debug_file, "? Standard format reset URL detected\n", FILE_APPEND);
    }

    // Lost password page format
    $is_lost_password_page = strpos($_SERVER['REQUEST_URI'], 'lost-password') !== false;
    if ($is_lost_password_page &&
        (isset($_GET['action']) && $_GET['action'] === 'reset_password' ||
         isset($_GET['show-reset-form']) && $_GET['show-reset-form'] === 'true')) {
        $is_reset_url = true;
        file_put_contents($debug_file, "? Lost password page format detected\n", FILE_APPEND);
    }

    if ($is_reset_url) {
        $log = "==== PASSWORD RESET URL DETECTED ====\n";
        $log .= "GET data: " . print_r($_GET, true) . "\n";
        $log .= "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
        $log .= "Is lost password page: " . ($is_lost_password_page ? 'Yes' : 'No') . "\n";
        file_put_contents($debug_file, $log, FILE_APPEND);

        // For lost-password page, we need to add a script to force the change form to show
        if ($is_lost_password_page) {
            // Generate a reset key for the lost-password page
            $user = null;
            $key = null;

            // If we have a login parameter, try to get the user
            if (isset($_GET['login'])) {
                $login = sanitize_text_field($_GET['login']);
                $user = get_user_by('login', $login);
            }

            // If we don't have a key parameter but have a user, generate a key
            if (!isset($_GET['key']) && $user) {
                // Get or generate a reset key
                $key = get_password_reset_key($user);
                file_put_contents($debug_file, "? Generated new reset key for user: {$user->user_login}, key: {$key}\n", FILE_APPEND);
            } else if (isset($_GET['key'])) {
                $key = $_GET['key'];
                file_put_contents($debug_file, "? Using key from URL: {$key}\n", FILE_APPEND);
            }

            // Make sure we have a key
            if (empty($key) && $user) {
                $key = get_password_reset_key($user);
                file_put_contents($debug_file, "? Generated fallback reset key for user: {$user->user_login}, key: {$key}\n", FILE_APPEND);
            }

            add_action('wp_footer', function() use ($key) {
                echo '<script>
                    document.addEventListener("DOMContentLoaded", function() {
                        console.log("Lost password page detected, forcing change form to show");

                        function showChangeForm() {
                            var changeForm = document.getElementById("lci-change-password-form");
                            var loginForm = document.getElementById("lci-login-form");

                            if (changeForm && loginForm) {
                                loginForm.classList.add("d-none");
                                changeForm.classList.remove("d-none");
                                changeForm.style.display = "block";
                                changeForm.style.opacity = "1";
                                changeForm.style.transform = "translateY(0)";

                                // Set the login field
                                var login = "' . (isset($_GET['login']) ? esc_js($_GET['login']) : '') . '";
                                if (login) {
                                    // Set the email field
                                    var emailField = document.getElementById("lci-change-email");
                                    if (emailField) {
                                        emailField.value = login;
                                        console.log("Email field set to:", login);
                                    }

                                    // Also store the login in a hidden field for form submission
                                    var loginField = document.getElementById("lci-reset-login");
                                    if (!loginField) {
                                        loginField = document.createElement("input");
                                        loginField.type = "hidden";
                                        loginField.id = "lci-reset-login";
                                        loginField.name = "login";
                                        changeForm.appendChild(loginField);
                                    }
                                    loginField.value = login;
                                    console.log("Login field set to:", login);
                                }

                                // Create or update the key field
                                var keyField = document.getElementById("lci-reset-key");
                                if (!keyField) {
                                    keyField = document.createElement("input");
                                    keyField.type = "hidden";
                                    keyField.id = "lci-reset-key";
                                    keyField.name = "key";
                                    changeForm.appendChild(keyField);
                                }

                                // Set the key value
                                if (keyField) {
                                    keyField.value = "' . esc_js($key) . '";
                                    console.log("Reset key set to: ' . esc_js($key) . '");
                                }
                            }
                        }

                        // Try multiple times to ensure it works
                        showChangeForm();
                        setTimeout(showChangeForm, 500);
                        setTimeout(showChangeForm, 1500);
                    });
                </script>';
            });
        }

        // We'll handle this in the main login view with JavaScript
        // No need to include a separate template
    }

    // The password reset handling is now done in the condition above
}

/**
 * Force display of the reset form by adding JavaScript to the page content
 */
function lci_force_display_reset_form($content) {
    // Only run once
    remove_filter('the_content', 'lci_force_display_reset_form');

    // Get or generate a reset key if needed
    $key = '';
    $user = null;

    if (isset($_GET['login'])) {
        $login = sanitize_text_field($_GET['login']);
        $user = get_user_by('login', $login);
    }

    if (isset($_GET['key'])) {
        $key = sanitize_text_field($_GET['key']);
    } else if ($user) {
        // Generate a reset key if we have a user but no key
        $key = get_password_reset_key($user);
        $debug_file = __DIR__ . '/debug-password-reset.txt';
        file_put_contents($debug_file, "? Generated new reset key in force_display_reset_form for user: {$user->user_login}\n", FILE_APPEND);
    }

    // Add JavaScript to force display of the reset form
    $script = '<script>
    document.addEventListener("DOMContentLoaded", function() {
        console.log("Content filter forcing display of reset form");

        function forceResetForm() {
            var changeForm = document.getElementById("lci-change-password-form");
            var loginForm = document.getElementById("lci-login-form");

            if (changeForm && loginForm) {
                console.log("Forms found, forcing display");
                loginForm.classList.add("d-none");
                changeForm.classList.remove("d-none");
                changeForm.style.display = "block";
                changeForm.style.opacity = "1";

                // Set the email field
                var emailField = document.getElementById("lci-change-email");
                var login = "' . (isset($_GET['login']) ? esc_js($_GET['login']) : '') . '";
                if (emailField && login) {
                    emailField.value = login;
                    console.log("Email field set to:", login);
                }

                // Also store the login in a hidden field for form submission
                var loginField = document.getElementById("lci-reset-login");
                if (!loginField) {
                    loginField = document.createElement("input");
                    loginField.type = "hidden";
                    loginField.id = "lci-reset-login";
                    loginField.name = "login";
                    changeForm.appendChild(loginField);
                }

                if (loginField && login) {
                    loginField.value = login;
                    console.log("Login field set to:", login);
                }

                // Create or update the key field
                var keyField = document.getElementById("lci-reset-key");
                if (!keyField) {
                    keyField = document.createElement("input");
                    keyField.type = "hidden";
                    keyField.id = "lci-reset-key";
                    keyField.name = "key";
                    changeForm.appendChild(keyField);
                }

                // Set the key value
                if (keyField) {
                    keyField.value = "' . esc_js($key) . '";
                    console.log("Reset key set to: ' . esc_js($key) . '");
                }
            }
        }

        // Try multiple times to ensure it works
        forceResetForm();
        setTimeout(forceResetForm, 500);
        setTimeout(forceResetForm, 1500);
    });
    </script>';

    return $content . $script;
}
add_action('template_redirect', 'lci_process_password_reset');

/**
 * Handle password reset redirects to ensure parameters are preserved
 */
function lci_handle_password_reset_redirect() {
    // This function is intentionally simplified to avoid redirect loops
    // We'll let the direct handler take care of showing the form
    return;
}

/**
 * Process magic link login
 */
function lci_process_magic_link_login() {
    // Check if this is a magic link login request
    if (isset($_GET['lci_magic_token']) && isset($_GET['lci_magic_user']) && isset($_GET['action']) && $_GET['action'] === 'magic_login') {
        $debug_file = __DIR__ . '/debug-magic-login.txt';
        $log = "==== MAGIC LINK LOGIN ATTEMPT ====\n";
        $log .= print_r($_GET, true);
        file_put_contents($debug_file, $log, FILE_APPEND);

        $token = sanitize_text_field($_GET['lci_magic_token']);
        $user_id = intval($_GET['lci_magic_user']);

        // Get user
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            file_put_contents($debug_file, "? User not found: {$user_id}\n", FILE_APPEND);
            wp_redirect(home_url('/lci-dashboard/?login_error=invalid_user'));
            exit;
        }

        // Get stored token and expiration
        $stored_token = get_user_meta($user->ID, 'lci_magic_link_token', true);
        $expiration = get_user_meta($user->ID, 'lci_magic_link_expiration', true);

        // Validate token
        if (empty($stored_token) || $token !== $stored_token) {
            file_put_contents($debug_file, "? Invalid token for user: {$user_id}\n", FILE_APPEND);
            wp_redirect(home_url('/lci-dashboard/?login_error=invalid_token'));
            exit;
        }

        // Check if token has expired
        if (empty($expiration) || time() > $expiration) {
            file_put_contents($debug_file, "? Token expired for user: {$user_id}\n", FILE_APPEND);
            wp_redirect(home_url('/lci-dashboard/?login_error=expired_token'));
            exit;
        }

        // Clear token and expiration
        delete_user_meta($user->ID, 'lci_magic_link_token');
        delete_user_meta($user->ID, 'lci_magic_link_expiration');

        // Log the user in
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID, true);
        do_action('wp_login', $user->user_login, $user);

        file_put_contents($debug_file, "? Magic link login successful for user: {$user_id}\n", FILE_APPEND);

        // Redirect to dashboard
        wp_redirect(home_url('/lci-dashboard/?login=success'));
        exit;
    }
}


// ✅ Save profile details (AJAX)
add_action('wp_ajax_lci_update_profile', function () {
  // Check if user is logged in
  if (!is_user_logged_in()) {
    wp_send_json_error(['message' => 'You must be logged in to update your profile']);
    return;
  }

  // Verify nonce
  if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci_profile_nonce')) {
    wp_send_json_error(['message' => 'Security check failed']);
    return;
  }

  // Get and sanitize data
  $user_id = get_current_user_id();
  $email = sanitize_email($_POST['email']);
  $phone = sanitize_text_field($_POST['phone']);
  $association = isset($_POST['association']) ? sanitize_text_field($_POST['association']) : '';
  $club_no = isset($_POST['club_no']) ? sanitize_text_field($_POST['club_no']) : '';
  $club_position = isset($_POST['club_position']) ? sanitize_text_field($_POST['club_position']) : '';
  $specific_diet = isset($_POST['specific_diet']) ? sanitize_text_field($_POST['specific_diet']) : '';

  // Process allergies
  $allergies = [];
  if (isset($_POST['allergies']) && !empty($_POST['allergies'])) {
    $allergies_json = sanitize_text_field($_POST['allergies']);
    $allergies = json_decode(stripslashes($allergies_json), true);

    // Sanitize each allergy value
    foreach ($allergies as $key => $value) {
      $allergies[$key] = sanitize_text_field($value);
    }
  }

  // Validate email
  if (empty($email) || !is_email($email)) {
    wp_send_json_error(['message' => 'Please provide a valid email address']);
    return;
  }

  // Check if email is already in use by another user
  $existing_user = get_user_by('email', $email);
  if ($existing_user && $existing_user->ID !== $user_id) {
    wp_send_json_error(['message' => 'This email address is already in use by another account']);
    return;
  }

  // Update user data
  $update_result = wp_update_user([
    'ID' => $user_id,
    'user_email' => $email
  ]);

  if (is_wp_error($update_result)) {
    wp_send_json_error(['message' => $update_result->get_error_message()]);
    return;
  }

  // Update phone number
  update_user_meta($user_id, 'billing_phone', $phone);

  // Update club number and position
  update_user_meta($user_id, 'club_no', $club_no);
  update_user_meta($user_id, 'club_position', $club_position);
  update_user_meta($user_id, 'position', $club_position);

  // Update dietary preference
  update_user_meta($user_id, 'specific_diet', $specific_diet);

  // Update allergies
  update_user_meta($user_id, 'alergies', $allergies);

  // Update association
  if (!empty($association)) {
    update_user_meta($user_id, 'your_association', $association);

    // Update all association details based on the provided values
    // This allows us to maintain values for each association type
    // even when switching between them

    // RT Association
    if (isset($_POST['rt_association'])) {
      $rt_association = sanitize_text_field($_POST['rt_association']);
      if (empty($rt_association)) {
        delete_user_meta($user_id, 'rt_association');
      } else {
        update_user_meta($user_id, 'rt_association', $rt_association);
      }
    }

    // LC Association
    if (isset($_POST['lc_association'])) {
      $lc_association = sanitize_text_field($_POST['lc_association']);
      if (empty($lc_association)) {
        delete_user_meta($user_id, 'lc_association');
      } else {
        update_user_meta($user_id, 'lc_association', $lc_association);
      }
    }

    // TANGENT Association
    if (isset($_POST['tangent_association'])) {
      $tangent_association = sanitize_text_field($_POST['tangent_association']);
      if (empty($tangent_association)) {
        delete_user_meta($user_id, 'tangent_association');
      } else {
        update_user_meta($user_id, 'tangent_association', $tangent_association);
      }
    }

    // AGORA Association
    if (isset($_POST['agora_association'])) {
      $agora_association = sanitize_text_field($_POST['agora_association']);
      if (empty($agora_association)) {
        delete_user_meta($user_id, 'agora_association');
      } else {
        update_user_meta($user_id, 'agora_association', $agora_association);
      }
    }

    // C41 Club
    if (isset($_POST['c41_club'])) {
      $c41_club = sanitize_text_field($_POST['c41_club']);
      if (empty($c41_club)) {
        delete_user_meta($user_id, 'c41_club');
      } else {
        update_user_meta($user_id, 'c41_club', $c41_club);
      }
    }
  }

  wp_send_json_success(['message' => 'Profile updated successfully']);
});

// ✅ Change password (AJAX)
add_action('wp_ajax_lci_update_password', function () {
  // Check if user is logged in
  if (!is_user_logged_in()) {
    wp_send_json_error(['message' => 'You must be logged in to change your password']);
    return;
  }

  // Get current user
  $user = wp_get_current_user();

  // Validate input
  if (empty($_POST['current_password']) || empty($_POST['password'])) {
    wp_send_json_error(['message' => 'All password fields are required']);
    return;
  }

  $current_password = $_POST['current_password'];
  $new_password = $_POST['password'];

  // Verify current password
  if (!wp_check_password($current_password, $user->user_pass, $user->ID)) {
    wp_send_json_error(['message' => 'Current password is incorrect']);
    return;
  }

  // Validate new password length
  if (strlen($new_password) < 8) {
    wp_send_json_error(['message' => 'New password must be at least 8 characters long']);
    return;
  }

  // Set new password
  wp_set_password($new_password, $user->ID);

  // Re-authenticate user
  wp_set_auth_cookie($user->ID, true);

  // Log the success for debugging
  file_put_contents($debug_file, "? Password changed successfully for user: {$user->user_login}\n", FILE_APPEND);

  // Prepare the response data
  $response_data = [
    'message' => 'Password changed successfully',
    'status' => 'success',
    'user' => $user->user_login
  ];

  // Log the response for debugging
  file_put_contents($debug_file, "? Sending response: " . json_encode($response_data) . "\n", FILE_APPEND);

  // Send a detailed success response
  wp_send_json_success($response_data);
});

/**
 * Direct password reset handler - adds JavaScript to handle password reset directly on the dashboard page
 * This avoids the need for redirects which can cause redirect loops
 */
function lci_direct_password_reset_handler() {
    // Check if we need to show the password reset form
    $show_reset_form = false;

    // Check if this is a password reset URL with key and login
    if (isset($_GET['key']) && isset($_GET['login']) && isset($_GET['action']) && $_GET['action'] === 'reset_password') {
        $show_reset_form = true;
    }

    // Also check if this is a show-reset-form request
    if (isset($_GET['show-reset-form']) && $_GET['show-reset-form'] === 'true') {
        $show_reset_form = true;
    }

    // If we don't need to show the reset form, return
    if (!$show_reset_form) {
        return;
    }

    // Get the parameters
    $key = isset($_GET['key']) ? sanitize_text_field($_GET['key']) : '';
    $login = isset($_GET['login']) ? sanitize_text_field($_GET['login']) : '';

    // Debug log
    $debug_file = __DIR__ . '/debug-direct-handler.txt';
    $log = "==== DIRECT PASSWORD RESET HANDLER ====\n";
    $log .= "GET data: " . print_r($_GET, true) . "\n";
    $log .= "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
    $log .= "Key: {$key}\n";
    $log .= "Login: {$login}\n";
    file_put_contents($debug_file, $log, FILE_APPEND);

    // Output JavaScript to handle the password reset directly
    echo '<script>
    document.addEventListener("DOMContentLoaded", function() {
        console.log("Direct password reset handler activated");

        // Function to show the change password form
        function showResetForm() {
            console.log("Showing reset form");

            // Get the forms
            var changeForm = document.getElementById("lci-change-password-form");
            var loginForm = document.getElementById("lci-login-form");
            var forgotForm = document.getElementById("lci-forgot-password-form");
            var magicForm = document.getElementById("lci-magic-link-form");

            if (!changeForm) {
                console.error("Change password form not found");
                return;
            }

            // Hide all other forms
            if (loginForm) {
                loginForm.classList.add("d-none");
                console.log("Login form hidden");
            }

            if (forgotForm) {
                forgotForm.classList.add("d-none");
                console.log("Forgot form hidden");
            }

            if (magicForm) {
                magicForm.classList.add("d-none");
                console.log("Magic form hidden");
            }

            // Show the change password form
            changeForm.classList.remove("d-none");
            changeForm.style.display = "block";
            changeForm.style.opacity = "1";
            changeForm.style.transform = "translateY(0)";
            console.log("Change form shown");

            // Set the key and login fields if available
            var key = "' . esc_js($key) . '";
            var login = "' . esc_js($login) . '";

            if (key && login) {
                // Set the email field
                var emailField = document.getElementById("lci-change-email");
                if (emailField) {
                    emailField.value = login;
                    console.log("Email field set to:", login);
                }

                // Create or update the login field
                var loginField = document.getElementById("lci-reset-login");
                if (!loginField) {
                    loginField = document.createElement("input");
                    loginField.type = "hidden";
                    loginField.id = "lci-reset-login";
                    loginField.name = "login";
                    changeForm.appendChild(loginField);
                }
                loginField.value = login;
                console.log("Login field set to:", login);

                // Create or update the key field
                var keyField = document.getElementById("lci-reset-key");
                if (!keyField) {
                    keyField = document.createElement("input");
                    keyField.type = "hidden";
                    keyField.id = "lci-reset-key";
                    keyField.name = "key";
                    changeForm.appendChild(keyField);
                }
                keyField.value = key;
                console.log("Key field set to:", key);
            }

            // Add a message to inform the user
            if (!document.querySelector("#lci-change-password-form .alert-info")) {
                var messageEl = document.createElement("div");
                messageEl.className = "alert alert-info text-center mb-4";
                messageEl.innerHTML = "Please create a new password for your account.";

                var heading = document.querySelector("#lci-change-password-form h2");
                if (heading && heading.parentNode) {
                    heading.parentNode.insertBefore(messageEl, heading.nextSibling);
                }
            }
        }

        // Try multiple times to ensure it works
        showResetForm();
        setTimeout(showResetForm, 500);
        setTimeout(showResetForm, 1500);
    });
    </script>';
}
