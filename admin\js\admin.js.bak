/**
 * LCI 2025 Dashboard Admin JavaScript
 */

// Define Alpine.js components
document.addEventListener('alpine:init', function() {
    // Dashboard component
    Alpine.data('dashboard', function() {
        return {
            stats: {
                total: 0,
                registration_types: {},
                payment_status: {},
                badge_status: {}
            },
            isLoading: true,
            charts: {},

            init() {
                this.fetchStats();
            },

            fetchStats() {
                this.isLoading = true;

                fetch(lciAdmin.ajaxUrl + '?action=lci_get_participants&nonce=' + lciAdmin.nonce)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.processStats(data.data.participants);
                            this.initCharts();
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching stats:', error);
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },

            processStats(participants) {
                this.stats.total = participants.length;

                // Process registration types
                const regTypes = {};
                const paymentStatus = {};
                const badgeStatus = {};

                participants.forEach(participant => {
                    // Registration types
                    if (!regTypes[participant.registration_type]) {
                        regTypes[participant.registration_type] = 0;
                    }
                    regTypes[participant.registration_type]++;

                    // Payment status
                    if (!paymentStatus[participant.payment_status]) {
                        paymentStatus[participant.payment_status] = 0;
                    }
                    paymentStatus[participant.payment_status]++;

                    // Badge status
                    if (!badgeStatus[participant.badge_status]) {
                        badgeStatus[participant.badge_status] = 0;
                    }
                    badgeStatus[participant.badge_status]++;
                });

                this.stats.registration_types = regTypes;
                this.stats.payment_status = paymentStatus;
                this.stats.badge_status = badgeStatus;
            },

            initCharts() {
                // Initialize charts if Chart.js is available
                if (typeof Chart !== 'undefined') {
                    // Registration types chart
                    const regTypesCtx = document.getElementById('regTypesChart');
                    if (regTypesCtx) {
                        const labels = Object.keys(this.stats.registration_types);
                        const data = Object.values(this.stats.registration_types);

                        this.charts.regTypes = new Chart(regTypesCtx, {
                            type: 'doughnut',
                            data: {
                                labels: labels,
                                datasets: [{
                                    data: data,
                                    backgroundColor: [
                                        '#36b1dc',
                                        '#5ebbf5',
                                        '#ef476f',
                                        '#ffd166',
                                        '#06d6a0',
                                        '#8338ec',
                                        '#fb5607',
                                        '#3a86ff'
                                    ],
                                    borderWidth: 0
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                layout: {
                                    padding: {
                                        top: 10,
                                        bottom: 10
                                    }
                                },
                                plugins: {
                                    legend: {
                                        position: 'right',
                                        labels: {
                                            boxWidth: 12,
                                            padding: 15,
                                            font: {
                                                size: 11
                                            }
                                        }
                                    },
                                    title: {
                                        display: true,
                                        text: 'Registration Types',
                                        font: {
                                            size: 14,
                                            weight: 'bold'
                                        },
                                        padding: {
                                            bottom: 10
                                        }
                                    }
                                },
                                cutout: '70%'
                            }
                        });

                        // Set chart container height
                        regTypesCtx.parentNode.style.height = '250px';
                    }

                    // Payment status chart
                    const paymentStatusCtx = document.getElementById('paymentStatusChart');
                    if (paymentStatusCtx) {
                        const labels = Object.keys(this.stats.payment_status);
                        const data = Object.values(this.stats.payment_status);

                        // Generate colors for each payment status
                        const statusColors = {
                            'completed': '#06d6a0',   // Green for completed
                            'processing': '#ffd166',  // Yellow for processing
                            'on-hold': '#ef476f',     // Red for on-hold
                            'pending': '#36b1dc',     // Blue for pending
                            'failed': '#fb5607',      // Orange for failed
                            'refunded': '#8338ec',    // Purple for refunded
                            'cancelled': '#6c757d'    // Gray for cancelled
                        };

                        // Create background colors array based on labels
                        const backgroundColors = labels.map(label => {
                            const status = label.toLowerCase();
                            return statusColors[status] || '#36b1dc'; // Default to blue if status not found
                        });

                        this.charts.paymentStatus = new Chart(paymentStatusCtx, {
                            type: 'bar',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: 'Payment Status',
                                    data: data,
                                    backgroundColor: backgroundColors,
                                    borderRadius: 6
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                layout: {
                                    padding: {
                                        top: 10,
                                        bottom: 10
                                    }
                                },
                                plugins: {
                                    legend: {
                                        display: false
                                    },
                                    tooltip: {
                                        callbacks: {
                                            title: function(tooltipItems) {
                                                return tooltipItems[0].label;
                                            },
                                            label: function(context) {
                                                return `Count: ${context.raw}`;
                                            }
                                        }
                                    },
                                    title: {
                                        display: true,
                                        text: 'Payment Status',
                                        font: {
                                            size: 14,
                                            weight: 'bold'
                                        },
                                        padding: {
                                            bottom: 10
                                        }
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            precision: 0
                                        }
                                    },
                                    x: {
                                        ticks: {
                                            maxRotation: 45,
                                            minRotation: 45
                                        }
                                    }
                                }
                            }
                        });

                        // Set chart container height
                        paymentStatusCtx.parentNode.style.height = '250px';
                    }
                }
            }
        };
    });

    // Participants table component
    Alpine.data('participantsTable', function() {
        return {
            participants: [],
            filteredParticipants: [],
            isLoading: true,
            searchQuery: '',
            regTypeFilter: '',
            paymentStatusFilter: '',
            availableRegTypes: [],
            availablePaymentStatuses: [],
            sortField: 'order_date',
            sortAsc: false, // Sort by order date in descending order (newest first)

            // Format date for display
            formatDate(dateString) {
                if (!dateString) return 'N/A';
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            },
            currentPage: 1,
            perPage: 10,
            totalPages: 1,
            isSyncing: false,

            init() {
                this.fetchParticipants();
            },

            fetchParticipants() {
                this.isLoading = true;

                fetch(lciAdmin.ajaxUrl + '?action=lci_get_participants&nonce=' + lciAdmin.nonce)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.participants = data.data.participants;

                            // Extract unique registration types and payment statuses
                            const regTypes = new Set();
                            const paymentStatuses = new Set();

                            this.participants.forEach(participant => {
                                if (participant.registration_type) {
                                    regTypes.add(participant.registration_type);
                                }
                                if (participant.payment_status) {
                                    paymentStatuses.add(participant.payment_status);
                                }
                            });

                            this.availableRegTypes = Array.from(regTypes).sort();
                            this.availablePaymentStatuses = Array.from(paymentStatuses).sort();

                            this.filterParticipants();
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching participants:', error);
                        this.showToast('Error fetching participants', 'error');
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },

            filterParticipants() {
                // Start with all participants
                let filtered = [...this.participants];

                // Apply search filter if provided
                if (this.searchQuery && this.searchQuery.trim() !== '') {
                    const query = this.searchQuery.toLowerCase();
                    filtered = filtered.filter(participant => {
                        return (
                            (participant.first_name && participant.first_name.toLowerCase().includes(query)) ||
                            (participant.last_name && participant.last_name.toLowerCase().includes(query)) ||
                            (participant.email && participant.email.toLowerCase().includes(query)) ||
                            (participant.unique_reg_id && participant.unique_reg_id.toLowerCase().includes(query)) ||
                            (participant.original_reg_id && participant.original_reg_id.toLowerCase().includes(query)) ||
                            (participant.phone && participant.phone.toLowerCase().includes(query))
                        );
                    });
                }

                // Apply registration type filter if selected
                if (this.regTypeFilter) {
                    filtered = filtered.filter(participant =>
                        participant.registration_type === this.regTypeFilter
                    );
                }

                // Apply payment status filter if selected
                if (this.paymentStatusFilter) {
                    filtered = filtered.filter(participant =>
                        participant.payment_status === this.paymentStatusFilter
                    );
                }

                this.filteredParticipants = filtered;
                this.sortParticipants();
                this.updatePagination();
            },

            debouncedSearch() {
                clearTimeout(this._searchTimeout);
                this._searchTimeout = setTimeout(() => {
                    this.filterParticipants();
                }, 300);
            },

            sortParticipants() {
                this.filteredParticipants.sort((a, b) => {
                    let aValue = a[this.sortField];
                    let bValue = b[this.sortField];

                    // Handle string comparison
                    if (typeof aValue === 'string') {
                        aValue = aValue.toLowerCase();
                        bValue = bValue.toLowerCase();
                    }

                    if (aValue < bValue) {
                        return this.sortAsc ? -1 : 1;
                    }
                    if (aValue > bValue) {
                        return this.sortAsc ? 1 : -1;
                    }
                    return 0;
                });
            },

            sort(field) {
                if (this.sortField === field) {
                    this.sortAsc = !this.sortAsc;
                } else {
                    this.sortField = field;
                    this.sortAsc = true;
                }

                this.sortParticipants();
            },

            updatePagination() {
                this.totalPages = Math.ceil(this.filteredParticipants.length / this.perPage);
                this.currentPage = Math.min(this.currentPage, this.totalPages);

                if (this.currentPage < 1) {
                    this.currentPage = 1;
                }
            },

            get paginatedParticipants() {
                const start = (this.currentPage - 1) * this.perPage;
                const end = start + this.perPage;
                return this.filteredParticipants.slice(start, end);
            },

            syncAll() {
                if (this.isSyncing) return;

                if (!confirm(lciAdmin.strings.confirmSync)) {
                    return;
                }

                this.isSyncing = true;

                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_sync_all_participants',
                        nonce: lciAdmin.nonce
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.showToast(data.data.message, 'success');
                            this.fetchParticipants();
                        } else {
                            this.showToast(data.data.message || lciAdmin.strings.syncError, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error syncing participants:', error);
                        this.showToast(lciAdmin.strings.syncError, 'error');
                    })
                    .finally(() => {
                        this.isSyncing = false;
                    });
            },

            syncParticipant(orderId) {
                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_sync_participant',
                        nonce: lciAdmin.nonce,
                        order_id: orderId
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.showToast(data.data.message, 'success');
                            this.fetchParticipants();
                        } else {
                            this.showToast(data.data.message || 'Error syncing participant', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error syncing participant:', error);
                        this.showToast('Error syncing participant', 'error');
                    });
            },

            showToast(message, type = 'info') {
                // Create toast element
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            ${type === 'success' ? '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'error' ? '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'info' ? '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v4a1 1 0 102 0V7zm-1-5a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"></path></svg>' : ''}
                        </div>
                        <div>${message}</div>
                    </div>
                    <button class="absolute top-2 right-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                `;

                document.body.appendChild(toast);

                // Show toast
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);

                // Hide toast after 5 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }, 5000);
            }
        };
    });

    // Participant detail component
    Alpine.data('participantDetail', function() {
        return {
            participant: {},
            isLoading: true,
            isSaving: false,
            activeTab: 'personal',

            // Parse accommodation string into an array of items
            parseAccommodation(accommodationStr) {
                if (!accommodationStr || accommodationStr.trim() === '') {
                    return [];
                }

                // Split by comma and trim each item
                return accommodationStr.split(',').map(item => item.trim()).filter(item => item !== '');
            },

            // Parse tours string into an array of items
            parseTours(toursStr) {
                if (!toursStr || toursStr.trim() === '') {
                    return [];
                }

                // Try to parse as JSON first
                try {
                    const toursData = JSON.parse(toursStr);
                    if (Array.isArray(toursData)) {
                        return toursData.map(tour => {
                            if (typeof tour === 'object') {
                                // Format tour object into a readable string
                                const tourName = tour.name || tour.product_name || '';
                                const tourId = tour.product_id || '';
                                return `${tourName} (ID: ${tourId})`;
                            }
                            return String(tour);
                        });
                    } else if (typeof toursData === 'object') {
                        // Handle single tour object
                        return Object.entries(toursData).map(([key, value]) => {
                            return `${key}: ${value}`;
                        });
                    }
                } catch (e) {
                    // Not valid JSON, continue with string parsing
                }

                // Split by comma and trim each item
                return toursStr.split(',').map(item => item.trim()).filter(item => item !== '');
            },

            init() {
                const participantDataEl = document.getElementById('participant-data');
                if (participantDataEl) {
                    try {
                        this.participant = JSON.parse(participantDataEl.textContent);
                        this.isLoading = false;

                        // Check if tab parameter is present in URL
                        const urlParams = new URLSearchParams(window.location.search);
                        const tabParam = urlParams.get('tab');
                        if (tabParam) {
                            // Set active tab based on URL parameter
                            const validTabs = ['personal', 'registration', 'tours', 'accommodation', 'diet', 'notes'];
                            if (validTabs.includes(tabParam)) {
                                this.activeTab = tabParam;
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing participant data:', e);
                    }
                }
            },

            saveParticipant() {
                this.isSaving = true;

                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_update_participant',
                        nonce: lciAdmin.nonce,
                        id: this.participant.id,
                        data: JSON.stringify(this.participant)
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.showToast(data.data.message || lciAdmin.strings.updateSuccess, 'success');
                        } else {
                            this.showToast(data.data.message || lciAdmin.strings.updateError, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error updating participant:', error);
                        this.showToast(lciAdmin.strings.updateError, 'error');
                    })
                    .finally(() => {
                        this.isSaving = false;
                    });
            },

            showToast(message, type = 'info') {
                // Create toast element
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            ${type === 'success' ? '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'error' ? '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'info' ? '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v4a1 1 0 102 0V7zm-1-5a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"></path></svg>' : ''}
                        </div>
                        <div>${message}</div>
                    </div>
                    <button class="absolute top-2 right-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                `;

                document.body.appendChild(toast);

                // Show toast
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);

                // Hide toast after 5 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }, 5000);
            }
        };
    });

    // Tours manager component
    Alpine.data('toursManager', function() {
        return {
            activeTab: 'main_pretour',
            participants: [],
            filteredParticipants: [],
            isLoading: true,
            isSyncing: false,
            isSyncingAll: false,
            showSyncModal: false,
            searchQuery: '',
            currentPage: 1,
            perPage: 10,
            totalPages: 1,
            missingCounts: typeof lciToursMissingCounts !== 'undefined' ? lciToursMissingCounts : {
                main_pretour: 0,
                legends_wildlife: 0,
                royal_elegance: 0,
                brasov_highlights: 0
            },
            tourTables: typeof lciTourTables !== 'undefined' ? lciTourTables : {
                main_pretour: { exists: false, count: 0 },
                legends_wildlife: { exists: false, count: 0 },
                royal_elegance: { exists: false, count: 0 },
                brasov_highlights: { exists: false, count: 0 }
            },

            // Format date for display
            formatDate(dateString) {
                if (!dateString) return 'N/A';
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            },

            init() {
                this.checkTourTables();
                this.fetchTourParticipants();
            },

            checkTourTables() {
                // Check if any tour table is missing or empty
                const needsSync = Object.values(this.tourTables).some(table => !table.exists || table.count === 0);

                if (needsSync) {
                    // Show sync modal
                    this.showSyncModal = true;
                }
            },

            getMissingTablesMessage() {
                const missingTables = [];

                if (!this.tourTables.main_pretour.exists || this.tourTables.main_pretour.count === 0) {
                    missingTables.push('Main Pre-Tour');
                }

                if (!this.tourTables.legends_wildlife.exists || this.tourTables.legends_wildlife.count === 0) {
                    missingTables.push('Legends & Wildlife');
                }

                if (!this.tourTables.royal_elegance.exists || this.tourTables.royal_elegance.count === 0) {
                    missingTables.push('Royal Elegance');
                }

                if (!this.tourTables.brasov_highlights.exists || this.tourTables.brasov_highlights.count === 0) {
                    missingTables.push('Brasov Highlights');
                }

                if (missingTables.length === 0) {
                    return 'All tour tables are up to date.';
                } else if (missingTables.length === 1) {
                    return `The ${missingTables[0]} tour table needs to be synced.`;
                } else {
                    const lastTable = missingTables.pop();
                    return `The ${missingTables.join(', ')} and ${lastTable} tour tables need to be synced.`;
                }
            },

            syncAllTours() {
                if (this.isSyncingAll) return;

                this.isSyncingAll = true;

                const tourTypes = ['main_pretour', 'legends_wildlife', 'royal_elegance', 'brasov_highlights'];
                const productIds = [743, 744, 745, 746];

                // Create promises for each tour sync
                const syncPromises = tourTypes.map((tourType, index) => {
                    return new Promise((resolve, reject) => {
                        const formData = new FormData();
                        formData.append('action', 'lci_sync_tour_participants');
                        formData.append('nonce', lciAdmin.nonce);
                        formData.append('tour_type', tourType);
                        formData.append('product_id', productIds[index]);

                        fetch(lciAdmin.ajaxUrl, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                resolve(data.data);
                            } else {
                                reject(data.data.message || 'Error syncing tour');
                            }
                        })
                        .catch(error => {
                            reject(error.message || 'Network error');
                        });
                    });
                });

                // Execute all sync operations
                Promise.all(syncPromises)
                    .then(results => {
                        // All syncs completed successfully
                        const totalSuccess = results.reduce((sum, result) => sum + result.success_count, 0);
                        const totalErrors = results.reduce((sum, result) => sum + result.error_count, 0);

                        this.showToast(`Sync completed. ${totalSuccess} participants synced successfully, ${totalErrors} errors.`, 'success');

                        // Refresh tour participants
                        this.fetchTourParticipants();

                        // Close modal
                        this.showSyncModal = false;
                    })
                    .catch(error => {
                        console.error('Error syncing tours:', error);
                        this.showToast(`Error syncing tours: ${error}`, 'error');
                    })
                    .finally(() => {
                        this.isSyncingAll = false;
                    });
            },

            fetchTourParticipants() {
                this.isLoading = true;

                fetch(lciAdmin.ajaxUrl + '?action=lci_get_tour_participants&nonce=' + lciAdmin.nonce + '&tour_type=' + this.activeTab)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.participants = data.data.participants;
                            this.filterParticipants();

                            // Check if we need to show the sync modal
                            if (this.participants.length === 0 && !this.tourTables[this.activeTab].exists) {
                                this.showSyncModal = true;
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching tour participants:', error);
                        this.showToast('Error fetching tour participants', 'error');
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },

            filterParticipants() {
                if (!this.searchQuery || this.searchQuery.trim() === '') {
                    this.filteredParticipants = [...this.participants];
                } else {
                    const query = this.searchQuery.toLowerCase();
                    this.filteredParticipants = this.participants.filter(participant => {
                        return (
                            (participant.first_name && participant.first_name.toLowerCase().includes(query)) ||
                            (participant.last_name && participant.last_name.toLowerCase().includes(query)) ||
                            (participant.email && participant.email.toLowerCase().includes(query)) ||
                            (participant.unique_reg_id && participant.unique_reg_id.toLowerCase().includes(query)) ||
                            (participant.phone && participant.phone.toLowerCase().includes(query))
                        );
                    });
                }

                this.updatePagination();
            },

            updatePagination() {
                this.totalPages = Math.ceil(this.filteredParticipants.length / this.perPage);
                this.currentPage = Math.min(this.currentPage, this.totalPages || 1);

                if (this.currentPage < 1) {
                    this.currentPage = 1;
                }
            },

            get paginatedParticipants() {
                const start = (this.currentPage - 1) * this.perPage;
                const end = start + this.perPage;
                return this.filteredParticipants.slice(start, end);
            },

            getTourTitle() {
                switch (this.activeTab) {
                    case 'main_pretour':
                        return 'Main Pretour Participants';
                    case 'legends_wildlife':
                        return 'Legends & Wildlife Escape Participants';
                    case 'royal_elegance':
                        return 'Royal Elegance & Sparkling Delights Participants';
                    case 'brasov_highlights':
                        return 'Brasov Highlights Participants';
                    default:
                        return 'Tour Participants';
                }
            },

            getProductId() {
                switch (this.activeTab) {
                    case 'main_pretour':
                        return 743; // Main Pretour product ID
                    case 'legends_wildlife':
                        return 744; // Legends & Wildlife product ID
                    case 'royal_elegance':
                        return 745; // Royal Elegance product ID
                    case 'brasov_highlights':
                        return 746; // Brasov Highlights product ID
                    default:
                        return 0;
                }
            },

            getMissingCount() {
                return this.missingCounts[this.activeTab] || 0;
            },

            syncTour() {
                if (this.isSyncing) return;

                this.isSyncing = true;

                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_sync_tour_participants',
                        nonce: lciAdmin.nonce,
                        tour_type: this.activeTab,
                        product_id: this.getProductId()
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.showToast(data.data.message, 'success');
                            // Update missing count
                            this.missingCounts[this.activeTab] = 0;
                            // Refresh participants list
                            this.fetchTourParticipants();
                        } else {
                            this.showToast(data.data.message || 'Error syncing tour participants', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error syncing tour participants:', error);
                        this.showToast('Error syncing tour participants', 'error');
                    })
                    .finally(() => {
                        this.isSyncing = false;
                    });
            },

            clearAndResyncTour() {
                if (this.isSyncing) return;

                if (!confirm('Are you sure you want to clear and resync this tour? This will delete all existing data for this tour.')) {
                    return;
                }

                this.isSyncing = true;

                // First clear the tour table
                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_clear_tour_table',
                        nonce: lciAdmin.nonce,
                        tour_type: this.activeTab
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.showToast('Tour table cleared successfully. Resyncing...', 'info');

                            // Now sync the tour participants
                            return fetch(lciAdmin.ajaxUrl, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded'
                                },
                                body: new URLSearchParams({
                                    action: 'lci_sync_tour_participants',
                                    nonce: lciAdmin.nonce,
                                    tour_type: this.activeTab,
                                    product_id: this.getProductId()
                                })
                            });
                        } else {
                            throw new Error(data.data.message || 'Error clearing tour table');
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.showToast('Tour participants resynced successfully.', 'success');
                            // Update missing count
                            this.missingCounts[this.activeTab] = 0;
                            // Refresh participants list
                            this.fetchTourParticipants();
                        } else {
                            throw new Error(data.data.message || 'Error syncing tour participants');
                        }
                    })
                    .catch(error => {
                        console.error('Error clearing and resyncing tour:', error);
                        this.showToast(error.message || 'Error clearing and resyncing tour', 'error');
                    })
                    .finally(() => {
                        this.isSyncing = false;
                    });
            },

            showToast(message, type = 'info') {
                // Create toast element
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            ${type === 'success' ? '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'error' ? '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'info' ? '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v4a1 1 0 102 0V7zm-1-5a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"></path></svg>' : ''}
                        </div>
                        <div>${message}</div>
                    </div>
                    <button class="absolute top-2 right-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                `;

                document.body.appendChild(toast);

                // Show toast
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);

                // Hide toast after 5 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }, 5000);
            },

            syncAll() {
                if (this.isSyncing) return;

                if (!confirm(lciAdmin.strings.confirmSync)) {
                    return;
                }

                this.isSyncing = true;
                this.progress = 0;
                this.processedOrders = 0;
                this.successCount = 0;
                this.errorCount = 0;

                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_sync_all_participants',
                        nonce: lciAdmin.nonce
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.successCount = data.data.success_count;
                            this.errorCount = data.data.error_count;
                            this.showToast(data.data.message, 'success');
                        } else {
                            this.showToast(data.data.message || lciAdmin.strings.syncError, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error syncing participants:', error);
                        this.showToast(lciAdmin.strings.syncError, 'error');
                    })
                    .finally(() => {
                        this.isSyncing = false;
                        this.progress = 100;
                    });
            },

            showToast(message, type = 'info') {
                // Create toast element
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            ${type === 'success' ? '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'error' ? '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'info' ? '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v4a1 1 0 102 0V7zm-1-5a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"></path></svg>' : ''}
                        </div>
                        <div>${message}</div>
                    </div>
                    <button class="absolute top-2 right-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                `;

                document.body.appendChild(toast);

                // Show toast
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);

                // Hide toast after 5 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }, 5000);
            }
        };
    });

    // Sync tool component
    Alpine.data('syncTool', function() {
        return {
            isSyncing: false,
            progress: 0,
            totalOrders: 0,
            processedOrders: 0,
            successCount: 0,
            errorCount: 0,

            syncAll() {
                if (this.isSyncing) return;

                if (!confirm(lciAdmin.strings.confirmSync)) {
                    return;
                }

                this.isSyncing = true;
                this.progress = 0;
                this.processedOrders = 0;
                this.successCount = 0;
                this.errorCount = 0;

                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_sync_all_participants',
                        nonce: lciAdmin.nonce
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.successCount = data.data.success_count;
                            this.errorCount = data.data.error_count;
                            this.showToast(data.data.message, 'success');
                        } else {
                            this.showToast(data.data.message || lciAdmin.strings.syncError, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error syncing participants:', error);
                        this.showToast(lciAdmin.strings.syncError, 'error');
                    })
                    .finally(() => {
                        this.isSyncing = false;
                        this.progress = 100;
                    });
            },

            showToast(message, type = 'info') {
                // Create toast element
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            ${type === 'success' ? '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'error' ? '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'info' ? '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v4a1 1 0 102 0V7zm-1-5a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"></path></svg>' : ''}
                        </div>
                        <div>${message}</div>
                    </div>
                    <button class="absolute top-2 right-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                `;

                document.body.appendChild(toast);

                // Show toast
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);

                // Hide toast after 5 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }, 5000);
            }
        };
    });

    // Allergies and Diet table component
    Alpine.data('allergiesDietTable', function() {
        return {
            participants: [],
            filteredParticipants: [],
            isLoading: true,
            searchQuery: '',
            dietFilter: '',
            hasAllergies: '',
            sortField: 'last_name',
            sortAsc: true,
            currentPage: 1,
            perPage: 10,
            totalPages: 1,
            searchTimeout: null,

            init() {
                // Initialize filteredParticipants as an empty array to prevent errors
                this.filteredParticipants = [];
                this.fetchParticipants();
            },

            fetchParticipants() {
                this.isLoading = true;

                fetch(lciAdmin.ajaxUrl + '?action=lci_get_participants&nonce=' + lciAdmin.nonce)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.participants = data.data.participants;
                            this.filterParticipants();
                        } else {
                            console.error('Error fetching participants:', data.data.message);
                            this.showToast('Error fetching participants', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching participants:', error);
                        this.showToast('Error fetching participants', 'error');
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },

            filterParticipants() {
                // Filter by search query
                let filtered = this.participants;

                if (this.searchQuery.trim() !== '') {
                    const query = this.searchQuery.toLowerCase();
                    filtered = filtered.filter(participant => {
                        return (
                            (participant.first_name && participant.first_name.toLowerCase().includes(query)) ||
                            (participant.last_name && participant.last_name.toLowerCase().includes(query)) ||
                            (participant.email && participant.email.toLowerCase().includes(query)) ||
                            (participant.diet && participant.diet.toLowerCase().includes(query)) ||
                            (participant.allergies && participant.allergies.toLowerCase().includes(query))
                        );
                    });
                }

                // Filter by diet
                if (this.dietFilter.trim() !== '') {
                    const dietQuery = this.dietFilter.toLowerCase();
                    filtered = filtered.filter(participant => {
                        return participant.diet && participant.diet.toLowerCase().includes(dietQuery);
                    });
                }

                // Filter by has allergies
                if (this.hasAllergies === 'yes') {
                    filtered = filtered.filter(participant => {
                        return participant.allergies && participant.allergies.trim() !== '';
                    });
                } else if (this.hasAllergies === 'no') {
                    filtered = filtered.filter(participant => {
                        return !participant.allergies || participant.allergies.trim() === '';
                    });
                }

                // Sort the filtered participants
                filtered = this.sortParticipants(filtered);

                this.filteredParticipants = filtered;
                this.totalPages = Math.ceil(this.filteredParticipants.length / this.perPage);
                this.currentPage = Math.min(this.currentPage, this.totalPages || 1);
            },

            sortParticipants(participants) {
                return [...participants].sort((a, b) => {
                    let aValue = a[this.sortField] || '';
                    let bValue = b[this.sortField] || '';

                    // Handle name sorting
                    if (this.sortField === 'first_name') {
                        aValue = a.first_name + ' ' + a.last_name;
                        bValue = b.first_name + ' ' + b.last_name;
                    }

                    // Handle numeric values
                    if (!isNaN(aValue) && !isNaN(bValue)) {
                        return this.sortAsc ? aValue - bValue : bValue - aValue;
                    }

                    // Handle string values
                    if (typeof aValue === 'string' && typeof bValue === 'string') {
                        return this.sortAsc ?
                            aValue.localeCompare(bValue) :
                            bValue.localeCompare(aValue);
                    }

                    return 0;
                });
            },

            sort(field) {
                if (this.sortField === field) {
                    this.sortAsc = !this.sortAsc;
                } else {
                    this.sortField = field;
                    this.sortAsc = true;
                }

                this.filterParticipants();
            },

            debounceSearch() {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.filterParticipants();
                }, 300);
            },

            get paginatedParticipants() {
                const start = (this.currentPage - 1) * this.perPage;
                const end = start + this.perPage;
                return this.filteredParticipants.slice(start, end);
            },

            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            ${type === 'success' ? '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'error' ? '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' : ''}
                            ${type === 'info' ? '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v4a1 1 0 102 0V7zm-1-5a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"></path></svg>' : ''}
                        </div>
                        <div>${message}</div>
                    </div>
                    <button class="absolute top-2 right-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                `;

                document.body.appendChild(toast);

                // Show toast
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);

                // Hide toast after 5 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }, 5000);
            }
        };
    });
});
