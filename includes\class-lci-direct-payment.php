<?php
/**
 * LCI Direct Payment Handler
 *
 * Handles direct payment processing within the LCI Dashboard
 * without redirecting to WooCommerce checkout.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class to handle direct payments in the LCI Dashboard
 */
class LCI_Direct_Payment {
    /**
     * Initialize the handler
     */
    public static function init() {
        // Add AJAX handler
        add_action('wp_ajax_lci_process_direct_payment', [__CLASS__, 'ajax_process_direct_payment']);

        // Add scripts and styles
        add_action('wp_enqueue_scripts', [__CLASS__, 'enqueue_scripts']);

        // Add shortcode for payment form
        add_shortcode('lci_payment_form', [__CLASS__, 'payment_form_shortcode']);
    }

    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        // Only enqueue on pages with our shortcode or payment page
        global $post;
        if ((!is_a($post, 'WP_Post') || !has_shortcode($post->post_content, 'lci_payment_form')) &&
            (!isset($_GET['tab']) || $_GET['tab'] !== 'payment')) {
            return;
        }

        // Enqueue our custom script
        wp_enqueue_script(
            'lci-direct-payment',
            LCI2025_URL . 'assets/js/direct-payment.js',
            ['jquery'],
            LCI2025_VERSION,
            true
        );

        // Localize script with data
        wp_localize_script('lci-direct-payment', 'lciDirectPayment', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lci_direct_payment_nonce'),
            'i18n' => [
                'processingPayment' => __('Processing payment...', 'lci-2025-dashboard'),
                'paymentSuccess' => __('Payment successful!', 'lci-2025-dashboard'),
                'paymentError' => __('Payment failed. Please try again.', 'lci-2025-dashboard'),
                'redirecting' => __('Redirecting to payment gateway...', 'lci-2025-dashboard'),
                'bankTransferInstructions' => __('Please make payment to the following bank account:', 'lci-2025-dashboard'),
            ]
        ]);

        // Create the direct payment JS file if it doesn't exist
        $js_dir = LCI2025_PATH . 'assets/js';
        if (!file_exists($js_dir)) {
            wp_mkdir_p($js_dir);
        }

        $js_file = $js_dir . '/direct-payment.js';
        if (!file_exists($js_file)) {
            $js_content = <<<'EOT'
/**
 * LCI Direct Payment Handler
 */
(function($) {
    'use strict';

    // Initialize payment form
    function initPaymentForm() {
        // Handle payment method selection
        $('.lci-payment-method-input').on('change', function() {
            const selectedMethod = $(this).val();

            // Show/hide payment method descriptions
            $('.lci-payment-method-description').hide();
            $('#lci-payment-method-description-' + selectedMethod).show();

            // Update button text
            if (selectedMethod === 'bacs') {
                $('.lci-payment-button').text('Complete Bank Transfer Order');
            } else {
                $('.lci-payment-button').text('Proceed to Payment');
            }
        });

        // Handle form submission
        $('.lci-payment-form').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const submitButton = form.find('.lci-payment-button');
            const messageContainer = $('.lci-payment-messages');
            const selectedMethod = form.find('.lci-payment-method-input:checked').val();

            if (!selectedMethod) {
                messageContainer.html('<div class="lci-notice lci-notice-error"><p>Please select a payment method.</p></div>');
                return;
            }

            // Disable button and show loading message
            submitButton.prop('disabled', true).text(lciDirectPayment.i18n.processingPayment);
            messageContainer.html('<div class="lci-notice lci-notice-info"><p>' + lciDirectPayment.i18n.processingPayment + '</p></div>');

            // Get form data
            const formData = new FormData(form[0]);
            formData.append('action', 'lci_process_direct_payment');
            formData.append('security', lciDirectPayment.nonce);

            // Process payment
            $.ajax({
                url: lciDirectPayment.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        // Handle different payment types
                        if (response.data.payment_type === 'bacs') {
                            // Show bank transfer instructions
                            messageContainer.html('<div class="lci-notice lci-notice-success"><p>' + lciDirectPayment.i18n.paymentSuccess + '</p></div>');
                            $('.lci-payment-instructions').html(response.data.instructions).show();

                            // Reset button
                            submitButton.prop('disabled', false).text('Complete Bank Transfer Order');

                            // Hide form
                            form.find('.lci-payment-methods').hide();
                            form.find('.lci-payment-button').hide();

                            // Show order details
                            if (response.data.order_id) {
                                $('.lci-order-details').html('<p>Order #' + response.data.order_id + ' has been created. Please use this as a reference when making your bank transfer.</p>').show();
                            }
                        } else if (response.data.payment_type === 'redirect' && response.data.redirect_url) {
                            // Show redirect message
                            messageContainer.html('<div class="lci-notice lci-notice-info"><p>' + lciDirectPayment.i18n.redirecting + '</p></div>');

                            // Redirect to payment gateway
                            setTimeout(function() {
                                window.location.href = response.data.redirect_url;
                            }, 1000);
                        } else {
                            // Generic success
                            messageContainer.html('<div class="lci-notice lci-notice-success"><p>' + lciDirectPayment.i18n.paymentSuccess + '</p></div>');
                            submitButton.prop('disabled', false).text('Payment Completed');
                        }
                    } else {
                        // Show error message
                        messageContainer.html('<div class="lci-notice lci-notice-error"><p>' + (response.data.message || lciDirectPayment.i18n.paymentError) + '</p></div>');
                        submitButton.prop('disabled', false).text('Try Again');
                    }
                },
                error: function() {
                    // Show error message
                    messageContainer.html('<div class="lci-notice lci-notice-error"><p>' + lciDirectPayment.i18n.paymentError + '</p></div>');
                    submitButton.prop('disabled', false).text('Try Again');
                }
            });
        });

        // Trigger change on the checked payment method
        $('.lci-payment-method-input:checked').trigger('change');
    }

    // Initialize on document ready
    $(document).ready(function() {
        initPaymentForm();
    });
})(jQuery);
EOT;
            file_put_contents($js_file, $js_content);
        }

        // Enqueue CSS
        wp_enqueue_style(
            'lci-direct-payment',
            LCI2025_URL . 'assets/css/direct-payment.css',
            [],
            LCI2025_VERSION
        );

        // Create the CSS file if it doesn't exist
        $css_dir = LCI2025_PATH . 'assets/css';
        if (!file_exists($css_dir)) {
            wp_mkdir_p($css_dir);
        }

        $css_file = $css_dir . '/direct-payment.css';
        if (!file_exists($css_file)) {
            $css_content = <<<'EOT'
/**
 * LCI Direct Payment Styles
 */

/* Payment Form */
.lci-payment-form {
    margin-bottom: 30px;
}

.lci-payment-methods {
    margin-bottom: 20px;
}

.lci-payment-method {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.lci-payment-method-header {
    padding: 15px;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.lci-payment-method-header:hover {
    background-color: #f0f0f0;
}

.lci-payment-method-input {
    margin-right: 10px;
}

.lci-payment-method-label {
    display: flex;
    flex-direction: column;
    flex: 1;
    cursor: pointer;
}

.lci-payment-method-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
}

.lci-payment-method-description {
    padding: 15px;
    background-color: #fff;
    border-top: 1px solid #ddd;
    display: none;
}

.lci-payment-button {
    background-color: #36b1dc;
    color: #fff;
    border: none;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    text-transform: uppercase;
    width: 100%;
    max-width: 300px;
}

.lci-payment-button:hover {
    background-color: #2c99c2;
}

.lci-payment-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Payment Messages */
.lci-payment-messages {
    margin-bottom: 20px;
}

.lci-notice {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.lci-notice p {
    margin: 0;
}

.lci-notice-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.lci-notice-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.lci-notice-info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* Payment Instructions */
.lci-payment-instructions {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: none;
}

.lci-payment-instructions h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

.lci-payment-instructions p {
    margin-bottom: 15px;
}

.lci-payment-instructions ul.bacs-accounts {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
}

.lci-payment-instructions ul.bacs-accounts li {
    padding: 10px;
    margin-bottom: 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Order Details */
.lci-order-details {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: none;
}

/* Mobile Styles */
@media (max-width: 768px) {
    .lci-payment-method-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .lci-payment-method-input {
        margin-bottom: 10px;
    }

    .lci-payment-button {
        width: 100%;
        max-width: none;
    }
}
EOT;
            file_put_contents($css_file, $css_content);
        }
    }

    /**
     * Payment form shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Shortcode output
     */
    public static function payment_form_shortcode($atts) {
        // Parse attributes
        $atts = shortcode_atts([
            'amount' => '0',
            'description' => 'Custom Payment',
            'product_id' => '',
            'quantity' => '1',
        ], $atts, 'lci_payment_form');

        // Get available payment gateways
        $available_gateways = [];

        if (function_exists('WC') && isset(WC()->payment_gateways)) {
            $gateways = WC()->payment_gateways->payment_gateways();

            // Filter for only the gateways we want

            if (isset($gateways['bacs']) && $gateways['bacs']->enabled === 'yes') {
                $available_gateways['bacs'] = [
                    'id' => 'bacs',
                    'title' => __('Bank Transfer', 'lci-2025-dashboard'),
                    'description' => __('Make your payment directly into our bank account.', 'lci-2025-dashboard'),
                    'gateway' => $gateways['bacs'],
                ];
            }
        }

        // Start output buffer
        ob_start();

        // Check if we have available gateways
        if (empty($available_gateways)) {
            echo '<div class="lci-notice lci-notice-error"><p>' . __('No payment methods available.', 'lci-2025-dashboard') . '</p></div>';
            return ob_get_clean();
        }

        // Payment form
        ?>
        <div class="lci-direct-payment-container">
            <div class="lci-payment-messages"></div>

            <form class="lci-payment-form" method="post">
                <div class="lci-payment-methods">
                    <h3><?php _e('Select Payment Method', 'lci-2025-dashboard'); ?></h3>

                    <?php foreach ($available_gateways as $gateway_id => $gateway_data) : ?>
                        <div class="lci-payment-method">
                            <div class="lci-payment-method-header">
                                <input type="radio" name="payment_method" id="payment_method_<?php echo esc_attr($gateway_id); ?>" value="<?php echo esc_attr($gateway_id); ?>" class="lci-payment-method-input" <?php checked($gateway_id, array_key_first($available_gateways)); ?>>
                                <label for="payment_method_<?php echo esc_attr($gateway_id); ?>" class="lci-payment-method-label">
                                    <span class="lci-payment-method-title"><?php echo esc_html($gateway_data['title']); ?></span>
                                </label>
                            </div>
                            <div id="lci-payment-method-description-<?php echo esc_attr($gateway_id); ?>" class="lci-payment-method-description">
                                <?php echo wp_kses_post($gateway_data['description']); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <input type="hidden" name="amount" value="<?php echo esc_attr($atts['amount']); ?>">
                <input type="hidden" name="description" value="<?php echo esc_attr($atts['description']); ?>">
                <?php if (!empty($atts['product_id'])) : ?>
                    <input type="hidden" name="product_id" value="<?php echo esc_attr($atts['product_id']); ?>">
                    <input type="hidden" name="quantity" value="<?php echo esc_attr($atts['quantity']); ?>">
                <?php endif; ?>

                <div class="lci-payment-submit">
                    <button type="submit" class="lci-payment-button"><?php _e('Proceed to Payment', 'lci-2025-dashboard'); ?></button>
                </div>
            </form>

            <div class="lci-payment-instructions"></div>
            <div class="lci-order-details"></div>
        </div>
        <?php

        // Return the output
        return ob_get_clean();
    }

    /**
     * AJAX handler for processing direct payments
     */
    public static function ajax_process_direct_payment() {
        // Check nonce
        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci_direct_payment_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
            return;
        }

        // Check if payment method is provided
        if (!isset($_POST['payment_method']) || empty($_POST['payment_method'])) {
            wp_send_json_error(['message' => 'Payment method is required.']);
            return;
        }

        $payment_method = sanitize_text_field($_POST['payment_method']);

        // Check if WooCommerce is available
        if (!class_exists('WooCommerce') || empty(WC()->payment_gateways()->payment_gateways())) {
            wp_send_json_error(['message' => 'WooCommerce is not available.']);
            return;
        }

        // Get available payment gateways
        $payment_gateways = WC()->payment_gateways()->payment_gateways();

        // Check if the selected payment method is available
        if (!isset($payment_gateways[$payment_method])) {
            wp_send_json_error(['message' => 'Selected payment method is not available.']);
            return;
        }

        try {
            // Create WooCommerce Order
            $order = wc_create_order();

            // Set customer ID
            $user_id = get_current_user_id();
            $order->set_customer_id($user_id);

            // Check if we're processing a product or a custom fee
            if (isset($_POST['product_id']) && !empty($_POST['product_id'])) {
                // Add product to order
                $product_id = intval($_POST['product_id']);
                $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;

                $product = wc_get_product($product_id);
                if (!$product) {
                    wp_send_json_error(['message' => 'Invalid product.']);
                    return;
                }

                $order->add_product($product, $quantity);
            } else {
                // Add custom fee to order
                $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
                $description = isset($_POST['description']) ? sanitize_text_field($_POST['description']) : 'Custom Payment';

                if ($amount <= 0) {
                    wp_send_json_error(['message' => 'Invalid payment amount.']);
                    return;
                }

                $item = new WC_Order_Item_Fee();
                $item->set_name($description);
                $item->set_amount($amount);
                $item->set_total($amount);
                $item->set_tax_status('none');
                $order->add_item($item);
            }

            // Set billing and shipping information if available
            if (function_exists('lci_get_user_data')) {
                $user_data = lci_get_user_data($user_id);

                if (!empty($user_data['billing'])) {
                    $order->set_billing_first_name($user_data['billing']['first_name']);
                    $order->set_billing_last_name($user_data['billing']['last_name']);
                    $order->set_billing_company($user_data['billing']['company']);
                    $order->set_billing_address_1($user_data['billing']['address_1']);
                    $order->set_billing_address_2($user_data['billing']['address_2']);
                    $order->set_billing_city($user_data['billing']['city']);
                    $order->set_billing_state($user_data['billing']['state']);
                    $order->set_billing_postcode($user_data['billing']['postcode']);
                    $order->set_billing_country($user_data['billing']['country']);
                    $order->set_billing_email($user_data['billing']['email']);
                    $order->set_billing_phone($user_data['billing']['phone']);
                }

                if (!empty($user_data['shipping'])) {
                    $order->set_shipping_first_name($user_data['shipping']['first_name']);
                    $order->set_shipping_last_name($user_data['shipping']['last_name']);
                    $order->set_shipping_company($user_data['shipping']['company']);
                    $order->set_shipping_address_1($user_data['shipping']['address_1']);
                    $order->set_shipping_address_2($user_data['shipping']['address_2']);
                    $order->set_shipping_city($user_data['shipping']['city']);
                    $order->set_shipping_state($user_data['shipping']['state']);
                    $order->set_shipping_postcode($user_data['shipping']['postcode']);
                    $order->set_shipping_country($user_data['shipping']['country']);
                }
            }

            // Set payment method
            $order->set_payment_method($payment_method);

            // Calculate totals
            $order->calculate_totals();

            // Process based on payment method
            if ($payment_method === 'bacs') {
                // Process BACS payment
                $bacs_gateway = $payment_gateways['bacs'];

                // Update order status to on-hold
                $order->update_status('on-hold', __('Awaiting BACS payment.', 'lci-2025-dashboard'));

                // Reduce stock levels
                wc_reduce_stock_levels($order->get_id());

                // Remove cart
                WC()->cart->empty_cart();

                // Get BACS account details
                $bacs_accounts = $bacs_gateway->account_details;

                // Format BACS instructions
                $instructions = '<h3>' . __('Bank Account Details', 'lci-2025-dashboard') . '</h3>';
                $instructions .= '<p>' . $bacs_gateway->get_option('instructions') . '</p>';

                if (!empty($bacs_accounts)) {
                    $instructions .= '<ul class="bacs-accounts">';

                    foreach ($bacs_accounts as $account) {
                        $instructions .= '<li>';

                        if (!empty($account['account_name'])) {
                            $instructions .= '<strong>' . __('Account Name', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['account_name']) . '<br/>';
                        }

                        if (!empty($account['bank_name'])) {
                            $instructions .= '<strong>' . __('Bank', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['bank_name']) . '<br/>';
                        }

                        if (!empty($account['account_number'])) {
                            $instructions .= '<strong>' . __('Account Number', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['account_number']) . '<br/>';
                        }

                        if (!empty($account['sort_code'])) {
                            $instructions .= '<strong>' . __('Sort Code', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['sort_code']) . '<br/>';
                        }

                        if (!empty($account['iban'])) {
                            $instructions .= '<strong>' . __('IBAN', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['iban']) . '<br/>';
                        }

                        if (!empty($account['bic'])) {
                            $instructions .= '<strong>' . __('BIC', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['bic']) . '<br/>';
                        }

                        $instructions .= '</li>';
                    }

                    $instructions .= '</ul>';
                }

                // Add reference number
                $instructions .= '<p><strong>' . __('Reference', 'lci-2025-dashboard') . ':</strong> ' . $order->get_order_number() . '</p>';

                // Return success with instructions
                wp_send_json_success([
                    'payment_type' => 'bacs',
                    'instructions' => $instructions,
                    'order_id' => $order->get_id(),
                ]);
            } else {
                // Process other payment gateways
                $gateway = $payment_gateways[$payment_method];

                // Process the payment
                $result = $gateway->process_payment($order->get_id());

                if (isset($result['result']) && $result['result'] === 'success' && !empty($result['redirect'])) {
                    // Return success with redirect URL
                    wp_send_json_success([
                        'payment_type' => 'redirect',
                        'redirect_url' => esc_url_raw($result['redirect']),
                        'order_id' => $order->get_id(),
                    ]);
                } else {
                    // Payment failed
                    wp_send_json_error([
                        'message' => __('Payment processing failed.', 'lci-2025-dashboard'),
                        'details' => $result,
                    ]);
                }
            }
        } catch (Exception $e) {
            // Handle exceptions
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }
}

// Initialize the handler
LCI_Direct_Payment::init();
