<?php
// Check if user has permission
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.', 'lci-2025-dashboard'));
}

// Get main event table status
$table_status = LCI_Main_Event::check_main_event_table();
?>

<!-- Add Tailwind CSS via CDN -->
<script src="https://cdn.tailwindcss.com"></script>
<script>
  tailwind.config = {
    theme: {
      extend: {
        colors: {
          primary: '#0073aa', // WordPress primary blue
          secondary: '#46b450', // WordPress success green
          danger: '#dc3232', // WordPress error red
          warning: '#ffb900', // WordPress warning yellow
        }
      }
    }
  }
</script>

<style>
    /* Hide elements with x-cloak until Alpine.js is loaded */
    [x-cloak] { display: none !important; }
</style>

<div class="wrap lci-admin-wrap">
    <h1 class="wp-heading-inline"><?php _e('Main Event Participants', 'lci-2025-dashboard'); ?></h1>

    <div class="lci-admin-content" x-data="mainEventManager()" x-init="init()">
        <!-- Sync Tool Section -->
        <div class="bg-white rounded-xl shadow-neumorph p-6 mb-6 relative">
            <div x-show="isSyncing" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
                <div class="spinner"></div>
                <span class="loading-text">Syncing participants...</span>
            </div>

            <h2 class="text-xl font-medium text-gray-800 mb-4">Main Event Participants Sync Tool</h2>

            <p class="text-gray-600 mb-6">
                This tool synchronizes main event participants from WooCommerce orders based on specific product IDs:
                <ul class="list-disc ml-6 mb-4">
                    <li>Product ID 40 (Councilor Package) + Product ID 41 (Main Event) = "Councilor Package + Main Event"</li>
                    <li>Product ID 41 (Main Event) only = "Main Event"</li>
                    <li>Product ID 42 (Partners Package) = "Partners Package"</li>
                </ul>
            </p>

            <div class="mb-6">
                <p class="text-gray-700 mb-2">
                    <strong>Database Status:</strong>
                    <?php if ($table_status['exists']): ?>
                        <span class="text-green-600">Table exists</span>
                        <?php if ($table_status['has_data']): ?>
                            <span class="text-green-600">(<?php echo $table_status['count']; ?> participants)</span>
                        <?php else: ?>
                            <span class="text-yellow-600">(No data)</span>
                        <?php endif; ?>
                    <?php else: ?>
                        <span class="text-red-600">Table does not exist</span>
                    <?php endif; ?>
                </p>
            </div>

            <!-- Sync and Clear Buttons -->
            <div class="flex items-center space-x-4">
                <button
                    @click="syncParticipants"
                    class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-200"
                    :disabled="isSyncing || isClearing"
                >
                    <template x-if="!isSyncing">
                        <span>Sync Main Event Participants</span>
                    </template>
                    <template x-if="isSyncing">
                        <div class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Syncing...</span>
                        </div>
                    </template>
                </button>

                <button
                    @click="clearTable"
                    class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 transition-colors duration-200 mr-2"
                    :disabled="isSyncing || isClearing || isRecreating"
                >
                    <template x-if="!isClearing">
                        <span>Clear Table</span>
                    </template>
                    <template x-if="isClearing">
                        <div class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Clearing...</span>
                        </div>
                    </template>
                </button>

                <button
                    @click="recreateTable"
                    class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50 transition-colors duration-200"
                    :disabled="isSyncing || isClearing || isRecreating"
                >
                    <template x-if="!isRecreating">
                        <span>Recreate Table</span>
                    </template>
                    <template x-if="isRecreating">
                        <div class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Recreating...</span>
                        </div>
                    </template>
                </button>
            </div>

            <!-- Sync Results -->
            <div x-show="syncResult !== null" class="mt-6 p-4 border rounded" :class="{'bg-green-50 border-green-200': !syncError, 'bg-red-50 border-red-200': syncError}">
                <p x-text="syncMessage" :class="{'text-green-700': !syncError, 'text-red-700': syncError}"></p>

                <div x-show="syncResult !== null && !syncError" class="mt-3">
                    <p class="text-gray-700">
                        <strong>Results:</strong>
                    </p>
                    <ul class="list-disc ml-6 mt-2">
                        <li>Total orders processed: <span x-text="syncResult ? syncResult.total_count : 0"></span></li>
                        <li>Successful: <span x-text="syncResult ? syncResult.success_count : 0"></span></li>
                        <li>Errors: <span x-text="syncResult ? syncResult.error_count : 0"></span></li>
                        <li>Councilor + Main Event: <span x-text="syncResult ? syncResult.councilor_main_count : 0"></span></li>
                        <li>Main Event only: <span x-text="syncResult ? syncResult.main_event_count : 0"></span></li>
                        <li>Partners Package: <span x-text="syncResult ? syncResult.partners_count : 0"></span></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Participants Table Section -->
        <div class="bg-white rounded-xl shadow-neumorph p-6 mb-6 relative">
            <div x-show="isLoading" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
                <div class="spinner"></div>
                <span class="loading-text">Loading participants...</span>
            </div>

            <h2 class="text-xl font-medium text-gray-800 mb-4">Main Event Participants</h2>

            <!-- Search and Filter Controls -->
            <div class="flex flex-wrap items-center justify-between mb-4">
                <div class="w-full md:w-auto mb-4 md:mb-0">
                    <div class="flex items-center">
                        <input
                            type="text"
                            x-model="searchQuery"
                            @input="applyFilters()"
                            placeholder="Search participants..."
                            class="border border-gray-300 rounded px-3 py-2 w-full md:w-64 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                </div>

                <div class="w-full md:w-auto">
                    <div class="flex flex-wrap items-center space-x-0 md:space-x-4 space-y-2 md:space-y-0">
                        <select
                            x-model="packageFilter"
                            @change="applyFilters()"
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                        >
                            <option value="">All Packages</option>
                            <option value="Councilor Package + Main Event">Councilor Package + Main Event</option>
                            <option value="Main Event">Main Event</option>
                            <option value="Partners Package">Partners Package</option>
                        </select>

                        <select
                            x-model="statusFilter"
                            @change="applyFilters()"
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                        >
                            <option value="">All Statuses</option>
                            <option value="completed">Completed</option>
                            <option value="processing">Processing</option>
                            <option value="on-hold">On Hold</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="pending">Pending</option>
                        </select>

                        <div class="flex items-center">
                            <select
                                x-model="countryFilter"
                                @change="applyFilters()"
                                class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                            >
                                <option value="">All Countries</option>
                                <template x-if="availableCountries.length === 0">
                                    <option value="" disabled>Loading countries...</option>
                                </template>
                                <template x-for="country in availableCountries" :key="country">
                                    <option :value="country" x-text="country"></option>
                                </template>
                            </select>
                            <button
                                @click="fetchCountries()"
                                class="p-1 text-blue-500 hover:text-blue-700 focus:outline-none"
                                title="Refresh Countries List"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </button>
                        </div>

                        <div class="flex space-x-2">
                            <button
                                @click="resetFilters()"
                                class="mt-2 md:mt-0 px-3 py-2 bg-secondary text-white rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-opacity-50 transition-colors duration-200"
                            >
                                Reset Filters
                            </button>

                            <button
                                @click="fetchParticipants()"
                                class="mt-2 md:mt-0 px-3 py-2 bg-primary text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                                :disabled="isLoading"
                            >
                                <span x-show="!isLoading">Refresh</span>
                                <span x-show="isLoading">Loading...</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Participants Table -->
            <div class="overflow-x-auto" x-show="!isLoading && filteredParticipants.length > 0">
                <table class="min-w-full bg-white border border-gray-200">
                    <thead>
                        <tr class="bg-gray-100">

                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" @click="sortField = 'last_name'; sortAsc = !sortAsc; sortParticipants()">
                                Name
                                <span x-show="sortField === 'last_name'" class="ml-1">
                                    <span x-show="sortAsc">▲</span>
                                    <span x-show="!sortAsc">▼</span>
                                </span>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" @click="sortField = 'email'; sortAsc = !sortAsc; sortParticipants()">
                                Email
                                <span x-show="sortField === 'email'" class="ml-1">
                                    <span x-show="sortAsc">▲</span>
                                    <span x-show="!sortAsc">▼</span>
                                </span>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" @click="sortField = 'country'; sortAsc = !sortAsc; sortParticipants()">
                                Country
                                <span x-show="sortField === 'country'" class="ml-1">
                                    <span x-show="sortAsc">▲</span>
                                    <span x-show="!sortAsc">▼</span>
                                </span>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" @click="sortField = 'order_date'; sortAsc = !sortAsc; sortParticipants()">
                                Order Date
                                <span x-show="sortField === 'order_date'" class="ml-1">
                                    <span x-show="sortAsc">▲</span>
                                    <span x-show="!sortAsc">▼</span>
                                </span>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" @click="sortField = 'payment_status'; sortAsc = !sortAsc; sortParticipants()">
                                Status
                                <span x-show="sortField === 'payment_status'" class="ml-1">
                                    <span x-show="sortAsc">▲</span>
                                    <span x-show="!sortAsc">▼</span>
                                </span>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" @click="sortField = 'event_package'; sortAsc = !sortAsc; sortParticipants()">
                                Package
                                <span x-show="sortField === 'event_package'" class="ml-1">
                                    <span x-show="sortAsc">▲</span>
                                    <span x-show="!sortAsc">▼</span>
                                </span>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="(participant, index) in filteredParticipants" :key="participant.id">
                            <tr :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'">

                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b" x-text="participant.first_name + ' ' + participant.last_name"></td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b" x-text="participant.email"></td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b" x-text="participant.country || 'N/A'"></td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b" x-text="formatDate(participant.order_date)"></td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm border-b">
                                    <span
                                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                        :class="{
                                            'bg-green-100 text-green-800': participant.payment_status === 'completed',
                                            'bg-yellow-100 text-yellow-800': participant.payment_status === 'processing' || participant.payment_status === 'on-hold',
                                            'bg-red-100 text-red-800': participant.payment_status === 'failed' || participant.payment_status === 'cancelled',
                                            'bg-gray-100 text-gray-800': !['completed', 'processing', 'on-hold', 'failed', 'cancelled'].includes(participant.payment_status)
                                        }"
                                        x-text="participant.payment_status"
                                    ></span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b" x-text="participant.event_package"></td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b">
                                    <button
                                        onclick="editParticipant(this.getAttribute('data-participant'))"
                                        :data-participant="JSON.stringify(participant)"
                                        class="px-2 py-1 bg-primary text-white rounded hover:bg-blue-600 mr-2"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        @click="deleteParticipant(participant)"
                                        class="px-2 py-1 bg-danger text-white rounded hover:bg-red-600"
                                    >
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- Results Count -->
            <div class="mt-4 flex items-center justify-between" x-show="!isLoading && filteredParticipants.length > 0">
                <div class="text-sm text-gray-700">
                    Showing <span x-text="filteredParticipants.length"></span> participants
                </div>
            </div>

            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mt-4" x-show="!isLoading && filteredParticipants.length === 0">
                <h3 class="font-bold">No participants found</h3>
                <p class="mt-2">
                    <template x-if="participants.length > 0">
                        <div>
                            <p>There are <span x-text="participants.length"></span> participants in the database, but none match your current filters.</p>
                            <div class="mt-3 flex space-x-2">
                                <button
                                    @click="resetFilters()"
                                    class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition-colors duration-200"
                                >
                                    Reset All Filters
                                </button>
                                <button
                                    @click="showAllParticipants()"
                                    class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-200"
                                >
                                    Show All Participants
                                </button>
                            </div>
                        </div>
                    </template>
                    <template x-if="participants.length === 0">
                        <span>No participants found in the database. Use the sync tool above to import participants.</span>
                    </template>
                </p>
            </div>
        </div>
    </div>

    <!-- Edit Participant Modal -->
    <div id="edit-modal" style="display: none;" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 overflow-hidden">
            <div class="bg-gray-100 px-6 py-4 border-b flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Edit Participant</h3>
                <button onclick="document.getElementById('edit-modal').style.display = 'none';" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="px-6 py-4">
                <form id="edit-form">
                    <input type="hidden" id="edit-id" name="id">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                            <input
                                type="text"
                                id="edit-first-name"
                                name="first_name"
                                class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            >
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                            <input
                                type="text"
                                id="edit-last-name"
                                name="last_name"
                                class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            >
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input
                                type="email"
                                id="edit-email"
                                name="email"
                                class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            >
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                            <input
                                type="text"
                                id="edit-phone"
                                name="phone"
                                class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                            <select
                                id="edit-country"
                                name="country"
                                class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">Select a country</option>
                                <!-- Countries will be populated by JavaScript -->
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                            <select
                                id="edit-payment-status"
                                name="payment_status"
                                class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="completed">Completed</option>
                                <option value="processing">Processing</option>
                                <option value="on-hold">On Hold</option>
                                <option value="failed">Failed</option>
                                <option value="cancelled">Cancelled</option>
                                <option value="pending">Pending</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Event Package</label>
                            <select
                                id="edit-event-package"
                                name="event_package"
                                class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="Councilor Package + Main Event">Councilor Package + Main Event</option>
                                <option value="Main Event">Main Event</option>
                                <option value="Partners Package">Partners Package</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">WordPress User ID</label>
                            <input
                                type="number"
                                id="edit-user-id"
                                name="user_id"
                                class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                                min="0"
                            >
                            <p class="text-xs text-gray-500 mt-1">Enter 0 for no WordPress user</p>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button
                            type="button"
                            onclick="document.getElementById('edit-modal').style.display = 'none';"
                            class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors duration-200"
                        >
                            Cancel
                        </button>

                        <button
                            type="submit"
                            id="save-button"
                            class="px-4 py-2 bg-primary text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                        >
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Define Alpine.js component for Main Event Manager
function mainEventManager() {
    return {
        participants: [],
        filteredParticipants: [],
        isLoading: true,
        isSyncing: false,
        isUpdating: false,
        isClearing: false,
        isRecreating: false,
        searchQuery: '',
        packageFilter: '',
        countryFilter: '',
        statusFilter: '',
        availableCountries: [],
        sortField: 'order_date',
        sortAsc: false,

        syncResult: null,
        syncMessage: '',
        syncError: false,
        showEditModal: false, // Modal is hidden by default
        editingParticipant: {},

        init() {
            // Ensure modal is hidden on initialization
            this.showEditModal = false;

            // Fetch data
            this.fetchParticipants();
            this.fetchCountries();
        },

        fetchParticipants() {
            this.isLoading = true;

            // Add a timestamp to prevent caching
            const timestamp = new Date().getTime();

            // Build URL with filter parameters (no pagination)
            let url = `${lciAdmin.ajaxUrl}?action=lci_get_main_event_participants&nonce=${lciAdmin.nonce}&per_page=-1`;

            // Add filter parameters if they exist
            if (this.searchQuery) {
                url += `&search=${encodeURIComponent(this.searchQuery)}`;
            }

            if (this.packageFilter) {
                url += `&event_package=${encodeURIComponent(this.packageFilter)}`;
            }

            if (this.countryFilter) {
                url += `&country=${encodeURIComponent(this.countryFilter)}`;
            }

            if (this.statusFilter) {
                url += `&payment_status=${encodeURIComponent(this.statusFilter)}`;
            }

            // Add timestamp to prevent caching
            url += `&_=${timestamp}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Check if participants exists in the response
                        if (data.data && Array.isArray(data.data.participants)) {
                            this.participants = data.data.participants;
                        } else {
                            this.participants = [];
                        }

                        // Apply filters after setting participants
                        this.applyFilters();
                    } else {
                        this.showToast(data.data && data.data.message ? data.data.message : 'Error fetching participants', 'error');
                        this.participants = [];
                        this.filteredParticipants = [];
                    }
                })
                .catch(error => {
                    this.showToast('Error fetching participants', 'error');
                    this.participants = [];
                    this.filteredParticipants = [];
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },

        syncParticipants() {
            if (this.isSyncing) return;

            if (!confirm(lciAdmin.strings.confirmSync)) {
                return;
            }

            this.isSyncing = true;
            this.syncResult = null;
            this.syncMessage = '';
            this.syncError = false;

            const formData = new FormData();
            formData.append('action', 'lci_sync_main_event_participants');
            formData.append('nonce', lciAdmin.nonce);

            fetch(lciAdmin.ajaxUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('Sync response:', data);

                if (data.success) {
                    this.syncResult = data.data.result;
                    this.syncMessage = data.data.message;
                    this.syncError = false;
                    this.showToast(data.data.message, 'success');

                    // Refresh participants list
                    setTimeout(() => {
                        console.log('Refreshing participants after sync');
                        this.fetchParticipants();
                    }, 1000); // Add a small delay to ensure the database has been updated
                } else {
                    console.error('Error in sync response:', data);
                    this.syncMessage = data.data.message || 'Error syncing participants';
                    this.syncError = true;
                    this.showToast(this.syncMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Error syncing participants:', error);
                this.syncMessage = 'Error syncing participants';
                this.syncError = true;
                this.showToast(this.syncMessage, 'error');
            })
            .finally(() => {
                this.isSyncing = false;
            });
        },

        applyFilters() {
            // Apply filters to current data without fetching from server
            if (Array.isArray(this.participants)) {
                // Filter the participants based on search query, package filter, and country filter
                this.filteredParticipants = this.participants.filter(p => {
                    // Search query filter
                    const searchMatch = !this.searchQuery ||
                        (p.first_name && p.first_name.toLowerCase().includes(this.searchQuery.toLowerCase())) ||
                        (p.last_name && p.last_name.toLowerCase().includes(this.searchQuery.toLowerCase())) ||
                        (p.email && p.email.toLowerCase().includes(this.searchQuery.toLowerCase())) ||
                        (p.unique_reg_id && p.unique_reg_id.toLowerCase().includes(this.searchQuery.toLowerCase()));

                    // Package filter
                    const packageMatch = !this.packageFilter ||
                        (p.event_package && p.event_package === this.packageFilter);

                    // Country filter
                    const countryMatch = !this.countryFilter ||
                        (p.country && p.country === this.countryFilter);

                    // Status filter
                    const statusMatch = !this.statusFilter ||
                        (p.payment_status && p.payment_status === this.statusFilter);

                    return searchMatch && packageMatch && countryMatch && statusMatch;
                });

                // Sort the filtered participants
                this.sortParticipants();
            }
        },

        sortParticipants() {
            if (!Array.isArray(this.filteredParticipants)) {
                this.filteredParticipants = [];
                return;
            }

            try {
                // Sort filtered participants
                this.filteredParticipants.sort((a, b) => {
                    try {
                        // Check if objects are valid
                        if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {
                            return 0;
                        }

                        let aValue = a[this.sortField];
                        let bValue = b[this.sortField];

                        // Handle date fields
                        if (this.sortField === 'order_date' || this.sortField === 'created_at' || this.sortField === 'updated_at') {
                            aValue = new Date(aValue || '1970-01-01').getTime();
                            bValue = new Date(bValue || '1970-01-01').getTime();
                        }

                        // Handle string fields
                        if (typeof aValue === 'string') {
                            aValue = aValue.toLowerCase();
                        }
                        if (typeof bValue === 'string') {
                            bValue = bValue.toLowerCase();
                        }

                        // Compare values
                        if (aValue < bValue) return this.sortAsc ? -1 : 1;
                        if (aValue > bValue) return this.sortAsc ? 1 : -1;
                        return 0;
                    } catch (error) {
                        return 0;
                    }
                });
            } catch (error) {
                // Silently handle errors
            }
        },

        showToast(message, type = 'info') {
            // Create a toast element
            const toast = document.createElement('div');
            toast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded shadow-lg z-50 transition-opacity duration-500';

            // Set background color based on type
            if (type === 'success') {
                toast.classList.add('bg-green-500', 'text-white');
            } else if (type === 'error') {
                toast.classList.add('bg-red-500', 'text-white');
            } else {
                toast.classList.add('bg-blue-500', 'text-white');
            }

            // Add message
            toast.textContent = message;

            // Add to document
            document.body.appendChild(toast);

            // Remove after 3 seconds
            setTimeout(() => {
                toast.classList.add('opacity-0');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 500);
            }, 3000);
        },

        formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        resetFilters() {
            this.searchQuery = '';
            this.packageFilter = '';
            this.countryFilter = '';
            this.statusFilter = '';
            this.applyFilters();
        },

        showAllParticipants() {
            this.resetFilters();

            // Fetch all participants without filters
            this.fetchParticipants();
        },

        debugFirstParticipant() {
            if (this.participants.length > 0) {
                console.log('First participant in participants array:', this.participants[0]);

                // Check event_package field
                const firstParticipant = this.participants[0];
                console.log('Event package:', firstParticipant.event_package);
                console.log('Event package type:', typeof firstParticipant.event_package);

                // Check if it matches any of our filter options
                const packages = ['Councilor Package', 'Main Event', 'Partners Package'];
                packages.forEach(pkg => {
                    console.log(`Matches "${pkg}"?`, firstParticipant.event_package === pkg);
                    console.log(`Includes "${pkg}"?`, firstParticipant.event_package?.includes(pkg));
                });

                // Show all available fields
                console.log('All fields:', Object.keys(firstParticipant));
            } else {
                console.log('No participants available to debug');
            }
        },



        fetchCountries() {
            // Add a timestamp to prevent caching
            const timestamp = new Date().getTime();
            const url = `${lciAdmin.ajaxUrl}?action=lci_get_main_event_countries&nonce=${lciAdmin.nonce}&_=${timestamp}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && Array.isArray(data.data.countries)) {
                        this.availableCountries = data.data.countries;
                    } else {
                        this.showToast('Error fetching countries', 'error');
                    }
                })
                .catch(error => {
                    this.showToast('Error fetching countries', 'error');
                });
        },

        // This method is no longer used - we're using plain JavaScript now
        editParticipant(participant) {
            // This is kept for compatibility but not used
        },

        updateParticipant() {
            if (this.isUpdating) return;

            this.isUpdating = true;

            const formData = new FormData();
            formData.append('action', 'lci_update_main_event_participant');
            formData.append('nonce', lciAdmin.nonce);
            formData.append('id', this.editingParticipant.id);

            // Add participant fields
            formData.append('first_name', this.editingParticipant.first_name);
            formData.append('last_name', this.editingParticipant.last_name);
            formData.append('email', this.editingParticipant.email);
            formData.append('phone', this.editingParticipant.phone || '');
            formData.append('country', this.editingParticipant.country || '');
            formData.append('payment_status', this.editingParticipant.payment_status);
            formData.append('event_package', this.editingParticipant.event_package);

            fetch(lciAdmin.ajaxUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {

                if (data.success) {
                    this.showToast(data.data.message, 'success');

                    // Update participant in the list
                    const index = this.participants.findIndex(p => p.id === this.editingParticipant.id);
                    if (index !== -1) {
                        this.participants[index] = data.data.participant;
                        this.filteredParticipants = [...this.participants];
                    }

                    // Close modal
                    this.showEditModal = false;
                } else {
                    this.showToast(data.data && data.data.message ? data.data.message : 'Error updating participant', 'error');
                }
            })
            .catch(error => {
                console.error('Error updating participant:', error);
                this.showToast('Error updating participant', 'error');
            })
            .finally(() => {
                this.isUpdating = false;
            });
        },

        deleteParticipant(participant) {
            if (!confirm(lciAdmin.strings.confirmDelete)) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'lci_delete_main_event_participant');
            formData.append('nonce', lciAdmin.nonce);
            formData.append('id', participant.id);

            fetch(lciAdmin.ajaxUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.showToast(data.data.message, 'success');

                    // Remove participant from the list
                    this.participants = this.participants.filter(p => p.id !== participant.id);
                    this.filteredParticipants = [...this.participants];
                } else {
                    this.showToast(data.data && data.data.message ? data.data.message : 'Error deleting participant', 'error');
                }
            })
            .catch(error => {
                this.showToast('Error deleting participant', 'error');
            });
        },

        clearTable() {
            if (!confirm('Are you sure you want to clear the entire main event table? This action cannot be undone.')) {
                return;
            }

            this.isClearing = true;

            const formData = new FormData();
            formData.append('action', 'lci_clear_main_event_table');
            formData.append('nonce', lciAdmin.nonce);

            fetch(lciAdmin.ajaxUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.showToast(data.data.message, 'success');

                    // Clear participants list
                    this.participants = [];
                    this.filteredParticipants = [];
                    this.availableCountries = [];
                } else {
                    this.showToast(data.data.message || 'Error clearing table', 'error');
                }
            })
            .catch(error => {
                console.error('Error clearing table:', error);
                this.showToast('Error clearing table', 'error');
            })
            .finally(() => {
                this.isClearing = false;
            });
        },

        recreateTable() {
            if (!confirm('Are you sure you want to recreate the main event table? This will drop and recreate the table structure. All data will be lost.')) {
                return;
            }

            this.isRecreating = true;

            const formData = new FormData();
            formData.append('action', 'lci_recreate_main_event_table');
            formData.append('nonce', lciAdmin.nonce);

            fetch(lciAdmin.ajaxUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.showToast(data.data.message, 'success');

                    // Clear participants list
                    this.participants = [];
                    this.filteredParticipants = [];
                    this.availableCountries = [];
                } else {
                    this.showToast(data.data.message || 'Error recreating table', 'error');
                }
            })
            .catch(error => {
                console.error('Error recreating table:', error);
                this.showToast('Error recreating table', 'error');
            })
            .finally(() => {
                this.isRecreating = false;
            });
        }
    };
}
</script>

<script>
// Plain JavaScript for edit functionality
function editParticipant(participantJson) {
    try {
        // Parse the participant JSON
        const participant = JSON.parse(participantJson);

        // Populate the form fields
        document.getElementById('edit-id').value = participant.id;
        document.getElementById('edit-first-name').value = participant.first_name || '';
        document.getElementById('edit-last-name').value = participant.last_name || '';
        document.getElementById('edit-email').value = participant.email || '';
        document.getElementById('edit-phone').value = participant.phone || '';
        document.getElementById('edit-country').value = participant.country || '';
        document.getElementById('edit-payment-status').value = participant.payment_status || 'completed';
        document.getElementById('edit-event-package').value = participant.event_package || 'Main Event';
        document.getElementById('edit-user-id').value = participant.user_id || '0';

        // Show the modal
        document.getElementById('edit-modal').style.display = 'flex';
    } catch (error) {
        alert('Error opening edit modal: ' + error.message);
    }
}

// Handle form submission
document.addEventListener('DOMContentLoaded', function() {
    const editForm = document.getElementById('edit-form');

    editForm.addEventListener('submit', function(event) {
        event.preventDefault();

        // Get form data
        const formData = new FormData(editForm);
        formData.append('action', 'lci_update_main_event_participant');
        formData.append('nonce', lciAdmin.nonce);

        // Disable the save button
        const saveButton = document.getElementById('save-button');
        saveButton.disabled = true;
        saveButton.innerHTML = 'Saving...';

        // Send the request
        fetch(lciAdmin.ajaxUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                alert(data.data.message);

                // Close the modal
                document.getElementById('edit-modal').style.display = 'none';

                // Refresh the page to show updated data
                window.location.reload();
            } else {
                // Show error message
                alert(data.data && data.data.message ? data.data.message : 'Error updating participant');
            }
        })
        .catch(error => {
            // Show error message
            alert('Error updating participant: ' + error.message);
        })
        .finally(() => {
            // Re-enable the save button
            saveButton.disabled = false;
            saveButton.innerHTML = 'Save Changes';
        });
    });
});
</script>
