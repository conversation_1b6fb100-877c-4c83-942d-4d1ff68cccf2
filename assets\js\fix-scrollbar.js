/**
 * Fix scrollbar issues with BeTheme
 */
(function() {
    // Function to ensure scrollbar is visible
    function fixScrollbar() {
        // Remove classes from HTML element
        const htmlElement = document.documentElement;
        htmlElement.classList.remove(
            'mfn-hidden-scrollbar',
            'mfn-cart-opened',
            'mfn-cart-showing',
            'mfn-active-cart',
            'mfn-hidden-overlay'
        );

        // Remove classes from BODY element
        const bodyElement = document.body;
        bodyElement.classList.remove(
            'mfn-hidden-overlay',
            'mfn-cart-opened',
            'mfn-cart-showing',
            'mfn-active-cart'
        );

        // Remove any inline styles that might affect scrolling on HTML
        if (htmlElement.style.overflow === 'hidden') {
            htmlElement.style.overflow = 'auto';
        }

        if (htmlElement.style.paddingRight) {
            htmlElement.style.paddingRight = '0';
        }

        if (htmlElement.style.marginRight) {
            htmlElement.style.marginRight = '0';
        }

        // Remove any inline styles that might affect scrolling on BODY
        if (bodyElement.style.overflow === 'hidden') {
            bodyElement.style.overflow = 'auto';
        }

        if (bodyElement.style.paddingRight) {
            bodyElement.style.paddingRight = '0';
        }

        if (bodyElement.style.marginRight) {
            bodyElement.style.marginRight = '0';
        }

        // Remove any BeTheme overlay elements
        const overlays = document.querySelectorAll('.mfn-cart-overlay, #mfn-cart-overlay, [class*="mfn-cart-overlay"]');
        overlays.forEach(overlay => {
            if (overlay) {
                overlay.style.display = 'none';
                overlay.style.opacity = '0';
                overlay.style.visibility = 'hidden';
                overlay.style.pointerEvents = 'none';
            }
        });
    }

    // Run on page load
    document.addEventListener('DOMContentLoaded', fixScrollbar);

    // Run when the window is fully loaded
    window.addEventListener('load', fixScrollbar);

    // Run periodically to ensure scrollbar remains visible
    setInterval(fixScrollbar, 500);

    // Run when BeTheme might be adding items to cart
    document.addEventListener('added_to_cart', fixScrollbar);
    document.addEventListener('wc_fragments_refreshed', fixScrollbar);

    // Run when our cart is opened or closed
    window.addEventListener('lci:cartUpdated', fixScrollbar);
    window.addEventListener('open-mini-cart', fixScrollbar);

    // Set up a MutationObserver to watch for class changes on HTML and BODY
    const observeClassChanges = (element) => {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.attributeName === 'class') {
                    const currentClasses = mutation.target.className;
                    if (currentClasses.includes('mfn-cart-opened') ||
                        currentClasses.includes('mfn-hidden-overlay') ||
                        currentClasses.includes('mfn-cart-showing') ||
                        currentClasses.includes('mfn-active-cart')) {
                        // BeTheme cart class detected, remove it
                        fixScrollbar();
                    }
                }
            });
        });

        observer.observe(element, { attributes: true });
        return observer;
    };

    // Observe both HTML and BODY elements
    const htmlObserver = observeClassChanges(document.documentElement);
    const bodyObserver = observeClassChanges(document.body);
})();
