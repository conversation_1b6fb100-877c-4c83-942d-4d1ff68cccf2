<?php
/**
 * Main Event Accommodation View
 *
 * Displays Main Event accommodation options in Brasov
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Enqueue the accommodation CSS
wp_enqueue_style('accommodation-css', LCI2025_URL . 'assets/css/accommodation.css', array(), LCI2025_VERSION);

// Add inline CSS for the accommodation products
$accommodation_products_css = "
.accommodation-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.accommodation-product-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.accommodation-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accommodation-product-image {
    height: 350px;
    overflow: hidden;
    position: relative;
}

.accommodation-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.accommodation-product-card:hover .accommodation-product-image img {
    transform: scale(1.05);
}

.accommodation-product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.accommodation-product-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.accommodation-product-title {
    color: #00b2e3;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.accommodation-product-description {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
    flex-grow: 1;
}

.accommodation-product-price {
    font-weight: bold;
    font-size: 18px;
    color: #343a40;
    margin-bottom: 15px;
}

.accommodation-product-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #36b1dc;
    color: white !important;
    border: none;
    width: 100%;
    box-shadow: 0 4px 6px rgba(54, 177, 220, 0.2);
}

.accommodation-product-button i {
    color: white;
}

.accommodation-product-button:hover {
    background-color: #2d9cc3;
    color: white !important;
    box-shadow: 0 6px 12px rgba(54, 177, 220, 0.3);
    transform: translateY(-2px);
}

.accommodation-product-button:hover i {
    color: white;
}

/* Variation radio buttons */
.accommodation-variations-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.accommodation-product-variations {
    margin-bottom: 15px;
}

.accommodation-variation-radio-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
    width: 100%;
}

.accommodation-variation-radio-label:hover {
    background-color: #f8f9fa;
}

.accommodation-variation-radio-label input[type=radio] {
    margin-right: 10px;
}

.accommodation-product-variation-name {
    font-weight: 500;
    color: #495057;
    flex-grow: 1;
    margin-right: 10px;
}

.accommodation-product-variation-price {
    color: #36b1dc;
    font-weight: 600;
}

/* Disabled variation styles */
.variation-disabled {
    opacity: 0.5;
    pointer-events: none;
}

.variation-disabled input[type=radio] {
    opacity: 0.5;
}

/* Settings button removed */

.accommodation-product-variations {
    margin-top: 15px;
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.accommodation-product-variation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.accommodation-product-variation-name {
    font-size: 14px;
    color: #495057;
}

.accommodation-product-variation-price {
    font-weight: bold;
    color: #00b2e3;
}

.accommodation-product-stars {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: #ffc107;
}

.accommodation-product-stars i {
    margin-right: 3px;
}

.accommodation-product-star-description {
    font-size: 14px;
    color: #6c757d;
    margin-left: 8px;
}

/* Features List with Checkmarks */
.accommodation-product-features {
    margin-bottom: 15px;
}

.accommodation-features-list {
    list-style: none;
    padding: 0;
    margin: 2px !important;
}

.accommodation-features-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    font-size: 14px;
    color: #495057;
    line-height: 1.4;
}

.accommodation-features-list li i {
    color: #36b1dc;
    margin-right: 8px;
    font-size: 16px;
    flex-shrink: 0;
    margin-top: 2px;
}

.accommodation-product-soldout {
    opacity: 0.7;
    position: relative;
}

.accommodation-product-soldout::after {
    content: 'SOLD OUT';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-15deg);
    background-color: rgba(220, 53, 69, 0.8);
    color: white;
    padding: 10px 20px;
    font-weight: bold;
    font-size: 24px;
    border-radius: 5px;
    z-index: 10;
}

.accommodation-product-variation.soldout {
    opacity: 0.5;
    text-decoration: line-through;
}

@media (max-width: 768px) {
    .accommodation-products-grid {
        grid-template-columns: 1fr;
    }

    .accommodation-header-title {
        font-size: 18px !important;
    }
}
";

wp_add_inline_style('accommodation-css', $accommodation_products_css);

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Check if user has product ID 40 (Councilor Package)
$has_councilor_package = false;
$customer_orders = wc_get_orders([
    'customer_id' => $user_id,
    'status' => ['processing', 'completed', 'on-hold'],
    'limit' => -1,
]);

foreach ($customer_orders as $order) {
    foreach ($order->get_items() as $item) {
        $product_id = $item->get_product_id();
        if ($product_id == 40) {
            $has_councilor_package = true;
            break 2;
        }
    }
}

// Get Main Event accommodation products based on whether user has Councilor Package
$main_event_products = [];
$category_id = $has_councilor_package ? 20 : 18; // Show category 20 for councilors, 18 for others

$args = [
    'post_type' => 'product',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'post__not_in' => [137], // Exclude product ID 137
    'tax_query' => array(
        array(
            'taxonomy' => 'product_cat',
            'field' => 'id',
            'terms' => $category_id,
        ),
    ),
];

$posts = get_posts($args);
foreach ($posts as $post) {
    $product = wc_get_product($post->ID);
    if ($product) {
        $main_event_products[] = $product;
    }
}

// Check if any products are in the cart
$products_in_cart = [];
if (WC()->cart) {
    foreach (WC()->cart->get_cart() as $cart_item) {
        $products_in_cart[] = $cart_item['product_id'];
    }
}
?>

<div class="accommodation-container">
    <!-- Header with title and mini cart -->
    <div class="dashboard-header mb-4">

        <div class="dashboard-mini-cart-container d-flex justify-content-between align-items-center">
         <!-- Back to Accommodation Button -->


            <!-- Mini Cart Button -->
            <?php echo lci_get_mini_cart_html(); ?>
        </div>
    </div>
    <div class="mb-3">
        <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation', remove_query_arg('category_id'))); ?>" class="btn btn-sm btn-outline-secondary d-inline-flex align-items-center">
            <i class="fas fa-arrow-left me-2"></i> Back
        </a>
    </div>


    <!-- Introduction Section -->
    <div class="mb-5">
        <div class="mb-4" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); overflow: hidden;">
            <div style="background-color: #36b1dc; padding: 15px 20px; color: white;">
                <h4 class="accommodation-header-title" style="margin: 0; font-weight: 600; font-size: 20px; display: flex; align-items: center; color: white;">
                    <i class="fas fa-hotel me-2" style="color: white;"></i> Accommodation in Brasov
                </h4>
            </div>
            <div style="padding: 20px;">
                <p style="margin-bottom: 20px; color: #37474f; font-size: 15px; line-height: 1.5;">
                    Book your accommodation in Brasov for the main event period (August 21-24, 2025).
                    Select from our recommended hotels in Brasov.
                </p>

                <div id="current-settings-display" style="background-color: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); margin-top: 10px;">
                    <div style="margin-bottom: 12px;">
                        <div style="font-weight: 600; color: #36b1dc; font-size: 16px;">YOUR STAY PREFERENCES</div>
                    </div>

                    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px;">
                        <div style="flex: 1; min-width: 140px;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">DURATION</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-moon me-1" style="color: #36b1dc;"></i>
                                3 nights
                            </div>
                        </div>
                    </div>

                    <div style="height: 1px; background-color: #e0e0e0; margin: 15px 0;"></div>

                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <div style="flex: 1;">
                            <div style="color: #78909c; font-size: 12px; margin-bottom: 4px;">STAY DATES</div>
                            <div style="font-weight: 600; color: #37474f; font-size: 16px;">
                                <i class="fas fa-calendar-alt me-1" style="color: #36b1dc;"></i>
                                21/08/2025 - 24/08/2025
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (empty($main_event_products)): ?>
        <!-- No products available message -->
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span>No accommodation options are currently available for the main event. Please check back later.</span>
        </div>
        <?php else: ?>
        <!-- Products Grid -->
        <div class="accommodation-products-grid">
            <?php foreach ($main_event_products as $product):
                $product_id = $product->get_id();
                $in_cart = in_array($product_id, $products_in_cart);
                $image_url = wp_get_attachment_url($product->get_image_id());
                $price_html = $product->get_price_html();
                $description = $product->get_short_description();
                $is_in_stock = $product->is_in_stock();

                // Get hotel stars from product meta
                $hotel_stars = get_post_meta($product_id, '_number_of_stars', true);
                if (!$hotel_stars) $hotel_stars = 3; // Default to 3 stars

                // Get hotel features from product meta
                $hotel_features = get_post_meta($product_id, '_hotel_features', true);
                if ($hotel_features) {
                    $features = explode("\n", $hotel_features);
                } else {
                    $features = [];
                }

                // Get hotel website from product meta
                $hotel_website = get_post_meta($product_id, '_hotel_website', true);

                // Add soldout class if product is out of stock
                $soldout_class = !$is_in_stock ? 'accommodation-product-soldout' : '';
            ?>
            <div class="accommodation-product-card <?php echo $soldout_class; ?>">
                <div class="accommodation-product-image">
                    <?php if ($in_cart): ?>
                    <div class="accommodation-product-badge">In Cart</div>
                    <?php endif; ?>
                    <img src="<?php echo esc_url($image_url ? $image_url : 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121213/brasov-1.jpg'); ?>" alt="<?php echo esc_attr($product->get_name()); ?>">
                </div>
                <div class="accommodation-product-content">
                    <h4 class="accommodation-product-title"><?php echo esc_html($product->get_name()); ?></h4>

                    <!-- Hotel Stars -->
                    <div class="accommodation-product-stars">
                        <?php for ($i = 0; $i < $hotel_stars; $i++): ?>
                            <i class="fas fa-star"></i>
                        <?php endfor; ?>
                        <span class="accommodation-product-star-description">
                            <?php echo $hotel_stars; ?>-star hotel
                        </span>
                    </div>

                    <!-- Description List with Checkmarks -->
                    <div class="accommodation-product-features">
                        <ul class="accommodation-features-list">
                            <?php
                            // Get description fields from product meta
                            $descriere = get_post_meta($product_id, '_descriere', true);
                            $descriere1 = get_post_meta($product_id, '_descriere1', true);
                            $descriere2 = get_post_meta($product_id, '_descriere2', true);
                            $descriere3 = get_post_meta($product_id, '_descriere3', true);

                            // Display description fields if they exist
                            if ($descriere) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere) . '</li>';
                            if ($descriere1) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere1) . '</li>';
                            if ($descriere2) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere2) . '</li>';
                            if ($descriere3) echo '<li><i class="fas fa-check-circle"></i> ' . esc_html($descriere3) . '</li>';
                            ?>
                        </ul>
                    </div>

                    <div class="accommodation-product-description">
                        <?php if (!empty($features)): ?>
                        <ul class="mt-2 mb-0" style="padding-left: 20px;">
                            <?php foreach ($features as $feature): ?>
                            <li><?php echo esc_html(trim($feature)); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>

                        <?php if ($hotel_website): ?>
                        <div class="mt-2">
                            <a href="<?php echo esc_url($hotel_website); ?>" target="_blank" style="color: #00b2e3; text-decoration: none; display: inline-flex; align-items: center;">
                                <i class="fas fa-globe me-1"></i> Visit hotel website
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Price removed -->

                    <?php if ($product->is_type('variable')):
                        $variations = $product->get_available_variations();
                        $first_variation = true;
                    ?>
                    <div class="accommodation-product-variations">
                        <?php foreach ($variations as $variation):
                            $variation_obj = wc_get_product($variation['variation_id']);
                            if (!$variation_obj) continue;

                            $variation_id = $variation['variation_id'];
                            $variation_in_stock = $variation_obj->is_in_stock();
                            $variation_soldout_class = !$variation_in_stock ? 'soldout' : '';

                            // Get attribute names
                            $attribute_names = [];
                            foreach ($variation['attributes'] as $key => $value) {
                                if ($value) {
                                    // For product attributes
                                    $taxonomy = str_replace('attribute_', '', $key);
                                    $term = get_term_by('slug', $value, $taxonomy);
                                    if ($term) {
                                        $attribute_names[] = $term->name;
                                    } else {
                                        // For custom product attributes
                                        $attribute_names[] = $value;
                                    }
                                }
                            }

                            $variation_name = implode(' - ', $attribute_names);
                            $variation_price_html = $variation_obj->get_price_html();

                            // Check if this is a double room variation
                            $is_double_room = stripos($variation_name, 'double') !== false;

                            // Get variation price
                            $variation_price = $variation_obj->get_price();
                        ?>
                        <div class="accommodation-product-variation <?php echo $variation_soldout_class; ?>">
                            <label class="accommodation-variation-radio-label">
                                <input type="radio" name="variation_<?php echo esc_attr($product_id); ?>"
                                       value="<?php echo esc_attr($variation_id); ?>"
                                       data-variation-name="<?php echo esc_attr($variation_name); ?>"
                                       data-is-double="<?php echo $is_double_room ? 'true' : 'false'; ?>"
                                       data-price="<?php echo esc_attr($variation_price); ?>"
                                       <?php
                                       // Add data attributes for each attribute in the variation
                                       foreach ($variation['attributes'] as $attribute_name => $attribute_value) {
                                           $attribute_name = sanitize_title($attribute_name);
                                           echo 'data-' . esc_attr($attribute_name) . '="' . esc_attr($attribute_value) . '" ';
                                       }
                                       ?>
                                       <?php echo $first_variation ? 'checked' : ''; ?>>
                                <div class="accommodation-product-variation-name"><?php echo esc_html($variation_name); ?></div>
                                <div class="accommodation-product-variation-price">
                                    <?php if (!$variation_in_stock): ?>
                                    <span style="color: #dc3545; font-size: 12px;">SOLD OUT</span>
                                    <?php else: ?>
                                    <?php echo $variation_price_html; ?>
                                    <?php endif; ?>
                                </div>
                            </label>
                        </div>
                        <?php
                        $first_variation = false;
                        endforeach;
                        ?>
                    </div>
                    <?php else: ?>
                    <div class="accommodation-product-single-price">
                        <?php echo $price_html; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ($in_cart): ?>
                    <button class="accommodation-product-button" disabled style="background: #28a745;">
                        <i class="fas fa-check me-2"></i> BOOKED
                    </button>
                    <?php elseif (!$is_in_stock): ?>
                    <button class="accommodation-product-button" disabled style="background: #6c757d;">
                        <i class="fas fa-times me-2"></i> SOLD OUT
                    </button>
                    <?php else: ?>
                    <button class="accommodation-product-button add-to-cart-btn"
                            data-product-id="<?php echo esc_attr($product_id); ?>"
                            data-product-name="<?php echo esc_attr($product->get_name()); ?>"
                            data-product-type="<?php echo esc_attr($product->get_type()); ?>"
                            data-price="<?php echo esc_attr($product->get_price()); ?>"
                            onclick="showMainEventConfirmModal(this)">
                        <i class="fas fa-bed me-2"></i> BOOK
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add to Cart Confirmation Modal -->
<div class="accommodation-cart-confirm-modal" id="mainEventCartConfirmModal">
    <div class="accommodation-cart-confirm-overlay"></div>
    <div class="accommodation-cart-confirm-content">
        <div class="accommodation-cart-confirm-header">
            <h3><i class="fas fa-check-circle me-2"></i> Confirm Your Selection</h3>
            <button type="button" class="accommodation-cart-confirm-close" onclick="closeMainEventConfirmModal()">&times;</button>
        </div>
        <div class="accommodation-cart-confirm-body">
            <p>Please confirm your accommodation selection:</p>

            <div class="accommodation-cart-confirm-details">
                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Hotel:</span>
                    <span class="accommodation-cart-confirm-value" id="mainEventConfirmProductName"></span>
                </div>

                <div class="accommodation-cart-confirm-row" id="confirmRoomTypeRow" style="display: none;">
                    <span class="accommodation-cart-confirm-label">Room Type:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmRoomType"></span>
                </div>

                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Number of Nights:</span>
                    <span class="accommodation-cart-confirm-value">3 nights</span>
                </div>

                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Stay Dates:</span>
                    <span class="accommodation-cart-confirm-value">21/08/2025 - 24/08/2025</span>
                </div>

                <div class="accommodation-cart-confirm-row" id="confirmDoubleRoomRow" style="display: none;">
                    <span class="accommodation-cart-confirm-label">Double Room:</span>
                    <span class="accommodation-cart-confirm-value">Yes (quantity will be doubled)</span>
                </div>

                <div class="accommodation-cart-confirm-row accommodation-cart-confirm-total">
                    <span class="accommodation-cart-confirm-label">Total Price:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmTotalPrice">Calculating...</span>
                </div>
            </div>

            <div class="accommodation-cart-confirm-notice" id="confirmDoubleRoomNotice" style="display: none;">
                <i class="fas fa-info-circle"></i>
                <div>
                    <strong>Important:</strong> When booking a Double Room, the price is calculated per person.
                    <p class="mt-2 mb-0">Since this room is for 2 people, the system will automatically charge for both guests (<span id="confirmDoubleRoomCalc"></span> total).</p>
                    <p class="mt-2 mb-0">If you are booking alone, please make sure you will be sharing the room with someone, or choose a Single Room instead.</p>
                </div>
            </div>
        </div>
        <div class="accommodation-cart-confirm-footer">
            <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-secondary" onclick="closeMainEventConfirmModal()">
                CANCEL
            </button>
            <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-primary" onclick="addToCartMainEvent()">
                <i class="fas fa-bed me-2"></i> BOOK
            </button>
        </div>
    </div>
</div>

<style>
/* Accommodation Cart Confirm Modal Styles */
.accommodation-cart-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99999;
    display: none;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.accommodation-cart-confirm-modal.active {
    display: block;
    visibility: visible;
    opacity: 1;
}

.accommodation-cart-confirm-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

.accommodation-cart-confirm-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 95%;
    max-width: 700px;
    overflow: hidden;
}

.accommodation-cart-confirm-header {
    background-color: #36b1dc;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.accommodation-cart-confirm-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.accommodation-cart-confirm-header h3 i {
    color: white;
    margin-right: 8px;
}

.accommodation-cart-confirm-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.accommodation-cart-confirm-body {
    padding: 20px;
}

@media (min-width: 768px) {
    .accommodation-cart-confirm-header {
        padding: 20px 25px;
    }

    .accommodation-cart-confirm-header h3 {
        font-size: 22px;
    }

    .accommodation-cart-confirm-body {
        padding: 25px 30px;
    }

    .accommodation-cart-confirm-details {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .accommodation-cart-confirm-row {
        flex-direction: column;
        background-color: #f8f9fa;
        padding: 12px 15px;
        border-radius: 6px;
        border-bottom: none;
        margin-bottom: 0;
    }

    .accommodation-cart-confirm-label {
        margin-bottom: 5px;
        color: #6c757d;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .accommodation-cart-confirm-value {
        font-size: 16px;
        font-weight: 500;
    }

    .accommodation-cart-confirm-total {
        grid-column: 1 / -1;
        margin-top: 15px;
        background-color: #e3f2fd;
        border-top: none;
        padding-top: 0;
    }
}

.accommodation-cart-confirm-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.accommodation-cart-confirm-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.accommodation-cart-confirm-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.accommodation-cart-confirm-total {
    margin-top: 15px;
    border-top: 2px solid #e9ecef;
    padding-top: 15px;
    font-weight: bold;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-label {
    font-size: 16px;
    color: #343a40;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-value {
    font-size: 16px;
    color: #36b1dc;
}

.accommodation-cart-confirm-label {
    font-weight: 600;
    color: #495057;
}

.accommodation-cart-confirm-value {
    color: #343a40;
}

.accommodation-cart-confirm-notice {
    background-color: #e8f4fd;
    color: #0c5460;
    padding: 16px;
    border-radius: 8px;
    margin-top: 15px;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid #36b1dc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.accommodation-cart-confirm-notice i {
    margin-right: 12px;
    font-size: 18px;
    margin-top: 2px;
    color: #36b1dc;
}

.accommodation-cart-confirm-notice p {
    line-height: 1.5;
}

.accommodation-cart-confirm-notice strong {
    color: #0c5460;
    font-weight: 600;
}

.accommodation-cart-confirm-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
}

@media (min-width: 768px) {
    .accommodation-cart-confirm-footer {
        padding: 20px 25px;
    }

    .accommodation-cart-confirm-btn {
        min-width: 150px;
        padding: 12px 20px;
    }
}

.accommodation-cart-confirm-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    width: 48%;
    color: white;
}

.accommodation-cart-confirm-btn i {
    color: white;
    margin-right: 8px;
}

.accommodation-cart-confirm-btn-primary {
    background-color: #36b1dc;
    color: white;
}

.accommodation-cart-confirm-btn-primary:hover {
    background-color: #2d9cc3;
    color: white;
}

.accommodation-cart-confirm-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.accommodation-cart-confirm-btn-secondary:hover {
    background-color: #5a6268;
    color: white;
}

.accommodation-cart-confirm-dates-note {
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
}

.accommodation-cart-confirm-dates-note i {
    color: #36b1dc;
    margin-right: 6px;
    font-size: 14px;
}
</style>

<script>
// Variables to store product information for the confirmation modal
let mainEventProductId = 0;
let mainEventVariationId = 0;
let mainEventIsDoubleRoom = false;

// Function to show the confirmation modal - defined in global scope
window.showMainEventConfirmModal = function(button) {
    const productId = button.getAttribute('data-product-id');
    const productName = button.getAttribute('data-product-name');
    const productType = button.getAttribute('data-product-type');
    const productPrice = parseFloat(button.getAttribute('data-price') || 0);

    // Set hotel name in confirmation modal
    document.getElementById('mainEventConfirmProductName').textContent = productName;

    // Handle variation selection for variable products
    let variationId = null;
    let variationName = null;
    let isDoubleRoom = false;
    let variationAttributes = {};

    if (productType === 'variable') {
        // Get the selected variation
        const selectedVariation = document.querySelector(`input[name="variation_${productId}"]:checked`);

        if (selectedVariation) {
            variationId = selectedVariation.value;
            variationName = selectedVariation.getAttribute('data-variation-name');
            isDoubleRoom = selectedVariation.getAttribute('data-is-double') === 'true';

            // Collect all data attributes that start with "data-attribute_"
            Array.from(selectedVariation.attributes)
                .filter(attr => attr.name.startsWith('data-attribute_'))
                .forEach(attr => {
                    const attributeName = attr.name.replace('data-', '');
                    variationAttributes[attributeName] = attr.value;
                });

            // Show room type in confirmation modal
            document.getElementById('confirmRoomTypeRow').style.display = 'flex';
            document.getElementById('confirmRoomType').textContent = variationName;
        } else {
            // Hide room type if no variation selected
            document.getElementById('confirmRoomTypeRow').style.display = 'none';
        }
    } else {
        // Hide room type for simple products
        document.getElementById('confirmRoomTypeRow').style.display = 'none';
    }

    // Hide double room notice (no longer needed)
    document.getElementById('confirmDoubleRoomRow').style.display = 'none';
    document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

    // Calculate total price based on 3 nights
    const totalPrice = (productType === 'variable' && variationId) ?
        parseFloat(document.querySelector(`input[value="${variationId}"]`).getAttribute('data-price') || 0) * 3 :
        productPrice * 3;

    // Format the price with currency symbol
    const formattedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(totalPrice);

    // Update the price display
    document.getElementById('confirmTotalPrice').textContent = formattedPrice;

    // Store data for add to cart action
    mainEventProductId = productId;
    mainEventVariationId = variationId;
    mainEventIsDoubleRoom = isDoubleRoom;

    // Show confirmation modal
    const confirmModal = document.getElementById('mainEventCartConfirmModal');
    confirmModal.style.display = 'block';
    // Use setTimeout to ensure the display property is applied before adding the active class
    setTimeout(() => {
        confirmModal.classList.add('active');
    }, 10);

    // Prevent the default button action
    event.preventDefault();
    return false;
};

// Function to close the confirmation modal - defined in global scope
window.closeMainEventConfirmModal = function() {
    const confirmModal = document.getElementById('mainEventCartConfirmModal');
    confirmModal.classList.remove('active');
    setTimeout(() => {
        confirmModal.style.display = 'none';
    }, 300);
};

// Function to add to cart - defined in global scope
window.addToCartMainEvent = function() {
    // Check if we have a product ID
    if (!mainEventProductId) {
        console.error('No product ID found');
        return;
    }

    // Create a form to submit the add to cart request
    const form = document.createElement('form');
    form.method = 'post';
    form.action = window.location.href;

    // Add product ID
    const productIdInput = document.createElement('input');
    productIdInput.type = 'hidden';
    productIdInput.name = 'add-to-cart';
    productIdInput.value = mainEventProductId;
    form.appendChild(productIdInput);

    // Add quantity (3 nights)
    const quantityInput = document.createElement('input');
    quantityInput.type = 'hidden';
    quantityInput.name = 'quantity';
    quantityInput.value = '3';
    form.appendChild(quantityInput);

    // Add variation ID if applicable
    if (mainEventVariationId) {
        const variationIdInput = document.createElement('input');
        variationIdInput.type = 'hidden';
        variationIdInput.name = 'variation_id';
        variationIdInput.value = mainEventVariationId;
        form.appendChild(variationIdInput);

        // Get all attributes for this variation
        const selectedVariation = document.querySelector(`input[value="${mainEventVariationId}"]`);
        if (selectedVariation) {
            // Get all data attributes that start with "data-attribute_"
            Array.from(selectedVariation.attributes)
                .filter(attr => attr.name.startsWith('data-attribute_'))
                .forEach(attr => {
                    const attributeName = attr.name.replace('data-', '');
                    const attributeValue = attr.value;

                    const attributeInput = document.createElement('input');
                    attributeInput.type = 'hidden';
                    attributeInput.name = attributeName;
                    attributeInput.value = attributeValue;
                    form.appendChild(attributeInput);

                    console.log(`Adding attribute: ${attributeName} = ${attributeValue}`);
                });

            // If no attributes were found, try to determine from variation name
            const hasAttributes = Array.from(selectedVariation.attributes).some(attr => attr.name.startsWith('data-attribute_'));
            if (!hasAttributes) {
                const variationName = selectedVariation.getAttribute('data-variation-name');
                if (variationName && variationName.toLowerCase().includes('single')) {
                    const attributeInput = document.createElement('input');
                    attributeInput.type = 'hidden';
                    attributeInput.name = 'attribute_room-type';
                    attributeInput.value = 'single';
                    form.appendChild(attributeInput);
                    console.log('Adding attribute: attribute_room-type = single');
                } else if (variationName && variationName.toLowerCase().includes('double')) {
                    const attributeInput = document.createElement('input');
                    attributeInput.type = 'hidden';
                    attributeInput.name = 'attribute_room-type';
                    attributeInput.value = 'double';
                    form.appendChild(attributeInput);
                    console.log('Adding attribute: attribute_room-type = double');
                }
            }
        }
    }

    // Append form to body and submit
    document.body.appendChild(form);
    form.submit();
};

document.addEventListener('DOMContentLoaded', function() {
    // Add to Cart Confirmation Modal Functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    const confirmModal = document.getElementById('mainEventCartConfirmModal');

    // Add click event to all Add to Cart buttons
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');
            const productType = this.getAttribute('data-product-type');

            // Set hotel name in confirmation modal
            document.getElementById('mainEventConfirmProductName').textContent = productName;

            // Handle variation selection for variable products
            let variationId = null;
            let variationName = null;
            let isDoubleRoom = false;

            if (productType === 'variable') {
                // Get the selected variation
                const selectedVariation = document.querySelector(`input[name="variation_${productId}"]:checked`);

                if (selectedVariation) {
                    variationId = selectedVariation.value;
                    variationName = selectedVariation.getAttribute('data-variation-name');
                    isDoubleRoom = selectedVariation.getAttribute('data-is-double') === 'true';

                    // Show room type in confirmation modal
                    document.getElementById('confirmRoomTypeRow').style.display = 'flex';
                    document.getElementById('confirmRoomType').textContent = variationName;
                } else {
                    // Hide room type if no variation selected
                    document.getElementById('confirmRoomTypeRow').style.display = 'none';
                }
            } else {
                // Hide room type for simple products
                document.getElementById('confirmRoomTypeRow').style.display = 'none';
            }

            // Hide double room notice (no longer needed)
            document.getElementById('confirmDoubleRoomRow').style.display = 'none';
            document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

            // Calculate and display the total price
            let productPrice = 0;

            if (productType === 'variable' && variationId) {
                // Get price from the selected variation
                const selectedVariation = document.querySelector(`input[value="${variationId}"]`);
                if (selectedVariation) {
                    productPrice = parseFloat(selectedVariation.getAttribute('data-price') || 0);
                }
            } else {
                // Get price from the button's data attribute
                productPrice = parseFloat(this.getAttribute('data-price') || 0);
            }

            // Calculate total price based on 3 nights
            const totalPrice = productPrice * 3;

            // Format the price with currency symbol
            const formattedPrice = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'EUR',
                minimumFractionDigits: 2
            }).format(totalPrice);

            // Update the price display
            document.getElementById('confirmTotalPrice').textContent = formattedPrice;

            // Store data for add to cart action
            mainEventProductId = productId;
            mainEventVariationId = variationId;
            mainEventIsDoubleRoom = isDoubleRoom;

            // Show confirmation modal
            confirmModal.style.display = 'block';
            // Use setTimeout to ensure the display property is applied before adding the active class
            setTimeout(() => {
                confirmModal.classList.add('active');
            }, 10);
        });
    });

    // Settings button removed
});
</script>
