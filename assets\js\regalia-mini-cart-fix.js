/**
 * Regalia Shop specific mini-cart fix
 * This script specifically targets the duplication issue on the regalia shop page
 * while maintaining the standard mini-cart component
 */
(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Regalia mini-cart fix loaded');

        // Function to deduplicate items in the mini-cart
        function deduplicateMiniCartItems() {
            console.log('Deduplicating mini-cart items');

            // Get the mini-cart container
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (!miniCartContainer || !miniCartContainer.__x) {
                console.log('Mini-cart not found or not initialized with <PERSON>');
                return;
            }

            try {
                // Get Alpine.js data
                const data = miniCartContainer.__x.getUnobservedData();

                // Deduplicate items by key
                const uniqueItems = [];
                const keys = new Set();

                // Only add items with unique keys
                (data.items || []).forEach(item => {
                    if (item && item.key && !keys.has(item.key)) {
                        keys.add(item.key);
                        uniqueItems.push(item);
                    }
                });

                // Only update if we found duplicates
                if (uniqueItems.length !== data.items.length) {
                    console.log(`Fixed duplicate items: ${data.items.length} -> ${uniqueItems.length}`);
                    data.items = uniqueItems;
                    data.count = uniqueItems.length;
                }
            } catch (error) {
                console.error('Error deduplicating mini-cart items:', error);
            }
        }

        // Function to refresh the mini-cart
        function refreshMiniCart() {
            console.log('Refreshing mini-cart');

            // Get the mini-cart container
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (!miniCartContainer || !miniCartContainer.__x) {
                console.log('Mini-cart not found or not initialized with Alpine');
                return;
            }

            try {
                // Get Alpine.js data
                const data = miniCartContainer.__x.getUnobservedData();

                // First deduplicate any existing items
                deduplicateMiniCartItems();

                // Then fetch fresh data
                if (typeof data.fetchItems === 'function') {
                    // Use the component's own fetchItems method
                    data.fetchItems();
                    console.log('Mini-cart refreshed using fetchItems method');
                }
            } catch (error) {
                console.error('Error refreshing mini-cart:', error);
            }
        }

        // Function to handle add to cart events
        function handleAddToCart() {
            console.log('Add to cart detected on regalia shop page');

            // First deduplicate any existing items
            deduplicateMiniCartItems();

            // Then refresh the mini-cart after a short delay
            setTimeout(refreshMiniCart, 300);
        }

        // Listen for add to cart events
        document.body.addEventListener('added_to_cart', function() {
            setTimeout(handleAddToCart, 100);
        });

        // Listen for our custom events
        window.addEventListener('lci:cartUpdated', function(event) {
            // Only handle if we have cart data
            if (event.detail && (event.detail.cart_count !== undefined || event.detail.items)) {
                setTimeout(handleAddToCart, 100);
            }
        });

        // Listen for regalia-specific events
        window.addEventListener('regalia:added-to-cart', function(event) {
            console.log('Regalia-specific add to cart event detected');
            setTimeout(handleAddToCart, 100);
        });

        // Deduplicate items on page load
        setTimeout(deduplicateMiniCartItems, 1000);

        // Periodically check for and fix duplicates
        setInterval(deduplicateMiniCartItems, 2000);

        // Patch the mini-cart's fetchItems method to prevent duplicates
        function patchMiniCartFetchItems() {
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (miniCartContainer && miniCartContainer.__x) {
                try {
                    // Get Alpine.js data
                    const data = miniCartContainer.__x.getUnobservedData();

                    // Only patch if not already patched
                    if (data.fetchItems && !data.fetchItems._patched) {
                        // Store the original fetchItems method
                        const originalFetchItems = data.fetchItems;

                        // Replace with our version that deduplicates
                        data.fetchItems = function() {
                            // Call the original method
                            originalFetchItems.call(data);

                            // Deduplicate after a short delay
                            setTimeout(deduplicateMiniCartItems, 300);
                        };

                        // Mark as patched
                        data.fetchItems._patched = true;

                        console.log('Mini-cart fetchItems method patched');
                    }
                } catch (error) {
                    console.error('Error patching mini-cart fetchItems method:', error);
                }
            }
        }

        // Apply the patch after a short delay
        setTimeout(patchMiniCartFetchItems, 1000);

        // Also patch the removeItem method
        function patchMiniCartRemoveItem() {
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (miniCartContainer && miniCartContainer.__x) {
                try {
                    // Get Alpine.js data
                    const data = miniCartContainer.__x.getUnobservedData();

                    // Only patch if not already patched
                    if (data.removeItem && !data.removeItem._patched) {
                        // Store the original removeItem method
                        const originalRemoveItem = data.removeItem;

                        // Replace with our version that deduplicates
                        data.removeItem = function(key) {
                            // Call the original method
                            originalRemoveItem.call(data, key);

                            // Deduplicate after a short delay
                            setTimeout(deduplicateMiniCartItems, 300);
                        };

                        // Mark as patched
                        data.removeItem._patched = true;

                        console.log('Mini-cart removeItem method patched');
                    }
                } catch (error) {
                    console.error('Error patching mini-cart removeItem method:', error);
                }
            }
        }

        // Apply the patch after a short delay
        setTimeout(patchMiniCartRemoveItem, 1000);
    });
})();
