/**
 * Simplified Modal System for LCI 2025 Dashboard
 * Using a more direct approach to avoid Alpine.js initialization issues
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize modal globals
    window.lciModalSystem = {
        // Modal state
        modalElement: null,
        backdropElement: null,
        isOpen: false,
        timer: null,
        logoUrl: 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/11/22232933/LCI_Pack.webp',

        // Initialize the modal system
        init: function() {
            // Create modal elements if they don't exist
            if (!this.modalElement) {
                this.createModalElements();
            }

            // Listen for custom events
            window.addEventListener('lci:showModal', (event) => {
                this.showModal(
                    event.detail.title,
                    event.detail.message,
                    event.detail.type,
                    event.detail.showConfirmButton,
                    event.detail.confirmButtonText,
                    event.detail.showCancelButton,
                    event.detail.cancelButtonText,
                    event.detail.autoClose,
                    event.detail.autoCloseDelay
                );
            });
        },

        // Create modal DOM elements
        createModalElements: function() {
            // Create backdrop
            this.backdropElement = document.createElement('div');
            this.backdropElement.className = 'lci-modal-backdrop';
            this.backdropElement.style.display = 'none';

            // Create modal container
            this.modalElement = document.createElement('div');
            this.modalElement.className = 'lci-modal';

            // Add modal HTML structure
            this.modalElement.innerHTML = `
                <div class="lci-modal-header">
                    <div class="lci-modal-logo-container">
                        <img src="${this.logoUrl}" alt="LCI 2025 Logo" class="lci-modal-logo">
                    </div>
                    <h2 class="lci-modal-title"></h2>
                </div>

                <div class="lci-modal-body">
                    <div class="mb-4">
                        <i class="fas lci-modal-icon"></i>
                    </div>
                    <p class="lci-modal-message"></p>
                </div>

                <div class="lci-modal-footer">
                    <button class="lci-btn lci-btn-secondary lci-cancel-btn" style="display: none;">Cancel</button>
                    <button class="lci-btn lci-btn-primary lci-confirm-btn">OK</button>
                </div>

                <div class="lci-progress-bar" style="display: none;">
                    <div class="lci-progress-bar-inner"></div>
                </div>
            `;

            // Add modal to backdrop
            this.backdropElement.appendChild(this.modalElement);

            // Add backdrop to body
            document.body.appendChild(this.backdropElement);

            // Add event listeners
            this.backdropElement.addEventListener('click', (e) => {
                if (e.target === this.backdropElement) {
                    this.closeModal();
                }
            });

            this.modalElement.querySelector('.lci-confirm-btn').addEventListener('click', () => {
                this.confirm();
            });

            this.modalElement.querySelector('.lci-cancel-btn').addEventListener('click', () => {
                this.cancel();
            });
        },

        // Show the modal
        showModal: function(title, message, type = 'info', showConfirmButton = true, confirmButtonText = 'OK',
                         showCancelButton = false, cancelButtonText = 'Cancel', autoClose = false, autoCloseDelay = 3000) {
            // Set modal content
            this.modalElement.querySelector('.lci-modal-title').textContent = title;
            this.modalElement.querySelector('.lci-modal-message').textContent = message;

            // Set icon based on type
            const iconElement = this.modalElement.querySelector('.lci-modal-icon');
            iconElement.className = 'fas lci-modal-icon';

            switch(type) {
                case 'success':
                    iconElement.classList.add('fa-check-circle', 'text-green-500');
                    break;
                case 'error':
                    iconElement.classList.add('fa-times-circle', 'text-red-500');
                    break;
                case 'warning':
                    iconElement.classList.add('fa-exclamation-triangle', 'text-yellow-500');
                    break;
                case 'info':
                default:
                    iconElement.classList.add('fa-info-circle', 'text-blue-500');
                    break;
            }

            // Configure buttons
            const confirmBtn = this.modalElement.querySelector('.lci-confirm-btn');
            const cancelBtn = this.modalElement.querySelector('.lci-cancel-btn');

            confirmBtn.style.display = showConfirmButton ? 'inline-block' : 'none';
            confirmBtn.textContent = confirmButtonText;

            cancelBtn.style.display = showCancelButton ? 'inline-block' : 'none';
            cancelBtn.textContent = cancelButtonText;

            // Configure auto-close
            const progressBar = this.modalElement.querySelector('.lci-progress-bar');
            const progressBarInner = this.modalElement.querySelector('.lci-progress-bar-inner');

            progressBar.style.display = autoClose ? 'block' : 'none';

            // Clear any existing timer
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }

            // Show the modal
            this.backdropElement.style.display = 'flex';
            this.isOpen = true;

            // Add body class to prevent scrolling
            document.body.classList.add('modal-open');

            // Add animation classes
            this.modalElement.classList.add('slide-in');

            // Start progress bar animation if auto-close is enabled
            if (autoClose) {
                progressBarInner.style.transition = `transform ${autoCloseDelay}ms linear`;
                progressBarInner.style.transform = 'scaleX(0)';

                // Small delay to ensure the initial state is rendered
                setTimeout(() => {
                    progressBarInner.style.transform = 'scaleX(1)';
                }, 50);

                // Set up auto-close timer
                this.timer = setTimeout(() => {
                    this.closeModal();
                }, autoCloseDelay);
            }
        },

        // Close the modal
        closeModal: function() {
            if (!this.isOpen) return;

            // Add closing animation
            this.modalElement.classList.remove('slide-in');
            this.modalElement.classList.add('slide-out');

            // Clear any existing timer
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }

            // Hide after animation completes
            setTimeout(() => {
                this.backdropElement.style.display = 'none';
                this.modalElement.classList.remove('slide-out');
                this.isOpen = false;

                // Remove body class
                document.body.classList.remove('modal-open');

                // Dispatch event that modal was closed
                window.dispatchEvent(new CustomEvent('lci:modalClosed'));
            }, 300); // Match animation duration
        },

        // Confirm action
        confirm: function() {
            this.closeModal();
            // Dispatch confirm event
            window.dispatchEvent(new CustomEvent('lci:modalConfirm'));
        },

        // Cancel action
        cancel: function() {
            this.closeModal();
            // Dispatch cancel event
            window.dispatchEvent(new CustomEvent('lci:modalCancel'));
        }
    };

    // Initialize the modal system
    window.lciModalSystem.init();
});

/**
 * Helper function to show modals from anywhere in the code
 *
 * @param {string} title Modal title
 * @param {string} message Modal message
 * @param {string} type Modal type ('success', 'error', 'info', 'warning')
 * @param {boolean} showConfirmButton Whether to show confirm button
 * @param {string} confirmButtonText Text for confirm button
 * @param {boolean} showCancelButton Whether to show cancel button
 * @param {string} cancelButtonText Text for cancel button
 * @param {boolean} autoClose Whether to auto-close the modal
 * @param {number} autoCloseDelay Delay in ms before auto-closing
 */
function showLciModal(title, message, type = 'info', showConfirmButton = true, confirmButtonText = 'OK',
                     showCancelButton = false, cancelButtonText = 'Cancel', autoClose = false, autoCloseDelay = 3000) {
    window.dispatchEvent(new CustomEvent('lci:showModal', {
        detail: {
            title,
            message,
            type,
            showConfirmButton,
            confirmButtonText,
            showCancelButton,
            cancelButtonText,
            autoClose,
            autoCloseDelay
        }
    }));

    // Return a promise that resolves when the modal is confirmed or rejected
    return new Promise((resolve, reject) => {
        const confirmHandler = () => {
            window.removeEventListener('lci:modalConfirm', confirmHandler);
            window.removeEventListener('lci:modalCancel', cancelHandler);
            resolve(true);
        };

        const cancelHandler = () => {
            window.removeEventListener('lci:modalConfirm', confirmHandler);
            window.removeEventListener('lci:modalCancel', cancelHandler);
            resolve(false);
        };

        window.addEventListener('lci:modalConfirm', confirmHandler);
        window.addEventListener('lci:modalCancel', cancelHandler);
    });
}
