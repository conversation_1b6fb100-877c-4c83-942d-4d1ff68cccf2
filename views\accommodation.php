<?php
/**
 * Accommodation View
 *
 * Displays accommodation options for the user
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Enqueue the accommodation CSS
wp_enqueue_style('accommodation-css', LCI2025_URL . 'assets/css/accommodation.css', array(), LCI2025_VERSION);

// Add inline CSS for the accommodation options grid
$accommodation_options_css = "
.accommodation-section-heading {
    position: relative;
    text-align: center;
    padding: 0.2rem 0;
    margin: 0.2rem 0;
}

.accommodation-booking-header {
    margin-bottom: 0.5rem;
    text-align: left;
    width: 100%;
}

.dashboard-header {
    width: 100%;
    overflow: hidden;
}

.dashboard-mini-cart-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.dashboard-mini-cart-container .dashboard-title {
    float: left;
}

.mini-cart-button {
    display: inline-flex;
    align-items: center;
    background-color: #36b1dc;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.mini-cart-button:hover {
    background-color: #2d9cc3;
    color: white;
    text-decoration: none;
}

.mini-cart-count {
    background-color: white;
    color: #36b1dc;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: 8px;
    font-weight: bold;
}

.mini-cart-total {
    margin-left: 8px;
    font-weight: 600;
}

.mini-cart-button {
    display: inline-flex;
    align-items: center;
    background-color: #36b1dc;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.mini-cart-button:hover {
    background-color: #2d9cc3;
    color: white;
    text-decoration: none;
}

.mini-cart-count {
    background-color: white;
    color: #36b1dc;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: 8px;
    font-weight: bold;
}

.mini-cart-total {
    margin-left: 8px;
    font-weight: 600;
}

.accommodation-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.accommodation-option-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.accommodation-option-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accommodation-option-image {
    height: 350px;
    overflow: hidden;
    position: relative;
}

.accommodation-option-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.accommodation-option-card:hover .accommodation-option-image img {
    transform: scale(1.05);
}

.accommodation-option-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.accommodation-option-content {
    padding: 20px;
}

.accommodation-option-content h4 {
    color: #00b2e3;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.accommodation-option-content p {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
}

.accommodation-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.accommodation-btn-primary {
    background: linear-gradient(to right, #00b2e3, #0099cc);
    color: white;
    border: none;
}

.accommodation-btn-primary:hover {
    background: linear-gradient(to right, #0099cc, #0080b3);
    color: white;
    box-shadow: 0 4px 10px rgba(0, 178, 227, 0.3);
}

@media (max-width: 768px) {
    .accommodation-options-grid {
        grid-template-columns: 1fr;
    }

    .accommodation-booking-content {
        display: flex;
        padding: 0.75rem;
        gap: 1rem;
        position: relative;
        z-index: 2;
        align-items: center;
        justify-content: center;
    }

    .accommodation-booking-title {
        font-size: 16px;
    }

    .accommodation-booking-date {
        width: 100%;
        margin-bottom: 10px;
        float: none !important;
        display: block !important;
        margin-right: 0 !important;
    }

    .accommodation-booking-dates {
        text-align: left;
        margin-top: 10px !important;
    }

    /* Mobile styles for dashboard-mini-cart-container */
    .dashboard-header {
        width: 100%;
    }

    .dashboard-mini-cart-container {
        flex-direction: column;
        align-items: center;
    }

    .dashboard-mini-cart-container .dashboard-title {
        float: none;
        margin-bottom: 15px;
        text-align: center;
        width: 100%;
    }

    .dashboard-mini-cart-container a.mini-cart-button {
        float: none;
        width: 100%;
        text-align: center;
        justify-content: center;
    }
}

    /* Mobile styles for self-accommodation card */
    .self-accommodation-card {
        border-left: none !important;
        border-top: 4px solid #36b1dc !important;
    }

    .self-accommodation-content h4 {
        font-size: 18px !important;
        text-align: center !important;
        display: block !important;
        width: 100% !important;
    }

    .self-accommodation-content h4 i {
        display: none !important;
    }

    .self-accommodation-content h4 span {
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 60px !important;
    }

    .self-accommodation-content p {
        font-size: 14px !important;
        text-align: center !important;
    }
}
";

wp_add_inline_style('accommodation-css', $accommodation_options_css);

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$user_email = $current_user->user_email;
$first_name = get_user_meta($user_id, 'first_name', true);
$last_name = get_user_meta($user_id, 'last_name', true);

// Define accommodation category IDs
// These are the same IDs defined in the LCI_Accommodations class
$accommodation_categories = [
    1,  // MAIN_EVENT_CATEGORY_ID
    20, // COUNCILOR_PACK_CATEGORY_ID
    30, // ADDITIONAL_NIGHT_AFTER_PRETOUR_CATEGORY_ID
    37, // ADDITIONAL_NIGHT_BUCHAREST_CATEGORY_ID
    18  // SPECIAL_ACCOMMODATION_CATEGORY_ID
];

// Define product IDs for special filtering
$councilor_product_id = 40;  // Product ID for councilors
$special_product_ids = [41, 42]; // Product IDs for special accommodation

// Get user's orders
$customer_orders = [];
$user_orders = [];
$user_ordered_product_ids = [];
$accommodation_products = [];

// Track if user has ordered special products
$has_councilor_product = false;
$has_special_product = false;

// Check if WooCommerce is active
if (class_exists('WooCommerce')) {
    // Get customer orders
    $customer_orders = wc_get_orders([
        'customer_id' => $user_id,
        'status' => ['processing', 'completed', 'on-hold'],
        'limit' => -1,
    ]);

    // Get accommodation products
    foreach ($accommodation_categories as $accommodation_category_id) {
        $args = [
            'post_type' => 'product',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
            'tax_query'      => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'id',
                    'terms'    => $accommodation_category_id,
                ),
            ),
        ];

        $posts = get_posts($args);
        foreach ($posts as $post) {
            $product = wc_get_product($post->ID);
            if ($product) {
                $accommodation_products[] = $product;
            }
        }
    }

    // Filter orders to only include those with accommodation products
    // Also check for special products that determine which accommodation to show
    foreach ($customer_orders as $order) {
        $has_accommodation = false;
        $accommodation_items = [];

        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $product = wc_get_product($product_id);

            if ($product) {
                // Check if this is a special product that determines accommodation options
                if ($product_id == $councilor_product_id) {
                    $has_councilor_product = true;
                }

                if (in_array($product_id, $special_product_ids)) {
                    $has_special_product = true;
                }

                $categories = $product->get_category_ids();

                if (count(array_intersect($accommodation_categories, $categories)) > 0) {
                    $has_accommodation = true;
                    $accommodation_items[] = [
                        'id' => $product_id,
                        'name' => $item->get_name(),
                        'quantity' => $item->get_quantity(),
                        'total' => $item->get_total(),
                        'image' => wp_get_attachment_url($product->get_image_id()),
                    ];

                    // Add to the list of products the user has already ordered
                    if (!in_array($product_id, $user_ordered_product_ids)) {
                        $user_ordered_product_ids[] = $product_id;
                    }
                }
            }
        }

        if ($has_accommodation) {
            $user_orders[] = [
                'id' => $order->get_id(),
                'date' => $order->get_date_created()->date('Y-m-d H:i:s'),
                'status' => $order->get_status(),
                'total' => $order->get_total(),
                'items' => $accommodation_items,
            ];
        }
    }
}

// Get cart contents count
$cart_count = WC()->cart ? WC()->cart->get_cart_contents_count() : 0;

// Get cart total
$cart_total = WC()->cart ? WC()->cart->get_cart_total() : 0;
?>

<div class="accommodation-container">
    <!-- Header Section - 2025 UX/UI Style -->
    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 40px 20px; position: relative; overflow: hidden; border-radius: 0 0 30px 30px; box-shadow: 0 10px 30px rgba(54, 177, 220, 0.2); margin-bottom: 40px;">
        <!-- Animated background elements -->
        <div class="position-absolute" style="top: -50px; right: -50px; width: 200px; height: 200px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2), transparent 70%); border-radius: 50%; animation: pulse 8s infinite alternate;"></div>
        <div class="position-absolute" style="bottom: -30px; left: 10%; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15), transparent 70%); border-radius: 50%; animation: float 12s infinite alternate;"></div>
        <div class="position-absolute" style="top: 20%; right: 20%; width: 80px; height: 80px; background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%); border-radius: 50%; animation: float 7s infinite alternate-reverse;"></div>

        <!-- Glassmorphism overlay -->
        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);"></div>

        <div class="container position-relative" style="z-index: 2;">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <!-- Modern 3D icon with shadow and glow -->
                    <div style="background: rgba(255, 255, 255, 0.2); width: 60px; height: 60px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); transform: perspective(500px) rotateX(10deg); border: 1px solid rgba(255, 255, 255, 0.3);">
                        <div style="position: relative;">
                            <i class="fas fa-hotel" style="color: white; font-size: 28px; filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));"></i>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.8), transparent 70%); filter: blur(8px); opacity: 0.5; animation: glow 3s infinite alternate;"></div>
                        </div>
                    </div>

                    <!-- Typography with modern styling -->
                    <div>
                        <h1 style="color: white; font-size: 28px; font-weight: 800; margin: 0; letter-spacing: -0.5px; text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">Accommodation</h1>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; margin: 0; margin-top: 4px; font-weight: 300; letter-spacing: 0.5px;">Book your stay for LCI 2025 AGM in Brasov</p>
                    </div>
                </div>

                <!-- Mini Cart Button with modern styling -->
                <div style="position: relative; z-index: 10;">
                    <?php echo lci_get_mini_cart_html(); ?>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($_GET['category_id'])): ?>
    <div class="mb-3">
        <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation', remove_query_arg('category_id'))); ?>" class="btn btn-sm btn-outline-secondary d-inline-flex align-items-center">
            <i class="fas fa-arrow-left me-2"></i> Back to All Accommodations
        </a>
    </div>
    <?php endif; ?>

    <!-- My Accommodation Section -->
    <?php if (!empty($user_orders)): ?>
    <div class="mb-5">
        <!-- My Accommodation Section - 2025 UX/UI Style -->
        <div class="section-container" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden; margin-bottom: 30px;">
            <!-- Decorative elements -->
            <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
            <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

            <!-- Section header with 3D effect -->
            <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
                <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                    <i class="fas fa-bed" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                </div>
                <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">My Accommodation</h3>
            </div>

        <div class="accommodation-bookings-container">
            <?php
            // Flatten all accommodation items from all orders
            $all_bookings = [];
            $has_own_accommodation = false;
            $own_accommodation_products = [];

            foreach ($user_orders as $order) {
                foreach ($order['items'] as $item) {
                    $item['order_id'] = $order['id'];
                    $item['order_date'] = $order['date'];
                    $item['order_status'] = $order['status'];

                    // Check if this is a "book your own accommodation" product (1679 or 137)
                    if ($item['id'] == 1679 || $item['id'] == 137) {
                        $has_own_accommodation = true;
                        $own_accommodation_products[] = $item;
                    } else {
                        // Only add non-own-accommodation products to the regular bookings list
                        $all_bookings[] = $item;
                    }
                }
            }
            ?>

            <?php if ($has_own_accommodation): ?>
            <!-- Special message for users who selected to book their own accommodation -->
            <div class="mb-4 self-accommodation-card" style="background: linear-gradient(135deg, #ffffff, #f8f9fa); border-left: 4px solid #36b1dc; border-radius: 12px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05); overflow: hidden;">
                <div style="padding: 25px; position: relative;">
                    <!-- Decorative elements -->
                    <div style="position: absolute; top: -15px; right: -15px; width: 120px; height: 120px; border-radius: 50%; background: radial-gradient(circle, rgba(54, 177, 220, 0.1) 0%, rgba(255, 255, 255, 0) 70%);"></div>
                    <div style="position: absolute; bottom: -20px; left: -20px; width: 100px; height: 100px; border-radius: 50%; background: radial-gradient(circle, rgba(54, 177, 220, 0.08) 0%, rgba(255, 255, 255, 0) 70%);"></div>

                    <div class="self-accommodation-content">
                        <h4 style="color: #333; margin-bottom: 12px; font-weight: 700; font-size: 20px; position: relative; display: inline-block;">
                            <i class="fas fa-building" style="color: #36b1dc; margin-right: 8px; font-size: 18px;"></i>
                            Self-Arranged Accommodation
                            <span style="position: absolute; bottom: -4px; left: 0; width: 40px; height: 3px; background-color: #36b1dc; border-radius: 3px;"></span>
                        </h4>
                        <p style="color: #555; margin-bottom: 0; line-height: 1.6; font-size: 15px;">You've chosen the flexibility to arrange your own accommodation for the main event. This gives you the freedom to select any hotel, apartment, or lodging option in Brasov that suits your preferences and budget.</p>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($all_bookings)): ?>
            <div class="accommodation-bookings-list">
                <?php foreach ($all_bookings as $index => $booking): ?>
                <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); overflow: hidden; margin-bottom: 24px; border: 1px solid rgba(255, 255, 255, 0.8); transition: all 0.3s ease; position: relative; animation: fadeIn 0.5s ease forwards; animation-delay: calc(<?php echo $index; ?> * 0.1s); opacity: 0;"
                    onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.1)';"
                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0, 0, 0, 0.05)';">

                    <!-- Card Header with gradient background -->
                    <div style="background: linear-gradient(135deg, #36b1dc, #2980b9); padding: 20px; color: white; position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1), transparent 70%);"></div>

                        <div style="display: flex; justify-content: space-between; align-items: center; position: relative; z-index: 1;">
                            <h4 style="margin: 0; font-weight: 600; font-size: 18px; color: white; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);"><?php echo esc_html($booking['name']); ?></h4>

                            <?php if (!empty($booking['stars'])): ?>
                            <div style="display: flex; align-items: center;">
                                <?php for ($i = 0; $i < $booking['stars']; $i++): ?>
                                <i class="fas fa-star" style="color: #ffc107; margin-left: 2px; font-size: 14px; filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));"></i>
                                <?php endfor; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Card Content - Horizontal Layout -->
                    <div style="padding: 20px;">
                        <div style="display: flex; gap: 20px; align-items: stretch;">
                            <!-- Image Section - Left Side -->
                            <div style="width: 200px; min-width: 200px; border-radius: 12px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); align-self: stretch; display: flex; flex-direction: column;">
                                <?php if (!empty($booking['image'])): ?>
                                <img src="<?php echo esc_url($booking['image']); ?>" alt="<?php echo esc_attr($booking['name']); ?>" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                <?php else: ?>
                                <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #f5f7fa, #e4e8eb); display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-hotel" style="font-size: 48px; color: #36b1dc;"></i>
                                </div>
                                <?php endif; ?>
                            </div>

                            <!-- Content Section - Right Side -->
                            <div style="flex: 1; display: flex; flex-direction: column; gap: 15px; justify-content: space-between;">

                            <!-- Check-in/Check-out Dates -->
                            <?php
                            // Show check-in and check-out dates for products that are not 1679 or 137
                            if ($booking['id'] != 1679 && $booking['id'] != 137) {
                                $product = wc_get_product($booking['id']);
                                if ($product) {
                                    $checkin_date = get_post_meta($booking['id'], '_checkin_date', true);
                                    $checkout_date = get_post_meta($booking['id'], '_checkout_date', true);

                                    if ($checkin_date && $checkout_date) {
                                        echo '<div class="booking-info-row" style="display: flex; align-items: center; gap: 15px; background-color: #f8f9fa; padding: 10px; border-radius: 10px; margin-bottom: 10px;">';
                                        echo '<div style="flex: 1;">';
                                        echo '<div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Check-in</div>';
                                        echo '<div style="display: flex; align-items: center;">';
                                        echo '<i class="fas fa-calendar-day" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>';
                                        echo '<span style="font-size: 14px; color: #2c3e50; font-weight: 500;">' . date('F j, Y', strtotime($checkin_date)) . '</span>';
                                        echo '</div>';
                                        echo '</div>';

                                        echo '<div style="width: 1px; height: 30px; background-color: #dee2e6;"></div>';

                                        echo '<div style="flex: 1;">';
                                        echo '<div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Check-out</div>';
                                        echo '<div style="display: flex; align-items: center;">';
                                        echo '<i class="fas fa-calendar-day" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>';
                                        echo '<span style="font-size: 14px; color: #2c3e50; font-weight: 500;">' . date('F j, Y', strtotime($checkout_date)) . '</span>';
                                        echo '</div>';
                                        echo '</div>';
                                        echo '</div>';
                                    }
                                }
                            }
                            ?>

                            <!-- Booking Details -->
                            <div class="booking-info-row" style="display: flex; align-items: center; gap: 15px; background-color: #f8f9fa; padding: 12px; border-radius: 10px; margin-bottom: 10px;">
                                <div style="flex: 1;">
                                    <div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Booking Date</div>
                                    <div style="display: flex; align-items: center;">
                                        <i class="far fa-calendar-check" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>
                                        <span style="font-size: 14px; color: #2c3e50; font-weight: 500;"><?php echo date('F j, Y', strtotime($booking['order_date'])); ?></span>
                                    </div>
                                </div>

                                <div style="width: 1px; height: 30px; background-color: #dee2e6;"></div>

                                <div style="flex: 1;">
                                    <div style="font-size: 12px; color: #6c757d; margin-bottom: 2px;">Room Status</div>
                                    <div style="display: flex; align-items: center;">
                                        <?php
                                        // First check user meta for sharing information
                                        $user_id = get_current_user_id();
                                        $user_share_room_with = get_user_meta($user_id, 'share_room_with', true);
                                        $sharing_displayed = false;

                                        if (!empty($user_share_room_with)) {
                                            echo '<i class="fas fa-users" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>';
                                            echo '<span style="font-size: 14px; color: #2c3e50; font-weight: 500;">Sharing with: ' . esc_html($user_share_room_with) . '</span>';
                                            $sharing_displayed = true;
                                        }

                                        // Then check order item meta if sharing info wasn't already displayed
                                        if (!$sharing_displayed) {
                                            $order = wc_get_order($booking['order_id']);
                                            if ($order) {
                                                foreach ($order->get_items() as $item) {
                                                    if ($item->get_product_id() == $booking['id']) {
                                                        // Check room_share_options meta
                                                        $room_share_options = $item->get_meta('room_share_options');

                                                        if ($room_share_options === 'yes') {
                                                            // Check share_room_with meta
                                                            $share_room_with = $item->get_meta('share_room_with');

                                                            if (!empty($share_room_with)) {
                                                                // Display the name of the person they're sharing with
                                                                echo '<i class="fas fa-users" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>';
                                                                echo '<span style="font-size: 14px; color: #2c3e50; font-weight: 500;">Sharing with: ' . esc_html($share_room_with) . '</span>';
                                                                $sharing_displayed = true;
                                                            } else {
                                                                // If share_room_with is empty, show "To be decided"
                                                                echo '<i class="fas fa-users" style="color: #f39c12; font-size: 14px; margin-right: 5px;"></i>';
                                                                echo '<span style="font-size: 14px; color: #2c3e50; font-weight: 500;">Roommate: To be decided</span>';
                                                                $sharing_displayed = true;
                                                            }
                                                        } else if ($room_share_options === 'no') {
                                                            // If they chose not to share, show "Private room"
                                                            echo '<i class="fas fa-user" style="color: #27ae60; font-size: 14px; margin-right: 5px;"></i>';
                                                            echo '<span style="font-size: 14px; color: #2c3e50; font-weight: 500;">Private room</span>';
                                                            $sharing_displayed = true;
                                                        }

                                                        // Check for other sharing meta values as fallbacks
                                                        if (!$sharing_displayed && empty($room_share_options)) {
                                                            // Check for sharing_room meta (legacy field)
                                                            $sharing_room = $item->get_meta('sharing_room');
                                                            if (!empty($sharing_room)) {
                                                                echo '<i class="fas fa-users" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>';
                                                                echo '<span style="font-size: 14px; color: #2c3e50; font-weight: 500;">Sharing with: ' . esc_html($sharing_room) . '</span>';
                                                                $sharing_displayed = true;
                                                            }

                                                            // Check for roommate_id meta
                                                            if (!$sharing_displayed) {
                                                                $roommate_id = $item->get_meta('roommate_id');
                                                                if (!empty($roommate_id)) {
                                                                    $roommate_user = get_user_by('ID', $roommate_id);
                                                                    if ($roommate_user) {
                                                                        $roommate_name = $roommate_user->first_name . ' ' . $roommate_user->last_name;
                                                                        echo '<i class="fas fa-users" style="color: #36b1dc; font-size: 14px; margin-right: 5px;"></i>';
                                                                        echo '<span style="font-size: 14px; color: #2c3e50; font-weight: 500;">Roommate: ' . esc_html($roommate_name) . '</span>';
                                                                        $sharing_displayed = true;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        // If no sharing information was found anywhere, check if we should show "To be decided"
                                        if (!$sharing_displayed) {
                                            // Check if this is a shared room product by product ID or category
                                            $product = wc_get_product($booking['id']);
                                            if ($product) {
                                                $product_cats = $product->get_category_ids();
                                                // If this is a shared room product (you may need to adjust this logic)
                                                if (strpos(strtolower($product->get_name()), 'shared') !== false ||
                                                    strpos(strtolower($product->get_name()), 'double') !== false ||
                                                    strpos(strtolower($product->get_name()), 'twin') !== false) {
                                                    echo '<i class="fas fa-users" style="color: #f39c12; font-size: 14px; margin-right: 5px;"></i>';
                                                    echo '<span style="font-size: 14px; color: #2c3e50; font-weight: 500;">Roommate: To be decided</span>';
                                                }
                                            }
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php elseif (!$has_own_accommodation): ?>
            <!-- Show this only if there are no bookings and no own accommodation -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span>You haven't booked any accommodation yet. Check out the options below.</span>
            </div>
            <?php endif; ?>
        </div>

        <!-- Wizard button removed as requested -->
    </div>
    <?php endif; ?>

    <!-- Accommodation Options Section - 2025 UX/UI Style -->
    <div class="mb-5">
        <div class="section-container" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden; margin-bottom: 30px;">
            <!-- Decorative elements -->
            <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
            <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

            <!-- Section header with 3D effect -->
            <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
                <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                    <i class="fas fa-concierge-bell" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                </div>
                <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Accommodation Options</h3>
            </div>

        <?php
        // Check if user has Main Pretour (ID 743)
        $has_main_pretour = false;
        $has_bucharest_accommodation = false;
        $has_product_1679 = false;
        $has_product_137 = false;

        // Check user's orders for specific products
        foreach ($customer_orders as $order) {
            foreach ($order->get_items() as $item) {
                $product_id = $item->get_product_id();

                if ($product_id == 743) {
                    $has_main_pretour = true;
                }

                if ($product_id == 2151) { // Bucharest accommodation product ID
                    $has_bucharest_accommodation = true;
                }

                if ($product_id == 1679) {
                    $has_product_1679 = true;
                }

                if ($product_id == 137) {
                    $has_product_137 = true;
                }
            }
        }

        // Also check cart for Bucharest accommodation
        if (WC()->cart) {
            foreach (WC()->cart->get_cart() as $cart_item) {
                $product_id = $cart_item['product_id'];

                if ($product_id == 2151) { // Bucharest accommodation product ID
                    $has_bucharest_accommodation = true;
                }
            }
        }
        ?>

        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-top: 20px;">
            <!-- Bucharest Accommodation Option -->
            <?php if ($has_main_pretour && !$has_bucharest_accommodation): ?>
            <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.8); transition: all 0.3s ease; height: 100%; display: flex; flex-direction: column;"
                onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.1)';"
                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0, 0, 0, 0.05)';">

                <!-- Card Image with Overlay -->
                <div style="position: relative; height: 220px; overflow: hidden;">
                    <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2025/04/24185051/lci-bucharest.png" alt="Bucharest Accommodation" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">

                    <!-- Gradient Overlay -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.4)); z-index: 1;"></div>

                    <!-- Location Badge -->
                    <div style="position: absolute; top: 15px; left: 15px; background-color: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); padding: 5px 10px; border-radius: 20px; z-index: 2; display: flex; align-items: center;">
                        <i class="fas fa-map-marker-alt" style="color: #36b1dc; margin-right: 5px; font-size: 12px;"></i>
                        <span style="font-size: 12px; font-weight: 600; color: #2c3e50;">Bucharest</span>
                    </div>
                </div>

                <!-- Card Content -->
                <div style="padding: 20px; flex-grow: 1; display: flex; flex-direction: column;">
                    <h4 style="margin: 0 0 10px 0; font-size: 18px; font-weight: 700; color: #2c3e50; text-align: center;">Coming to Bucharest early for pretour?</h4>

                    <p style="margin: 0 0 20px 0; font-size: 14px; color: #5d6778; line-height: 1.5; flex-grow: 1;">Book your accommodation in Bucharest before the Main Pretour starts.</p>

                    <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation-bucharest', home_url('/lci-dashboard/'))); ?>" style="display: inline-flex; width: 100%; align-items: center; justify-content: center; padding: 12px 20px; background-color: #36b1dc; color: white; font-weight: 600; border-radius: 12px; box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3); transition: all 0.3s ease; text-decoration: none; text-transform: uppercase; font-size: 14px; letter-spacing: 0.5px; white-space: nowrap;">
                        <i class="fas fa-hotel me-2" style="color: white;"></i> <span>Bucharest</span>
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <!-- Pre-Event Brasov Accommodation Option -->
            <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.8); transition: all 0.3s ease; height: 100%; display: flex; flex-direction: column;"
                onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.1)';"
                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0, 0, 0, 0.05)';">

                <!-- Card Image with Overlay -->
                <div style="position: relative; height: 220px; overflow: hidden;">
                    <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2025/04/24181735/lci-pre-event.webp" alt="Pre-Event Accommodation" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">

                    <!-- Gradient Overlay -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.4)); z-index: 1;"></div>

                    <!-- Location Badge -->
                    <div style="position: absolute; top: 15px; left: 15px; background-color: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); padding: 5px 10px; border-radius: 20px; z-index: 2; display: flex; align-items: center;">
                        <i class="fas fa-map-marker-alt" style="color: #36b1dc; margin-right: 5px; font-size: 12px;"></i>
                        <span style="font-size: 12px; font-weight: 600; color: #2c3e50;">Brasov</span>
                    </div>

                    <!-- Date Badge -->
                    <div style="position: absolute; top: 15px; right: 15px; background-color: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); padding: 5px 10px; border-radius: 20px; z-index: 2; display: flex; align-items: center;">
                        <i class="fas fa-calendar-alt" style="color: #36b1dc; margin-right: 5px; font-size: 12px;"></i>
                        <span style="font-size: 12px; font-weight: 600; color: #2c3e50;">Pre-Event</span>
                    </div>
                </div>

                <!-- Card Content -->
                <div style="padding: 20px; flex-grow: 1; display: flex; flex-direction: column;">
                    <h4 style="margin: 0 0 10px 0; font-size: 18px; font-weight: 700; color: #2c3e50; text-align: center;">Coming early to Brasov?</h4>

                    <p style="margin: 0 0 20px 0; font-size: 14px; color: #5d6778; line-height: 1.5; flex-grow: 1;">Book your additional accommodation in Brasov before the main event.</p>

                    <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation-pre-event', home_url('/lci-dashboard/'))); ?>" style="display: inline-flex; width: 100%; align-items: center; justify-content: center; padding: 12px 20px; background-color: #36b1dc; color: white; font-weight: 600; border-radius: 12px; box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3); transition: all 0.3s ease; text-decoration: none; text-transform: uppercase; font-size: 14px; letter-spacing: 0.5px; white-space: nowrap;">
                        <i class="fas fa-hotel me-2" style="color: white;"></i> <span>Pre-Event</span>
                    </a>
                </div>
            </div>

            <!-- Main Event Accommodation Option - Only visible if user has products 1679 or 137 -->
            <?php if ($has_product_1679 || $has_product_137): ?>
            <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.8); transition: all 0.3s ease; height: 100%; display: flex; flex-direction: column;"
                onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.1)';"
                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0, 0, 0, 0.05)';">

                <!-- Card Image with Overlay -->
                <div style="position: relative; height: 220px; overflow: hidden;">
                    <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2025/04/24180526/lci-main-event.webp" alt="Main Event Accommodation" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">

                    <!-- Gradient Overlay -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.4)); z-index: 1;"></div>

                    <!-- Location Badge -->
                    <div style="position: absolute; top: 15px; left: 15px; background-color: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); padding: 5px 10px; border-radius: 20px; z-index: 2; display: flex; align-items: center;">
                        <i class="fas fa-map-marker-alt" style="color: #36b1dc; margin-right: 5px; font-size: 12px;"></i>
                        <span style="font-size: 12px; font-weight: 600; color: #2c3e50;">Brasov</span>
                    </div>

                    <!-- Featured Badge -->
                    <div style="position: absolute; top: 15px; right: 15px; background-color: rgba(54, 177, 220, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); padding: 5px 10px; border-radius: 20px; z-index: 2; display: flex; align-items: center;">
                        <i class="fas fa-star" style="color: white; margin-right: 5px; font-size: 12px;"></i>
                        <span style="font-size: 12px; font-weight: 600; color: white;">Main Event</span>
                    </div>
                </div>

                <!-- Card Content -->
                <div style="padding: 20px; flex-grow: 1; display: flex; flex-direction: column;">
                    <h4 style="margin: 0 0 10px 0; font-size: 18px; font-weight: 700; color: #2c3e50; text-align: center;">Main Event Accommodation</h4>

                    <p style="margin: 0 0 20px 0; font-size: 14px; color: #5d6778; line-height: 1.5; flex-grow: 1;">Book your accommodation in Brasov during the main event (August 21-24, 2025).</p>

                    <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation-main-event', home_url('/lci-dashboard/'))); ?>" style="display: inline-flex; width: 100%; align-items: center; justify-content: center; padding: 12px 20px; background-color: #36b1dc; color: white; font-weight: 600; border-radius: 12px; box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3); transition: all 0.3s ease; text-decoration: none; text-transform: uppercase; font-size: 14px; letter-spacing: 0.5px; white-space: nowrap;">
                        <i class="fas fa-hotel me-2" style="color: white;"></i> <span>Main Event</span>
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <!-- Post-Event Brasov Accommodation Option -->
            <div style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.8); transition: all 0.3s ease; height: 100%; display: flex; flex-direction: column;"
                onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.1)';"
                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0, 0, 0, 0.05)';">

                <!-- Card Image with Overlay -->
                <div style="position: relative; height: 220px; overflow: hidden;">
                    <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2025/04/24182902/lci-post-event.webp" alt="Post-Event Accommodation" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">

                    <!-- Gradient Overlay -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.4)); z-index: 1;"></div>

                    <!-- Location Badge -->
                    <div style="position: absolute; top: 15px; left: 15px; background-color: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); padding: 5px 10px; border-radius: 20px; z-index: 2; display: flex; align-items: center;">
                        <i class="fas fa-map-marker-alt" style="color: #36b1dc; margin-right: 5px; font-size: 12px;"></i>
                        <span style="font-size: 12px; font-weight: 600; color: #2c3e50;">Brasov</span>
                    </div>

                    <!-- Date Badge -->
                    <div style="position: absolute; top: 15px; right: 15px; background-color: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); padding: 5px 10px; border-radius: 20px; z-index: 2; display: flex; align-items: center;">
                        <i class="fas fa-calendar-alt" style="color: #36b1dc; margin-right: 5px; font-size: 12px;"></i>
                        <span style="font-size: 12px; font-weight: 600; color: #2c3e50;">Post-Event</span>
                    </div>
                </div>

                <!-- Card Content -->
                <div style="padding: 20px; flex-grow: 1; display: flex; flex-direction: column;">
                    <h4 style="margin: 0 0 10px 0; font-size: 18px; font-weight: 700; color: #2c3e50; text-align: center;">Staying after the main event?</h4>

                    <p style="margin: 0 0 20px 0; font-size: 14px; color: #5d6778; line-height: 1.5; flex-grow: 1;">Book your additional accommodation in Brasov after the main event.</p>

                    <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation-post-event', home_url('/lci-dashboard/'))); ?>" style="display: inline-flex; width: 100%; align-items: center; justify-content: center; padding: 12px 20px; background-color: #36b1dc; color: white; font-weight: 600; border-radius: 12px; box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3); transition: all 0.3s ease; text-decoration: none; text-transform: uppercase; font-size: 14px; letter-spacing: 0.5px; white-space: nowrap;">
                        <i class="fas fa-hotel me-2" style="color: white;"></i> <span>Post-Event</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- No Accommodation Yet Section - 2025 UX/UI Style -->
    <?php if (empty($user_orders)): ?>
    <div class="mb-5">
        <div class="section-container" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.02); padding: 30px; border: 1px solid rgba(255, 255, 255, 0.8); position: relative; overflow: hidden; margin-bottom: 30px;">
            <!-- Decorative elements -->
            <div class="position-absolute" style="top: -20px; right: -20px; width: 150px; height: 150px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.1), transparent 70%); border-radius: 50%; z-index: 0;"></div>
            <div class="position-absolute" style="bottom: -30px; left: -30px; width: 180px; height: 180px; background: radial-gradient(circle at center, rgba(54, 177, 220, 0.05), transparent 70%); border-radius: 50%; z-index: 0;"></div>

            <!-- Section header with 3D effect -->
            <div class="d-flex align-items-center mb-4 position-relative" style="z-index: 1;">
                <div style="background: linear-gradient(135deg, rgba(54, 177, 220, 0.2), rgba(54, 177, 220, 0.1)); width: 50px; height: 50px; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 8px 16px rgba(54, 177, 220, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5); transform: perspective(500px) rotateX(10deg);">
                    <i class="fas fa-info-circle" style="color: #36b1dc; font-size: 24px; filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));"></i>
                </div>
                <h3 style="color: #2c3e50; font-size: 22px; font-weight: 700; margin: 0; letter-spacing: -0.3px;">Book Your Accommodation</h3>
            </div>

            <!-- Alert with modern styling -->
            <div style="background: rgba(54, 177, 220, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 16px; padding: 20px; border: 1px solid rgba(54, 177, 220, 0.2); display: flex; align-items: flex-start; position: relative; z-index: 1;">
                <div style="background: rgba(54, 177, 220, 0.2); width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 16px; flex-shrink: 0;">
                    <i class="fas fa-info-circle" style="color: #36b1dc; font-size: 20px;"></i>
                </div>
                <div style="flex: 1;">
                    <p style="color: #2c3e50; margin: 0; font-size: 15px; line-height: 1.6;">You haven't booked any accommodation yet. Use the options above to find and book the perfect accommodation for your stay in Brasov.</p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Accommodation Add to Cart Confirmation Modal has been removed -->


<?php
// Accommodation JavaScript has been removed
?>

<!-- Add animations -->
<style>
    @keyframes pulse {
        0% { transform: scale(1); opacity: 0.5; }
        100% { transform: scale(1.2); opacity: 0.2; }
    }
    @keyframes float {
        0% { transform: translateY(0) translateX(0); }
        50% { transform: translateY(-20px) translateX(10px); }
        100% { transform: translateY(10px) translateX(-10px); }
    }
    @keyframes glow {
        0% { opacity: 0.3; }
        100% { opacity: 0.7; }
    }
    @keyframes fadeIn {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    /* Mobile Responsive Styles */
    @media (max-width: 767px) {
        /* Reduce padding on all containers */
        .container {
            padding-left: 10px !important;
            padding-right: 10px !important;
        }

        /* Make cards take full width with no padding */
        .section-container {
            padding: 0 !important;
            border-radius: 20px !important;
        }

        /* Adjust card styles */
        [style*="border-radius: 20px"] {
            border-radius: 15px !important;
        }

        /* Add padding only to content areas */
        [style*="padding: 20px; flex-grow: 1;"] {
            padding: 15px !important;
        }

        /* Make booking info rows stack on mobile */
        .booking-info-row {
            flex-direction: column !important;
            gap: 10px !important;
        }

        /* Remove divider on mobile */
        .booking-info-row [style*="width: 1px; height: 30px;"] {
            display: none !important;
        }

        /* Adjust spacing */
        .mb-5 {
            margin-bottom: 15px !important;
        }

        /* Make header more compact */
        h1 {
            font-size: 24px !important;
        }

        h3 {
            font-size: 18px !important;
        }

        p {
            font-size: 14px !important;
        }

        /* Center align text on mobile */
        h4, p {
            text-align: center !important;
        }

        /* Make grid single column on mobile */
        [style*="grid-template-columns: repeat(2, 1fr)"] {
            grid-template-columns: 1fr !important;
        }

        /* Stack accommodation card content on mobile */
        [style*="display: flex; gap: 20px; align-items: stretch"] {
            flex-direction: column !important;
        }

        [style*="width: 200px; min-width: 200px"] {
            width: 100% !important;
            min-width: 100% !important;
            margin-bottom: 15px !important;
        }

        /* Make button text smaller on mobile to fit on one line */
        [style*="display: inline-flex; width: 100%; align-items: center; justify-content: center"] {
            font-size: 12px !important;
            padding: 10px !important;
            white-space: nowrap !important;
        }

        /* Keep icons visible but make text smaller */
        [style*="display: inline-flex; width: 100%; align-items: center; justify-content: center"] span {
            font-size: 12px !important;
        }
        }
    }
</style>
