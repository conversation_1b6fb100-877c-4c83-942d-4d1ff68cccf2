<?php
/**
 * LCI Payment Handler
 *
 * Handles direct payment processing within the LCI Dashboard
 * without redirecting to WooCommerce checkout.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class to handle payments directly in the LCI Dashboard
 */
class LCI_Payment_Handler {
    /**
     * Initialize the handler
     */
    public static function init() {
        // Add AJAX handlers
        add_action('wp_ajax_lci_create_order', [__CLASS__, 'ajax_create_order']);
        add_action('wp_ajax_lci_process_payment', [__CLASS__, 'ajax_process_payment']);

        // Add hooks for displaying payment instructions
        add_action('lci_dashboard_after_payment_methods', [__CLASS__, 'render_payment_instructions']);

        // Add scripts
        add_action('wp_enqueue_scripts', [__CLASS__, 'enqueue_scripts']);
    }

    /**
     * Enqueue scripts
     */
    public static function enqueue_scripts() {
        // Only enqueue on the payment page
        if (!isset($_GET['tab']) || $_GET['tab'] !== 'payment') {
            return;
        }

        // Enqueue our custom script
        wp_enqueue_script(
            'lci-payment-handler',
            LCI2025_URL . 'assets/js/payment-handler.js',
            ['jquery'],
            LCI2025_VERSION,
            true
        );

        // Localize script with data
        wp_localize_script('lci-payment-handler', 'lciPaymentData', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lci_payment_nonce'),
            'i18n' => [
                'processingMessage' => __('Processing payment...', 'lci-2025-dashboard'),
                'successMessage' => __('Payment successful!', 'lci-2025-dashboard'),
                'errorMessage' => __('Payment failed. Please try again.', 'lci-2025-dashboard'),
                'bacsInstructions' => __('Please make payment to the following bank account:', 'lci-2025-dashboard'),
            ]
        ]);

        // Create the payment handler JS file if it doesn't exist
        $js_dir = LCI2025_PATH . 'assets/js';
        if (!file_exists($js_dir)) {
            wp_mkdir_p($js_dir);
        }

        $js_file = $js_dir . '/payment-handler.js';
        if (!file_exists($js_file)) {
            $js_content = <<<'EOT'
/**
 * LCI Payment Handler
 */
(function($) {
    'use strict';

    // Payment data
    let paymentData = {
        orderId: null,
        paymentMethod: null
    };

    // Initialize payment handler
    function initPaymentHandler() {
        console.log('Initializing payment handler');

        // Handle payment method selection
        $('input[name="payment_method"]').on('change', function() {
            paymentData.paymentMethod = $(this).val();

            // Show/hide payment instructions
            if (paymentData.paymentMethod === 'bacs') {
                $('#lci-bacs-instructions').hide(); // Hide until order is created
            } else {
                $('#lci-bacs-instructions').hide();
            }
        });

        // Handle form submission
        $('#lci-payment-form').on('submit', function(e) {
            e.preventDefault();

            // Get selected payment method
            const selectedMethod = $('input[name="payment_method"]:checked').val();
            if (!selectedMethod) {
                $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> Please select a payment method.</p></div>');
                return;
            }

            paymentData.paymentMethod = selectedMethod;

            // Process payment
            processPayment();
        });
    }

    // Process payment
    function processPayment() {
        console.log('Processing payment with method:', paymentData.paymentMethod);

        // Show loading state
        const submitButton = $('#lci-payment-form button[type="submit"]');
        submitButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i> ' + lciPaymentData.i18n.processingMessage);
        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-info"><p><i class="fas fa-spinner fa-spin me-2"></i> ' + lciPaymentData.i18n.processingMessage + '</p></div>');

        // Get form data
        const formData = new FormData($('#lci-payment-form')[0]);
        formData.append('action', 'lci_create_order');
        formData.append('security', lciPaymentData.nonce);
        formData.append('payment_method', paymentData.paymentMethod);

        // Send AJAX request to create order
        $.ajax({
            url: lciPaymentData.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    console.log('Order created successfully', response.data);

                    // Store order ID
                    paymentData.orderId = response.data.order_id;

                    // Process payment for the created order
                    processOrderPayment();
                } else {
                    console.error('Error creating order', response.data);
                    $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + (response.data.message || lciPaymentData.i18n.errorMessage) + '</p></div>');
                    submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error', error);
                $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + lciPaymentData.i18n.errorMessage + '</p></div>');
                submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
            }
        });
    }

    // Process payment for created order
    function processOrderPayment() {
        console.log('Processing payment for order:', paymentData.orderId);

        // Send AJAX request to process payment
        $.ajax({
            url: lciPaymentData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lci_process_payment',
                security: lciPaymentData.nonce,
                order_id: paymentData.orderId,
                payment_method: paymentData.paymentMethod
            },
            success: function(response) {
                if (response.success) {
                    console.log('Payment processed successfully', response.data);

                    // Handle different payment methods
                    if (paymentData.paymentMethod === 'bacs') {
                        // Show BACS instructions
                        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-success"><p><i class="fas fa-check-circle me-2"></i> Order created successfully. Please make the bank transfer using the details below.</p></div>');
                        $('#lci-bacs-instructions').html(response.data.instructions).show();

                        // Reset form
                        const submitButton = $('#lci-payment-form button[type="submit"]');
                        submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
                    } else if (response.data.redirect_url) {
                        // Show redirect message
                        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-info"><p><i class="fas fa-info-circle me-2"></i> Redirecting to payment gateway...</p></div>');

                        // Redirect to payment gateway
                        setTimeout(function() {
                            window.location.href = response.data.redirect_url;
                        }, 1000);
                    } else {
                        // Show success message
                        $('#lci-payment-messages').html('<div class="lci-notice lci-notice-success"><p><i class="fas fa-check-circle me-2"></i> ' + lciPaymentData.i18n.successMessage + '</p></div>');

                        // Redirect to success page if provided
                        if (response.data.return_url) {
                            setTimeout(function() {
                                window.location.href = response.data.return_url;
                            }, 1500);
                        }
                    }
                } else {
                    console.error('Error processing payment', response.data);
                    $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + (response.data.message || lciPaymentData.i18n.errorMessage) + '</p></div>');

                    // Reset form
                    const submitButton = $('#lci-payment-form button[type="submit"]');
                    submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error', error);
                $('#lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p><i class="fas fa-exclamation-circle me-2"></i> ' + lciPaymentData.i18n.errorMessage + '</p></div>');

                // Reset form
                const submitButton = $('#lci-payment-form button[type="submit"]');
                submitButton.prop('disabled', false).html('<i class="fas fa-lock me-2"></i> Complete Payment');
            }
        });
    }

    // Initialize on document ready
    $(document).ready(function() {
        initPaymentHandler();
    });
})(jQuery);
EOT;
            file_put_contents($js_file, $js_content);
        }
    }

    /**
     * Render payment instructions container
     */
    public static function render_payment_instructions() {
        // Only render on the payment page
        if (!isset($_GET['tab']) || $_GET['tab'] !== 'payment') {
            return;
        }

        echo '<div id="lci-bacs-instructions" class="lci-payment-instructions" style="display: none; margin-top: 20px; padding: 15px; background-color: #f8f8f8; border: 1px solid #ddd; border-radius: 4px;"></div>';
    }

    /**
     * AJAX: Create order
     */
    public static function ajax_create_order() {
        // Check nonce
        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci_payment_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
            return;
        }

        // Check if payment method is provided
        if (!isset($_POST['payment_method']) || empty($_POST['payment_method'])) {
            wp_send_json_error(['message' => 'Payment method is required.']);
            return;
        }

        $payment_method = sanitize_text_field($_POST['payment_method']);

        try {
            // Get user data
            $user_id = get_current_user_id();

            // Use the existing lci_get_user_data function from payment-processing.php
            if (function_exists('lci_get_user_data')) {
                $user_data = lci_get_user_data($user_id);
            } else {
                wp_send_json_error(['message' => 'User data function not available.']);
                return;
            }

            // Check if we're processing a single product or the cart
            if (isset($_POST['product_id']) && !empty($_POST['product_id'])) {
                // Single product checkout
                $product_id = intval($_POST['product_id']);
                $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;

                $product = wc_get_product($product_id);
                if (!$product) {
                    wp_send_json_error(['message' => 'Invalid product']);
                    return;
                }

                // Create products array
                $products = array(
                    array(
                        'product_id' => $product_id,
                        'quantity' => $quantity,
                        'subtotal' => $product->get_price() * $quantity,
                        'total' => $product->get_price() * $quantity,
                    )
                );
            } else {
                // Cart checkout
                if (!function_exists('WC') || WC()->cart->is_empty()) {
                    wp_send_json_error(['message' => 'Cart is empty']);
                    return;
                }

                // Get products from cart
                $products = array();
                foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                    $products[] = array(
                        'product_id' => $cart_item['product_id'],
                        'quantity' => $cart_item['quantity'],
                        'subtotal' => $cart_item['line_subtotal'],
                        'total' => $cart_item['line_total'],
                    );
                }
            }

            // Create the order
            $order = self::create_custom_order($user_id, $products, $user_data['billing'], $user_data['shipping'], $payment_method);

            // Return success
            wp_send_json_success([
                'order_id' => $order->get_id(),
                'message' => 'Order created successfully.'
            ]);
        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * AJAX: Process payment
     */
    public static function ajax_process_payment() {
        // Check nonce
        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'lci_payment_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
            return;
        }

        // Check required parameters
        if (!isset($_POST['order_id']) || !isset($_POST['payment_method'])) {
            wp_send_json_error(['message' => 'Missing required parameters.']);
            return;
        }

        $order_id = intval($_POST['order_id']);
        $payment_method = sanitize_text_field($_POST['payment_method']);

        // Get the order
        $order = wc_get_order($order_id);
        if (!$order) {
            wp_send_json_error(['message' => 'Invalid order.']);
            return;
        }

        try {
            // Process payment based on method
            if ($payment_method === 'bacs') {
                // Process BACS payment
                $result = self::process_bacs_payment($order);
                wp_send_json_success($result);
            } else {
                // Process other payment methods
                $result = self::process_gateway_payment($order, $payment_method);
                wp_send_json_success($result);
            }
        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Create a custom WooCommerce order
     *
     * @param int $user_id User ID
     * @param array $products Products to add to the order
     * @param array $billing Billing information
     * @param array $shipping Shipping information
     * @param string $payment_method Payment method
     * @return WC_Order The created order
     */
    public static function create_custom_order($user_id, $products, $billing, $shipping, $payment_method) {
        // Create the order
        $order = wc_create_order([
            'customer_id' => $user_id,
            'payment_method' => $payment_method,
        ]);

        // Add products to the order
        foreach ($products as $product_data) {
            $product_id = $product_data['product_id'];
            $quantity = $product_data['quantity'];

            $product = wc_get_product($product_id);
            if (!$product) {
                continue;
            }

            $order->add_product($product, $quantity);
        }

        // Add custom fee if needed
        if (isset($_POST['fee_amount']) && !empty($_POST['fee_amount'])) {
            $fee_amount = floatval($_POST['fee_amount']);
            $fee_name = isset($_POST['fee_name']) ? sanitize_text_field($_POST['fee_name']) : 'Processing Fee';

            $item_fee = new WC_Order_Item_Fee();
            $item_fee->set_name($fee_name);
            $item_fee->set_amount($fee_amount);
            $item_fee->set_total($fee_amount);
            $item_fee->set_tax_status('none');

            $order->add_item($item_fee);
        }

        // Set billing address
        $order->set_billing_first_name($billing['first_name']);
        $order->set_billing_last_name($billing['last_name']);
        $order->set_billing_company($billing['company']);
        $order->set_billing_address_1($billing['address_1']);
        $order->set_billing_address_2($billing['address_2']);
        $order->set_billing_city($billing['city']);
        $order->set_billing_state($billing['state']);
        $order->set_billing_postcode($billing['postcode']);
        $order->set_billing_country($billing['country']);
        $order->set_billing_email($billing['email']);
        $order->set_billing_phone($billing['phone']);

        // Set shipping address
        $order->set_shipping_first_name($shipping['first_name']);
        $order->set_shipping_last_name($shipping['last_name']);
        $order->set_shipping_company($shipping['company']);
        $order->set_shipping_address_1($shipping['address_1']);
        $order->set_shipping_address_2($shipping['address_2']);
        $order->set_shipping_city($shipping['city']);
        $order->set_shipping_state($shipping['state']);
        $order->set_shipping_postcode($shipping['postcode']);
        $order->set_shipping_country($shipping['country']);

        // Set payment method
        $order->set_payment_method($payment_method);

        // Calculate totals
        $order->calculate_totals();

        // Set order status to pending
        $order->update_status('pending', __('Order created via LCI Dashboard.', 'lci-2025-dashboard'));

        // Save the order
        $order->save();

        return $order;
    }

    /**
     * Process BACS payment
     *
     * @param WC_Order $order The order
     * @return array Payment result
     */
    public static function process_bacs_payment($order) {
        // Get BACS gateway
        $gateways = WC()->payment_gateways->payment_gateways();
        if (!isset($gateways['bacs'])) {
            throw new Exception('BACS payment gateway not available.');
        }

        $gateway = $gateways['bacs'];

        // Update order status to on-hold
        $order->update_status('on-hold', __('Awaiting BACS payment.', 'lci-2025-dashboard'));

        // Reduce stock levels
        wc_reduce_stock_levels($order->get_id());

        // Remove cart
        WC()->cart->empty_cart();

        // Get BACS account details
        $bacs_accounts = $gateway->account_details;

        // Format BACS instructions
        $instructions = '<h3>' . __('Bank Account Details', 'lci-2025-dashboard') . '</h3>';
        $instructions .= '<p>' . $gateway->get_option('instructions') . '</p>';

        if (!empty($bacs_accounts)) {
            $instructions .= '<ul class="bacs-accounts">';

            foreach ($bacs_accounts as $account) {
                $instructions .= '<li>';

                if (!empty($account['account_name'])) {
                    $instructions .= '<strong>' . __('Account Name', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['account_name']) . '<br/>';
                }

                if (!empty($account['bank_name'])) {
                    $instructions .= '<strong>' . __('Bank', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['bank_name']) . '<br/>';
                }

                if (!empty($account['account_number'])) {
                    $instructions .= '<strong>' . __('Account Number', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['account_number']) . '<br/>';
                }

                if (!empty($account['sort_code'])) {
                    $instructions .= '<strong>' . __('Sort Code', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['sort_code']) . '<br/>';
                }

                if (!empty($account['iban'])) {
                    $instructions .= '<strong>' . __('IBAN', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['iban']) . '<br/>';
                }

                if (!empty($account['bic'])) {
                    $instructions .= '<strong>' . __('BIC', 'lci-2025-dashboard') . ':</strong> ' . esc_html($account['bic']) . '<br/>';
                }

                $instructions .= '</li>';
            }

            $instructions .= '</ul>';
        }

        // Add reference number
        $instructions .= '<p><strong>' . __('Reference', 'lci-2025-dashboard') . ':</strong> ' . $order->get_order_number() . '</p>';

        // Return payment result
        return [
            'result' => 'success',
            'instructions' => $instructions,
            'return_url' => wc_get_endpoint_url('view-order', $order->get_id(), wc_get_page_permalink('myaccount')),
        ];
    }

    /**
     * Process payment through a gateway
     *
     * @param WC_Order $order The order
     * @param string $payment_method Payment method
     * @return array Payment result
     */
    public static function process_gateway_payment($order, $payment_method) {
        // Get payment gateway
        $gateways = WC()->payment_gateways->payment_gateways();
        if (!isset($gateways[$payment_method])) {
            throw new Exception('Payment gateway not available.');
        }

        $gateway = $gateways[$payment_method];

        // Process the payment
        $result = $gateway->process_payment($order->get_id());

        if ($result['result'] !== 'success') {
            throw new Exception('Payment processing failed.');
        }

        // Return payment result
        return [
            'result' => 'success',
            'redirect_url' => $result['redirect'],
            'return_url' => wc_get_endpoint_url('view-order', $order->get_id(), wc_get_page_permalink('myaccount')),
        ];
    }
}

// Initialize the handler
LCI_Payment_Handler::init();
