<?php
// This script cleans up console logs from PHP files

// Function to clean console logs from a file
function clean_console_logs($file_path) {
    if (!file_exists($file_path)) {
        echo "File not found: $file_path\n";
        return false;
    }

    // Read the file content
    $content = file_get_contents($file_path);
    if ($content === false) {
        echo "Failed to read file: $file_path\n";
        return false;
    }

    // Count original console logs
    $original_count = preg_match_all('/console\.(log|error|warn|info|debug)\s*\(/i', $content, $matches);

    // Replace console.log statements with comments
    $new_content = preg_replace('/console\.log\s*\([^;]*\);/i', '// Console log removed', $content);
    
    // Replace console.error statements with comments
    $new_content = preg_replace('/console\.error\s*\([^;]*\);/i', '// Console error removed', $new_content);
    
    // Replace console.warn statements with comments
    $new_content = preg_replace('/console\.warn\s*\([^;]*\);/i', '// Console warning removed', $new_content);
    
    // Replace console.info statements with comments
    $new_content = preg_replace('/console\.info\s*\([^;]*\);/i', '// Console info removed', $new_content);
    
    // Replace console.debug statements with comments
    $new_content = preg_replace('/console\.debug\s*\([^;]*\);/i', '// Console debug removed', $new_content);

    // Count remaining console logs
    $remaining_count = preg_match_all('/console\.(log|error|warn|info|debug)\s*\(/i', $new_content, $matches);

    // Write the modified content back to the file
    if (file_put_contents($file_path, $new_content) === false) {
        echo "Failed to write to file: $file_path\n";
        return false;
    }

    echo "Cleaned $file_path: Removed " . ($original_count - $remaining_count) . " console logs, " . $remaining_count . " remaining\n";
    return true;
}

// Get all PHP files in the current directory and subdirectories
$files = [];
$directory = new RecursiveDirectoryIterator('.');
$iterator = new RecursiveIteratorIterator($directory);
foreach ($iterator as $file) {
    if ($file->isFile() && $file->getExtension() === 'php') {
        $files[] = $file->getPathname();
    }
}

// Clean console logs from each file
$total_cleaned = 0;
foreach ($files as $file) {
    // Skip the current script
    if (basename($file) === basename(__FILE__)) {
        continue;
    }
    
    if (clean_console_logs($file)) {
        $total_cleaned++;
    }
}

echo "Cleaned console logs from $total_cleaned files\n";
?>
