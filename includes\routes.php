<?php

add_shortcode('lci2025_auth', 'lci2025_render_auth');
add_shortcode('lci2025_dashboard', 'lci2025_render_dashboard');
add_shortcode('lci2025_verify_invitation', 'lci2025_render_verify_invitation');
add_shortcode('lci2025_generate_invitation', 'lci2025_render_generate_invitation');

function lci2025_render_auth() {
    ob_start();
    include LCI2025_PATH . 'templates/auth.php';
    return ob_get_clean();
}

function lci2025_render_dashboard() {
    if (!is_user_logged_in()) {
        return do_shortcode('[lci2025_auth]');
    }

    ob_start();
    include LCI2025_PATH . 'templates/dashboard.php';

    // Load specific view
    $tab = $_GET['tab'] ?? 'my-registration';
    //if ($tab === 'profile') {
    //    include LCI2025_PATH . 'views/profile.php';
    //}
    return ob_get_clean();
}

/**
 * Render the invitation verification page
 */
function lci2025_render_verify_invitation() {
    ob_start();
    include LCI2025_PATH . 'views/verify-invitation.php';
    return ob_get_clean();
}

/**
 * Render the generate invitation letter page
 */
function lci2025_render_generate_invitation() {
    if (!is_user_logged_in()) {
        return do_shortcode('[lci2025_auth]');
    }

    ob_start();
    include LCI2025_PATH . 'views/generate-invitation.php';
    return ob_get_clean();
}

add_action('wp_enqueue_scripts', function () {
    // CSS files
    wp_enqueue_style('lci-bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css');
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css');
    wp_enqueue_style('tailwind-css', 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css');
    wp_enqueue_style('lci-custom', LCI2025_URL . 'assets/style.css', [], filemtime(LCI2025_PATH . 'assets/style.css'));
    wp_enqueue_style('lci-alpine-components', LCI2025_URL . 'assets/css/alpine-components.css', [], filemtime(LCI2025_PATH . 'assets/css/alpine-components.css'));

    // JavaScript files
    wp_enqueue_script('bootstrap-js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js', [], '5.3.3', true);
    wp_enqueue_script('jsbarcode', 'https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js', [], '3.11.5', true);

    // Modal components
    wp_enqueue_script('lci-modal-components', LCI2025_URL . 'assets/js/alpine-components.js', [], filemtime(LCI2025_PATH . 'assets/js/alpine-components.js'), true);

    // Custom scripts
    wp_enqueue_script('lci-profile-js', LCI2025_URL . 'assets/profile.js', ['jquery', 'bootstrap-js', 'lci-modal-components'], filemtime(LCI2025_PATH . 'assets/profile.js'), true);
    wp_enqueue_script('lci-auth', LCI2025_URL . 'assets/auth.js', ['jquery', 'lci-modal-components'], filemtime(LCI2025_PATH . 'assets/auth.js'), true);
    wp_enqueue_script('lci-modal-fix', LCI2025_URL . 'assets/js/modal-fix.js', ['jquery', 'bootstrap-js'], filemtime(LCI2025_PATH . 'assets/js/modal-fix.js'), true);
    wp_enqueue_script('lci-mini-cart-init', LCI2025_URL . 'assets/js/mini-cart-init.js', ['jquery'], filemtime(LCI2025_PATH . 'assets/js/mini-cart-init.js'), true);

    // Load PHP-based accommodation wizard
    if (isset($_GET['tab']) && $_GET['tab'] === 'accommodation-wizard-php') {
        // No special scripts needed for the PHP-based wizard
    }

    // Only load tours-mini-cart.js on the tours page
    if (isset($_GET['tab']) && $_GET['tab'] === 'tours') {
        wp_enqueue_script('lci-tours-mini-cart', LCI2025_URL . 'assets/js/tours-mini-cart.js', ['jquery'], filemtime(LCI2025_PATH . 'assets/js/tours-mini-cart.js'), true);

        // Localize the mini cart nonce for tours-mini-cart.js
        wp_localize_script('lci-tours-mini-cart', 'lci_ajax_object', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lci_mini_cart_nonce')
        ]);

        wp_enqueue_script('lci-tours', LCI2025_URL . 'assets/tours.js', ['jquery', 'bootstrap-js', 'lci-modal-components', 'lci-modal-fix', 'lci-tours-mini-cart'], filemtime(LCI2025_PATH . 'assets/tours.js'), true);
    } else {
        wp_enqueue_script('lci-tours', LCI2025_URL . 'assets/tours.js', ['jquery', 'bootstrap-js', 'lci-modal-components', 'lci-modal-fix'], filemtime(LCI2025_PATH . 'assets/tours.js'), true);
    }

    // Load payment styles and scripts on the payment page
    if (isset($_GET['tab']) && $_GET['tab'] === 'payment') {
        wp_enqueue_style('lci-payment-styles', LCI2025_URL . 'assets/css/payment.css', [], filemtime(LCI2025_PATH . 'assets/css/payment.css'));

        // Enqueue WooCommerce checkout scripts if needed
        if (function_exists('WC')) {
            wp_enqueue_script('wc-checkout');
        }
    }

    wp_enqueue_script('lci-regalia-shop', LCI2025_URL . 'assets/regalia-shop.js', ['jquery', 'bootstrap-js', 'lci-modal-components', 'lci-modal-fix', 'lci-mini-cart-init'], filemtime(LCI2025_PATH . 'assets/regalia-shop.js'), true);

    // Localize scripts
    $ajax_data = [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('lci-dashboard-add-to-cart-nonce'),
        'payment_nonce' => wp_create_nonce('lci_payment_nonce'),
    ];

    wp_localize_script('lci-auth', 'lci_ajax', $ajax_data);
    wp_localize_script('lci-profile-js', 'lci_ajax', $ajax_data);
    wp_localize_script('lci-regalia-shop', 'lci_ajax', $ajax_data);
    wp_localize_script('lci-tours', 'lci_ajax', $ajax_data);
});
