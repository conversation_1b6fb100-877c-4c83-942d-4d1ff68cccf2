<?php
/**
 * User Data Helper Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get user data for creating orders
 * 
 * @param int $user_id User ID
 * @return array User data
 */
function lci_get_user_data($user_id) {
    $user = get_userdata($user_id);
    
    if (!$user) {
        return [
            'billing' => [],
            'shipping' => []
        ];
    }
    
    // Get user meta
    $first_name = get_user_meta($user_id, 'first_name', true);
    $last_name = get_user_meta($user_id, 'last_name', true);
    $billing_first_name = get_user_meta($user_id, 'billing_first_name', true);
    $billing_last_name = get_user_meta($user_id, 'billing_last_name', true);
    $billing_company = get_user_meta($user_id, 'billing_company', true);
    $billing_address_1 = get_user_meta($user_id, 'billing_address_1', true);
    $billing_address_2 = get_user_meta($user_id, 'billing_address_2', true);
    $billing_city = get_user_meta($user_id, 'billing_city', true);
    $billing_state = get_user_meta($user_id, 'billing_state', true);
    $billing_postcode = get_user_meta($user_id, 'billing_postcode', true);
    $billing_country = get_user_meta($user_id, 'billing_country', true);
    $billing_email = get_user_meta($user_id, 'billing_email', true);
    $billing_phone = get_user_meta($user_id, 'billing_phone', true);
    
    $shipping_first_name = get_user_meta($user_id, 'shipping_first_name', true);
    $shipping_last_name = get_user_meta($user_id, 'shipping_last_name', true);
    $shipping_company = get_user_meta($user_id, 'shipping_company', true);
    $shipping_address_1 = get_user_meta($user_id, 'shipping_address_1', true);
    $shipping_address_2 = get_user_meta($user_id, 'shipping_address_2', true);
    $shipping_city = get_user_meta($user_id, 'shipping_city', true);
    $shipping_state = get_user_meta($user_id, 'shipping_state', true);
    $shipping_postcode = get_user_meta($user_id, 'shipping_postcode', true);
    $shipping_country = get_user_meta($user_id, 'shipping_country', true);
    
    // Use user data as fallback
    if (empty($billing_first_name)) $billing_first_name = $first_name;
    if (empty($billing_last_name)) $billing_last_name = $last_name;
    if (empty($billing_email)) $billing_email = $user->user_email;
    
    if (empty($shipping_first_name)) $shipping_first_name = $billing_first_name;
    if (empty($shipping_last_name)) $shipping_last_name = $billing_last_name;
    if (empty($shipping_address_1)) $shipping_address_1 = $billing_address_1;
    if (empty($shipping_address_2)) $shipping_address_2 = $billing_address_2;
    if (empty($shipping_city)) $shipping_city = $billing_city;
    if (empty($shipping_state)) $shipping_state = $billing_state;
    if (empty($shipping_postcode)) $shipping_postcode = $billing_postcode;
    if (empty($shipping_country)) $shipping_country = $billing_country;
    
    // Default country if empty
    if (empty($billing_country)) $billing_country = 'RO';
    if (empty($shipping_country)) $shipping_country = 'RO';
    
    // Return user data
    return [
        'billing' => [
            'first_name' => $billing_first_name,
            'last_name' => $billing_last_name,
            'company' => $billing_company,
            'address_1' => $billing_address_1,
            'address_2' => $billing_address_2,
            'city' => $billing_city,
            'state' => $billing_state,
            'postcode' => $billing_postcode,
            'country' => $billing_country,
            'email' => $billing_email,
            'phone' => $billing_phone,
        ],
        'shipping' => [
            'first_name' => $shipping_first_name,
            'last_name' => $shipping_last_name,
            'company' => $shipping_company,
            'address_1' => $shipping_address_1,
            'address_2' => $shipping_address_2,
            'city' => $shipping_city,
            'state' => $shipping_state,
            'postcode' => $shipping_postcode,
            'country' => $shipping_country,
        ],
    ];
}
