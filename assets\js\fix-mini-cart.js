/**
 * Fix for mini-cart duplication issues
 */
(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        // Flag to track if we're currently updating the cart
        let isUpdatingCart = false;

        // Debounce function to prevent multiple rapid calls
        function debounce(func, wait) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                    func.apply(context, args);
                }, wait);
            };
        }

        // Function to handle cart updates
        function handleCartUpdate() {
            // If we're already updating, don't trigger another update
            if (isUpdatingCart) {
                console.log('Cart update already in progress, skipping duplicate update');
                return;
            }

            // Set flag to indicate we're updating
            isUpdatingCart = true;

            // Get the mini-cart container
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (miniCartContainer && miniCartContainer.__x) {
                try {
                    // Get Alpine.js data
                    const data = miniCartContainer.__x.getUnobservedData();

                    // Clear items first to prevent duplicates
                    data.items = [];

                    // Only fetch items once
                    setTimeout(() => {
                        data.fetchItems();
                        console.log('Mini-cart update triggered');
                    }, 50);
                } catch (error) {
                    console.error('Error updating mini-cart:', error);
                }
            }

            // Reset the flag after a delay
            setTimeout(function() {
                isUpdatingCart = false;
            }, 500);
        }

        // Debounced version of the handler
        const debouncedCartUpdate = debounce(handleCartUpdate, 300);

        // Listen for cart updated events
        window.addEventListener('lci:cartUpdated', debouncedCartUpdate);

        // Also listen for WooCommerce events
        document.body.addEventListener('added_to_cart', debouncedCartUpdate);
        document.body.addEventListener('removed_from_cart', debouncedCartUpdate);
        document.body.addEventListener('updated_cart_totals', debouncedCartUpdate);

        // Patch the mini-cart's fetchItems method to prevent duplicate calls
        function patchMiniCartFetchItems() {
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (miniCartContainer && miniCartContainer.__x) {
                try {
                    // Get Alpine.js data
                    const data = miniCartContainer.__x.getUnobservedData();

                    // Store the original fetchItems method
                    const originalFetchItems = data.fetchItems;

                    // Replace with debounced version
                    data.fetchItems = debounce(function() {
                        // Only fetch if we're not already updating
                        if (!isUpdatingCart) {
                            isUpdatingCart = true;

                            // Clear items first to prevent duplicates
                            data.items = [];

                            // Call original method after a short delay
                            setTimeout(() => {
                                originalFetchItems.call(data);

                                // Reset the flag after a delay
                                setTimeout(() => {
                                    isUpdatingCart = false;
                                }, 500);
                            }, 50);
                        } else {
                            console.log('Fetch already in progress, skipping duplicate fetch');
                        }
                    }, 300);

                    // Also patch the items array to ensure it's always unique
                    if (!Object.getOwnPropertyDescriptor(data, 'items').__patched) {
                        // Store the original descriptor
                        const descriptor = Object.getOwnPropertyDescriptor(data, 'items');
                        const originalSetter = descriptor.set;

                        // Create a new descriptor with a patched setter
                        Object.defineProperty(data, 'items', {
                            get: descriptor.get,
                            set: function(newItems) {
                                // Deduplicate items by key
                                const uniqueItems = [];
                                const keys = new Set();

                                // Only add items with unique keys
                                (newItems || []).forEach(item => {
                                    if (item && item.key && !keys.has(item.key)) {
                                        keys.add(item.key);
                                        uniqueItems.push(item);
                                    }
                                });

                                // Call the original setter with the deduplicated items
                                if (originalSetter) {
                                    originalSetter.call(this, uniqueItems);
                                } else {
                                    this._items = uniqueItems;
                                }
                            },
                            enumerable: descriptor.enumerable,
                            configurable: descriptor.configurable
                        });

                        // Mark as patched
                        Object.getOwnPropertyDescriptor(data, 'items').__patched = true;
                    }

                    console.log('Mini-cart fetchItems method patched');
                } catch (error) {
                    console.error('Error patching mini-cart fetchItems method:', error);
                }
            }
        }

        // Apply the patch after a short delay to ensure Alpine.js is initialized
        setTimeout(patchMiniCartFetchItems, 1000);

        // Also apply the patch periodically to catch any late initializations
        setInterval(patchMiniCartFetchItems, 5000);

        // Add a more aggressive fix for duplicate items
        function fixDuplicateItems() {
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (miniCartContainer && miniCartContainer.__x) {
                try {
                    // Get Alpine.js data
                    const data = miniCartContainer.__x.getUnobservedData();

                    // Deduplicate items by key
                    const uniqueItems = [];
                    const keys = new Set();

                    // Only add items with unique keys
                    (data.items || []).forEach(item => {
                        if (item && item.key && !keys.has(item.key)) {
                            keys.add(item.key);
                            uniqueItems.push(item);
                        }
                    });

                    // Only update if we found duplicates
                    if (uniqueItems.length !== data.items.length) {
                        console.log(`Fixed duplicate items: ${data.items.length} -> ${uniqueItems.length}`);
                        data.items = uniqueItems;
                    }
                } catch (error) {
                    console.error('Error fixing duplicate items:', error);
                }
            }
        }

        // Apply the fix periodically
        setInterval(fixDuplicateItems, 1000);
    });
})();
