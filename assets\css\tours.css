/**
 * Tours View Styles
 */

/* Container */
.tours-container {
    padding: 0;
}

/* Scroll Fix */
html, body {
    overflow-y: auto !important;
    overflow-x: hidden;
    padding-right: 0 !important;
    margin-right: 0 !important;
    height: auto !important;
    position: relative !important;
}

/* Fix for modals */
.modal-open {
    overflow: auto !important;
    overflow-y: scroll !important;
    padding-right: 0 !important;
    position: relative !important;
    height: auto !important;
}

/* Fix for scrollbars in modals */
.modal-content {
    overflow-y: auto;
    max-height: 90vh;
}

/* Override any BeTheme styles */
html.mfn-hidden-scrollbar,
html.mfn-cart-showing,
html.mfn-active-cart,
html.mfn-cart-opened,
html.loaded.mfn-hidden-scrollbar,
html.loaded.mfn-cart-showing,
html.loaded.mfn-active-cart,
html.loaded.mfn-cart-opened,
html.mfn-hidden-scrollbar.loaded,
html.mfn-cart-showing.loaded,
html.mfn-active-cart.loaded,
html.mfn-cart-opened.loaded {
    margin-right: 0 !important;
    overflow: auto !important;
    overflow-y: scroll !important;
    padding-right: 0 !important;
}

/* Override Bootstrap modal styles */
body.modal-open {
    overflow: auto !important;
    overflow-y: scroll !important;
    padding-right: 0 !important;
}

/* Fix for fixed position elements */
.fixed-top, .fixed-bottom {
    padding-right: 0 !important;
}

/* Modern Section Headings - 2025 UX/UI Trends */
.tours-section-heading {
    position: relative;
    text-align: center;
    padding: 1.5rem 0;
    margin: 2rem 0;
}

.tours-section-heading h3 {
    font-size: 1.8rem;
    font-weight: 800;
    letter-spacing: 1.5px;
    color: #333;
    margin: 0;
    position: relative;
    display: inline-block;
    padding: 0 1rem;
    background: linear-gradient(to right, transparent, #fff, transparent);
}

.tours-section-heading .heading-underline {
    position: relative;
    height: 4px;
    width: 80px;
    background: linear-gradient(90deg, var(--primary-color), #fab33a);
    margin: 0.8rem auto 0;
    border-radius: 2px;
    transition: width 0.6s ease;
}

/* Wow Effect Animation */
.wow-heading {
    opacity: 0;
    animation: fadeInUp 0.8s ease forwards;
    animation-delay: 0.2s;
}

.wow-heading:hover .heading-underline {
    width: 120px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mini Cart Styling */
.mini-cart-wrapper {
    display: flex;
    justify-content: flex-end;
}

.mini-cart-wrapper .btn,
#mini-cart-button,
#tours-mini-cart-button {
    background-color: #36b1dc !important;
    border-color: #36b1dc !important;
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mini-cart-wrapper .btn:hover,
#mini-cart-button:hover,
#tours-mini-cart-button:hover {
    background-color: #2a8fb3 !important;
    border-color: #2a8fb3 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Ensure mini-cart button is visible on mobile */
.mini-cart-wrapper .btn,
#mini-cart-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center;
    min-width: 120px;
    visibility: visible !important;
    opacity: 1 !important;
}

.tours-header-title {
    width: 100%;
    text-align: center;
}

@media (min-width: 768px) {
    .tours-header {
        flex-direction: row;
        justify-content: space-between;
    }

    .tours-header-cart {
        order: 2;
        width: auto;
        margin-bottom: 0;
    }

    .tours-header-title {
        order: 1;
        width: auto;
        text-align: left;
    }
}

/* Products Grid */
.tours-products-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (min-width: 768px) {
    .tours-products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .tours-products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Product Card */
.tours-products-grid {
    position: relative;
}

.tours-products-grid::before {
    content: '';
    position: absolute;
    top: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(54, 177, 220, 0.2) 0%, rgba(54, 177, 220, 0.05) 50%, transparent 70%);
    border-radius: 50%;
    filter: blur(40px);
    z-index: -1;
}

.tours-products-grid::after {
    content: '';
    position: absolute;
    bottom: -50px;
    right: -50px;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(250, 179, 58, 0.15) 0%, rgba(250, 179, 58, 0.05) 50%, transparent 70%);
    border-radius: 50%;
    filter: blur(50px);
    z-index: -1;
}

.tours-product-card {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 0.75rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    cursor: pointer;
}

.tours-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #36b1dc, #2d93b7);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tours-product-card:hover {
    transform: scale(1.03);
    box-shadow: 0 12px 24px rgba(54, 177, 220, 0.2);
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.tours-product-card:hover::before {
    opacity: 1;
}

/* Product Image */
.tours-product-image {
    position: relative;
    padding-bottom: 100%; /* 1:1 Aspect Ratio */
    overflow: hidden;
}

.tours-product-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    padding: 0;
    transition: transform 0.3s ease;
}

.tours-product-card:hover .tours-product-image img {
    transform: scale(1.05);
}

/* Badges */
.tours-badges-left {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    z-index: 10;
}

.tours-badge-tour-type {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #2a8fb3 100%);
    color: white;
    padding: 6px 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3);
    letter-spacing: 0.5px;
    text-transform: uppercase;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
}

.tours-badge-tour-type::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    animation: shimmer 2s infinite;
    background-size: 200% 100%;
    border-radius: 6px;
}

.tours-badge-tour-type.pretour {
    background: linear-gradient(135deg, #fab33a 0%, #f59f0a 100%);
    box-shadow: 0 4px 15px rgba(250, 179, 58, 0.3);
}

.tours-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.85rem;
    border-radius: 30px;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    color: white;
    margin-bottom: 6px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    letter-spacing: 0.3px;
    text-transform: uppercase;
}

.tours-badge i {
    margin-right: 5px;
    font-size: 0.8rem;
}

.tours-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.tours-badge-sale {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.tours-badge-featured {
    background: linear-gradient(135deg, #36b1dc 0%, #2a8fb3 100%);
}

/* Product Content */
.tours-product-content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    position: relative;
}

.tours-product-title {
    font-weight: 600;
    color: #1a202c;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    line-height: 1.5;
    position: relative;
    padding-bottom: 0.75rem;
}

.tours-product-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background-color: #36b1dc;
    transition: width 0.3s ease;
}

.tours-product-card:hover .tours-product-title::after {
    width: 80px;
}

.tours-product-description {
    color: #666;
    margin-bottom: 1rem;
    flex-grow: 1;
    font-size: 0.95rem;
    line-height: 1.5;
    text-align: justify;
}

.tours-product-dates {
    margin-top: 12px;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: white;
    border-radius: 10px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
}

.tours-product-dates:hover {
    background: #f9f9f9;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.tours-product-dates i {
    color: var(--primary-color);
    margin-right: 10px;
    font-size: 1rem;
}

.tours-product-variants {
    font-size: 0.95rem;
    color: #36b1dc;
    text-align: center;
    margin-top: 0.5rem;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

/* Product Actions */
.tours-product-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    gap: 10px;
}

.tours-product-price {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    width: 30%;
    text-align: left;
}

.tours-price-regular {
    color: #36b1dc;
    font-weight: 700;
    font-size: 1.1rem;
    line-height: 2rem;
}

.tours-price-sale {
    display: flex;
    flex-direction: column;
}

.tours-price-new {
    color: #ff6b6b;
}

.tours-price-old {
    text-decoration: line-through;
    font-size: 0.85rem;
    color: #999;
}

/* Buttons */
.tours-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

/* Added to cart message */
.tours-added-to-cart {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0.75rem;
    background-color: rgba(54, 177, 220, 0.1);
    border-radius: 6px;
    color: #36b1dc;
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
    animation: fadeIn 0.3s ease-in-out;
}

.tours-added-to-cart i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.tours-btn-primary {
    background-color: #36b1dc;
    color: white;
    width: 70%;
}

.tours-btn-primary i,
.tours-btn-primary span {
    color: #fff !important;
}

.tours-btn-primary:hover {
    background-color: #2a8fb3;
}

.tours-btn-outline {
    background-color: transparent;
    border: 1px solid #36b1dc;
    color: #36b1dc;
}

.tours-btn-outline:hover {
    background-color: #f0f9fd;
}

.tours-btn-disabled {
    background-color: #e0e0e0;
    color: #999;
    cursor: not-allowed;
}

/* Variation Card */
.tours-variation-card,
.tours-quantity-card {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    z-index: 10;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
}

.tours-variation-card.active,
.tours-quantity-card.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.tours-variation-header,
.tours-quantity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.tours-variation-header h4,
.tours-quantity-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.close-variation-card,
.close-quantity-card {
    background: none;
    border: none;
    font-size: 1.1rem;
    color: #999;
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tours-variation-options,
.tours-quantity-options {
    flex-grow: 1;
    margin-bottom: 1rem;
}

.tours-variation-option {
    margin-bottom: 1rem;
}

.tours-variation-option label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.tours-variation-option select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.tours-variation-option select.error {
    border-color: #ff6b6b;
}

.tours-quantity {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
    max-width: 150px;
}

.tours-quantity button {
    background: #f5f5f5;
    border: none;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.tours-quantity input {
    flex-grow: 1;
    border: none;
    text-align: center;
    padding: 0.5rem;
    width: 50px;
    font-size: 0.9rem;
}

.tours-variation-actions,
.tours-quantity-actions {
    margin-top: auto;
}

/* Modern Tour Experiences Section - 2025 UX/UI Trends */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes pulse {
    0% { opacity: 0.5; transform: scale(0.95); }
    50% { opacity: 0.8; transform: scale(1.05); }
    100% { opacity: 0.5; transform: scale(0.95); }
}

@keyframes shimmer {
    0% { background-position: -100% 0; }
    100% { background-position: 200% 0; }
}

@keyframes shadowPulse {
    0% { box-shadow: 0 8px 20px rgba(54, 177, 220, 0.1); }
    50% { box-shadow: 0 12px 30px rgba(54, 177, 220, 0.2); }
    100% { box-shadow: 0 8px 20px rgba(54, 177, 220, 0.1); }
}

@keyframes rise {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(54, 177, 220, 0.1), 0 8px 20px rgba(54, 177, 220, 0.1); }
    50% { box-shadow: 0 0 10px rgba(54, 177, 220, 0.2), 0 10px 25px rgba(54, 177, 220, 0.2); }
    100% { box-shadow: 0 0 5px rgba(54, 177, 220, 0.1), 0 8px 20px rgba(54, 177, 220, 0.1); }
}

.tours-experiences-container {
    position: relative;
    margin-bottom: 2rem;
    --primary-color: #36b1dc;
    --primary-light: rgba(54, 177, 220, 0.1);
    --primary-medium: rgba(54, 177, 220, 0.3);
    --primary-dark: #2a8fb3;
    --accent-color: #fab33a;
    --text-dark: #333333;
    --text-medium: #666666;
    --text-light: #999999;
    --success-color: #28a745;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
}

.tours-experiences-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.tours-experiences-list {
    perspective: 1000px;
}

.tours-experience-card {
    position: relative;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    z-index: 1;
}

/* Tour Type Ribbon - 2025 UX Design Trends */
.tour-type-ribbon {
    position: absolute;
    top: 15px;
    left: -10px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #2a8fb3 100%);
    color: white;
    padding: 6px 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3);
    letter-spacing: 0.5px;
    text-transform: uppercase;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
}

.tour-type-ribbon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    animation: shimmer 2s infinite;
    background-size: 200% 100%;
    border-radius: 6px;
}

.tour-type-ribbon.pretour {
    background: linear-gradient(135deg, #fab33a 0%, #f59f0a 100%);
    box-shadow: 0 4px 15px rgba(250, 179, 58, 0.3);
}

/* Tour Dates Display - 2025 UX Design Trends */
.tour-dates {
    margin-top: 12px;
    padding: 5px 10px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

.tour-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
}

.tour-dates i {
    color: var(--primary-color);
    margin-right: 10px;
    font-size: 1rem;
}

.tour-short-description {
    margin: 12px 0;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    color: #555;
    line-height: 1.5;
    position: relative;
    border-radius: 8px;
    transition: all 0.3s ease;
    background-color: rgba(54, 177, 220, 0.05);
    max-height: 100px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color, #36b1dc) #f0f0f0;
}

.tour-description {
    max-height: 200px;
    overflow-y: auto;
    padding: 0.75rem 1rem;
    background-color: rgba(54, 177, 220, 0.05);
    border-radius: 8px;
    font-size: 0.9rem;
    color: #555;
    line-height: 1.5;
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color, #36b1dc) #f0f0f0;
    margin-top: 1rem;
}

.tour-description h6 {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.tours-experience-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.tours-experience-blur-circle {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    z-index: -1;
    animation: pulse 8s infinite ease-in-out;
}

.tours-experience-blur-circle-1 {
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(54, 177, 220, 0.5) 0%, rgba(54, 177, 220, 0.2) 70%, rgba(54, 177, 220, 0) 100%);
    top: -70px;
    left: -70px;
    animation-delay: 0s;
    filter: blur(30px);
}

.tours-experience-blur-circle-2 {
    width: 220px;
    height: 220px;
    background: radial-gradient(circle, rgba(250, 179, 58, 0.4) 0%, rgba(250, 179, 58, 0.1) 70%, rgba(250, 179, 58, 0) 100%);
    bottom: -100px;
    right: -100px;
    animation-delay: -4s;
    filter: blur(40px);
}

.tours-experience-content {
    display: flex;
    padding: .7rem;
    gap: 1rem;
    position: relative;
    z-index: 2;
    align-items: center;
    justify-content: center;
}

.tours-experience-image {
    flex: 0 0 180px;
    height: 180px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.tours-experience-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.tours-experience-card:hover .tours-experience-image img {
    transform: scale(1.1);
}

.tours-experience-no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e6f7ff 0%, #cceeff 100%);
    color: var(--primary-color);
    font-size: 3rem;
}

.tours-experience-status {
    position: relative;
    padding: 0.4rem 0.8rem;
    border-radius: 30px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.tours-experience-status-completed {
    background-color: rgba(40, 167, 69, 0.2);
    color: #155724;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.tours-experience-status-processing {
    background-color: rgba(13, 202, 240, 0.2);
    color: #055160;
    border: 1px solid rgba(13, 202, 240, 0.3);
}

.tours-experience-status-on-hold {
    background-color: rgba(255, 193, 7, 0.2);
    color: #664d03;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.tours-experience-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.tours-experience-header {
    margin-bottom: 0.2rem;
    text-align: center;
    width: 100%;
}

.tours-experience-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
    position: relative;
    display: inline-block;
}

.tours-experience-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    transition: width 0.3s ease;
}

.tours-experience-card:hover .tours-experience-title::after {
    width: 100%;
}



.tours-experience-info {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: center;
}

.tours-experience-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    color: var(--text-medium);
    background-color: rgba(255, 255, 255, 0.7);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tours-experience-info-item i {
    color: var(--primary-color);
    font-size: 1rem;
}

.tours-experience-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.6rem 1.2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    background-color: var(--primary-color);
    color: #fff !important;
    box-shadow: 0 4px 10px rgba(54, 177, 220, 0.3);
    text-decoration: none;
    width: 100%;
}

.tours-experience-btn i,
.tours-experience-btn span {
    color: #fff !important;
}

.tours-experience-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(54, 177, 220, 0.4);
    color: #fff !important;
    text-decoration: none;
}

.tours-experience-btn-secondary {
    background-color: transparent;
    color: #fff;
    border: 2px solid var(--primary-color);
    box-shadow: none;
    background-color: var(--primary-color);
}

.tours-experience-btn-secondary:hover {
    background-color: var(--primary-dark);
    color: #fff;
    border-color: var(--primary-dark);
}

.tours-experience-expanded {
    background-color: rgba(246, 249, 252, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-top: 1px solid rgba(54, 177, 220, 0.1);
    display: none; /* Hide by default, Alpine.js will show it */
    overflow: hidden;
    max-height: 0;
    transition: all 0.4s ease-out;
}

.tours-experience-expanded-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.tours-experience-expanded-section {
    background-color: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.tours-experience-expanded-section h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
}

.tours-experience-expanded-section h5 i {
    color: var(--primary-color);
}

.tours-experience-expanded-section p {
    color: var(--text-medium);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

.tours-experience-expanded-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.tours-experience-expanded-info-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.95rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.tours-experience-expanded-info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.tours-experience-expanded-info-label {
    color: var(--text-medium);
}

.tours-experience-expanded-info-value {
    font-weight: 600;
    color: var(--text-dark);
}

.tours-experience-qr {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.tours-experience-qr-code {
    width: 100px;
    height: 100px;
    background-color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    animation: float 6s ease-in-out infinite;
}

.tours-experience-qr-code::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(90deg,
        rgba(255,255,255,0) 0%,
        rgba(255,255,255,0.2) 50%,
        rgba(255,255,255,0) 100%);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
    background-size: 50% 100%;
}

.tours-experience-qr-code::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 8px;
    padding: 2px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0.7;
    animation: pulse 4s ease-in-out infinite;
}

.tours-experience-qr-text {
    flex: 1;
}

.tours-experience-qr-text p {
    margin-bottom: 0.5rem;
}

.tours-experience-qr-id {
    font-size: 0.85rem;
    color: var(--text-light);
    font-family: monospace;
}

.tours-experiences-footer {
    margin-top: 1.5rem;
    text-align: center;
}

.tours-experiences-tip {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-light);
    border-radius: 30px;
    font-size: 0.9rem;
    color: var(--primary-dark);
}

.tours-experiences-tip i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

/* Alpine.js Transitions */
.transition {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, max-height;
}

.duration-200 {
    transition-duration: 200ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.duration-400 {
    transition-duration: 400ms;
}

.ease-in {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.transform {
    transform: var(--transform-translate-x, 0) var(--transform-translate-y, 0) var(--transform-rotate, 0) var(--transform-skew-x, 0) var(--transform-skew-y, 0) var(--transform-scale-x, 1) var(--transform-scale-y, 1);
}

.opacity-0 {
    opacity: 0;
}

.opacity-100 {
    opacity: 1;
}

.max-h-0 {
    max-height: 0;
}

.max-h-1000 {
    max-height: 1000px;
}

.overflow-hidden {
    overflow: hidden;
}

.-translate-y-4 {
    --transform-translate-y: -1rem;
}

.translate-y-0 {
    --transform-translate-y: 0;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .tours-experience-content {
        flex-direction: column;
    }

    .tours-experience-image {
        width: 100%;
        height: 220px;
        flex: none;
    }

    .tours-experience-expanded-content {
        grid-template-columns: 1fr;
    }

    .tours-experience-title {
        text-align: center;
        display: block;
        width: 100%;
    }

    .tours-experience-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
}

@media (max-width: 576px) {
    /* Mobile heading styles */
    .tours-section-heading h3 {
        font-size: 1.4rem;
        letter-spacing: 1px;
    }

    .tours-section-heading {
        padding: 1rem 0;
        margin: 1.5rem 0;
    }

    .tours-experience-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .tours-experience-actions {
        flex-direction: column;
    }

    .tours-experience-qr {
        flex-direction: column;
        text-align: center;
    }

    .tours-experience-qr-code {
        margin: 0 auto;
    }

    .tour-info-row {
        flex-direction: column;
        gap: 10px;
    }

    .tours-experience-status {
        align-self: center;
        margin-top: 5px;
        width: 100%;
        justify-content: center;
    }

    .tour-dates {
        width: 100%;
        justify-content: center;
    }

    /* Mini cart fixes for mobile */
    .mini-cart-wrapper {
        display: block !important;
        width: 100% !important;
        margin-bottom: 15px;
    }

    #mini-cart-button {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        width: 100% !important;
        justify-content: center !important;
    }

    /* Ensure the header layout works on mobile */
    .d-flex.justify-content-between.align-items-center.mb-4 {
        flex-direction: column-reverse;
        text-align: center;
    }

    .d-flex.justify-content-between.align-items-center.mb-4 > div {
        margin: 5px 0;
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .d-flex.justify-content-between.align-items-center.mb-4 h2 {
        text-align: center;
        width: 100%;
    }
}

/* CTA Section */
.tours-cta-container {
    background-color: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.tours-cta-content {
    padding: 2rem;
    width: 100%;
    text-align: center;
}

.tours-cta-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #36b1dc;
    text-align: center;
}

.tours-cta-text {
    font-size: 1rem;
    line-height: 1.6;
    color: #555;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

/* Orders Section (Previous Implementation) */
.tours-orders-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

@media (min-width: 768px) {
    .tours-orders-container {
        grid-template-columns: repeat(1, 1fr);
    }
}

.tours-order-card {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 1.5rem;
}

.tours-order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.tours-order-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.tours-order-header p {
    margin: 0;
    font-size: 0.85rem;
}

.tours-order-items {
    margin-bottom: 1rem;
}

.tours-order-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f5f5f5;
}

.tours-order-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.tours-order-item-image {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
}

.tours-order-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tours-order-item-details {
    flex-grow: 1;
}

.tours-order-item-details h5 {
    font-size: 1rem;
    font-weight: 500;
    margin: 0 0 0.5rem 0;
}

.tours-order-item-details p {
    display: flex;
    justify-content: space-between;
    margin: 0;
    font-size: 0.9rem;
    color: #666;
}

.tours-order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.tours-order-total {
    font-weight: 600;
    font-size: 1.1rem;
}

/* Mini Cart Button */
#mini-cart-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

#mini-cart-button:hover {
    background-color: #f9f9f9;
}

#mini-cart-button .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #36b1dc;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

.mini-cart-button-icon {
    color: #36b1dc;
    font-size: 1.1rem;
}

.mini-cart-button-text {
    font-weight: 500;
    color: #333;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
    .tours-product-image {
        height: 200px;
    }

    .tours-product-content {
        padding: 1rem;
    }

    .tours-product-title {
        font-size: 1.1rem;
    }

    .tours-order-header {
        flex-direction: column;
    }

    .tours-order-status {
        margin-top: 0.5rem;
    }

    .tours-order-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}
