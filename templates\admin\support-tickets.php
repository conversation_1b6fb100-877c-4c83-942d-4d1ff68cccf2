<?php
/**
 * Admin Support Tickets Template
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define ticket categories
$ticket_categories = [
    'registration' => 'Registration Issues',
    'payment' => 'Payment & Invoices',
    'accommodation' => 'Accommodation',
    'tours' => 'Tours & Activities',
    'visa' => 'Visa & Travel',
    'other' => 'Other Questions'
];

// Define ticket statuses
$ticket_statuses = [
    'open' => 'Open',
    'in-progress' => 'In Progress',
    'resolved' => 'Resolved',
    'closed' => 'Closed'
];
?>

<script>
// Ensure lciAdminSupport is available
if (typeof window.lciAdminSupport === 'undefined') {
    window.lciAdminSupport = {
        ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
        nonce: '<?php echo wp_create_nonce('lci_admin_support_nonce'); ?>',
        messages: {
            replySubmitted: '<?php echo esc_js(__('Your reply has been submitted successfully.', 'lci-2025-dashboard')); ?>',
            replyError: '<?php echo esc_js(__('There was an error submitting your reply. Please try again.', 'lci-2025-dashboard')); ?>',
            statusUpdated: '<?php echo esc_js(__('Ticket status updated successfully.', 'lci-2025-dashboard')); ?>',
            statusError: '<?php echo esc_js(__('There was an error updating the ticket status. Please try again.', 'lci-2025-dashboard')); ?>',
            ticketDeleted: '<?php echo esc_js(__('Ticket deleted successfully.', 'lci-2025-dashboard')); ?>',
            ticketDeleteError: '<?php echo esc_js(__('There was an error deleting the ticket. Please try again.', 'lci-2025-dashboard')); ?>'
        }
    };
}
</script>

<div class="wrap lci-admin-wrap">
    <h1 class="text-3xl font-bold mb-6">Support Tickets</h1>

    <div x-data="{
        tickets: [],
        isLoading: true,
        activeTicket: null,
        showTicketDetails: false,
        replyMessage: '',
        isSubmitting: false,
        formSuccess: false,
        formError: false,
        errorMessage: '',
        successMessage: '',
        statusFilter: '',
        categoryFilter: '',
        searchQuery: '',
        currentPage: 1,
        totalPages: 1,
        perPage: 20,

        init() {
            this.loadTickets();
        },

        loadTickets() {
            this.isLoading = true;

            jQuery.ajax({
                url: lciAdminSupport.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lci_admin_get_tickets',
                    nonce: lciAdminSupport.nonce,
                    status: this.statusFilter,
                    category: this.categoryFilter,
                    search: this.searchQuery,
                    limit: this.perPage,
                    offset: (this.currentPage - 1) * this.perPage
                },
                success: (response) => {
                    if (response.success) {
                        this.tickets = response.data.tickets;
                        this.totalPages = response.data.pages;
                    } else {
                        this.showError(response.data.message);
                    }
                },
                error: () => {
                    this.showError('Error loading tickets. Please try again.');
                },
                complete: () => {
                    this.isLoading = false;
                }
            });
        },

        applyFilters() {
            this.currentPage = 1;
            this.loadTickets();
        },

        resetFilters() {
            this.statusFilter = '';
            this.categoryFilter = '';
            this.searchQuery = '';
            this.currentPage = 1;
            this.loadTickets();
        },

        changePage(page) {
            if (page < 1 || page > this.totalPages) return;
            this.currentPage = page;
            this.loadTickets();
        },

        viewTicket(ticketId) {
            this.isLoading = true;
            this.showTicketDetails = true;

            jQuery.ajax({
                url: lciAdminSupport.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lci_get_ticket_details',
                    nonce: lciAdminSupport.nonce,
                    ticket_id: ticketId
                },
                success: (response) => {
                    if (response.success) {
                        this.activeTicket = response.data.ticket;
                    } else {
                        this.showError(response.data.message);
                        this.showTicketDetails = false;
                    }
                },
                error: () => {
                    this.showError('Error loading ticket details. Please try again.');
                    this.showTicketDetails = false;
                },
                complete: () => {
                    this.isLoading = false;
                }
            });
        },

        closeTicketDetails() {
            this.showTicketDetails = false;
            this.activeTicket = null;
            this.replyMessage = '';
        },

        submitReply() {
            if (!this.activeTicket) {
                this.showError('No ticket selected.');
                return;
            }

            if (!this.replyMessage.trim()) {
                this.showError('Please enter a reply message.');
                return;
            }

            this.isSubmitting = true;

            const formData = new FormData();
            formData.append('action', 'lci_reply_to_ticket');
            formData.append('nonce', lciAdminSupport.nonce);
            formData.append('ticket_id', this.activeTicket.id);
            formData.append('message', this.replyMessage);

            const attachmentInput = document.getElementById('admin-reply-attachment');
            if (attachmentInput && attachmentInput.files.length > 0) {
                formData.append('attachment', attachmentInput.files[0]);
            }

            jQuery.ajax({
                url: lciAdminSupport.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: (response) => {
                    if (response.success) {
                        this.showSuccess(lciAdminSupport.messages.replySubmitted);
                        this.replyMessage = '';

                        // Reload ticket details to show the new reply
                        this.viewTicket(this.activeTicket.id);

                        // Clear file input
                        if (attachmentInput) {
                            attachmentInput.value = '';
                        }
                    } else {
                        this.showError(response.data.message);
                    }
                },
                error: () => {
                    this.showError(lciAdminSupport.messages.replyError);
                },
                complete: () => {
                    this.isSubmitting = false;
                }
            });
        },

        updateTicketStatus(status) {
            if (!this.activeTicket) {
                this.showError('No ticket selected.');
                return;
            }

            this.isSubmitting = true;

            jQuery.ajax({
                url: lciAdminSupport.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lci_update_ticket_status',
                    nonce: lciAdminSupport.nonce,
                    ticket_id: this.activeTicket.id,
                    status: status
                },
                success: (response) => {
                    if (response.success) {
                        this.showSuccess(lciAdminSupport.messages.statusUpdated);

                        // Update local status
                        this.activeTicket.status = status;

                        // Reload tickets list to reflect the status change
                        this.loadTickets();
                    } else {
                        this.showError(response.data.message);
                    }
                },
                error: () => {
                    this.showError(lciAdminSupport.messages.statusError);
                },
                complete: () => {
                    this.isSubmitting = false;
                }
            });
        },

        deleteTicket() {
            if (!this.activeTicket) {
                this.showError('No ticket selected.');
                return;
            }

            if (!confirm('Are you sure you want to delete this ticket? This action cannot be undone.')) {
                return;
            }

            this.isSubmitting = true;

            jQuery.ajax({
                url: lciAdminSupport.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lci_delete_ticket',
                    nonce: lciAdminSupport.nonce,
                    ticket_id: this.activeTicket.id
                },
                success: (response) => {
                    if (response.success) {
                        this.showSuccess(lciAdminSupport.messages.ticketDeleted);
                        this.closeTicketDetails();
                        this.loadTickets();
                    } else {
                        this.showError(response.data.message || lciAdminSupport.messages.ticketDeleteError);
                    }
                },
                error: () => {
                    this.showError(lciAdminSupport.messages.ticketDeleteError);
                },
                complete: () => {
                    this.isSubmitting = false;
                }
            });
        },

        showError(message) {
            this.errorMessage = message;
            this.formError = true;
            this.formSuccess = false;

            setTimeout(() => {
                this.formError = false;
            }, 5000);
        },

        showSuccess(message) {
            this.successMessage = message;
            this.formSuccess = true;
            this.formError = false;

            setTimeout(() => {
                this.formSuccess = false;
            }, 5000);
        },

        getStatusClass(status) {
            switch (status) {
                case 'open':
                    return 'bg-yellow-100 text-yellow-800';
                case 'in-progress':
                    return 'bg-blue-100 text-blue-800';
                case 'resolved':
                    return 'bg-green-100 text-green-800';
                case 'closed':
                    return 'bg-gray-100 text-gray-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        },

        getStatusText(status) {
            switch (status) {
                case 'open':
                    return 'Open';
                case 'in-progress':
                    return 'In Progress';
                case 'resolved':
                    return 'Resolved';
                case 'closed':
                    return 'Closed';
                default:
                    return status.charAt(0).toUpperCase() + status.slice(1);
            }
        },

        getCategoryText(category) {
            switch (category) {
                case 'registration':
                    return 'Registration Issues';
                case 'payment':
                    return 'Payment & Invoices';
                case 'accommodation':
                    return 'Accommodation';
                case 'tours':
                    return 'Tours & Activities';
                case 'visa':
                    return 'Visa & Travel';
                case 'other':
                    return 'Other Questions';
                default:
                    return category.charAt(0).toUpperCase() + category.slice(1);
            }
        },

        formatDate(dateString) {
            if (!dateString) return '';

            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        getUserName(userId) {
            // This would ideally fetch the user's name from WordPress
            // For now, we'll just return the user ID
            return 'User #' + userId;
        }
    }"
        <!-- Alerts -->
        <div x-show="formSuccess" x-transition class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
            <p x-text="successMessage"></p>
        </div>

        <div x-show="formError" x-transition class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <p x-text="errorMessage"></p>
        </div>

        <!-- Ticket List View -->
        <div x-show="!showTicketDetails" class="bg-white rounded-xl shadow-neumorph p-6 mb-6">
            <div class="flex flex-wrap items-center justify-between mb-6">
                <h2 class="text-xl font-medium text-gray-800">All Support Tickets</h2>

                <div class="flex flex-wrap items-center space-x-2">
                    <!-- Status Filter -->
                    <div class="relative">
                        <select x-model="statusFilter" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option value="">All Statuses</option>
                            <?php foreach ($ticket_statuses as $value => $label) : ?>
                                <option value="<?php echo esc_attr($value); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Category Filter -->
                    <div class="relative">
                        <select x-model="categoryFilter" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option value="">All Categories</option>
                            <?php foreach ($ticket_categories as $value => $label) : ?>
                                <option value="<?php echo esc_attr($value); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Search -->
                    <div class="relative">
                        <input type="text" x-model="searchQuery" placeholder="Search tickets..." class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    </div>

                    <!-- Apply Filters Button -->
                    <button @click="applyFilters" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Apply Filters
                    </button>

                    <!-- Reset Filters Button -->
                    <button @click="resetFilters" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Reset
                    </button>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div x-show="isLoading" class="flex justify-center items-center py-8">
                <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>

            <!-- Tickets Table -->
            <div x-show="!isLoading" class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="ticket in tickets" :key="ticket.id">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="ticket.ticket_id"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="ticket.subject"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="getCategoryText(ticket.category)"></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="getStatusClass(ticket.status)" x-text="getStatusText(ticket.status)"></span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(ticket.created_at)"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(ticket.updated_at)"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button @click="viewTicket(ticket.id)" class="text-blue-600 hover:text-blue-900">View</button>
                                </td>
                            </tr>
                        </template>

                        <!-- Empty State -->
                        <tr x-show="tickets.length === 0 && !isLoading">
                            <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                                No tickets found.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
                <div class="flex justify-between flex-1 sm:hidden">
                    <button @click="changePage(currentPage - 1)" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }">
                        Previous
                    </button>
                    <button @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }">
                        Next
                    </button>
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing page <span class="font-medium" x-text="currentPage"></span> of <span class="font-medium" x-text="totalPages"></span>
                        </p>
                    </div>
                    <div>
                        <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button @click="changePage(currentPage - 1)" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <template x-for="page in totalPages" :key="page">
                                <button @click="changePage(page)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium hover:bg-gray-50" :class="{ 'bg-blue-50 text-blue-600 z-10': page === currentPage, 'text-gray-500': page !== currentPage }" x-text="page"></button>
                            </template>

                            <button @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ticket Detail View -->
        <div x-show="showTicketDetails" class="bg-white rounded-xl shadow-neumorph p-6 mb-6">
            <!-- Loading Indicator -->
            <div x-show="isLoading" class="flex justify-center items-center py-8">
                <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>

            <div x-show="!isLoading && activeTicket">
                <!-- Ticket Header -->
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <button @click="closeTicketDetails" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                            </svg>
                            Back to Tickets
                        </button>
                    </div>

                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Status:</span>
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" :class="activeTicket ? getStatusClass(activeTicket.status) : ''" x-text="activeTicket ? getStatusText(activeTicket.status) : ''"></span>
                    </div>
                </div>

                <!-- Ticket Info -->
                <div class="bg-gray-50 p-4 rounded-lg mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h2 class="text-xl font-medium text-gray-900" x-text="activeTicket ? activeTicket.subject : ''"></h2>
                            <p class="text-sm text-gray-500 mt-1">
                                <span x-text="activeTicket ? 'Ticket ID: ' + activeTicket.ticket_id : ''"></span> |
                                <span x-text="activeTicket ? 'Category: ' + getCategoryText(activeTicket.category) : ''"></span>
                            </p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-500">
                                <span x-text="activeTicket ? 'Created: ' + formatDate(activeTicket.created_at) : ''"></span><br>
                                <span x-text="activeTicket ? 'Last Updated: ' + formatDate(activeTicket.updated_at) : ''"></span>
                            </p>
                        </div>
                    </div>

                    <div class="mt-4 p-4 bg-white rounded border border-gray-200">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                    <svg class="h-6 w-6 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900" x-text="activeTicket ? getUserName(activeTicket.user_id) : ''"></p>
                                <div class="mt-1 text-sm text-gray-700 whitespace-pre-wrap" x-text="activeTicket ? activeTicket.message : ''"></div>

                                <template x-if="activeTicket && activeTicket.attachment_url">
                                    <div class="mt-2">
                                        <a :href="activeTicket.attachment_url" target="_blank" class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                                            <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd" />
                                            </svg>
                                            View Attachment
                                        </a>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ticket Replies -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Conversation</h3>

                    <template x-if="activeTicket && activeTicket.replies && activeTicket.replies.length > 0">
                        <div class="space-y-4">
                            <template x-for="reply in activeTicket.replies" :key="reply.id">
                                <div class="p-4 rounded border border-gray-200" :class="{ 'bg-blue-50': reply.is_admin == 1, 'bg-white': reply.is_admin == 0 }">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <div class="h-10 w-10 rounded-full flex items-center justify-center" :class="{ 'bg-blue-300': reply.is_admin == 1, 'bg-gray-300': reply.is_admin == 0 }">
                                                <svg class="h-6 w-6" :class="{ 'text-blue-600': reply.is_admin == 1, 'text-gray-600': reply.is_admin == 0 }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-3">
                                            <div class="flex items-center">
                                                <p class="text-sm font-medium" :class="{ 'text-blue-900': reply.is_admin == 1, 'text-gray-900': reply.is_admin == 0 }" x-text="reply.is_admin == 1 ? 'Support Agent' : getUserName(reply.user_id)"></p>
                                                <span class="ml-2 text-xs text-gray-500" x-text="formatDate(reply.created_at)"></span>
                                            </div>
                                            <div class="mt-1 text-sm text-gray-700 whitespace-pre-wrap" x-text="reply.message"></div>

                                            <template x-if="reply.attachment_url">
                                                <div class="mt-2">
                                                    <a :href="reply.attachment_url" target="_blank" class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                                                        <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd" />
                                                        </svg>
                                                        View Attachment
                                                    </a>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>

                    <template x-if="activeTicket && (!activeTicket.replies || activeTicket.replies.length === 0)">
                        <div class="text-center py-4 text-gray-500">
                            No replies yet.
                        </div>
                    </template>
                </div>

                <!-- Admin Actions -->
                <div class="bg-gray-50 p-4 rounded-lg mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Admin Actions</h3>

                    <div class="flex flex-wrap gap-2 mb-4">
                        <button @click="updateTicketStatus('open')" :disabled="!activeTicket || activeTicket.status === 'open' || isSubmitting" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500" :class="{ 'opacity-50 cursor-not-allowed': !activeTicket || activeTicket.status === 'open' || isSubmitting }">
                            Mark as Open
                        </button>

                        <button @click="updateTicketStatus('in-progress')" :disabled="!activeTicket || activeTicket.status === 'in-progress' || isSubmitting" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" :class="{ 'opacity-50 cursor-not-allowed': !activeTicket || activeTicket.status === 'in-progress' || isSubmitting }">
                            Mark as In Progress
                        </button>

                        <button @click="updateTicketStatus('resolved')" :disabled="!activeTicket || activeTicket.status === 'resolved' || isSubmitting" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" :class="{ 'opacity-50 cursor-not-allowed': !activeTicket || activeTicket.status === 'resolved' || isSubmitting }">
                            Mark as Resolved
                        </button>

                        <button @click="updateTicketStatus('closed')" :disabled="!activeTicket || activeTicket.status === 'closed' || isSubmitting" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" :class="{ 'opacity-50 cursor-not-allowed': !activeTicket || activeTicket.status === 'closed' || isSubmitting }">
                            Close Ticket
                        </button>

                        <button @click="deleteTicket" :disabled="!activeTicket || isSubmitting" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" :class="{ 'opacity-50 cursor-not-allowed': !activeTicket || isSubmitting }">
                            <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                            Delete Ticket
                        </button>
                    </div>
                </div>

                <!-- Reply Form -->
                <div class="bg-white rounded-lg border border-gray-200 p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Reply to Ticket</h3>

                    <div class="mb-4">
                        <label for="admin-reply-message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                        <textarea id="admin-reply-message" x-model="replyMessage" rows="4" class="shadow-sm block w-full focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 rounded-md" placeholder="Type your reply here..."></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="admin-reply-attachment" class="block text-sm font-medium text-gray-700 mb-1">Attachment (optional)</label>
                        <input type="file" id="admin-reply-attachment" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    </div>

                    <div class="flex justify-end">
                        <button @click="submitReply" :disabled="isSubmitting || !replyMessage.trim()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" :class="{ 'opacity-50 cursor-not-allowed': isSubmitting || !replyMessage.trim() }">
                            <template x-if="isSubmitting">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </template>
                            <span x-text="isSubmitting ? 'Sending...' : 'Send Reply'"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Fallback to ensure Alpine.js is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (typeof window.Alpine === 'undefined') {
        console.error('Alpine.js is not loaded. Loading it now...');
        var alpineScript = document.createElement('script');
        alpineScript.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.12.0/dist/cdn.min.js';
        alpineScript.defer = true;
        document.head.appendChild(alpineScript);
    }
});
</script>