<div class="wrap lci-admin-wrap">
    <div x-data="syncTool">
        <h1 class="text-3xl font-bold mb-6">Sync Tool</h1>

        <div class="bg-white rounded-xl shadow-neumorph p-6 mb-6 relative">
            <div x-show="isSyncing" class="loading-overlay" x-transition:leave="transition-opacity duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
                <div class="spinner"></div>
                <span class="loading-text">Syncing participants...</span>
            </div>
            <h2 class="text-xl font-medium text-gray-800 mb-4">Synchronize Participant Data</h2>

            <p class="text-gray-600 mb-6">
                This tool synchronizes participant data from WooCommerce orders to the LCI 2025 Dashboard database.
                Use this when you need to update participant information or add new participants from recent orders.
            </p>

            <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                <button
                    @click="syncAll"
                    class="btn btn-primary flex items-center justify-center"
                    :class="{'opacity-50 cursor-not-allowed': isSyncing}"
                    :disabled="isSyncing"
                >
                    <template x-if="!isSyncing">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </template>
                    <template x-if="isSyncing">
                        <svg class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </template>
                    <span x-text="isSyncing ? 'Syncing...' : 'Sync All Participants'"></span>
                </button>
            </div>

            <!-- Progress bar -->
            <div x-show="isSyncing || progress > 0" class="mt-6">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Syncing participants...</span>
                    <span x-text="progress + '%'"></span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-primary h-2.5 rounded-full" :style="'width: ' + progress + '%'"></div>
                </div>
            </div>

            <!-- Results -->
            <div x-show="successCount > 0 || errorCount > 0" class="mt-6 p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Sync Results</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-green-800 font-medium">Success</span>
                        </div>
                        <div class="mt-2 text-green-700">
                            <span x-text="successCount"></span> participants synced successfully
                        </div>
                    </div>

                    <div x-show="errorCount > 0" class="bg-red-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-red-800 font-medium">Errors</span>
                        </div>
                        <div class="mt-2 text-red-700">
                            <span x-text="errorCount"></span> participants failed to sync
                        </div>
                        <div class="mt-2">
                            <a href="<?php echo admin_url('admin.php?page=lci-debug-logs'); ?>" class="text-red-700 underline hover:text-red-800">
                                View error details in Debug Logs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <h2 class="text-xl font-medium text-gray-800 mb-4">Sync Information</h2>

            <div class="space-y-4 text-gray-600">
                <div class="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p>
                        <strong class="text-gray-800">What does this tool do?</strong><br>
                        This tool synchronizes participant data from WooCommerce orders to the LCI 2025 Dashboard database.
                        It extracts relevant information such as personal details, registration type, payment status, and more.
                    </p>
                </div>

                <div class="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p>
                        <strong class="text-gray-800">When should I use it?</strong><br>
                        Use this tool when you need to update participant information or add new participants from recent orders.
                        It's also useful if you've made changes to the WooCommerce orders and want to reflect those changes in the dashboard.
                    </p>
                </div>

                <div class="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <p>
                        <strong class="text-gray-800">Is it safe to use?</strong><br>
                        Yes, this tool is safe to use. It only reads data from WooCommerce orders and updates the LCI 2025 Dashboard database.
                        It does not modify any WooCommerce data or affect your WordPress installation.
                    </p>
                </div>

                <div class="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <p>
                        <strong class="text-gray-800">Automatic Synchronization</strong><br>
                        The system automatically synchronizes new orders when they are created or when their status changes to "Processing" or "Completed".
                        This manual sync tool is provided for cases where you need to force a synchronization.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
