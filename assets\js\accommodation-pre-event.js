// Variables to store product information for the confirmation modal
let preEventProductId = 0;
let preEventVariationId = 0;
let preEventNights = 1;
let preEventIsDoubleRoom = false;

// Function to show the confirmation modal
function showPreEventConfirmModal(productId) {
    preEventProductId = productId;

    // Get saved nights from session storage
    preEventNights = parseInt(sessionStorage.getItem('preEventNights')) || 1;

    // Get product type
    const productType = document.querySelector(`.add-to-cart-btn[data-product-id="${productId}"]`).getAttribute('data-product-type');

    // Get selected variation if it's a variable product
    if (productType === 'variable') {
        const variationContainer = document.querySelector(`.accommodation-product-variations[data-product-id="${productId}"]`);
        if (variationContainer) {
            const selectedVariation = variationContainer.querySelector('input[type="radio"]:checked');
            if (selectedVariation) {
                preEventVariationId = selectedVariation.value;
                preEventIsDoubleRoom = selectedVariation.getAttribute('data-is-double') === 'true';
            } else {
                // No variation selected, show alert and return
                alert('Please select a room type before booking.');
                return;
            }
        }
    } else {
        preEventVariationId = 0;
        preEventIsDoubleRoom = false;
    }

    // Get product name
    const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${productId}"]`).closest('.accommodation-product-card');
    const productName = productCard.querySelector('.accommodation-product-title').textContent;

    // Get variation name if applicable
    let roomType = '';
    if (preEventVariationId) {
        const variationRadio = document.querySelector(`input[value="${preEventVariationId}"]`);
        if (variationRadio) {
            roomType = variationRadio.getAttribute('data-variation-name');
        }
    }

    // Calculate check-in date
    const checkinDate = calculatePreEventCheckinDate(preEventNights);

    // Update modal content
    document.getElementById('preEventConfirmProductName').textContent = productName;

    if (roomType) {
        document.getElementById('confirmRoomTypeRow').style.display = 'flex';
        document.getElementById('confirmRoomType').textContent = roomType;
    } else {
        document.getElementById('confirmRoomTypeRow').style.display = 'none';
    }

    document.getElementById('preEventConfirmCheckinDate').textContent = checkinDate;
    document.getElementById('preEventConfirmNights').textContent = `${preEventNights} ${preEventNights === 1 ? 'night' : 'nights'}`;

    // Hide double room notice (no longer needed)
    document.getElementById('confirmDoubleRoomRow').style.display = 'none';
    document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

    // Calculate and display the total price
    let productPrice = 0;

    if (preEventVariationId) {
        // Get price from the selected variation
        const selectedVariation = document.querySelector(`input[value="${preEventVariationId}"]`);
        if (selectedVariation) {
            productPrice = parseFloat(selectedVariation.getAttribute('data-price') || 0);
        }
    } else {
        // Get price from the button's data attribute
        const addToCartBtn = document.querySelector(`.add-to-cart-btn[data-product-id="${productId}"]`);
        if (addToCartBtn) {
            productPrice = parseFloat(addToCartBtn.getAttribute('data-price') || 0);
        }
    }

    // Calculate total price based on nights
    const totalPrice = productPrice * preEventNights;

    // Format the price with currency symbol
    const formattedPrice = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(totalPrice);

    // Update the price display
    document.getElementById('confirmTotalPrice').textContent = formattedPrice;

    // Show modal
    const confirmModal = document.getElementById('preEventCartConfirmModal');
    confirmModal.style.display = 'block';
}

// Function to close the confirmation modal
function closePreEventConfirmModal() {
    const confirmModal = document.getElementById('preEventCartConfirmModal');
    confirmModal.style.display = 'none';
}

// Function to calculate check-in date
function calculatePreEventCheckinDate(nights) {
    // Base date is 21/08/2025 (check-out date)
    const baseDate = new Date(2025, 7, 21); // Month is 0-indexed, so 7 = August

    // Calculate check-in date by subtracting nights from base date
    const checkinDate = new Date(baseDate);
    checkinDate.setDate(baseDate.getDate() - nights);

    // Format the date as DD/MM/YYYY
    const day = String(checkinDate.getDate()).padStart(2, '0');
    const month = String(checkinDate.getMonth() + 1).padStart(2, '0');
    const year = checkinDate.getFullYear();

    return `${day}/${month}/${year}`;
}

// Function to add product to cart
function addToCartPreEvent() {
    // Check if lci_accommodation is defined
    if (typeof lci_accommodation === 'undefined') {
        console.error('lci_accommodation is not defined');
        alert('An error occurred. Please refresh the page and try again.');
        return;
    }

    const confirmModal = document.getElementById('preEventCartConfirmModal');

    // Use nights as quantity (no special calculation for double rooms)
    const quantity = preEventNights;

    // Get the variation radio button
    const variationRadio = document.querySelector(`input[value="${preEventVariationId}"]`);

    // Prepare data for AJAX request
    const data = {
        action: 'lci-dashboard-add-to-cart',
        product_id: preEventProductId,
        quantity: quantity,
        security: lci_accommodation.nonce
    };

    // Add variation ID and attributes if applicable
    if (preEventVariationId) {
        data.variation_id = preEventVariationId;

        if (variationRadio) {
            // Get variation attributes
            const attributeName = variationRadio.getAttribute('data-attribute-name');
            const attributeValue = variationRadio.getAttribute('data-attribute-value');

            if (attributeName && attributeValue) {
                data[`attribute_${attributeName}`] = attributeValue;
            }
        }
    }

    console.log('Adding to cart with data:', data);

    // Send AJAX request using jQuery to ensure compatibility with WooCommerce
    jQuery.ajax({
        type: 'POST',
        url: lci_accommodation.ajaxurl,
        data: data,
        success: function(response) {
            console.log('Add to cart response:', response);
            
            if (response.success) {
                // Trigger WooCommerce's update_cart event
                jQuery(document.body).trigger('added_to_cart', [null, null, jQuery('.add-to-cart-btn')]);

                // Hide confirmation modal
                confirmModal.style.display = 'none';

                // Show success message instead of reloading
                const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${preEventProductId}"]`).closest('.accommodation-product-card');
                const addToCartBtn = productCard.querySelector('.add-to-cart-btn');

                // Replace button with success message
                addToCartBtn.innerHTML = '<i class="fas fa-check me-2"></i> BOOKED';
                addToCartBtn.style.backgroundColor = '#28a745';
                addToCartBtn.disabled = true;

                // Hide settings button
                const settingsBtn = productCard.querySelector('.accommodation-settings-button');
                if (settingsBtn) {
                    settingsBtn.style.display = 'none';
                }

                // Update mini cart count (if available)
                const miniCartCount = document.querySelector('.mini-cart-count');
                if (miniCartCount) {
                    const currentCount = parseInt(miniCartCount.textContent) || 0;
                    miniCartCount.textContent = currentCount + 1;
                }

                // Refresh the mini-cart contents
                refreshMiniCart();

                // Refresh LCI mini cart items
                if (typeof lci_get_mini_cart_items === 'function') {
                    console.log('Refreshing mini cart items');
                    lci_get_mini_cart_items();
                } else {
                    console.log('lci_get_mini_cart_items function not found');
                }
            } else {
                console.error('Add to cart failed:', response.data.message);
                alert('Failed to add to cart: ' + response.data.message);
            }
        },
        error: function(error) {
            console.error('Error adding to cart:', error);
            alert('An error occurred while adding to cart. Please try again.');

            // Hide confirmation modal
            confirmModal.style.display = 'none';
        }
    });
}

// Function to refresh the mini-cart
function refreshMiniCart() {
    // Get the mini-cart container
    const miniCartContainer = document.querySelector('.dashboard-mini-cart');
    if (!miniCartContainer) {
        console.warn('Mini-cart container not found');
        return;
    }

    console.log('Refreshing mini-cart...');

    // Add loading indicator
    miniCartContainer.classList.add('loading');

    // Fetch updated mini-cart HTML via AJAX
    fetch('?wc-ajax=get_refreshed_fragments', {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update fragments (including mini-cart)
        if (data.fragments) {
            for (let id in data.fragments) {
                const element = document.querySelector(id);
                if (element) {
                    element.innerHTML = data.fragments[id];
                }
            }
        }

        // Remove loading indicator
        miniCartContainer.classList.remove('loading');

        console.log('Mini-cart refreshed successfully');
    })
    .catch(error => {
        console.error('Error refreshing mini-cart:', error);
        miniCartContainer.classList.remove('loading');
    });
}

// Initialize event listeners when the document is ready
jQuery(document).ready(function($) {
    // Add click event to all Add to Cart buttons
    $('.add-to-cart-btn').on('click', function(e) {
        e.preventDefault();
        const productId = $(this).data('product-id');
        showPreEventConfirmModal(productId);
    });

    // Add click event to modal close button
    $('.accommodation-cart-confirm-close').on('click', closePreEventConfirmModal);

    // Add click event to modal cancel button
    $('.accommodation-cart-confirm-btn-secondary').on('click', closePreEventConfirmModal);
}); 