<?php
/*
Plugin Name: LCI 2025 Dashboard
Description: Custom login and user dashboard for LCI AGM 2025 Brașov
Version: 1.2
Author: <PERSON>
*/

if (!defined('ABSPATH')) exit;

// Define plugin constants
define('LCI2025_PLUGIN_FILE', __FILE__);
define('LCI2025_PATH', plugin_dir_path(__FILE__));
define('LCI2025_URL', plugin_dir_url(__FILE__));
define('LCI2025_VERSION', '9.72');
define('LCI2025_DB_VERSION', '1.2');

/**
 * Initialize WebP support for WordPress
 */
function lci2025_init_webp_support() {
    // Add WebP MIME type support
    add_filter('upload_mimes', function($mimes) {
        $mimes['webp'] = 'image/webp';
        return $mimes;
    });

    // Ensure WordPress can properly identify WebP files
    add_filter('file_is_displayable_image', function($result, $path) {
        if ($result === false) {
            $info = @getimagesize($path);
            if ($info && $info[2] === IMAGETYPE_WEBP) {
                $result = true;
            }
        }
        return $result;
    }, 10, 2);
}

/**
 * Register plugin settings
 */
function lci2025_register_settings() {
    register_setting('lci2025_settings', 'lci2025_reg_id_prefix', [
        'default' => 'LCI',
        'sanitize_callback' => 'sanitize_text_field',
    ]);

    register_setting('lci2025_settings', 'lci2025_debug_mode', [
        'default' => '0',
        'sanitize_callback' => 'sanitize_text_field',
    ]);
}

/**
 * Enqueue scripts and styles
 */
function lci2025_enqueue_scripts() {
    // Enqueue CSS to hide BeTheme's mini cart
    wp_enqueue_style('lci2025-hide-betheme-cart', LCI2025_URL . 'assets/css/hide-betheme-cart.css', [], LCI2025_VERSION);

    // Enqueue payment styles
    wp_enqueue_style('lci2025-payment-styles', LCI2025_URL . 'assets/css/payment-styles.css', [], LCI2025_VERSION);

    // Enqueue JavaScript to fix scrollbar issues
    wp_enqueue_script('lci2025-fix-scrollbar', LCI2025_URL . 'assets/js/fix-scrollbar.js', [], LCI2025_VERSION, true);

    // Enqueue JavaScript for cart calculations
    wp_enqueue_script('lci2025-cart-calculations', LCI2025_URL . 'assets/js/cart-calculations.js', [], LCI2025_VERSION, true);

    // Enqueue JavaScript for fundraising message refresh
    wp_enqueue_script('lci2025-fundraising-refresh', LCI2025_URL . 'assets/js/fundraising-refresh.js', ['jquery'], LCI2025_VERSION, true);

    // Enqueue Alpine.js for interactive components (with defer attribute to ensure it loads before our scripts)
    wp_enqueue_script('lci2025-alpine', 'https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js', [], '3.13.3', false);

    // Enqueue transfers wizard CSS and JS
    wp_enqueue_style('lci2025-transfers-wizard', LCI2025_URL . 'assets/css/transfers-wizard.css', [], LCI2025_VERSION);
    wp_enqueue_script('lci2025-transfers-wizard', LCI2025_URL . 'assets/js/transfers-wizard.js', ['jquery', 'lci2025-alpine'], LCI2025_VERSION, true);

    // Localize script with AJAX URL and nonce
    wp_localize_script('lci2025-fundraising-refresh', 'lci_ajax', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('lci_ajax_nonce')
    ]);

    // Also localize transfers wizard script
    wp_localize_script('lci2025-transfers-wizard', 'lci_ajax', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('lci_ajax_nonce')
    ]);
}

/**
 * Filter body classes to remove BeTheme's cart-related classes
 */
function lci2025_filter_body_classes($classes) {
    // Classes to remove
    $remove_classes = [
        'mfn-hidden-overlay',
        'mfn-cart-opened',
        'mfn-cart-showing',
        'mfn-active-cart'
    ];

    // Filter out the classes
    return array_diff($classes, $remove_classes);
}

// Initialize plugin
add_action('init', 'lci2025_init_webp_support');
add_action('admin_init', 'lci2025_register_settings');
add_action('wp_enqueue_scripts', 'lci2025_enqueue_scripts');
add_filter('body_class', 'lci2025_filter_body_classes');

// Include required files
require_once LCI2025_PATH . 'includes/routes.php';
require_once LCI2025_PATH . 'includes/helpers.php';
require_once LCI2025_PATH . 'includes/auth.php';
require_once LCI2025_PATH . 'includes/profile.php';
require_once LCI2025_PATH . 'includes/regalia-shop.php';
require_once LCI2025_PATH . 'includes/ajax-handlers.php';
require_once LCI2025_PATH . 'includes/mini-cart.php';
require_once LCI2025_PATH . 'includes/fundraising.php';
require_once LCI2025_PATH . 'includes/payment-processing.php';
require_once LCI2025_PATH . 'includes/class-lci-payment-handler.php';
require_once LCI2025_PATH . 'includes/class-lci-direct-payment.php';
require_once LCI2025_PATH . 'includes/cart-fix.php';
require_once LCI2025_PATH . 'includes/invitation-letter-generator.php';
require_once LCI2025_PATH . 'includes/class-lci-support.php';
require_once LCI2025_PATH . 'includes/class-lci-transfers.php';
require_once LCI2025_PATH . 'includes/class-lci-transfers-wc.php';

// Include shortcodes
require_once LCI2025_PATH . 'includes/shortcodes/merchandise-shop.php';

// Include admin functionality if in admin area
if (is_admin()) {
    require_once LCI2025_PATH . 'includes/class-lci-database.php';
    require_once LCI2025_PATH . 'includes/class-lci-database-repair.php';
    require_once LCI2025_PATH . 'includes/class-lci-participant.php';
    require_once LCI2025_PATH . 'includes/class-lci-tours.php';
    require_once LCI2025_PATH . 'includes/class-lci-tours-unified.php';
    require_once LCI2025_PATH . 'includes/class-lci-accommodations.php';
    require_once LCI2025_PATH . 'includes/class-lci-db-inspector.php';
    require_once LCI2025_PATH . 'includes/class-lci-main-event.php';
    require_once LCI2025_PATH . 'includes/class-lci-accommodation-unified.php';
    require_once LCI2025_PATH . 'includes/fundraising-admin.php';
    require_once LCI2025_PATH . 'admin/class-lci-admin.php';

    // Initialize classes with AJAX handlers
    LCI_Accommodations::init();
    LCI_Support::init();
    LCI_Main_Event::init();
    LCI_Tours_Unified::init();
    LCI_Accommodation_Unified::init();

    // Initialize the transfers class
    $lci_transfers = new LCI_Transfers();

    // Add admin scripts hook for support system
    add_action('admin_enqueue_scripts', ['LCI_Support', 'admin_enqueue_scripts']);
}
