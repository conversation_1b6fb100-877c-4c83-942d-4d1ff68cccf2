<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirect to Forgot Password</title>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 30px;
            max-width: 500px;
        }
        h1 {
            color: #36b1dc;
            margin-bottom: 20px;
        }
        p {
            color: #4a5568;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .btn {
            background-color: #36b1dc;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 12px 24px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background-color: #2d93b7;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(45, 147, 183, 0.2);
        }
        .loading {
            display: none;
            margin-top: 20px;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 4px solid #36b1dc;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LCI 2025 Password Reset</h1>
        <p>Click the button below to go to the password reset page. Once the page loads, the forgot password form will automatically appear.</p>
        
        <button id="redirectBtn" class="btn">Go to Password Reset</button>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Redirecting to the password reset page...</p>
        </div>
    </div>

    <script>
        document.getElementById('redirectBtn').addEventListener('click', function() {
            // Show loading indicator
            document.getElementById('loading').style.display = 'block';
            this.style.display = 'none';
            
            // Redirect to the login page with a special parameter
            window.location.href = 'https://lci2025brasov.com/lci-dashboard/?show_forgot=1';
        });
    </script>
</body>
</html>
