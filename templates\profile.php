
<?php
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$avatar_id = get_user_meta($user_id, 'wp_user_avatar', true);
$avatar_url = $avatar_id ? wp_get_attachment_url($avatar_id) : get_avatar_url($user_id);
$first_name = get_user_meta($user_id, 'first_name', true);
$last_name = get_user_meta($user_id, 'last_name', true);
$phone = get_user_meta($user_id, 'billing_phone', true);
$email = $current_user->user_email;
?>

<div class="card shadow p-4 rounded-4 border-0">
  <div class="d-flex align-items-center mb-4">
    <div class="position-relative me-4">
      <div class="avatar-container position-relative" style="width: 100px; height: 100px;">
        <img src="<?php echo esc_url($avatar_url); ?>" alt="Avatar" id="lci-avatar-img" class="rounded-circle border border-4 border-primary shadow" width="100" height="100">
        <div class="avatar-overlay position-absolute top-0 start-0 w-100 h-100 rounded-circle d-flex align-items-center justify-content-center" style="background-color: rgba(54, 177, 220, 0.7); opacity: 0; transition: opacity 0.3s ease;">
          <i class="fas fa-camera text-white" style="font-size: 1.5rem;"></i>
        </div>
      </div>
      <button type="button" class="btn btn-primary position-absolute bottom-0 end-0 rounded-circle p-2 shadow" id="lci-avatar-btn" title="Upload new avatar" style="width: 36px; height: 36px; z-index: 2;">
        <i class="fas fa-camera"></i>
      </button>
      <input type="file" id="lci-avatar-input" accept="image/jpeg,image/png,image/gif,image/webp" style="display:none;">
    </div>
    <div>
      <h4 class="mb-0"><?php echo esc_html($first_name . ' ' . $last_name); ?></h4>
      <small class="text-muted">Edit your profile below</small>
    </div>
  </div>
</div>
