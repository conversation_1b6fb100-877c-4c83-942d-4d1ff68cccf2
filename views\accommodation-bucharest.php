<?php
/**
 * Bucharest Accommodation View
 *
 * Displays Bucharest accommodation options for users with Main Pretour
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Enqueue the accommodation CSS
wp_enqueue_style('accommodation-css', LCI2025_URL . 'assets/css/accommodation.css', array(), LCI2025_VERSION);

// Enqueue Alpine.js
wp_enqueue_script('alpine-js', 'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js', array('jquery'), null, false);

// Add x-cloak style to hide Alpine elements before initialization
wp_add_inline_style('accommodation-css', '[x-cloak] { display: none !important; }');

// Add inline CSS for the accommodation products
$accommodation_products_css = "
.accommodation-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.accommodation-product-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.accommodation-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.accommodation-product-image {
    height: 350px;
    overflow: hidden;
    position: relative;
}

.accommodation-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.accommodation-product-card:hover .accommodation-product-image img {
    transform: scale(1.05);
}

.accommodation-product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.accommodation-product-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.accommodation-product-title {
    color: #00b2e3;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.accommodation-product-description {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
    flex-grow: 1;
}

.accommodation-product-price {
    font-weight: bold;
    font-size: 18px;
    color: #343a40;
    margin-bottom: 15px;
}

.accommodation-product-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #36b1dc;
    color: white !important;
    border: none;
    width: 100%;
    box-shadow: 0 4px 6px rgba(54, 177, 220, 0.2);
}

.accommodation-product-button i {
    color: white;
}

.accommodation-product-button:hover {
    background-color: #36b1dc;
    color: white !important;
    box-shadow: 0 6px 12px rgba(54, 177, 220, 0.3);
    transform: translateY(-2px);
}

.accommodation-product-variations {
    margin-top: 15px;
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.accommodation-product-variation {
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.accommodation-variation-radio-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.accommodation-variation-radio-label:hover {
    background-color: #f8f9fa;
}

.variation-disabled {
    opacity: 0.5;
    pointer-events: none;
}

.accommodation-variation-radio-label input[type=radio] {
    margin-right: 10px;
}

.accommodation-product-variation-name {
    font-size: 14px;
    color: #495057;
    flex-grow: 1;
    margin-right: 10px;
}

.accommodation-product-variation-price {
    font-weight: bold;
    color: #00b2e3;
    white-space: nowrap;
}

.accommodation-product-stars {
    display: flex;
    margin-bottom: 10px;
    color: #ffc107;
    align-items: center;
}

.accommodation-product-star-description {
    margin-left: 8px;
    color: #666;
    font-size: 14px;
}

.accommodation-product-short-description {
    margin: 10px 0;
    color: #495057;
    font-size: 14px;
    line-height: 1.5;
    max-height: 84px; /* Approximately 4 lines */
    overflow: hidden;
}

.accommodation-variations-title {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.accommodation-settings-button {
    display: block;
    width: 100%;
    padding: 8px 15px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    color: #495057;
    text-align: center;
    margin-top: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.accommodation-settings-button:hover {
    background-color: #e9ecef;
    color: #212529;
}

/* Accommodation Cart Confirmation Modal Styles */
.accommodation-cart-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.accommodation-cart-confirm-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

.accommodation-cart-confirm-content {
    position: relative;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    overflow: hidden;
    z-index: 1;
}

.accommodation-cart-confirm-header {
    background-color: #36b1dc;
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.accommodation-cart-confirm-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.accommodation-cart-confirm-header h3 i {
    color: white;
    margin-right: 8px;
}

.accommodation-cart-confirm-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    line-height: 1;
}

.accommodation-cart-confirm-body {
    padding: 20px;
}

.accommodation-cart-confirm-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.accommodation-cart-confirm-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.accommodation-cart-confirm-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.accommodation-cart-confirm-total {
    margin-top: 15px;
    border-top: 2px solid #e9ecef;
    padding-top: 15px;
    font-weight: bold;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-label {
    font-size: 16px;
    color: #343a40;
}

.accommodation-cart-confirm-total .accommodation-cart-confirm-value {
    font-size: 16px;
    color: #36b1dc;
}

/* Mini-cart loading indicator */
.dashboard-mini-cart.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.dashboard-mini-cart.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #36b1dc;
    border-top-color: transparent;
    border-radius: 50%;
    animation: mini-cart-spinner 0.8s linear infinite;
}

@keyframes mini-cart-spinner {
    to {
        transform: rotate(360deg);
    }
}

.accommodation-cart-confirm-label {
    font-weight: 600;
    color: #495057;
}

.accommodation-cart-confirm-value {
    color: #343a40;
}

.accommodation-cart-confirm-notice {
    background-color: #e8f4fd;
    color: #0c5460;
    padding: 16px;
    border-radius: 8px;
    margin-top: 15px;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid #36b1dc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.accommodation-cart-confirm-notice i {
    margin-right: 12px;
    font-size: 18px;
    margin-top: 2px;
    color: #36b1dc;
}

.accommodation-cart-confirm-notice p {
    line-height: 1.5;
}

.accommodation-cart-confirm-notice strong {
    color: #0c5460;
    font-weight: 600;
}

.accommodation-cart-confirm-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
}

.accommodation-cart-confirm-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    width: 48%;
    color: white;
}

.accommodation-cart-confirm-btn i {
    color: white;
    margin-right: 8px;
}

.accommodation-cart-confirm-btn-primary {
    background-color: #36b1dc;
    color: white;
}

.accommodation-cart-confirm-btn-primary:hover {
    background-color: #2d9cc3;
    color: white;
}

.accommodation-cart-confirm-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.accommodation-cart-confirm-btn-secondary:hover {
    background-color: #5a6268;
    color: white;
}

/* Bucharest Accommodation Modal Styles */
.bucharest-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.bucharest-modal {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    overflow: hidden;
}

.bucharest-modal-header {
    background-color: #36b1dc;
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bucharest-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.bucharest-modal-header h3 i {
    color: white;
    margin-right: 8px;
}

.bucharest-modal-body {
    padding: 20px;
}

.bucharest-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
}

.bucharest-modal-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    width: 48%;
    color: white;
}

.bucharest-modal-btn i {
    color: white;
    margin-right: 8px;
}

.bucharest-modal-btn-primary {
    background-color: #36b1dc;
    color: white;
}

.bucharest-modal-btn-primary:hover {
    background-color: #2d9cc3;
    color: white;
}

.bucharest-modal-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.bucharest-modal-btn-secondary:hover {
    background-color: #5a6268;
    color: white;
}

.bucharest-modal-form-group {
    margin-bottom: 20px;
}

.bucharest-modal-dates {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #e9ecef;
}

.bucharest-modal-dates-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.bucharest-modal-dates-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.bucharest-modal-dates-label {
    font-weight: 600;
    color: #495057;
}

.bucharest-modal-dates-value {
    color: #36b1dc;
    font-weight: 500;
}

.bucharest-modal-dates-note {
    margin-top: 10px;
    font-size: 13px;
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
}

.bucharest-modal-dates-note i {
    color: #36b1dc;
    margin-right: 6px;
    font-size: 14px;
}

.accommodation-cart-confirm-dates-note {
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
}

.accommodation-cart-confirm-dates-note i {
    color: #36b1dc;
    margin-right: 6px;
    font-size: 14px;
}

.bucharest-modal-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

.bucharest-modal-form-group select,
.bucharest-modal-form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
}

.bucharest-modal-notice {
    background-color: #e8f4fd;
    color: #0c5460;
    padding: 16px;
    border-radius: 8px;
    margin-top: 15px;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid #36b1dc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.bucharest-modal-notice i {
    margin-right: 12px;
    font-size: 18px;
    margin-top: 2px;
    color: #36b1dc;
}

.bucharest-modal-notice p {
    line-height: 1.5;
}

.bucharest-modal-notice strong {
    color: #0c5460;
    font-weight: 600;
}

.nights-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
}

.nights-selector-container {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

.nights-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: none;
    background-color: #36b1dc;
    color: white;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(54, 177, 220, 0.3);
}

.nights-btn:hover {
    background-color: #2d9cc3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(54, 177, 220, 0.4);
}

.nights-btn:disabled {
    background-color: #e0e0e0;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.nights-display {
    margin: 0 20px;
    text-align: center;
    min-width: 80px;
}

.nights-count {
    font-size: 28px;
    font-weight: 700;
    color: #343a40;
    display: block;
    margin-bottom: 2px;
}

.nights-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

@media (max-width: 768px) {
    .accommodation-products-grid {
        grid-template-columns: 1fr;
    }

    /* Mobile styles for Bucharest info card */
    .bucharest-info-content h4 i {
        display: none !important;
    }

    .bucharest-info-content h4 {
        font-size: 18px !important;
    }

    .bucharest-info-content p {
        font-size: 14px !important;
    }

    .bucharest-modal {
        width: 95%;
    }
}
";

wp_add_inline_style('accommodation-css', $accommodation_products_css);

// Get current user
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Check if user has Main Pretour (ID 743)
$has_main_pretour = false;
$customer_orders = wc_get_orders([
    'customer_id' => $user_id,
    'status' => ['processing', 'completed', 'on-hold'],
    'limit' => -1,
]);

foreach ($customer_orders as $order) {
    foreach ($order->get_items() as $item) {
        $product_id = $item->get_product_id();
        if ($product_id == 743) {
            $has_main_pretour = true;
            break 2;
        }
    }
}

// Get Bucharest accommodation products (category ID 33)
$bucharest_products = [];
$args = [
    'post_type' => 'product',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'tax_query' => array(
        array(
            'taxonomy' => 'product_cat',
            'field' => 'id',
            'terms' => 33, // Bucharest accommodation category ID
        ),
    ),
];

$posts = get_posts($args);
foreach ($posts as $post) {
    $product = wc_get_product($post->ID);
    if ($product) {
        $bucharest_products[] = $product;
    }
}

// Check if any products are in the cart
$products_in_cart = [];
if (WC()->cart) {
    foreach (WC()->cart->get_cart() as $cart_item) {
        $products_in_cart[] = $cart_item['product_id'];
    }
}
?>

<div class="accommodation-container">
    <!-- Header with title and mini cart -->
    <div class="dashboard-header mb-4">
        <div class="dashboard-mini-cart-container d-flex justify-content-between align-items-center">
            <div class="dashboard-title">
                <h2 class="text-primary mb-0"><i class="fas fa-hotel me-2"></i> Bucharest Accommodation</h2>
            </div>

            <!-- Mini Cart Button -->
            <?php echo lci_get_mini_cart_html(); ?>
        </div>
    </div>

    <!-- Back to Accommodation Button -->
    <div class="mb-3">
        <a href="<?php echo esc_url(add_query_arg('tab', 'accommodation', remove_query_arg('category_id'))); ?>" class="btn btn-sm btn-outline-secondary d-inline-flex align-items-center">
            <i class="fas fa-arrow-left me-2"></i> Back to All Accommodations
        </a>
    </div>

    <!-- Introduction Section -->
    <div class="mb-5">


        <div class="mb-4" style="background: linear-gradient(135deg, #ffffff, #f8f9fa); border-left: 4px solid #36b1dc; border-radius: 12px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05); overflow: hidden;">
            <div style="padding: 25px; position: relative;">
                <!-- Decorative elements -->
                <div style="position: absolute; top: -15px; right: -15px; width: 120px; height: 120px; border-radius: 50%; background: radial-gradient(circle, rgba(54, 177, 220, 0.1) 0%, rgba(255, 255, 255, 0) 70%);"></div>
                <div style="position: absolute; bottom: -20px; left: -20px; width: 100px; height: 100px; border-radius: 50%; background: radial-gradient(circle, rgba(54, 177, 220, 0.08) 0%, rgba(255, 255, 255, 0) 70%);"></div>

                <div class="bucharest-info-content">
                    <h4 style="color: #333; margin-bottom: 12px; font-weight: 700; font-size: 20px; position: relative; display: inline-block; text-align: center; width: 100%;">
                        <i class="fas fa-building" style="color: #36b1dc; margin-right: 8px; font-size: 18px;"></i>
                        Coming to Bucharest Early?
                        <span style="position: absolute; bottom: -4px; left: 50%; transform: translateX(-50%); width: 40px; height: 3px; background-color: #36b1dc; border-radius: 3px;"></span>
                    </h4>
                    <p style="color: #555; margin-bottom: 0; line-height: 1.6; font-size: 15px; text-align: center;">If you're arriving in Bucharest before the Main Pretour starts, you can book your accommodation here. Select from our recommended hotels in Bucharest.</p>
                </div>
            </div>
        </div>

        <?php if (!$has_main_pretour): ?>
        <!-- Warning for users without Main Pretour -->
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span>This accommodation is only for participants of the Main Pretour. If you haven't registered for the Main Pretour yet, please do so first.</span>
        </div>
        <?php endif; ?>

        <?php if (empty($bucharest_products)): ?>
        <!-- No products available message -->
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span>No accommodation options are currently available for Bucharest. Please check back later.</span>
        </div>
        <?php else: ?>
        <!-- Products Grid -->
        <div class="accommodation-products-grid">
            <?php foreach ($bucharest_products as $product):
                $product_id = $product->get_id();
                $in_cart = in_array($product_id, $products_in_cart);
                $image_url = wp_get_attachment_url($product->get_image_id());
                $price_html = $product->get_price_html();
                $description = $product->get_short_description();

                // Get hotel stars from product meta
                $hotel_stars = get_post_meta($product_id, '_number_of_stars', true);
                if (!$hotel_stars) $hotel_stars = 3; // Default to 3 stars

                // Get hotel features from product meta
                $hotel_features = get_post_meta($product_id, '_hotel_features', true);
                if ($hotel_features) {
                    $features = explode("\n", $hotel_features);
                } else {
                    $features = [];
                }

                // Get product full description and limit to 4 lines
                $full_description = $product->get_description();
                $description_lines = explode("\n", $full_description);
                $short_description = implode("\n", array_slice($description_lines, 0, 4));

                // Get hotel website from product meta
                $hotel_website = get_post_meta($product_id, '_hotel_website', true);
            ?>
            <div class="accommodation-product-card">
                <div class="accommodation-product-image">
                    <?php if ($in_cart): ?>
                    <div class="accommodation-product-badge">In Cart</div>
                    <?php endif; ?>
                    <img src="<?php echo esc_url($image_url ? $image_url : 'https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2025/04/23123928/bucharest.jpg'); ?>" alt="<?php echo esc_attr($product->get_name()); ?>">
                </div>
                <div class="accommodation-product-content">
                    <h4 class="accommodation-product-title"><?php echo esc_html($product->get_name()); ?></h4>

                    <!-- Hotel Stars -->
                    <div class="accommodation-product-stars">
                        <?php for ($i = 0; $i < $hotel_stars; $i++): ?>
                            <i class="fas fa-star"></i>
                        <?php endfor; ?>
                        <span class="accommodation-product-star-description">
                            <?php echo $hotel_stars; ?>-star hotel
                        </span>
                    </div>

                    <!-- Product Description (limited to 4 lines) -->
                    <div class="accommodation-product-short-description">
                        <?php echo wp_kses_post($short_description); ?>
                    </div>

                    <div class="accommodation-product-description">

                        <?php if (!empty($features)): ?>
                        <ul class="mt-2 mb-0" style="padding-left: 20px;">
                            <?php foreach ($features as $feature): ?>
                            <li><?php echo esc_html(trim($feature)); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>

                        <?php if ($hotel_website): ?>
                        <div class="mt-2">
                            <a href="<?php echo esc_url($hotel_website); ?>" target="_blank" style="color: #00b2e3; text-decoration: none; display: inline-flex; align-items: center;">
                                <i class="fas fa-globe me-1"></i> Visit hotel website
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($product->is_type('variable')):
                        $variations = $product->get_available_variations();
                    ?>
                    <div class="accommodation-product-variations" data-product-id="<?php echo esc_attr($product_id); ?>">
                        <h5 class="accommodation-variations-title">Available Room Types:</h5>
                        <?php
                        $first_variation = true;
                        foreach ($variations as $variation):
                            $variation_obj = wc_get_product($variation['variation_id']);
                            if (!$variation_obj || !$variation_obj->is_in_stock()) continue;

                            $variation_id = $variation['variation_id'];

                            // Get attribute names instead of values
                            $attribute_names = [];
                            foreach ($variation['attributes'] as $key => $value) {
                                $taxonomy = str_replace('attribute_', '', $key);
                                $term = get_term_by('slug', $value, $taxonomy);
                                if ($term) {
                                    $attribute_names[] = $term->name;
                                } else {
                                    // For custom product attributes
                                    $attribute_names[] = $value;
                                }
                            }

                            $variation_name = implode(' - ', $attribute_names);
                            $variation_price_html = $variation_obj->get_price_html();

                            // Check if this is a double room variation
                            $is_double_room = stripos($variation_name, 'double') !== false;
                        ?>
                        <div class="accommodation-product-variation">
                            <label class="accommodation-variation-radio-label">
                                <input type="radio" name="variation_<?php echo esc_attr($product_id); ?>"
                                       value="<?php echo esc_attr($variation_id); ?>"
                                       data-variation-name="<?php echo esc_attr($variation_name); ?>"
                                       data-is-double="<?php echo $is_double_room ? 'true' : 'false'; ?>"
                                       <?php echo $first_variation ? 'checked' : ''; ?>>
                                <div class="accommodation-product-variation-name"><?php echo esc_html($variation_name); ?></div>
                                <div class="accommodation-product-variation-price"><?php echo $variation_price_html; ?></div>
                            </label>
                        </div>
                        <?php
                        $first_variation = false;
                        endforeach;
                        ?>
                    </div>
                    <?php else: ?>
                    <div class="accommodation-product-single-price">
                        <?php echo $price_html; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ($in_cart): ?>
                    <button class="accommodation-product-button" disabled style="background: #28a745;">
                        <i class="fas fa-check me-2"></i> Added to Cart
                    </button>
                    <?php else: ?>
                    <button class="accommodation-product-button add-to-cart-btn"
                            data-product-id="<?php echo esc_attr($product_id); ?>"
                            data-product-name="<?php echo esc_attr($product->get_name()); ?>"
                            data-product-type="<?php echo esc_attr($product->get_type()); ?>">
                        <i class="fas fa-shopping-cart me-2"></i> Add to Cart
                    </button>
                    <button onclick="showBucharestModal(event)" class="accommodation-settings-button">
                        <i class="fas fa-cog me-2"></i> Change Stay Settings
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add to Cart Confirmation Modal -->
<div class="accommodation-cart-confirm-modal" id="accommodationCartConfirmModal" style="display: none;">
    <div class="accommodation-cart-confirm-overlay"></div>
    <div class="accommodation-cart-confirm-content">
        <div class="accommodation-cart-confirm-header">
            <h3><i class="fas fa-check-circle me-2"></i> Confirm Your Selection</h3>
            <button type="button" class="accommodation-cart-confirm-close" onclick="document.getElementById('accommodationCartConfirmModal').style.display='none';">&times;</button>
        </div>
        <div class="accommodation-cart-confirm-body">
            <p>Please confirm your accommodation selection:</p>

            <div class="accommodation-cart-confirm-details">
                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Hotel:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmHotelName"></span>
                </div>

                <div class="accommodation-cart-confirm-row" id="confirmRoomTypeRow">
                    <span class="accommodation-cart-confirm-label">Room Type:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmRoomType"></span>
                </div>

                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Number of Nights:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmNights"></span>
                </div>

                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Check-in Date:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmCheckinDate"></span>
                </div>

                <div class="accommodation-cart-confirm-row">
                    <span class="accommodation-cart-confirm-label">Check-out Date:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmCheckoutDate">18/08/2025</span>
                </div>

                <div class="accommodation-cart-confirm-dates-note">
                    <i class="fas fa-info-circle"></i> Note: The night of August 17 is included in the pre-tour price.
                </div>

                <div class="accommodation-cart-confirm-row" id="confirmDoubleRoomRow" style="display: none;">
                    <span class="accommodation-cart-confirm-label">Double Room:</span>
                    <span class="accommodation-cart-confirm-value">Yes (quantity will be doubled)</span>
                </div>

                <div class="accommodation-cart-confirm-row accommodation-cart-confirm-total">
                    <span class="accommodation-cart-confirm-label">Total Price:</span>
                    <span class="accommodation-cart-confirm-value" id="confirmTotalPrice">Calculating...</span>
                </div>
            </div>

            <div class="accommodation-cart-confirm-notice" id="confirmDoubleRoomNotice" style="display: none;">
                <i class="fas fa-info-circle"></i>
                <div>
                    <strong>Important:</strong> When booking a Double Room, the price is calculated per person.
                    <p class="mt-2 mb-0">Since this room is for 2 people, the system will automatically charge for both guests (<span id="confirmDoubleRoomCalc"></span> total).</p>
                    <p class="mt-2 mb-0">If you are booking alone, please make sure you will be sharing the room with someone, or choose a Single Room instead.</p>
                </div>
            </div>
        </div>
        <div class="accommodation-cart-confirm-footer">
            <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-secondary" onclick="document.getElementById('accommodationCartConfirmModal').style.display='none';">
                Cancel
            </button>
            <button type="button" class="accommodation-cart-confirm-btn accommodation-cart-confirm-btn-primary" id="confirmAddToCartBtn">
                <i class="fas fa-shopping-cart me-2"></i> Add to Cart
            </button>
        </div>
    </div>
</div>

<!-- Bucharest Accommodation Modal -->
<div x-data="bucharestAccommodation()" x-init="initModal()">
    <div class="bucharest-modal-overlay" id="bucharestModalOverlay" style="display: none;">
        <div class="bucharest-modal">
            <div class="bucharest-modal-header">
                <h3><i class="fas fa-hotel me-2"></i> Bucharest Accommodation</h3>
                <button onclick="closeBucharestModal()" style="background: none; border: none; color: white; font-size: 20px; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="bucharest-modal-body">
                <p>Please select your accommodation preferences for your stay in Bucharest:</p>

                <!-- Nights Selector -->
                <div class="bucharest-modal-form-group">
                    <label for="nights">How many nights will you stay?</label>
                    <div class="nights-selector">
                        <div class="nights-selector-container">
                            <button type="button" class="nights-btn" @click="decrementNights()" :disabled="nights <= 1">
                                <i class="fas fa-minus"></i>
                            </button>
                            <div class="nights-display">
                                <span class="nights-count" x-text="nights"></span>
                                <span class="nights-label" x-text="nights === 1 ? 'Night' : 'Nights'"></span>
                            </div>
                            <button type="button" class="nights-btn" @click="incrementNights()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Room Type Selector -->
                <div class="bucharest-modal-form-group">
                    <label for="roomType">Room Type:</label>
                    <select id="roomType" x-model="roomType" class="form-select">
                        <option value="single">Single Room (1 person)</option>
                        <option value="double">Double Room (2 people)</option>
                    </select>
                </div>

                <!-- Stay Dates Information -->
                <div class="bucharest-modal-dates">
                    <div class="bucharest-modal-dates-row">
                        <span class="bucharest-modal-dates-label">Check-in Date:</span>
                        <span class="bucharest-modal-dates-value" x-text="calculateCheckinDate()"></span>
                    </div>
                    <div class="bucharest-modal-dates-row">
                        <span class="bucharest-modal-dates-label">Check-out Date:</span>
                        <span class="bucharest-modal-dates-value">18/08/2025</span>
                    </div>
                    <div class="bucharest-modal-dates-note">
                        <i class="fas fa-info-circle"></i> Note: The night of August 17 is included in the pre-tour price.
                    </div>
                </div>

                <!-- Removed Double Room Notice -->
            </div>
            <div class="bucharest-modal-footer">
                <button onclick="closeBucharestModal()" class="bucharest-modal-btn bucharest-modal-btn-secondary">
                    Cancel
                </button>
                <button @click="savePreferences()" class="bucharest-modal-btn bucharest-modal-btn-primary">
                    <i class="fas fa-check me-2"></i> Save Preferences
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function bucharestAccommodation() {
    return {
        showModal: false,
        nights: 1,
        roomType: 'single',

        initModal() {
            console.log('Initializing Bucharest modal');

            // Load saved preferences if available
            const savedNights = sessionStorage.getItem('bucharestNights');
            const savedRoomType = sessionStorage.getItem('bucharestRoomType');

            if (savedNights) this.nights = parseInt(savedNights);
            if (savedRoomType) this.roomType = savedRoomType;

            // Always apply preferences to buttons
            this.applyPreferencesToButtons();

            // Force modal to show on page load
            setTimeout(() => {
                this.showModal = true;
                console.log('Modal should be visible now');
            }, 500);

            // Listen for modal visibility changes
            this.$watch('showModal', value => {
                if (value) {
                    console.log('Modal is now visible');
                    document.querySelector('.bucharest-modal-overlay').style.display = 'flex';
                }
            });
        },

        closeModal() {
            this.showModal = false;
            document.querySelector('.bucharest-modal-overlay').style.display = 'none';
        },

        incrementNights() {
            console.log('Incrementing nights');
            this.nights++;
            document.querySelector('.nights-count').textContent = this.nights;
            this.updateNightsDisplay();
        },

        decrementNights() {
            console.log('Decrementing nights');
            if (this.nights > 1) {
                this.nights--;
                document.querySelector('.nights-count').textContent = this.nights;
                this.updateNightsDisplay();
            }
        },

        updateNightsDisplay() {
            console.log('Updating nights display');

            // Update nights label
            const nightsLabel = document.querySelector('.nights-label');
            if (nightsLabel) {
                nightsLabel.textContent = this.nights === 1 ? 'Night' : 'Nights';
            }

            // Update check-in date display
            const checkinDateElement = document.querySelector('.bucharest-modal-dates-value');
            if (checkinDateElement) {
                checkinDateElement.textContent = this.calculateCheckinDate();
                console.log('Updated check-in date to:', this.calculateCheckinDate());
            }

            // Double room calculation removed
        },

        calculateCheckinDate() {
            // Base date is 17/08/2025 (included in pre-tour)
            // Note: We use 17/08/2025 for calculation, but show 18/08/2025 as checkout date to users
            const baseDate = new Date(2025, 7, 17); // Month is 0-indexed, so 7 = August

            // Calculate check-in date by subtracting nights from base date
            // For 1 night: Check-in on 16/08, stay night of 16-17, check out on 18/08
            // For 2 nights: Check-in on 15/08, stay nights of 15-16 and 16-17, check out on 18/08
            const checkinDate = new Date(baseDate);
            checkinDate.setDate(baseDate.getDate() - this.nights);

            // Format the date as DD/MM/YYYY
            const day = String(checkinDate.getDate()).padStart(2, '0');
            const month = String(checkinDate.getMonth() + 1).padStart(2, '0');
            const year = checkinDate.getFullYear();

            return `${day}/${month}/${year}`;
        },

        savePreferences() {
            console.log('Alpine savePreferences called');

            try {
                // Save preferences to session storage
                sessionStorage.setItem('bucharestNights', this.nights.toString());
                sessionStorage.setItem('bucharestRoomType', this.roomType);
                console.log('Saved to session storage:', { nights: this.nights, roomType: this.roomType });

                // Apply preferences to buttons in Alpine component
                this.applyPreferencesToButtons();

                // Call global functions to ensure everything is updated
                if (typeof updateAddToCartButtons === 'function') {
                    updateAddToCartButtons(this.nights, this.roomType);
                }

                if (typeof applyRoomTypePreferences === 'function') {
                    applyRoomTypePreferences();
                }

                // Close the modal
                this.closeModal();
            } catch (error) {
                console.error('Error saving preferences:', error);
            }
        },

        applyPreferencesToButtons() {
            // Get all add to cart buttons
            const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

            // Update all product variations based on room type preference
            const productVariationContainers = document.querySelectorAll('.accommodation-product-variations');

            productVariationContainers.forEach(container => {
                const productId = container.getAttribute('data-product-id');
                const variations = container.querySelectorAll('.accommodation-product-variation');

                let singleRoomVariation = null;
                let doubleRoomVariation = null;

                // Find single and double room variations
                variations.forEach(variation => {
                    const radioInput = variation.querySelector('input[type=radio]');
                    const variationName = radioInput.getAttribute('data-variation-name');

                    if (variationName.toLowerCase().includes('single')) {
                        singleRoomVariation = variation;
                    } else if (variationName.toLowerCase().includes('double')) {
                        doubleRoomVariation = variation;
                    }
                });

                // Apply room type preference
                if (singleRoomVariation && doubleRoomVariation) {
                    const singleRadio = singleRoomVariation.querySelector('input[type=radio]');
                    const doubleRadio = doubleRoomVariation.querySelector('input[type=radio]');

                    if (this.roomType === 'double') {
                        // Select double room variation
                        doubleRadio.checked = true;

                        // Disable single room variation
                        singleRoomVariation.classList.add('variation-disabled');
                        singleRadio.disabled = true;

                        // Enable double room variation
                        doubleRoomVariation.classList.remove('variation-disabled');
                        doubleRadio.disabled = false;
                    } else {
                        // When single room is selected
                        // Enable single room variation
                        singleRoomVariation.classList.remove('variation-disabled');
                        singleRadio.disabled = false;

                        // Disable double room variation
                        doubleRoomVariation.classList.add('variation-disabled');
                        doubleRadio.disabled = true;

                        // Default to single room
                        singleRadio.checked = true;
                    }
                }
            });

            // Update button text to reflect preferences
            addToCartButtons.forEach(button => {
                const buttonText = this.roomType === 'double'
                    ? `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${this.nights} nights × 2 people)`
                    : `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${this.nights} nights)`;

                button.innerHTML = buttonText;
            });
        },

        addToCart(productId, quantity, variationId = null) {
            // Create form data
            const data = new FormData();
            data.append('add-to-cart', productId);
            data.append('quantity', quantity);
            if (variationId) {
                data.append('variation_id', variationId);
            }

            // Send AJAX request
            fetch(window.location.href, {
                method: 'POST',
                body: data,
                credentials: 'same-origin'
            })
            .then(response => {
                // Reload the page to show updated cart
                window.location.reload();
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                alert('There was an error adding this item to your cart. Please try again.');
            });
        }
    };
}
</script>

<!-- Script for modal functionality -->
<script>
// Function to show the Bucharest modal
function showBucharestModal(event) {
    event.preventDefault();
    const modalElement = document.querySelector('.bucharest-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'flex';
        console.log('Modal displayed via direct function call');
    }
}

// Function to close the Bucharest modal
function closeBucharestModal() {
    const modalElement = document.querySelector('.bucharest-modal-overlay');
    if (modalElement) {
        modalElement.style.display = 'none';
        console.log('Modal hidden via direct function call');
    }
}

// Function to save preferences and close the modal
function saveBucharestPreferences() {
    console.log('Save preferences clicked');

    try {
        // Get nights value - try multiple selectors to ensure we find it
        let nights = 1;
        const nightsElement = document.querySelector('.nights-count');
        if (nightsElement) {
            nights = parseInt(nightsElement.textContent) || 1;
            console.log('Found nights from .nights-count:', nights);
        } else {
            console.warn('Could not find .nights-count element, using default value');
        }

        // Get room type value - try multiple approaches
        let roomType = 'single';
        const roomTypeElement = document.querySelector('#roomType');
        if (roomTypeElement) {
            roomType = roomTypeElement.value || 'single';
            console.log('Found room type from #roomType:', roomType);
        } else {
            // Fallback: check if double room radio is checked
            const doubleRoomRadio = document.querySelector('input[data-is-double="true"]:checked');
            if (doubleRoomRadio) {
                roomType = 'double';
                console.log('Detected double room from checked radio');
            }
            console.warn('Could not find #roomType element, using detected or default value');
        }

        console.log('Final values:', { nights, roomType });

        // Save to session storage
        sessionStorage.setItem('bucharestNights', nights.toString());
        sessionStorage.setItem('bucharestRoomType', roomType);
        console.log('Saved to session storage');

        // Update Add to Cart buttons
        updateAddToCartButtons(nights, roomType);

        // Apply room type preferences
        applyRoomTypePreferences();

        console.log('Preferences saved:', { nights, roomType });
    } catch (error) {
        console.error('Error saving preferences:', error);
    }

    // Close the modal
    closeBucharestModal();
}

// Function to update Add to Cart buttons with new nights value
function updateAddToCartButtons(nights, roomType) {
    console.log('Updating Add to Cart buttons with:', { nights, roomType });
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    console.log('Found buttons:', addToCartButtons.length);

    addToCartButtons.forEach(button => {
        // Skip buttons that are already in "Added to Cart" state
        if (button.disabled) {
            console.log('Skipping disabled button');
            return;
        }

        const buttonText = `<i class="fas fa-shopping-cart me-2"></i> Add to Cart (${nights} nights)`;
        console.log('Setting button text to:', buttonText);
        button.innerHTML = buttonText;
    });
}

// Apply room type preferences - global function
function applyRoomTypePreferences() {
    try {
        console.log('Applying room type preferences');
        const savedRoomType = sessionStorage.getItem('bucharestRoomType') || 'single';
        console.log('Saved room type:', savedRoomType);

        // Get saved nights
        const savedNights = parseInt(sessionStorage.getItem('bucharestNights')) || 1;
        console.log('Saved nights:', savedNights);

        // Update all product variations based on room type preference
        const productVariationContainers = document.querySelectorAll('.accommodation-product-variations');
        console.log('Found variation containers:', productVariationContainers.length);

        if (productVariationContainers.length === 0) {
            console.warn('No variation containers found, nothing to update');
            return;
        }

        // Update Add to Cart buttons first
        updateAddToCartButtons(savedNights, savedRoomType);

        productVariationContainers.forEach(container => {
            try {
                const variations = container.querySelectorAll('.accommodation-product-variation');
                console.log('Found variations:', variations.length);

                if (variations.length === 0) {
                    console.warn('No variations found in container, skipping');
                    return;
                }

                let singleRoomVariation = null;
                let doubleRoomVariation = null;

                // Find single and double room variations
                variations.forEach(variation => {
                    try {
                        const radioInput = variation.querySelector('input[type=radio]');
                        if (!radioInput) {
                            console.warn('No radio input found in variation, skipping');
                            return;
                        }

                        const variationName = radioInput.getAttribute('data-variation-name') || '';
                        console.log('Checking variation:', variationName);

                        if (variationName.toLowerCase().includes('single')) {
                            singleRoomVariation = variation;
                            console.log('Found single room variation');
                        } else if (variationName.toLowerCase().includes('double')) {
                            doubleRoomVariation = variation;
                            console.log('Found double room variation');
                        }
                    } catch (err) {
                        console.error('Error processing variation:', err);
                    }
                });

                // Apply room type preference
                if (singleRoomVariation && doubleRoomVariation) {
                    const singleRadio = singleRoomVariation.querySelector('input[type=radio]');
                    const doubleRadio = doubleRoomVariation.querySelector('input[type=radio]');

                    if (savedRoomType === 'double') {
                        console.log('Applying double room preference');
                        // Select double room variation
                        if (doubleRadio) doubleRadio.checked = true;

                        // Disable single room variation
                        singleRoomVariation.classList.add('variation-disabled');
                        if (singleRadio) singleRadio.disabled = true;

                        // Enable double room variation
                        doubleRoomVariation.classList.remove('variation-disabled');
                        if (doubleRadio) doubleRadio.disabled = false;
                    } else {
                        console.log('Applying single room preference');
                        // When single room is selected
                        // Enable single room variation
                        singleRoomVariation.classList.remove('variation-disabled');
                        if (singleRadio) singleRadio.disabled = false;

                        // Disable double room variation
                        doubleRoomVariation.classList.add('variation-disabled');
                        if (doubleRadio) doubleRadio.disabled = true;

                        // Default to single room
                        if (singleRadio) singleRadio.checked = true;
                    }
                } else {
                    console.log('Could not find both single and double variations');
                }
            } catch (err) {
                console.error('Error processing container:', err);
            }
        });
    } catch (error) {
        console.error('Error in applyRoomTypePreferences:', error);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for Alpine.js');

    // Check if Alpine.js is loaded
    if (typeof Alpine === 'undefined') {
        console.log('Alpine.js not loaded, loading it manually');

        // Load Alpine.js manually if it's not loaded
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js';
        script.defer = true;
        document.head.appendChild(script);

        script.onload = function() {
            console.log('Alpine.js loaded manually');

            // Initialize Alpine.js
            Alpine.start();
        };
    }

    // Force modal to show on page load
    setTimeout(function() {
        const modalElement = document.getElementById('bucharestModalOverlay');
        if (modalElement) {
            modalElement.style.display = 'flex';
            console.log('Modal display set to flex via DOM');
        }
    }, 1000);

    // Apply preferences on page load
    applyRoomTypePreferences();

    // Add to Cart Confirmation Modal Functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    const confirmModal = document.getElementById('accommodationCartConfirmModal');
    const confirmAddToCartBtn = document.getElementById('confirmAddToCartBtn');

    // Get saved preferences
    const getNights = function() {
        const savedNights = sessionStorage.getItem('bucharestNights');
        return savedNights ? parseInt(savedNights) : 1;
    };

    const getRoomType = function() {
        const savedRoomType = sessionStorage.getItem('bucharestRoomType');
        return savedRoomType || 'single';
    };

    // Add click event to all Add to Cart buttons
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');
            const productType = this.getAttribute('data-product-type');

            // Set hotel name in confirmation modal
            document.getElementById('confirmHotelName').textContent = productName;

            // Get current nights value from session storage
            const nights = getNights();

            // Set nights in confirmation modal
            document.getElementById('confirmNights').textContent = nights + (nights === 1 ? ' night' : ' nights');

            // Calculate and display check-in date
            const calculateCheckinDate = function(nights) {
                // Base date is 17/08/2025 (included in pre-tour)
                // Note: We use 17/08/2025 for calculation, but show 18/08/2025 as checkout date to users
                const baseDate = new Date(2025, 7, 17); // Month is 0-indexed, so 7 = August

                // Calculate check-in date by subtracting nights from base date
                // For 1 night: Check-in on 16/08, stay night of 16-17, check out on 18/08
                // For 2 nights: Check-in on 15/08, stay nights of 15-16 and 16-17, check out on 18/08
                const checkinDate = new Date(baseDate);
                checkinDate.setDate(baseDate.getDate() - nights);

                // Format the date as DD/MM/YYYY
                const day = String(checkinDate.getDate()).padStart(2, '0');
                const month = String(checkinDate.getMonth() + 1).padStart(2, '0');
                const year = checkinDate.getFullYear();

                return `${day}/${month}/${year}`;
            };

            // Set check-in date
            document.getElementById('confirmCheckinDate').textContent = calculateCheckinDate(nights);

            // Handle variation selection for variable products
            let variationId = null;
            let variationName = null;
            let isDoubleRoom = false;

            // Get user room type preference
            const userRoomType = getRoomType();
            const isUserDoubleRoom = userRoomType === 'double';

            if (productType === 'variable') {
                // Find the appropriate variation based on user preference
                let selectedVariation = null;

                if (isUserDoubleRoom) {
                    // If user prefers double room, find double room variation
                    selectedVariation = document.querySelector(`input[name="variation_${productId}"][data-is-double="true"]`);
                } else {
                    // Otherwise, get the selected variation
                    selectedVariation = document.querySelector(`input[name="variation_${productId}"]:checked`);
                }

                if (selectedVariation) {
                    variationId = selectedVariation.value;
                    variationName = selectedVariation.getAttribute('data-variation-name');
                    isDoubleRoom = selectedVariation.getAttribute('data-is-double') === 'true' || isUserDoubleRoom;

                    // Show room type in confirmation modal
                    document.getElementById('confirmRoomTypeRow').style.display = 'flex';
                    document.getElementById('confirmRoomType').textContent = variationName;
                } else {
                    // Hide room type if no variation selected
                    document.getElementById('confirmRoomTypeRow').style.display = 'none';
                }
            } else {
                // Hide room type for simple products
                document.getElementById('confirmRoomTypeRow').style.display = 'none';
            }

            // Hide double room notice (no longer needed)
            document.getElementById('confirmDoubleRoomRow').style.display = 'none';
            document.getElementById('confirmDoubleRoomNotice').style.display = 'none';

            // Calculate and display the total price
            let productPrice = 0;

            if (productType === 'variable' && variationId) {
                // Get price from the selected variation
                const selectedVariation = document.querySelector(`input[value="${variationId}"]`);
                if (selectedVariation) {
                    productPrice = parseFloat(selectedVariation.getAttribute('data-price') || 0);
                }
            } else {
                // Get price from the button's data attribute
                productPrice = parseFloat(this.getAttribute('data-price') || 0);
            }

            // Calculate total price based on nights
            const totalPrice = productPrice * nights;

            // Format the price with currency symbol
            const formattedPrice = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'EUR',
                minimumFractionDigits: 2
            }).format(totalPrice);

            // Update the price display
            document.getElementById('confirmTotalPrice').textContent = formattedPrice;

            // Calculate and display total price
            const calculateTotal = function() {
                let price = 0;
                let priceHtml = '';

                if (productType === 'variable' && variationId) {
                    // For variable products, get the selected variation price
                    const variationPriceElement = document.querySelector(`.accommodation-product-variation input[value="${variationId}"]`)
                        ?.closest('.accommodation-product-variation')
                        ?.querySelector('.accommodation-product-variation-price');

                    if (variationPriceElement) {
                        priceHtml = variationPriceElement.innerHTML;

                        // Extract numeric price from HTML with Euro symbol
                        const priceMatch = priceHtml.match(/([0-9,.]+)/);
                        if (priceMatch) {
                            // Replace comma with dot for proper parsing
                            price = parseFloat(priceMatch[0].replace(/,/g, '.'));
                        }
                    }
                } else {
                    // For simple products, get the product price
                    const priceElement = document.querySelector(`.accommodation-product-card[data-product-id="${productId}"] .accommodation-product-single-price`);

                    if (priceElement) {
                        priceHtml = priceElement.innerHTML;

                        // Extract numeric price from HTML with Euro symbol
                        const priceMatch = priceHtml.match(/([0-9,.]+)/);
                        if (priceMatch) {
                            // Replace comma with dot for proper parsing
                            price = parseFloat(priceMatch[0].replace(/,/g, '.'));
                        }
                    }
                }

                // Calculate total based on nights (no special calculation for double rooms)
                const totalQuantity = nights;
                const totalPrice = price * totalQuantity;

                // Display total price
                if (totalPrice > 0) {
                    document.getElementById('confirmTotalPrice').innerHTML =
                        `<strong>${totalPrice.toFixed(2)} €</strong> (${totalQuantity} nights × ${price.toFixed(2)} €)`;
                } else {
                    document.getElementById('confirmTotalPrice').textContent = 'Price information not available';
                }
            };

            // Calculate total price
            calculateTotal();

            // Store data for add to cart action
            confirmAddToCartBtn.setAttribute('data-product-id', productId);
            if (variationId) {
                confirmAddToCartBtn.setAttribute('data-variation-id', variationId);
            } else {
                confirmAddToCartBtn.removeAttribute('data-variation-id');
            }
            confirmAddToCartBtn.setAttribute('data-is-double-room', (isDoubleRoom || isUserDoubleRoom).toString());

            // Show confirmation modal
            confirmModal.style.display = 'flex';
        });
    });

    // Add to Cart action
    confirmAddToCartBtn.addEventListener('click', function() {
        const productId = this.getAttribute('data-product-id');
        const variationId = this.getAttribute('data-variation-id');

        // Get nights from preferences (use as quantity)
        const quantity = getNights();

        // Create a direct add-to-cart URL
        let addToCartUrl = '?add-to-cart=' + productId + '&quantity=' + quantity;

        // Add variation ID if applicable
        if (variationId) {
            addToCartUrl += '&variation_id=' + variationId;
        }

        // Send AJAX request using jQuery to ensure compatibility with WooCommerce
        jQuery.ajax({
            type: 'GET',
            url: addToCartUrl,
            success: function(response) {
                // Trigger WooCommerce's update_cart event
                jQuery(document.body).trigger('added_to_cart', [null, null, jQuery('.add-to-cart-btn')]);

                // Hide confirmation modal
                confirmModal.style.display = 'none';

                // Show success message instead of reloading
                const productCard = document.querySelector(`.accommodation-product-card [data-product-id="${productId}"]`).closest('.accommodation-product-card');
                const addToCartBtn = productCard.querySelector('.add-to-cart-btn');

                // Replace button with success message
                addToCartBtn.innerHTML = '<i class="fas fa-check me-2"></i> Added to Cart';
                addToCartBtn.style.backgroundColor = '#28a745';
                addToCartBtn.disabled = true;

                // Hide settings button
                const settingsBtn = productCard.querySelector('.accommodation-settings-button');
                if (settingsBtn) {
                    settingsBtn.style.display = 'none';
                }

                // Update mini cart count (if available)
                const miniCartCount = document.querySelector('.mini-cart-count');
                if (miniCartCount) {
                    const currentCount = parseInt(miniCartCount.textContent) || 0;
                    miniCartCount.textContent = currentCount + 1;
                }

                // Refresh the mini-cart contents
                refreshMiniCart();
            },
            error: function(error) {
                console.error('Error adding to cart:', error);

                // Hide confirmation modal
                confirmModal.style.display = 'none';
            }
        });
    });
});

// Function to refresh the mini-cart - global function
function refreshMiniCart() {
    // Get the mini-cart container
    const miniCartContainer = document.querySelector('.dashboard-mini-cart');
    if (!miniCartContainer) {
        console.warn('Mini-cart container not found');
        return;
    }

    console.log('Refreshing mini-cart...');

    // Add loading indicator
    miniCartContainer.classList.add('loading');

    // Fetch updated mini-cart HTML via AJAX
    fetch('?wc-ajax=get_refreshed_fragments', {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update fragments (including mini-cart)
        if (data.fragments) {
            for (let id in data.fragments) {
                const element = document.querySelector(id);
                if (element) {
                    element.innerHTML = data.fragments[id];
                }
            }
        }

        // Remove loading indicator
        miniCartContainer.classList.remove('loading');

        console.log('Mini-cart refreshed successfully');
    })
    .catch(error => {
        console.error('Error refreshing mini-cart:', error);
        miniCartContainer.classList.remove('loading');
    });
}

</script>

<!-- Add CSS for mini-cart loading indicator -->
<style>
.dashboard-mini-cart.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.dashboard-mini-cart.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #36b1dc;
    border-top-color: transparent;
    border-radius: 50%;
    animation: mini-cart-spinner 0.8s linear infinite;
}

@keyframes mini-cart-spinner {
    to {
        transform: rotate(360deg);
    }
}
</style>
