
<?php
// File: includes/profile.php

/**
 * Handle avatar upload including WebP support
 */
add_action('wp_ajax_lci_upload_avatar', function () {
  // Check if user is logged in
  if (!is_user_logged_in()) {
    wp_send_json_error(['message' => 'You must be logged in to upload an avatar']);
    return;
  }

  // Check if file was uploaded
  if (empty($_FILES['avatar'])) {
    wp_send_json_error(['message' => 'No file was uploaded']);
    return;
  }

  $file = $_FILES['avatar'];

  // Validate file for errors
  if ($file['error'] !== UPLOAD_ERR_OK) {
    $error_message = 'Unknown upload error';
    switch ($file['error']) {
      case UPLOAD_ERR_INI_SIZE:
      case UPLOAD_ERR_FORM_SIZE:
        $error_message = 'The file is too large';
        break;
      case UPLOAD_ERR_PARTIAL:
        $error_message = 'The file was only partially uploaded';
        break;
      case UPLOAD_ERR_NO_FILE:
        $error_message = 'No file was uploaded';
        break;
    }
    wp_send_json_error(['message' => $error_message]);
    return;
  }

  // Validate file type
  $allowed_mime_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  $file_info = wp_check_filetype_and_ext($file['tmp_name'], $file['name']);

  if (!in_array($file_info['type'], $allowed_mime_types)) {
    wp_send_json_error(['message' => 'Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.']);
    return;
  }

  // Load required files for media handling
  require_once ABSPATH . 'wp-admin/includes/file.php';
  require_once ABSPATH . 'wp-admin/includes/media.php';
  require_once ABSPATH . 'wp-admin/includes/image.php';

  // Add WebP to allowed mime types if not already included
  add_filter('upload_mimes', function ($mimes) {
    if (!isset($mimes['webp'])) {
      $mimes['webp'] = 'image/webp';
    }
    return $mimes;
  });

  // Handle the upload
  $upload = wp_handle_upload($file, ['test_form' => false]);

  if (isset($upload['error'])) {
    wp_send_json_error(['message' => $upload['error']]);
    return;
  }

  // Create attachment post
  $attachment = [
    'guid' => $upload['url'],
    'post_mime_type' => $upload['type'],
    'post_title' => sanitize_file_name($file['name']),
    'post_content' => '',
    'post_status' => 'inherit'
  ];

  // Insert attachment into media library
  $attach_id = wp_insert_attachment($attachment, $upload['file']);

  if (is_wp_error($attach_id)) {
    wp_send_json_error(['message' => $attach_id->get_error_message()]);
    return;
  }

  // Generate metadata and thumbnails
  $attach_data = wp_generate_attachment_metadata($attach_id, $upload['file']);
  wp_update_attachment_metadata($attach_id, $attach_data);

  // Delete previous avatar if exists
  $previous_avatar_id = get_user_meta(get_current_user_id(), 'wp_user_avatar', true);
  if (!empty($previous_avatar_id) && $previous_avatar_id != $attach_id) {
    wp_delete_attachment($previous_avatar_id, true);
  }

  // Update user meta with new avatar
  update_user_meta(get_current_user_id(), 'wp_user_avatar', $attach_id);

  // Return success response
  wp_send_json_success([
    'url' => $upload['url'],
    'message' => 'Avatar updated successfully.'
  ]);
});

/**
 * Add WebP to list of allowed mime types for WordPress
 */
add_filter('mime_types', function ($mimes) {
  $mimes['webp'] = 'image/webp';
  return $mimes;
});

/**
 * Ensure WordPress can check WebP mime types correctly
 */
add_filter('wp_check_filetype_and_ext', function ($data, $file, $filename, $mimes) {
  if (empty($data['type'])) {
    $ext = pathinfo($filename, PATHINFO_EXTENSION);
    if ($ext === 'webp') {
      $data['type'] = 'image/webp';
      $data['ext'] = 'webp';
    }
  }
  return $data;
}, 10, 4);
