<?php
/**
 * Travelpayouts Settings Admin Template
 */

// Handle form submission
if (isset($_POST['submit'])) {
    update_option('lci_travelpayouts_api_token', sanitize_text_field($_POST['api_token']));
    update_option('lci_travelpayouts_marker', sanitize_text_field($_POST['marker']));
    update_option('lci_travelpayouts_partner_id', sanitize_text_field($_POST['partner_id']));

    echo '<div class="notice notice-success"><p>Settings saved successfully!</p></div>';
}

// Get current settings
$api_token = get_option('lci_travelpayouts_api_token', '');
$marker = get_option('lci_travelpayouts_marker', '');
$partner_id = get_option('lci_travelpayouts_partner_id', '');

// Calculate potential earnings
$total_participants = LCI_Participant::count_participants([]);
$estimated_bookings = ceil($total_participants * 0.3); // Assume 30% will book travel
$avg_commission = 25; // Average commission per booking in EUR
$estimated_monthly_earnings = $estimated_bookings * $avg_commission;
?>

<div class="wrap lci-admin-wrap">
    <h1 class="text-3xl font-bold mb-6">Flight Booking Monetization</h1>

    <!-- Earnings Potential Dashboard -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Estimated Monthly Earnings</p>
                    <p class="text-2xl font-bold text-gray-900">€<?php echo number_format($estimated_monthly_earnings); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Participants</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($total_participants); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-neumorph p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Estimated Bookings</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo number_format($estimated_bookings); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Setup Instructions -->
    <div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
        <h2 class="text-xl font-semibold text-blue-900 mb-4">🚀 Setup Instructions</h2>
        <div class="space-y-3 text-blue-800">
            <p><strong>1. Sign up for Travelpayouts:</strong> Visit <a href="https://www.travelpayouts.com/" target="_blank" class="underline">travelpayouts.com</a> and create a free account</p>
            <p><strong>2. Get your API credentials:</strong> Go to Tools → API in your Travelpayouts dashboard</p>
            <p><strong>3. Configure below:</strong> Enter your API token, marker, and partner ID</p>
            <p><strong>4. Add widgets:</strong> Flight search widget will automatically appear on user dashboards</p>
        </div>
    </div>

    <!-- Configuration Form -->
    <div class="bg-white rounded-xl shadow-neumorph p-6">
        <h2 class="text-xl font-semibold mb-6">API Configuration</h2>

        <form method="post" action="">
            <?php wp_nonce_field('lci_travelpayouts_settings'); ?>

            <div class="space-y-6">
                <div>
                    <label for="api_token" class="block text-sm font-medium text-gray-700 mb-2">
                        API Token
                        <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="api_token"
                        name="api_token"
                        value="<?php echo esc_attr($api_token); ?>"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Your Travelpayouts API token"
                        required
                    >
                    <p class="mt-1 text-sm text-gray-500">
                        Found in your Travelpayouts dashboard under Tools → API
                    </p>
                </div>

                <div>
                    <label for="marker" class="block text-sm font-medium text-gray-700 mb-2">
                        Marker (Affiliate ID)
                        <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="marker"
                        name="marker"
                        value="<?php echo esc_attr($marker); ?>"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Your affiliate marker"
                        required
                    >
                    <p class="mt-1 text-sm text-gray-500">
                        Your unique affiliate identifier for tracking commissions
                    </p>
                </div>

                <div>
                    <label for="partner_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Partner ID
                    </label>
                    <input
                        type="text"
                        id="partner_id"
                        name="partner_id"
                        value="<?php echo esc_attr($partner_id); ?>"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Your partner ID (optional)"
                    >
                    <p class="mt-1 text-sm text-gray-500">
                        Additional tracking parameter (optional)
                    </p>
                </div>
            </div>

            <div class="mt-8">
                <button
                    type="submit"
                    name="submit"
                    class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                    Save Settings
                </button>
            </div>
        </form>
    </div>

    <!-- Widget Preview -->
    <?php if (!empty($api_token) && !empty($marker)): ?>
    <div class="mt-8 bg-white rounded-xl shadow-neumorph p-6">
        <h2 class="text-xl font-semibold mb-6">Flight Widget Preview</h2>

        <div class="max-w-2xl mx-auto">
            <!-- Flight Widget Preview -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium mb-4">Flight Search Widget</h3>
                <?php echo LCI_Travelpayouts::get_flight_widget('London, UK'); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Monetization Tips -->
    <div class="mt-8 bg-green-50 border border-green-200 rounded-xl p-6">
        <h2 class="text-xl font-semibold text-green-900 mb-4">💡 Monetization Tips</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-green-800">
            <div>
                <h3 class="font-medium mb-2">Maximize Flight Bookings:</h3>
                <ul class="space-y-1 text-sm">
                    <li>• Place flight widget prominently on dashboard</li>
                    <li>• Pre-fill event dates (Aug 16-25, 2025)</li>
                    <li>• Target international participants</li>
                    <li>• Promote early booking for better prices</li>
                    <li>• Offer 3 destination options: Bucharest, Brasov, Sibiu</li>
                </ul>
            </div>
            <div>
                <h3 class="font-medium mb-2">Flight Commission Details:</h3>
                <ul class="space-y-1 text-sm">
                    <li>• Flight bookings: 1-3% commission</li>
                    <li>• Average commission: €15-50 per booking</li>
                    <li>• Higher rates for premium airlines</li>
                    <li>• Bonus for volume milestones</li>
                    <li>• Monthly payment schedule</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.lci-travel-widget {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    background: #f9fafb;
}

.widget-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #374151;
}

.travel-search-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.travel-search-form .form-group {
    display: flex;
    flex-direction: column;
}

.travel-search-form label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.travel-search-form .form-control {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
}

.travel-search-form .btn {
    padding: 0.75rem 1.5rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.travel-search-form .btn:hover {
    background: #2563eb;
}

.travel-results {
    margin-top: 1rem;
    min-height: 100px;
}

.travel-loading {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.travel-error {
    padding: 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 4px;
    color: #dc2626;
}

.travel-results-list h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #374151;
}

.travel-result-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    background: white;
}

.result-info {
    flex: 1;
}

.result-actions .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.airport-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d1d5db;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 10;
}

.airport-suggestion {
    padding: 0.5rem;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
}

.airport-suggestion:hover {
    background: #f3f4f6;
}
</style>
