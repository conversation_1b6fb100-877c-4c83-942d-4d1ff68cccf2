<?php
/**
 * Admin template for Accommodation management
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Get accommodation table status
$table_status = LCI_Accommodation_Unified::check_accommodation_table();
?>

<!-- Add Tailwind CSS via CDN -->
<script src="https://cdn.tailwindcss.com"></script>
<script>
  tailwind.config = {
    theme: {
      extend: {
        colors: {
          primary: '#0073aa', // WordPress primary blue
          secondary: '#46b450', // WordPress success green
          danger: '#dc3232', // WordPress error red
          warning: '#ffb900', // WordPress warning yellow
        }
      }
    }
  }
</script>

<div class="wrap lci-admin-wrap">
    <h1 class="wp-heading-inline"><?php _e('Accommodation Management', 'lci-2025-dashboard'); ?></h1>

    <div id="accommodation-manager">
        <!-- Sync Tool -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-medium text-gray-800 mb-4">Sync Accommodation Data</h2>

            <div class="mb-4">
                <p class="mb-2">This tool will sync accommodation data from WooCommerce orders with the following categories:</p>
                <ul class="list-disc pl-5 mb-4">
                    <li>Category ID 18 - Accommodation for Main Event (Check-in: 21/08/2025, Check-out: 24/08/2025)</li>
                    <li>Category ID 20 - Accommodation for Councillors (Check-in: 20/08/2025, Check-out: 24/08/2025)</li>
                    <li>Category ID 37 - Additional night in Brasov</li>
                    <li>Category ID 39 - Additional night in Bucharest</li>
                    <li>Category ID 30 - Additional night after pretour</li>
                </ul>
                <p class="mb-4">The tool will also map product IDs to hotels and extract room sharing information.</p>
            </div>

            <div class="flex items-center">
                <button
                    id="sync-button"
                    onclick="syncAccommodation()"
                    class="px-4 py-2 bg-primary text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="sync-text">Sync Accommodation Data</span>
                    <div id="sync-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Syncing...</span>
                    </div>
                </button>

                <div class="ml-4" id="sync-message-container">
                    <div id="sync-success-message" class="text-secondary" style="display: none;">
                        <span id="sync-success-text"></span>
                    </div>
                    <div id="sync-error-message" class="text-danger" style="display: none;">
                        <span id="sync-error-text"></span>
                    </div>
                </div>
            </div>

            <div class="mt-4 flex space-x-4">
                <button
                    id="clear-table-button"
                    onclick="clearTable()"
                    class="px-4 py-2 bg-warning text-white rounded hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-warning focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="clear-table-text">Clear Accommodation Table</span>
                    <div id="clear-table-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Clearing...</span>
                    </div>
                </button>

                <button
                    id="update-table-structure-button"
                    onclick="updateTableStructure()"
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="update-table-structure-text">Update Table Structure</span>
                    <div id="update-table-structure-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Updating...</span>
                    </div>
                </button>

                <button
                    id="recreate-table-button"
                    onclick="recreateTable()"
                    class="px-4 py-2 bg-danger text-white rounded hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-danger focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="recreate-table-text">Recreate Accommodation Table</span>
                    <div id="recreate-table-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Recreating...</span>
                    </div>
                </button>
            </div>
        </div>

        <!-- Bulk Update Dates Tool -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-medium text-gray-800 mb-4">Bulk Update Check-in/Check-out Dates</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="bulk-category-select" class="block text-sm font-medium text-gray-700 mb-1">Accommodation Category</label>
                    <select
                        id="bulk-category-select"
                        class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"
                    >
                        <option value="">Select Category</option>
                        <option value="18">Main Event</option>
                        <option value="20">Councillors</option>
                        <option value="37">Additional night in Brasov</option>
                        <option value="39">Additional night in Bucharest</option>
                        <option value="30">Additional night after pretour</option>
                    </select>
                </div>
                <div>
                    <label for="bulk-checkin-date" class="block text-sm font-medium text-gray-700 mb-1">Check-in Date</label>
                    <input
                        type="date"
                        id="bulk-checkin-date"
                        class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"
                    >
                </div>
                <div>
                    <label for="bulk-checkout-date" class="block text-sm font-medium text-gray-700 mb-1">Check-out Date</label>
                    <input
                        type="date"
                        id="bulk-checkout-date"
                        class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"
                    >
                </div>
            </div>

            <div class="flex items-center">
                <button
                    id="bulk-update-button"
                    onclick="bulkUpdateDates()"
                    class="px-4 py-2 bg-primary text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                >
                    <span id="bulk-update-text">Update Dates</span>
                    <div id="bulk-update-loading" class="flex items-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Updating...</span>
                    </div>
                </button>

                <div class="ml-4" id="bulk-update-message-container">
                    <div id="bulk-update-success-message" class="text-secondary" style="display: none;">
                        <span id="bulk-update-success-text"></span>
                    </div>
                    <div id="bulk-update-error-message" class="text-danger" style="display: none;">
                        <span id="bulk-update-error-text"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Participants List -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-medium text-gray-800">Accommodation Participants</h2>
            </div>

            <!-- Search and Filter Controls -->
            <div class="flex flex-wrap items-center justify-between mb-4">
                <div class="w-full md:w-auto mb-4 md:mb-0">
                    <div class="flex items-center">
                        <input
                            type="text"
                            id="search-query-input"
                            oninput="setSearchQuery(this.value)"
                            placeholder="Search by name, email..."
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-64"
                        >
                    </div>
                </div>

                <div class="w-full md:w-auto">
                    <div class="flex flex-wrap items-center space-x-0 md:space-x-4 space-y-2 md:space-y-0">
                        <select
                            id="hotel-filter-select"
                            onchange="setHotelFilter(this.value)"
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                        >
                            <option value="">All Hotels</option>
                            <!-- Hotel options will be populated by JavaScript -->
                        </select>

                        <select
                            id="accommodation-type-filter-select"
                            onchange="setAccommodationTypeFilter(this.value)"
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                        >
                            <option value="">All Accommodation Types</option>
                            <option value="Main Event">Main Event</option>
                            <option value="Councillors">Councillors</option>
                            <option value="Additional night in Brasov">Additional night in Brasov</option>
                            <option value="Additional night in Bucharest">Additional night in Bucharest</option>
                            <option value="Additional night after pretour">Additional night after pretour</option>
                        </select>

                        <select
                            id="room-type-filter-select"
                            onchange="setRoomTypeFilter(this.value)"
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                        >
                            <option value="">All Room Types</option>
                            <!-- Room type options will be populated by JavaScript -->
                        </select>

                        <select
                            id="room-sharing-filter-select"
                            onchange="setRoomSharingFilter(this.value)"
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                        >
                            <option value="">All Room Sharing</option>
                            <option value="shared">Shared Room</option>
                            <option value="single">Single Room</option>
                        </select>

                        <select
                            id="status-filter-select"
                            onchange="setStatusFilter(this.value)"
                            class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                        >
                            <option value="">All Statuses</option>
                            <option value="completed">Completed</option>
                            <option value="processing">Processing</option>
                            <option value="on-hold">On Hold</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="pending">Pending</option>
                        </select>

                        <div class="flex items-center">
                            <select
                                id="country-filter-select"
                                onchange="setCountryFilter(this.value)"
                                class="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full md:w-auto mr-0 md:mr-2"
                            >
                                <option value="">All Countries</option>
                                <!-- Country options will be populated by JavaScript -->
                            </select>
                        </div>

                        <div class="flex space-x-2">
                            <button
                                onclick="resetFilters()"
                                class="mt-2 md:mt-0 px-3 py-2 bg-secondary text-white rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-opacity-50 transition-colors duration-200"
                            >
                                Reset Filters
                            </button>

                            <button
                                id="refresh-button"
                                onclick="refreshParticipants()"
                                class="mt-2 md:mt-0 px-3 py-2 bg-primary text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                            >
                                <span id="refresh-text">Refresh</span>
                                <span id="loading-text" style="display: none;">Loading...</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading indicator -->
            <div id="loading-indicator" class="flex justify-center items-center py-8" style="display: none;">
                <svg class="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="ml-2 text-gray-600">Loading participants...</span>
            </div>

            <!-- Participants table -->
            <div id="participants-table-container" class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th id="sort-name" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('last_name')">
                                Name
                                <span id="sort-name-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-name-asc" style="display: none;">▲</span>
                                    <span id="sort-name-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-email" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('email')">
                                Email
                                <span id="sort-email-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-email-asc" style="display: none;">▲</span>
                                    <span id="sort-email-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-hotel" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('hotel_name')">
                                Hotel
                                <span id="sort-hotel-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-hotel-asc" style="display: none;">▲</span>
                                    <span id="sort-hotel-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-type" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('accommodation_type')">
                                Type
                                <span id="sort-type-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-type-asc" style="display: none;">▲</span>
                                    <span id="sort-type-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-variation" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('variation_name')">
                                Room Type
                                <span id="sort-variation-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-variation-asc" style="display: none;">▲</span>
                                    <span id="sort-variation-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-room-sharing" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('room_share_option')">
                                Room Sharing
                                <span id="sort-room-sharing-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-room-sharing-asc" style="display: none;">▲</span>
                                    <span id="sort-room-sharing-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-checkin" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('checkin_date')">
                                Check-in
                                <span id="sort-checkin-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-checkin-asc" style="display: none;">▲</span>
                                    <span id="sort-checkin-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-checkout" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('checkout_date')">
                                Check-out
                                <span id="sort-checkout-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-checkout-asc" style="display: none;">▲</span>
                                    <span id="sort-checkout-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th id="sort-status" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b cursor-pointer" onclick="setSortField('payment_status')">
                                Status
                                <span id="sort-status-indicator" class="ml-1" style="display: none;">
                                    <span id="sort-status-asc" style="display: none;">▲</span>
                                    <span id="sort-status-desc" style="display: none;">▼</span>
                                </span>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr id="no-participants-row" style="display: none;">
                            <td colspan="9" class="px-4 py-4 text-center text-gray-500">
                                No participants found. Try adjusting your filters or sync the accommodation data.
                            </td>
                        </tr>
                        <!-- Participant rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div> <!-- End of accommodation-manager div -->

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
            <p class="mb-4">Are you sure you want to delete the participant <span id="delete-participant-name" class="font-semibold"></span>?</p>
            <div class="flex justify-end space-x-4">
                <button
                    type="button"
                    onclick="document.getElementById('delete-modal').style.display = 'none';"
                    class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors duration-200"
                >
                    Cancel
                </button>
                <button
                    type="button"
                    id="confirm-delete-button"
                    onclick="deleteParticipant()"
                    class="px-4 py-2 bg-danger text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-danger focus:ring-opacity-50 transition-colors duration-200"
                >
                    Delete
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Participant Modal -->
    <div id="edit-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Edit Participant</h3>
                <button
                    type="button"
                    onclick="document.getElementById('edit-modal').style.display = 'none';"
                    class="text-gray-400 hover:text-gray-500 focus:outline-none"
                >
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <form id="edit-form">
                <input type="hidden" id="edit-id" name="id">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="edit-first-name" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                        <input type="text" id="edit-first-name" name="first_name" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="edit-last-name" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                        <input type="text" id="edit-last-name" name="last_name" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="edit-email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" id="edit-email" name="email" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="edit-phone" class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <input type="text" id="edit-phone" name="phone" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="edit-country" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                        <select id="edit-country" name="country" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select Country</option>
                            <!-- Country options will be populated by JavaScript -->
                        </select>
                    </div>
                    <div>
                        <label for="edit-payment-status" class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                        <select id="edit-payment-status" name="payment_status" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="completed">Completed</option>
                            <option value="processing">Processing</option>
                            <option value="on-hold">On Hold</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                    <div>
                        <label for="edit-hotel-name" class="block text-sm font-medium text-gray-700 mb-1">Hotel</label>
                        <select id="edit-hotel-name" name="hotel_name" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select Hotel</option>
                            <option value="Hotel Aro Palace">Hotel Aro Palace</option>
                            <option value="Hotel Kronwell">Hotel Kronwell</option>
                            <option value="No Hotel">No Hotel</option>
                            <option value="Hotel Radisson Blu">Hotel Radisson Blu</option>
                            <option value="Hotel Gott">Hotel Gott</option>
                            <option value="ARMATTI HOTEL">ARMATTI HOTEL</option>
                            <option value="Pullman Bucharest">Pullman Bucharest</option>
                            <option value="Hotel Capitol">Hotel Capitol</option>
                            <option value="Hotel Ambient">Hotel Ambient</option>
                            <option value="Hotel Belfort">Hotel Belfort</option>
                            <option value="Hotel Coroana">Hotel Coroana</option>
                            <option value="Hotel Cubix">Hotel Cubix</option>
                            <option value="Hotel Decebal">Hotel Decebal</option>
                            <option value="Hotel Golden Time">Hotel Golden Time</option>
                            <option value="Hotel Brasov">Hotel Brasov</option>
                            <option value="Hotel Citrin">Hotel Citrin</option>
                            <option value="Hotel Curtea Brasoveana">Hotel Curtea Brasoveana</option>
                            <option value="Hotel Dacia">Hotel Dacia</option>
                            <option value="Hotel Kolping">Hotel Kolping</option>
                            <option value="Hotel Ramada">Hotel Ramada</option>
                            <option value="Hotel Residence Ambient">Hotel Residence Ambient</option>
                            <option value="Hotel Safrano Palace">Hotel Safrano Palace</option>
                            <option value="Hotel Bella Muzica">Hotel Bella Muzica</option>
                            <option value="Hotel Casa Wagner">Hotel Casa Wagner</option>
                        </select>
                    </div>
                    <div>
                        <label for="edit-accommodation-type" class="block text-sm font-medium text-gray-700 mb-1">Accommodation Type</label>
                        <select id="edit-accommodation-type" name="accommodation_type" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="Main Event">Main Event</option>
                            <option value="Councillors">Councillors</option>
                            <option value="Additional night in Brasov">Additional night in Brasov</option>
                            <option value="Additional night in Bucharest">Additional night in Bucharest</option>
                            <option value="Additional night after pretour">Additional night after pretour</option>
                        </select>
                    </div>
                    <div>
                        <label for="edit-variation-name" class="block text-sm font-medium text-gray-700 mb-1">Room Type</label>
                        <input type="text" id="edit-variation-name" name="variation_name" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="edit-checkin-date" class="block text-sm font-medium text-gray-700 mb-1">Check-in Date</label>
                        <input type="date" id="edit-checkin-date" name="checkin_date" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="edit-checkout-date" class="block text-sm font-medium text-gray-700 mb-1">Check-out Date</label>
                        <input type="date" id="edit-checkout-date" name="checkout_date" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="edit-room-share-option" class="block text-sm font-medium text-gray-700 mb-1">Room Share Option</label>
                        <select id="edit-room-share-option" name="room_share_option" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select Option</option>
                            <option value="single">Single Room</option>
                            <option value="shared">Shared Room</option>
                        </select>
                    </div>
                    <div>
                        <label for="edit-share-room-with" class="block text-sm font-medium text-gray-700 mb-1">Share Room With</label>
                        <input type="text" id="edit-share-room-with" name="share_room_with" class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="flex justify-end">
                    <button
                        type="button"
                        onclick="document.getElementById('edit-modal').style.display = 'none';"
                        class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors duration-200 mr-4"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        class="px-4 py-2 bg-primary text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors duration-200"
                    >
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Initialize when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing accommodation manager');

    // Initialize the global data object
    window.accommodationManagerData = {
        searchQuery: '',
        hotelFilter: '',
        accommodationTypeFilter: '',
        roomTypeFilter: '',
        roomSharingFilter: '',
        countryFilter: '',
        statusFilter: '',
        sortField: 'last_name',
        sortAsc: true,
        participants: [],
        filteredParticipants: [],
        countries: [],
        hotels: [],
        roomTypes: []
    };

    // Fetch hotels
    fetchHotelsDirectly();

    // Fetch countries
    fetchCountriesDirectly();

    // Fetch participants
    fetchParticipantsDirectly();

    // Set up event listeners for the edit form
    document.getElementById('edit-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveParticipant();
    });
});

// Helper functions
function setSearchQuery(value) {
    window.accommodationManagerData.searchQuery = value;
    applyFilters();
}

function setHotelFilter(value) {
    window.accommodationManagerData.hotelFilter = value;
    applyFilters();
}

function setAccommodationTypeFilter(value) {
    window.accommodationManagerData.accommodationTypeFilter = value;
    applyFilters();
}

function setRoomTypeFilter(value) {
    window.accommodationManagerData.roomTypeFilter = value;
    applyFilters();
}

function setRoomSharingFilter(value) {
    window.accommodationManagerData.roomSharingFilter = value;
    applyFilters();
}

function setStatusFilter(value) {
    window.accommodationManagerData.statusFilter = value;
    applyFilters();
}

function setCountryFilter(value) {
    window.accommodationManagerData.countryFilter = value;
    applyFilters();
}

function resetFilters() {
    // Reset filter variables
    window.accommodationManagerData.searchQuery = '';
    window.accommodationManagerData.hotelFilter = '';
    window.accommodationManagerData.accommodationTypeFilter = '';
    window.accommodationManagerData.countryFilter = '';
    window.accommodationManagerData.statusFilter = '';

    // Reset DOM elements
    document.getElementById('search-query-input').value = '';
    document.getElementById('hotel-filter-select').value = '';
    document.getElementById('accommodation-type-filter-select').value = '';
    document.getElementById('status-filter-select').value = '';
    document.getElementById('country-filter-select').value = '';

    // Apply filters
    applyFilters();
}

function setSortField(field) {
    if (window.accommodationManagerData.sortField === field) {
        // Toggle sort direction if the same field is clicked
        window.accommodationManagerData.sortAsc = !window.accommodationManagerData.sortAsc;
    } else {
        // Set new sort field and default to ascending
        window.accommodationManagerData.sortField = field;
        window.accommodationManagerData.sortAsc = true;
    }

    // Update sort indicators
    updateSortIndicators();

    // Sort the participants
    sortParticipants();

    // Render the table
    renderParticipantsTable();
}

function updateSortIndicators() {
    // Hide all indicators
    const fields = ['name', 'email', 'hotel', 'type', 'checkin', 'checkout', 'status'];
    fields.forEach(function(field) {
        document.getElementById(`sort-${field}-indicator`).style.display = 'none';
        document.getElementById(`sort-${field}-asc`).style.display = 'none';
        document.getElementById(`sort-${field}-desc`).style.display = 'none';
    });

    // Show the active indicator
    let activeField;
    switch (window.accommodationManagerData.sortField) {
        case 'last_name':
            activeField = 'name';
            break;
        case 'email':
            activeField = 'email';
            break;
        case 'hotel_name':
            activeField = 'hotel';
            break;
        case 'accommodation_type':
            activeField = 'type';
            break;
        case 'checkin_date':
            activeField = 'checkin';
            break;
        case 'checkout_date':
            activeField = 'checkout';
            break;
        case 'payment_status':
            activeField = 'status';
            break;
        default:
            return;
    }

    document.getElementById(`sort-${activeField}-indicator`).style.display = 'inline';
    if (window.accommodationManagerData.sortAsc) {
        document.getElementById(`sort-${activeField}-asc`).style.display = 'inline';
    } else {
        document.getElementById(`sort-${activeField}-desc`).style.display = 'inline';
    }
}

function sortParticipants() {
    if (!Array.isArray(window.accommodationManagerData.filteredParticipants)) {
        window.accommodationManagerData.filteredParticipants = [];
        return;
    }

    try {
        // Sort filtered participants
        window.accommodationManagerData.filteredParticipants.sort((a, b) => {
            try {
                // Check if objects are valid
                if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {
                    return 0;
                }

                let aValue = a[window.accommodationManagerData.sortField];
                let bValue = b[window.accommodationManagerData.sortField];

                // Handle date fields
                if (window.accommodationManagerData.sortField === 'checkin_date' || window.accommodationManagerData.sortField === 'checkout_date' || window.accommodationManagerData.sortField === 'created_at' || window.accommodationManagerData.sortField === 'updated_at') {
                    aValue = new Date(aValue || '1970-01-01').getTime();
                    bValue = new Date(bValue || '1970-01-01').getTime();
                }

                // Handle string fields
                if (typeof aValue === 'string') {
                    aValue = aValue.toLowerCase();
                }
                if (typeof bValue === 'string') {
                    bValue = bValue.toLowerCase();
                }

                // Compare values
                if (aValue < bValue) return window.accommodationManagerData.sortAsc ? -1 : 1;
                if (aValue > bValue) return window.accommodationManagerData.sortAsc ? 1 : -1;
                return 0;
            } catch (error) {
                return 0;
            }
        });
    } catch (error) {
        // Silently handle errors
    }
}

function applyFilters() {
    if (!Array.isArray(window.accommodationManagerData.participants)) {
        window.accommodationManagerData.filteredParticipants = [];
        return;
    }

    window.accommodationManagerData.filteredParticipants = window.accommodationManagerData.participants.filter(p => {
        // Search query filter
        const searchMatch = !window.accommodationManagerData.searchQuery ||
            (p.first_name && p.first_name.toLowerCase().includes(window.accommodationManagerData.searchQuery.toLowerCase())) ||
            (p.last_name && p.last_name.toLowerCase().includes(window.accommodationManagerData.searchQuery.toLowerCase())) ||
            (p.email && p.email.toLowerCase().includes(window.accommodationManagerData.searchQuery.toLowerCase()));

        // Hotel filter
        const hotelMatch = !window.accommodationManagerData.hotelFilter ||
            (p.hotel_name && p.hotel_name === window.accommodationManagerData.hotelFilter);

        // Accommodation type filter
        const typeMatch = !window.accommodationManagerData.accommodationTypeFilter ||
            (p.accommodation_type && p.accommodation_type === window.accommodationManagerData.accommodationTypeFilter);

        // Room type filter
        const roomTypeMatch = !window.accommodationManagerData.roomTypeFilter ||
            (p.variation_name && p.variation_name === window.accommodationManagerData.roomTypeFilter);

        // Room sharing filter
        const roomSharingMatch = !window.accommodationManagerData.roomSharingFilter ||
            (p.room_share_option && p.room_share_option === window.accommodationManagerData.roomSharingFilter);

        // Country filter
        const countryMatch = !window.accommodationManagerData.countryFilter ||
            (p.country && p.country === window.accommodationManagerData.countryFilter);

        // Status filter
        const statusMatch = !window.accommodationManagerData.statusFilter ||
            (p.payment_status && p.payment_status === window.accommodationManagerData.statusFilter);

        return searchMatch && hotelMatch && typeMatch && roomTypeMatch && roomSharingMatch && countryMatch && statusMatch;
    });

    // Sort the filtered participants
    sortParticipants();

    // Render the participants table
    renderParticipantsTable();
}

function fetchHotelsDirectly() {
    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();
    const url = `${lciAdmin.ajaxUrl}?action=lci_get_accommodation_hotels&nonce=${lciAdmin.nonce}&_=${timestamp}`;

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }
            return response.json();
        })
        .then(function(data) {
            if (data.success && Array.isArray(data.data.hotels)) {
                // Store hotels in the global data object
                window.accommodationManagerData.hotels = data.data.hotels;

                // Update hotel filter dropdown
                updateHotelFilterDropdown(data.data.hotels);
            } else {
                console.error('Error fetching hotels:', data);
                // Use fallback hotels if the API fails
                const fallbackHotels = [
                    'Hotel Aro Palace',
                    'Hotel Kronwell',
                    'No Hotel',
                    'Hotel Radisson Blu',
                    'Hotel Gott',
                    'ARMATTI HOTEL',
                    'Pullman Bucharest',
                    'Hotel Capitol',
                    'Hotel Ambient',
                    'Hotel Belfort',
                    'Hotel Coroana',
                    'Hotel Cubix',
                    'Hotel Decebal',
                    'Hotel Golden Time',
                    'Hotel Brasov',
                    'Hotel Citrin',
                    'Hotel Curtea Brasoveana',
                    'Hotel Dacia',
                    'Hotel Kolping',
                    'Hotel Ramada',
                    'Hotel Residence Ambient',
                    'Hotel Safrano Palace',
                    'Hotel Bella Muzica',
                    'Hotel Casa Wagner'
                ];

                // Store fallback hotels
                window.accommodationManagerData.hotels = fallbackHotels;

                // Update hotel filter dropdown with fallback hotels
                updateHotelFilterDropdown(fallbackHotels);
            }
        })
        .catch(function(error) {
            console.error('Error fetching hotels:', error);
            // Use fallback hotels if the API fails
            const fallbackHotels = [
                'Hotel Aro Palace',
                'Hotel Kronwell',
                'No Hotel',
                'Hotel Radisson Blu',
                'Hotel Gott',
                'ARMATTI HOTEL',
                'Pullman Bucharest',
                'Hotel Capitol',
                'Hotel Ambient',
                'Hotel Belfort',
                'Hotel Coroana',
                'Hotel Cubix',
                'Hotel Decebal',
                'Hotel Golden Time',
                'Hotel Brasov',
                'Hotel Citrin',
                'Hotel Curtea Brasoveana',
                'Hotel Dacia',
                'Hotel Kolping',
                'Hotel Ramada',
                'Hotel Residence Ambient',
                'Hotel Safrano Palace',
                'Hotel Bella Muzica',
                'Hotel Casa Wagner'
            ];

            // Store fallback hotels
            window.accommodationManagerData.hotels = fallbackHotels;

            // Update hotel filter dropdown with fallback hotels
            updateHotelFilterDropdown(fallbackHotels);
        });
}

function updateHotelFilterDropdown(hotels) {
    // Get the hotel select element
    const hotelSelect = document.getElementById('hotel-filter-select');

    // Clear existing options (except the first one)
    while (hotelSelect.options.length > 1) {
        hotelSelect.remove(1);
    }

    // Add new options
    hotels.forEach(function(hotel) {
        const option = document.createElement('option');
        option.value = hotel;
        option.textContent = hotel;
        hotelSelect.appendChild(option);
    });
}

function updateRoomTypeFilterDropdown() {
    // Get the room type select element
    const roomTypeSelect = document.getElementById('room-type-filter-select');

    // Clear existing options (except the first one)
    while (roomTypeSelect.options.length > 1) {
        roomTypeSelect.remove(1);
    }

    // Get unique room types from participants
    const roomTypes = [];
    if (Array.isArray(window.accommodationManagerData.participants)) {
        window.accommodationManagerData.participants.forEach(function(participant) {
            if (participant.variation_name && !roomTypes.includes(participant.variation_name)) {
                roomTypes.push(participant.variation_name);
            }
        });
    }

    // Sort room types alphabetically
    roomTypes.sort();

    // Store room types in the global data object
    window.accommodationManagerData.roomTypes = roomTypes;

    // Add new options
    roomTypes.forEach(function(roomType) {
        const option = document.createElement('option');
        option.value = roomType;
        option.textContent = roomType;
        roomTypeSelect.appendChild(option);
    });
}
function fetchCountriesDirectly() {
    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();
    // Use the existing tours countries endpoint
    const url = `${lciAdmin.ajaxUrl}?action=lci_get_tours_countries&nonce=${lciAdmin.nonce}&_=${timestamp}`;

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }
            return response.json();
        })
        .then(function(data) {
            if (data.success && Array.isArray(data.data.countries)) {
                // Store countries in the global data object
                window.accommodationManagerData.countries = data.data.countries;

                // Update country filter dropdown
                updateCountryFilterDropdown(data.data.countries);

                // Update edit form country dropdown
                updateEditCountryDropdown(data.data.countries);
            } else {
                console.error('Error fetching countries:', data);
                // Use fallback countries if the API fails
                const fallbackCountries = [
                    'United States', 'Canada', 'United Kingdom', 'Australia',
                    'Germany', 'France', 'Italy', 'Spain', 'Romania', 'Bulgaria',
                    'Netherlands', 'Belgium', 'Switzerland', 'Austria', 'Sweden',
                    'Norway', 'Denmark', 'Finland', 'Ireland', 'Portugal', 'Greece',
                    'Poland', 'Czech Republic', 'Hungary', 'Slovakia', 'Croatia',
                    'Serbia', 'Slovenia', 'Bosnia and Herzegovina', 'Montenegro',
                    'North Macedonia', 'Albania', 'Moldova', 'Ukraine', 'Belarus',
                    'Russia', 'Turkey', 'Israel', 'Egypt', 'South Africa', 'Brazil',
                    'Argentina', 'Mexico', 'Japan', 'China', 'India', 'South Korea',
                    'Singapore', 'Malaysia', 'Indonesia', 'Thailand', 'Vietnam',
                    'Philippines', 'New Zealand'
                ];

                // Store fallback countries
                window.accommodationManagerData.countries = fallbackCountries;

                // Update dropdowns with fallback countries
                updateCountryFilterDropdown(fallbackCountries);
                updateEditCountryDropdown(fallbackCountries);
            }
        })
        .catch(function(error) {
            console.error('Error fetching countries:', error);
            // Use fallback countries if the API fails
            const fallbackCountries = [
                'United States', 'Canada', 'United Kingdom', 'Australia',
                'Germany', 'France', 'Italy', 'Spain', 'Romania', 'Bulgaria',
                'Netherlands', 'Belgium', 'Switzerland', 'Austria', 'Sweden',
                'Norway', 'Denmark', 'Finland', 'Ireland', 'Portugal', 'Greece',
                'Poland', 'Czech Republic', 'Hungary', 'Slovakia', 'Croatia',
                'Serbia', 'Slovenia', 'Bosnia and Herzegovina', 'Montenegro',
                'North Macedonia', 'Albania', 'Moldova', 'Ukraine', 'Belarus',
                'Russia', 'Turkey', 'Israel', 'Egypt', 'South Africa', 'Brazil',
                'Argentina', 'Mexico', 'Japan', 'China', 'India', 'South Korea',
                'Singapore', 'Malaysia', 'Indonesia', 'Thailand', 'Vietnam',
                'Philippines', 'New Zealand'
            ];

            // Store fallback countries
            window.accommodationManagerData.countries = fallbackCountries;

            // Update dropdowns with fallback countries
            updateCountryFilterDropdown(fallbackCountries);
            updateEditCountryDropdown(fallbackCountries);
        });
}

function updateCountryFilterDropdown(countries) {
    // Get the country select element
    const countrySelect = document.getElementById('country-filter-select');

    // Clear existing options (except the first one)
    while (countrySelect.options.length > 1) {
        countrySelect.remove(1);
    }

    // Add new options
    countries.forEach(function(country) {
        const option = document.createElement('option');
        option.value = country;
        option.textContent = country;
        countrySelect.appendChild(option);
    });
}

function updateEditCountryDropdown(countries) {
    // Get the country select element
    const countrySelect = document.getElementById('edit-country');

    // Clear existing options (except the first one)
    while (countrySelect.options.length > 1) {
        countrySelect.remove(1);
    }

    // If no countries are provided, add some common ones as a fallback
    if (!countries || countries.length === 0) {
        const commonCountries = [
            'United States', 'Canada', 'United Kingdom', 'Australia',
            'Germany', 'France', 'Italy', 'Spain', 'Romania', 'Bulgaria',
            'Netherlands', 'Belgium', 'Switzerland', 'Austria', 'Sweden',
            'Norway', 'Denmark', 'Finland', 'Ireland', 'Portugal', 'Greece',
            'Poland', 'Czech Republic', 'Hungary', 'Slovakia', 'Croatia',
            'Serbia', 'Slovenia', 'Bosnia and Herzegovina', 'Montenegro',
            'North Macedonia', 'Albania', 'Moldova', 'Ukraine', 'Belarus',
            'Russia', 'Turkey', 'Israel', 'Egypt', 'South Africa', 'Brazil',
            'Argentina', 'Mexico', 'Japan', 'China', 'India', 'South Korea',
            'Singapore', 'Malaysia', 'Indonesia', 'Thailand', 'Vietnam',
            'Philippines', 'New Zealand'
        ];

        countries = commonCountries;
    }

    // Add new options
    countries.forEach(function(country) {
        const option = document.createElement('option');
        option.value = country;
        option.textContent = country;
        countrySelect.appendChild(option);
    });
}
function fetchParticipantsDirectly() {
    // Show loading state
    document.getElementById('loading-indicator').style.display = 'flex';
    document.getElementById('participants-table-container').style.display = 'none';
    document.getElementById('refresh-text').style.display = 'none';
    document.getElementById('loading-text').style.display = 'inline';
    document.getElementById('refresh-button').disabled = true;

    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();
    let url = `${lciAdmin.ajaxUrl}?action=lci_get_accommodation_unified&nonce=${lciAdmin.nonce}&_=${timestamp}`;

    // Add filters if they exist
    if (window.accommodationManagerData.searchQuery) {
        url += `&search=${encodeURIComponent(window.accommodationManagerData.searchQuery)}`;
    }

    if (window.accommodationManagerData.hotelFilter) {
        url += `&hotel=${encodeURIComponent(window.accommodationManagerData.hotelFilter)}`;
    }

    if (window.accommodationManagerData.accommodationTypeFilter) {
        url += `&accommodation_type=${encodeURIComponent(window.accommodationManagerData.accommodationTypeFilter)}`;
    }

    if (window.accommodationManagerData.countryFilter) {
        url += `&country=${encodeURIComponent(window.accommodationManagerData.countryFilter)}`;
    }

    if (window.accommodationManagerData.statusFilter) {
        url += `&payment_status=${encodeURIComponent(window.accommodationManagerData.statusFilter)}`;
    }

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }
            return response.json();
        })
        .then(function(data) {
            if (data.success && Array.isArray(data.data.participants)) {
                window.accommodationManagerData.participants = data.data.participants;
                window.accommodationManagerData.filteredParticipants = [...window.accommodationManagerData.participants];
                sortParticipants();

                // Update room type filter dropdown
                updateRoomTypeFilterDropdown();

                // Render the participants table
                renderParticipantsTable();

                // Show success message if table is empty but API call was successful
                if (data.data.participants.length === 0) {
                    showToast('No participants found. Try syncing accommodation data first.', 'info');
                }
            } else {
                const errorMessage = data.data?.message || 'Unknown error';
                showToast('Error fetching participants: ' + errorMessage, 'error');
                console.error('Error fetching participants:', data);

                window.accommodationManagerData.participants = [];
                window.accommodationManagerData.filteredParticipants = [];

                // Show the no participants message
                document.getElementById('no-participants-row').style.display = 'table-row';
            }
        })
        .catch(function(error) {
            showToast('Error fetching participants: ' + error.message, 'error');
            console.error('Error fetching participants:', error);

            window.accommodationManagerData.participants = [];
            window.accommodationManagerData.filteredParticipants = [];

            // Show the no participants message
            document.getElementById('no-participants-row').style.display = 'table-row';
        })
        .finally(function() {
            // Hide loading state
            document.getElementById('loading-indicator').style.display = 'none';
            document.getElementById('participants-table-container').style.display = 'block';
            document.getElementById('refresh-text').style.display = 'inline';
            document.getElementById('loading-text').style.display = 'none';
            document.getElementById('refresh-button').disabled = false;
        });
}

function refreshParticipants() {
    fetchParticipantsDirectly();
}
function renderParticipantsTable() {
    const tableBody = document.querySelector('#participants-table-container table tbody');
    const noParticipantsRow = document.getElementById('no-participants-row');

    // Clear existing rows except the no-participants-row
    const rows = tableBody.querySelectorAll('tr:not(#no-participants-row)');
    rows.forEach(row => row.remove());

    // Check if there are any participants
    if (window.accommodationManagerData.filteredParticipants.length === 0) {
        noParticipantsRow.style.display = 'table-row';
        return;
    }

    // Hide the no participants message
    noParticipantsRow.style.display = 'none';

    // Add rows for each participant
    window.accommodationManagerData.filteredParticipants.forEach((participant, index) => {
        const row = document.createElement('tr');
        row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';

        // Name cell
        const nameCell = document.createElement('td');
        nameCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        nameCell.textContent = participant.first_name + ' ' + participant.last_name;
        row.appendChild(nameCell);

        // Email cell
        const emailCell = document.createElement('td');
        emailCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        emailCell.textContent = participant.email;
        row.appendChild(emailCell);

        // Hotel cell
        const hotelCell = document.createElement('td');
        hotelCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        hotelCell.textContent = participant.hotel_name || 'N/A';
        row.appendChild(hotelCell);

        // Accommodation type cell
        const typeCell = document.createElement('td');
        typeCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        typeCell.textContent = participant.accommodation_type || 'N/A';
        row.appendChild(typeCell);

        // Variation name cell (Room Type)
        const variationCell = document.createElement('td');
        variationCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        variationCell.textContent = participant.variation_name || 'N/A';
        row.appendChild(variationCell);

        // Room sharing cell
        const roomSharingCell = document.createElement('td');
        roomSharingCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';

        if (participant.room_share_option === 'shared') {
            roomSharingCell.textContent = 'Shared' + (participant.share_room_with ? ' with ' + participant.share_room_with : '');
            roomSharingCell.classList.add('text-blue-600', 'font-medium');
        } else if (participant.room_share_option === 'single') {
            roomSharingCell.textContent = 'Single Room';
            roomSharingCell.classList.add('text-green-600', 'font-medium');
        } else {
            roomSharingCell.textContent = 'N/A';
        }

        row.appendChild(roomSharingCell);

        // Check-in date cell
        const checkinCell = document.createElement('td');
        checkinCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        checkinCell.textContent = participant.checkin_date ? formatDate(participant.checkin_date, true) : 'N/A';
        row.appendChild(checkinCell);

        // Check-out date cell
        const checkoutCell = document.createElement('td');
        checkoutCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';
        checkoutCell.textContent = participant.checkout_date ? formatDate(participant.checkout_date, true) : 'N/A';
        row.appendChild(checkoutCell);

        // Status cell
        const statusCell = document.createElement('td');
        statusCell.className = 'px-4 py-2 whitespace-nowrap text-sm border-b';

        const statusSpan = document.createElement('span');
        statusSpan.className = 'px-2 inline-flex text-xs leading-5 font-semibold rounded-full';

        // Add status-specific classes
        if (participant.payment_status === 'completed') {
            statusSpan.classList.add('bg-green-100', 'text-green-800');
        } else if (participant.payment_status === 'processing' || participant.payment_status === 'on-hold') {
            statusSpan.classList.add('bg-yellow-100', 'text-yellow-800');
        } else if (participant.payment_status === 'failed' || participant.payment_status === 'cancelled') {
            statusSpan.classList.add('bg-red-100', 'text-red-800');
        } else {
            statusSpan.classList.add('bg-gray-100', 'text-gray-800');
        }

        statusSpan.textContent = participant.payment_status;
        statusCell.appendChild(statusSpan);
        row.appendChild(statusCell);

        // Actions cell
        const actionsCell = document.createElement('td');
        actionsCell.className = 'px-4 py-2 whitespace-nowrap text-sm text-gray-900 border-b';

        const editButton = document.createElement('button');
        editButton.className = 'px-2 py-1 bg-primary text-white rounded hover:bg-blue-600 mr-2';
        editButton.textContent = 'Edit';
        editButton.setAttribute('data-participant', JSON.stringify(participant));
        editButton.onclick = function() {
            editParticipant(this.getAttribute('data-participant'));
        };
        actionsCell.appendChild(editButton);

        const deleteButton = document.createElement('button');
        deleteButton.className = 'px-2 py-1 bg-danger text-white rounded hover:bg-red-600';
        deleteButton.textContent = 'Delete';
        deleteButton.setAttribute('data-participant-id', participant.id);
        deleteButton.onclick = function() {
            confirmDeleteParticipant(this.getAttribute('data-participant-id'));
        };
        actionsCell.appendChild(deleteButton);

        row.appendChild(actionsCell);

        // Add the row to the table
        tableBody.appendChild(row);
    });
}

// Helper function to format dates
function formatDate(dateString, dateOnly = false) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return dateOnly ? date.toLocaleDateString() : date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (e) {
        return dateString;
    }
}
function showToast(message, type) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded shadow-lg z-50';

    // Set background color based on type
    if (type === 'success') {
        toast.classList.add('bg-green-500', 'text-white');
    } else if (type === 'error') {
        toast.classList.add('bg-red-500', 'text-white');
    } else {
        toast.classList.add('bg-blue-500', 'text-white');
    }

    // Set message
    toast.textContent = message;

    // Add to document
    document.body.appendChild(toast);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 500);
    }, 3000);
}

function confirmDeleteParticipant(participantId) {
    // Find the participant by ID
    const participant = window.accommodationManagerData.filteredParticipants.find(p => p.id == participantId);
    if (participant) {
        // Show the delete confirmation modal
        document.getElementById('delete-modal').style.display = 'flex';
        document.getElementById('delete-participant-name').textContent = participant.first_name + ' ' + participant.last_name;
        document.getElementById('confirm-delete-button').setAttribute('data-participant-id', participant.id);
    } else {
        console.error('Participant not found with ID:', participantId);
    }
}

function deleteParticipant() {
    const participantId = document.getElementById('confirm-delete-button').getAttribute('data-participant-id');
    if (!participantId) {
        console.error('No participant ID found for deletion');
        return;
    }

    // Hide the modal
    document.getElementById('delete-modal').style.display = 'none';

    // Show loading state
    const deleteButton = document.getElementById('confirm-delete-button');
    const originalText = deleteButton.textContent;
    deleteButton.textContent = 'Deleting...';
    deleteButton.disabled = true;

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_delete_accommodation_participant');
    formData.append('nonce', lciAdmin.nonce);
    formData.append('participant_id', participantId);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            showToast('Participant deleted successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error deleting participant';
            showToast('Error: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
        // Reset button state
        deleteButton.textContent = originalText;
        deleteButton.disabled = false;
    });
}
function editParticipant(participantJson) {
    let participant;
    try {
        participant = JSON.parse(participantJson);
    } catch (e) {
        console.error('Error parsing participant JSON:', e);
        return;
    }

    // Populate the edit form
    document.getElementById('edit-id').value = participant.id;
    document.getElementById('edit-first-name').value = participant.first_name || '';
    document.getElementById('edit-last-name').value = participant.last_name || '';
    document.getElementById('edit-email').value = participant.email || '';
    document.getElementById('edit-phone').value = participant.phone || '';

    // Set the country dropdown value
    const countrySelect = document.getElementById('edit-country');
    if (participant.country) {
        // Check if the country exists in the dropdown
        let countryExists = false;
        for (let i = 0; i < countrySelect.options.length; i++) {
            if (countrySelect.options[i].value === participant.country) {
                countrySelect.value = participant.country;
                countryExists = true;
                break;
            }
        }

        // If the country doesn't exist in the dropdown, add it
        if (!countryExists && participant.country) {
            const option = document.createElement('option');
            option.value = participant.country;
            option.textContent = participant.country;
            countrySelect.appendChild(option);
            countrySelect.value = participant.country;
        }
    } else {
        countrySelect.value = '';
    }

    document.getElementById('edit-payment-status').value = participant.payment_status || '';
    document.getElementById('edit-hotel-name').value = participant.hotel_name || '';
    document.getElementById('edit-accommodation-type').value = participant.accommodation_type || '';
    document.getElementById('edit-variation-name').value = participant.variation_name || '';

    // Set date fields
    if (participant.checkin_date) {
        document.getElementById('edit-checkin-date').value = participant.checkin_date.split(' ')[0];
    } else {
        document.getElementById('edit-checkin-date').value = '';
    }

    if (participant.checkout_date) {
        document.getElementById('edit-checkout-date').value = participant.checkout_date.split(' ')[0];
    } else {
        document.getElementById('edit-checkout-date').value = '';
    }

    // Set room sharing fields
    document.getElementById('edit-room-share-option').value = participant.room_share_option || '';
    document.getElementById('edit-share-room-with').value = participant.share_room_with || '';

    // Show the modal
    document.getElementById('edit-modal').style.display = 'flex';
}

function saveParticipant() {
    // Get form data
    const form = document.getElementById('edit-form');
    const formData = new FormData(form);

    // Add action and nonce
    formData.append('action', 'lci_update_accommodation_participant');
    formData.append('nonce', lciAdmin.nonce);

    // Show loading state
    const saveButton = document.querySelector('#edit-form button[type="submit"]');
    const originalText = saveButton.textContent;
    saveButton.textContent = 'Saving...';
    saveButton.disabled = true;

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            showToast('Participant updated successfully', 'success');

            // Hide the modal
            document.getElementById('edit-modal').style.display = 'none';

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error updating participant';
            showToast('Error: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
        // Reset button state
        saveButton.textContent = originalText;
        saveButton.disabled = false;
    });
}

function syncAccommodation() {
    // Show loading state
    document.getElementById('sync-text').style.display = 'none';
    document.getElementById('sync-loading').style.display = 'flex';
    document.getElementById('sync-button').disabled = true;

    // Hide any existing messages
    document.getElementById('sync-success-message').style.display = 'none';
    document.getElementById('sync-error-message').style.display = 'none';

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_sync_accommodation_unified');
    formData.append('nonce', lciAdmin.nonce);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            const message = data.data?.message || 'Accommodation data synced successfully';

            // Update DOM directly
            document.getElementById('sync-success-text').textContent = message;
            document.getElementById('sync-success-message').style.display = 'block';

            showToast('Accommodation data synced successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error syncing accommodation data';

            // Update DOM directly
            document.getElementById('sync-error-text').textContent = message;
            document.getElementById('sync-error-message').style.display = 'block';

            showToast('Error syncing accommodation data: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        const message = 'Error syncing accommodation data: ' + error.message;

        // Update DOM directly
        document.getElementById('sync-error-text').textContent = message;
        document.getElementById('sync-error-message').style.display = 'block';

        showToast(message, 'error');
    })
    .finally(function() {
        // Reset the button state
        document.getElementById('sync-text').style.display = 'inline';
        document.getElementById('sync-loading').style.display = 'none';
        document.getElementById('sync-button').disabled = false;
    });
}

function clearTable() {
    // Confirm before proceeding
    if (!confirm('Are you sure you want to clear the accommodation table? This will delete all accommodation data.')) {
        return;
    }

    // Show loading state
    document.getElementById('clear-table-text').style.display = 'none';
    document.getElementById('clear-table-loading').style.display = 'flex';
    document.getElementById('clear-table-button').disabled = true;

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_clear_accommodation_table_unified');
    formData.append('nonce', lciAdmin.nonce);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            showToast('Accommodation table cleared successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error clearing accommodation table';
            showToast('Error: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
        // Reset button state
        document.getElementById('clear-table-text').style.display = 'inline';
        document.getElementById('clear-table-loading').style.display = 'none';
        document.getElementById('clear-table-button').disabled = false;
    });
}

function updateTableStructure() {
    // Show loading state
    document.getElementById('update-table-structure-text').style.display = 'none';
    document.getElementById('update-table-structure-loading').style.display = 'flex';
    document.getElementById('update-table-structure-button').disabled = true;

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_update_table_structure_unified');
    formData.append('nonce', lciAdmin.nonce);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            showToast('Accommodation table structure updated successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error updating accommodation table structure';
            showToast('Error: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
        // Reset button state
        document.getElementById('update-table-structure-text').style.display = 'inline';
        document.getElementById('update-table-structure-loading').style.display = 'none';
        document.getElementById('update-table-structure-button').disabled = false;
    });
}

function recreateTable() {
    // Confirm before proceeding
    if (!confirm('Are you sure you want to recreate the accommodation table? This will delete all accommodation data and recreate the table structure.')) {
        return;
    }

    // Show loading state
    document.getElementById('recreate-table-text').style.display = 'none';
    document.getElementById('recreate-table-loading').style.display = 'flex';
    document.getElementById('recreate-table-button').disabled = true;

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_recreate_accommodation_table_unified');
    formData.append('nonce', lciAdmin.nonce);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            showToast('Accommodation table recreated successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error recreating accommodation table';
            showToast('Error: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        showToast('Error: ' + error.message, 'error');
    })
    .finally(function() {
        // Reset button state
        document.getElementById('recreate-table-text').style.display = 'inline';
        document.getElementById('recreate-table-loading').style.display = 'none';
        document.getElementById('recreate-table-button').disabled = false;
    });
}

function bulkUpdateDates() {
    // Get form values
    const categoryId = document.getElementById('bulk-category-select').value;
    const checkinDate = document.getElementById('bulk-checkin-date').value;
    const checkoutDate = document.getElementById('bulk-checkout-date').value;

    // Validate required fields
    if (!categoryId) {
        showToast('Please select an accommodation category', 'error');
        return;
    }

    if (!checkinDate || !checkoutDate) {
        showToast('Please select both check-in and check-out dates', 'error');
        return;
    }

    // Show loading state
    document.getElementById('bulk-update-text').style.display = 'none';
    document.getElementById('bulk-update-loading').style.display = 'flex';
    document.getElementById('bulk-update-button').disabled = true;

    // Hide any existing messages
    document.getElementById('bulk-update-success-message').style.display = 'none';
    document.getElementById('bulk-update-error-message').style.display = 'none';

    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'lci_bulk_update_accommodation_dates');
    formData.append('nonce', lciAdmin.nonce);
    formData.append('category_id', categoryId);
    formData.append('checkin_date', checkinDate);
    formData.append('checkout_date', checkoutDate);

    // Make the AJAX request
    fetch(lciAdmin.ajaxUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(function(data) {
        if (data.success) {
            // Success case
            const message = data.data?.message || 'Dates updated successfully';

            // Update DOM directly
            document.getElementById('bulk-update-success-text').textContent = message;
            document.getElementById('bulk-update-success-message').style.display = 'block';

            showToast('Dates updated successfully', 'success');

            // Refresh the participants list
            refreshParticipants();
        } else {
            // Error case
            const message = data.data?.message || 'Error updating dates';

            // Update DOM directly
            document.getElementById('bulk-update-error-text').textContent = message;
            document.getElementById('bulk-update-error-message').style.display = 'block';

            showToast('Error updating dates: ' + message, 'error');
        }
    })
    .catch(function(error) {
        // Handle network or other errors
        const message = 'Error updating dates: ' + error.message;

        // Update DOM directly
        document.getElementById('bulk-update-error-text').textContent = message;
        document.getElementById('bulk-update-error-message').style.display = 'block';

        showToast(message, 'error');
    })
    .finally(function() {
        // Reset the button state
        document.getElementById('bulk-update-text').style.display = 'inline';
        document.getElementById('bulk-update-loading').style.display = 'none';
        document.getElementById('bulk-update-button').disabled = false;
    });
}
</script>

<?php
// End of file
?>