==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [show-reset-form] => true
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?show-reset-form=true&action=reset_password
Key: 
Login: 
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [show-reset-form] => true
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/lost-password/?show-reset-form=true&action=reset_password
Key: 
Login: 
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => Wb6l2NOJq7uU8ZqaG8n5
    [login] => georgian.marin
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=Wb6l2NOJq7uU8ZqaG8n5&login=georgian.marin&action=reset_password
Key: Wb6l2NOJq7uU8ZqaG8n5
Login: georgian.marin
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => upntK7VoFjdDePAPl40I
    [login] => louise krogager.jespersen
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=upntK7VoFjdDePAPl40I&login=louise%20krogager.jespersen&action=reset_password
Key: upntK7VoFjdDePAPl40I
Login: louise krogager.jespersen
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => Zb9XXC8QRv7A1QHs7MiV
    [login] => clare.pengelly
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=Zb9XXC8QRv7A1QHs7MiV&login=clare.pengelly&action=reset_password
Key: Zb9XXC8QRv7A1QHs7MiV
Login: clare.pengelly
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => Jg4q4aePBODwnHlmd8y8
    [login] => anne.schmitt
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=Jg4q4aePBODwnHlmd8y8&login=anne.schmitt&action=reset_password
Key: Jg4q4aePBODwnHlmd8y8
Login: anne.schmitt
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => 9JMulcFMtsK8svu8NEQp
    [login] => anette.hagen borg helligso
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=9JMulcFMtsK8svu8NEQp&login=anette.hagen%20borg%20helligso&action=reset_password
Key: 9JMulcFMtsK8svu8NEQp
Login: anette.hagen borg helligso
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => 9JMulcFMtsK8svu8NEQp
    [login] => anette.hagen borg helligso
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=9JMulcFMtsK8svu8NEQp&login=anette.hagen%20borg%20helligso&action=reset_password
Key: 9JMulcFMtsK8svu8NEQp
Login: anette.hagen borg helligso
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => 9JMulcFMtsK8svu8NEQp
    [login] => anette.hagen borg helligso
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=9JMulcFMtsK8svu8NEQp&login=anette.hagen%20borg%20helligso&action=reset_password
Key: 9JMulcFMtsK8svu8NEQp
Login: anette.hagen borg helligso
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => ugUxa8VU6YesuBSGkrWU
    [login] => lena.nystedt
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=ugUxa8VU6YesuBSGkrWU&login=lena.nystedt&action=reset_password
Key: ugUxa8VU6YesuBSGkrWU
Login: lena.nystedt
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => 9NdDXZmyqQV8KjWNnd00
    [login] => kirsa.kofoed
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=9NdDXZmyqQV8KjWNnd00&login=kirsa.kofoed&action=reset_password
Key: 9NdDXZmyqQV8KjWNnd00
Login: kirsa.kofoed
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => 9NdDXZmyqQV8KjWNnd00
    [login] => kirsa.kofoed
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=9NdDXZmyqQV8KjWNnd00&login=kirsa.kofoed&action=reset_password
Key: 9NdDXZmyqQV8KjWNnd00
Login: kirsa.kofoed
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => mREC0cBnJ31J6fqxJ6ff
    [login] => jessica.refshammer
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=mREC0cBnJ31J6fqxJ6ff&login=jessica.refshammer&action=reset_password
Key: mREC0cBnJ31J6fqxJ6ff
Login: jessica.refshammer
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => wXhRRTUVRjBXJ2qIooVP
    [login] => karin.olen
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=wXhRRTUVRjBXJ2qIooVP&login=karin.olen&action=reset_password
Key: wXhRRTUVRjBXJ2qIooVP
Login: karin.olen
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => 2AIqqmzJ0fk229FrbIeM
    [login] => deepika.nagasamy
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=2AIqqmzJ0fk229FrbIeM&login=deepika.nagasamy&action=reset_password
Key: 2AIqqmzJ0fk229FrbIeM
Login: deepika.nagasamy
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => 2AIqqmzJ0fk229FrbIeM
    [login] => deepika.nagasamy
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=2AIqqmzJ0fk229FrbIeM&login=deepika.nagasamy&action=reset_password
Key: 2AIqqmzJ0fk229FrbIeM
Login: deepika.nagasamy
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => yEcCy5vxNgQb0l5C9bxP
    [login] => helene.svanholm
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=yEcCy5vxNgQb0l5C9bxP&login=helene.svanholm&action=reset_password
Key: yEcCy5vxNgQb0l5C9bxP
Login: helene.svanholm
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => vQSa9TTyRMYhDzfso6XD
    [login] => anna.macura
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=vQSa9TTyRMYhDzfso6XD&login=anna.macura&action=reset_password
Key: vQSa9TTyRMYhDzfso6XD
Login: anna.macura
==== DIRECT PASSWORD RESET HANDLER ====
GET data: Array
(
    [key] => TPZb3Ef2GMInU0vF0GR7
    [login] => lucy.nguyen
    [action] => reset_password
)

REQUEST_URI: /lci-dashboard/?key=TPZb3Ef2GMInU0vF0GR7&login=lucy.nguyen&action=reset_password
Key: TPZb3Ef2GMInU0vF0GR7
Login: lucy.nguyen
