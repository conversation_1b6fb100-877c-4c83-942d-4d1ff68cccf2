<?php
// This is a new step that shows 3 cards for Brasov accommodation periods
// It appears immediately after the user selects "Yes, I need accommodation in Brasov"
?>

<h3 class="text-center mb-4">When do you need accommodation in Brasov?</h3>
<div class="info-box">
    <i class="fas fa-info-circle me-2"></i>
    <span>Please select when you need accommodation in Brasov</span>
</div>

<div class="option-cards">
    <!-- Pre-event Card -->
    <label class="option-card <?php echo isset($wizard['brasov_period']) && $wizard['brasov_period'] === 'pre' ? 'selected' : ''; ?>">
        <input type="radio" name="brasov_period" value="pre" style="display: none;" <?php checked(isset($wizard['brasov_period']) && $wizard['brasov_period'] === 'pre', true); ?>>
        <div class="option-card-image">
            <div style="position: absolute; top: 10px; left: 10px; background-color: rgba(0,0,0,0.6); color: white; padding: 5px 10px; border-radius: 20px; font-size: 12px; font-weight: bold; z-index: 2;">
                <i class="fas fa-calendar-minus me-1"></i> PRE-EVENT
            </div>
            <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121214/pre-event.jpg" alt="Pre-event Accommodation">
        </div>
        <div class="option-card-content">
            <h4 class="option-card-title">Pre-event Accommodation</h4>
            <p class="option-card-description">Stay in Brasov before the event (before August 21, 2025)</p>
        </div>
    </label>

    <!-- Main Event Card -->
    <label class="option-card <?php echo isset($wizard['brasov_period']) && $wizard['brasov_period'] === 'main' ? 'selected' : ''; ?>">
        <input type="radio" name="brasov_period" value="main" style="display: none;" <?php checked(isset($wizard['brasov_period']) && $wizard['brasov_period'] === 'main', true); ?>>
        <div class="option-card-image">
            <div style="position: absolute; top: 10px; left: 10px; background-color: rgba(0,178,227,0.8); color: white; padding: 5px 10px; border-radius: 20px; font-size: 12px; font-weight: bold; z-index: 2;">
                <i class="fas fa-calendar-day me-1"></i> DURING EVENT
            </div>
            <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121213/brasov-1.jpg" alt="Main Event Accommodation">
        </div>
        <div class="option-card-content">
            <h4 class="option-card-title">Main Event Accommodation</h4>
            <p class="option-card-description">Stay in Brasov during the event (August 21-24, 2025)</p>
        </div>
    </label>

    <!-- Post-event Card -->
    <label class="option-card <?php echo isset($wizard['brasov_period']) && $wizard['brasov_period'] === 'post' ? 'selected' : ''; ?>">
        <input type="radio" name="brasov_period" value="post" style="display: none;" <?php checked(isset($wizard['brasov_period']) && $wizard['brasov_period'] === 'post', true); ?>>
        <div class="option-card-image">
            <div style="position: absolute; top: 10px; left: 10px; background-color: rgba(0,0,0,0.6); color: white; padding: 5px 10px; border-radius: 20px; font-size: 12px; font-weight: bold; z-index: 2;">
                <i class="fas fa-calendar-plus me-1"></i> POST-EVENT
            </div>
            <img src="https://lci2025brasov.s3.eu-central-1.amazonaws.com/wp-content/uploads/2024/04/10121215/post-event.jpg" alt="Post-event Accommodation">
        </div>
        <div class="option-card-content">
            <h4 class="option-card-title">Post-event Accommodation</h4>
            <p class="option-card-description">Stay in Brasov after the event (after August 24, 2025)</p>
        </div>
    </label>
</div>

<script>
// Simple script to handle option card selection
document.addEventListener('DOMContentLoaded', function() {
    const optionCards = document.querySelectorAll('.option-card');
    optionCards.forEach(card => {
        card.addEventListener('click', function() {
            // Find the radio input inside this card and check it
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;

                // Remove selected class from all cards
                optionCards.forEach(c => c.classList.remove('selected'));

                // Add selected class to this card
                this.classList.add('selected');

                // Don't auto-submit - let the user click the Next button
                // This gives them time to review their selection
            }
        });
    });
});
</script>
