/**
 * Alpine.js Components Styles for LCI 2025 Dashboard
 */

/* Modal styles */
.lci-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;
}

.lci-modal {
    background-color: white;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.lci-modal-enter {
    opacity: 0;
    transform: translateY(20px);
}

.lci-modal-leave {
    opacity: 0;
    transform: translateY(-20px);
}

.lci-modal-header {
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    background: linear-gradient(to bottom, rgba(54, 177, 220, 0.1), rgba(255, 255, 255, 0));
    border-radius: 1rem 1rem 0 0;
}

.lci-modal-logo-container {
    width: 80px;
    height: 80px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.lci-modal-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 8px;
}

.lci-modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin: 0;
    position: relative;
    padding-bottom: 0.5rem;
}

.lci-modal-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background-color: #36b1dc;
    border-radius: 3px;
}

.lci-modal-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.lci-modal-body {
    padding: 1.5rem;
    text-align: center;
    color: #555;
    font-size: 1rem;
    line-height: 1.5;
    background-color: #fafafa;
}

.lci-modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Mini-cart footer mobile styles */
@media (max-width: 576px) {
    .lci-modal-footer .d-flex.justify-content-between.align-items-center.w-100 {
        flex-direction: column;
        gap: 15px;
    }

    .lci-modal-footer .mini-cart-total-container {
        width: 100%;
        text-align: center;
        font-size: 1.2rem;
        font-weight: 600;
    }

    .lci-modal-footer .d-flex.justify-content-between.align-items-center.w-100 > div:last-child {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .lci-modal-footer .lci-btn {
        flex: 1;
        margin: 0 5px;
        white-space: nowrap;
    }

    .lci-modal-footer .lci-btn-secondary {
        margin-right: 5px !important;
    }

    .lci-modal-footer .lci-btn-primary {
        margin-left: 5px;
    }
}

.lci-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    outline: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.lci-btn-primary {
    background-color: #36b1dc;
    color: #fff;
    position: relative;
    overflow: hidden;
}

.lci-btn-primary:hover {
    background-color: #2d93b7;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.lci-btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.lci-btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.lci-btn-primary:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

.lci-btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    position: relative;
    overflow: hidden;
}

.lci-btn-secondary:hover {
    background-color: #e5e7eb;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.lci-btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Progress bar for auto-close */
.lci-progress-bar {
    height: 4px;
    background-color: #e5e7eb;
    width: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 0 0 0.5rem 0.5rem;
}

.lci-progress-bar-inner {
    position: absolute;
    height: 100%;
    background-color: #36b1dc;
    width: 100%;
    transform-origin: left;
    transition: transform linear;
}

/* Animation for modal entrance and exit */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(-20px); opacity: 0; }
}

.fade-in {
    animation: fadeIn 0.3s ease forwards;
}

.fade-out {
    animation: fadeOut 0.3s ease forwards;
}

.slide-in {
    animation: slideIn 0.3s ease forwards;
}

.slide-out {
    animation: slideOut 0.3s ease forwards;
}

/* Prevent body scrolling when modal is open */
body.modal-open {
    overflow: hidden;
}

/* Fix for modal z-index issues */
.modal-backdrop { z-index: 1050 !important; }
.modal { z-index: 1055 !important; }
.lci-modal-backdrop { z-index: 1060 !important; }

/* Fix for duplicate modal backdrops */
.modal-backdrop + .modal-backdrop { display: none !important; }
body > .modal-backdrop:nth-child(n+2) { display: none !important; }

/* Fix for modal padding issues */
body.modal-open { padding-right: 0 !important; }

/* Mini Cart Button */
#mini-cart-button {
    display: flex;
    align-items: center;
    background-color: var(--primary-color, #36b1dc);
    color: white !important;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    visibility: visible !important;
    opacity: 1 !important;
}

#mini-cart-button:hover {
    background-color: #2a8fb3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dashboard Mini Cart Container */
.dashboard-mini-cart-container {
    margin-bottom: 1.5rem;
}

@media (max-width: 576px) {
    .dashboard-mini-cart-container #mini-cart-button,
    .dashboard-mini-cart-container .mini-cart-wrapper {
        width: 100%;
        justify-content: center;
    }
}
