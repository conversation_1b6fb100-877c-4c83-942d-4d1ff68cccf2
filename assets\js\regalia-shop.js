/**
 * Regalia Shop JavaScript
 */

jQuery(document).ready(function($) {
    // Mini Cart functionality
    const miniCartModal = new bootstrap.Modal(document.getElementById('miniCartModal'));
    const miniCartButton = document.getElementById('mini-cart-button');
    const addToCartConfirmModal = new bootstrap.Modal(document.getElementById('addToCartConfirmModal'));
    const viewCartBtn = document.getElementById('view-cart-btn');

    // Show mini cart when button is clicked
    if (miniCartButton) {
        miniCartButton.addEventListener('click', function(e) {
            e.preventDefault();
            loadMiniCartItems();
            miniCartModal.show();
        });
    }

    // View cart button in confirmation modal
    if (viewCartBtn) {
        viewCartBtn.addEventListener('click', function() {
            addToCartConfirmModal.hide();
            loadMiniCartItems();
            miniCartModal.show();
        });
    }

    // Function to load mini cart items
    function loadMiniCartItems() {
        const miniCartItemsContainer = document.getElementById('mini-cart-items');

        if (!miniCartItemsContainer) {
            console.error('Mini cart container not found');
            return;
        }

        // Show loading state
        miniCartItemsContainer.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading your items...</p>
            </div>
        `;

        // Get cart count from badge
        const cartCountElement = document.querySelector('#mini-cart-button .badge');
        const cartCount = cartCountElement ? parseInt(cartCountElement.textContent) : 0;

        // Get cart total from mini-cart-total
        const miniCartTotal = document.querySelector('.mini-cart-total');
        const cartTotal = miniCartTotal ? miniCartTotal.textContent : '0.00 €';

        // If cart is empty, show empty message
        if (cartCount === 0) {
            miniCartItemsContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-shopping-bag text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-3">Your goodies bag is empty</p>
                </div>
            `;

            const modalTotal = document.querySelector('.mini-cart-modal-total');
            if (modalTotal) {
                modalTotal.textContent = cartTotal;
            }
            return;
        }

        // Create a nonce for security
        const nonce = lci_ajax_object.nonce;

        // Fetch cart contents via our custom AJAX endpoint
        fetch(lci_ajax_object.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=lci_get_mini_cart_items&nonce=' + nonce
        })
        .then(response => response.json())
        .then(data => {
            console.log('Cart items response:', data);

            if (data.success && data.data.items && data.data.items.length > 0) {
                const cartItems = data.data.items;
                let cartItemsHtml = '<div class="cart-items-list">';

                cartItems.forEach(item => {
                    cartItemsHtml += `
                        <div class="mini-cart-item d-flex align-items-center p-3 border-bottom">
                            <div class="mini-cart-item-image me-3">
                                <img src="${item.image}" alt="${item.name}" class="img-fluid rounded" style="width: 60px; height: 60px; object-fit: contain;">
                            </div>
                            <div class="mini-cart-item-details flex-grow-1">
                                <h6 class="mini-cart-item-name mb-1">${item.name}</h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="mini-cart-item-quantity badge bg-light text-dark">${item.quantity} ×</span>
                                    <span class="mini-cart-item-price text-primary fw-bold">${item.price}</span>
                                </div>
                            </div>
                            <div class="mini-cart-item-remove ms-2">
                                <a href="#" class="text-danger remove-cart-item" data-cart-item-key="${item.key}">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    `;
                });

                cartItemsHtml += '</div>';
                miniCartItemsContainer.innerHTML = cartItemsHtml;

                // Update the total in the modal footer
                const modalTotal = document.querySelector('.mini-cart-modal-total');
                if (modalTotal) {
                    modalTotal.innerHTML = data.data.total;
                }

                // Add event listeners to remove buttons
                document.querySelectorAll('.remove-cart-item').forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const cartItemKey = this.getAttribute('data-cart-item-key');
                        if (cartItemKey) {
                            removeCartItem(cartItemKey);
                        }
                    });
                });
            } else {
                // Show empty cart message
                miniCartItemsContainer.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-bag text-muted" style="font-size: 3rem;"></i>
                        <p class="mt-3">Your goodies bag is empty</p>
                    </div>
                `;

                // Update the total in the modal footer
                const modalTotal = document.querySelector('.mini-cart-modal-total');
                if (modalTotal) {
                    modalTotal.innerHTML = '0.00 €';
                }
            }
        })
        .catch(error => {
            console.error('Error loading cart items:', error);
            miniCartItemsContainer.innerHTML = `
                <div class="p-4">
                    <div class="alert alert-danger mb-0">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Error loading cart items. Please try again.
                    </div>
                </div>
            `;
        });
    }

    // Function to remove cart item
    function removeCartItem(cartItemKey) {
        // Create a nonce for security
        const nonce = lci_ajax_object.nonce;

        fetch(lci_ajax_object.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=lci_remove_cart_item&nonce=' + nonce + '&cart_item_key=' + cartItemKey
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload mini cart items
                loadMiniCartItems();

                // Update page elements
                const cartCount = data.data.cart_count || 0;
                const cartCountBadge = document.querySelector('#mini-cart-button .badge');

                if (cartCountBadge) {
                    if (cartCount > 0) {
                        cartCountBadge.textContent = cartCount;
                        cartCountBadge.style.display = 'block';
                    } else {
                        cartCountBadge.style.display = 'none';
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error removing cart item:', error);
        });
    }

    // Add to cart functionality for simple products
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.getAttribute('data-product-id');
            const productCard = this.closest('.regalia-product-card');
            
            if (!productId || !productCard) {
                console.error('Product ID or card not found');
                return;
            }

            // Add loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            this.disabled = true;

            // Add to cart via AJAX
            const formData = new FormData();
            formData.append('action', 'lci-dashboard-add-to-cart');
            formData.append('product_id', productId);
            formData.append('quantity', 1);
            formData.append('security', lci_ajax_object.add_to_cart_nonce);

            fetch(lci_ajax_object.ajax_url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;

                if (data.error) {
                    alert(data.message || 'Error adding product to cart');
                } else {
                    // Get product image and name for confirmation modal
                    const productImage = productCard.querySelector('.regalia-product-image img');
                    const productTitle = productCard.querySelector('.regalia-product-title');

                    // Set product details in confirmation modal
                    document.getElementById('added-product-image').src = productImage ? productImage.src : '';
                    document.getElementById('added-product-name').textContent = productTitle ? productTitle.textContent : 'Product';

                    // Remove all backdrops first
                    document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());

                    // Show confirmation modal
                    addToCartConfirmModal.show();

                    // Auto-close functionality with progress bar
                    const progressBar = document.querySelector('#autoCloseProgress .progress-bar');
                    progressBar.style.width = '0%';

                    // Reset and start the progress bar
                    setTimeout(() => {
                        progressBar.style.width = '100%';
                    }, 100);

                    // Auto-close after 3 seconds
                    setTimeout(() => {
                        addToCartConfirmModal.hide();
                    }, 3100);

                    // Clean up again after showing
                    setTimeout(cleanupModalBackdrops, 50);

                    // Update mini cart in background
                    loadMiniCartItems();
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                this.innerHTML = originalText;
                this.disabled = false;
                alert('Error adding product to cart. Please try again.');
            });
        });
    });

    // Show variations button functionality for variable products
    const showVariationsButtons = document.querySelectorAll('.show-variations-btn');

    showVariationsButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the product card and variation card
            const productCard = this.closest('.regalia-product-card');

            if (!productCard) {
                console.error('Product card not found');
                return;
            }

            const variationCard = productCard.querySelector('.regalia-variation-card');

            if (!variationCard) {
                console.error('Variation card not found');
                return;
            }

            // Show the variation card with animation
            variationCard.classList.add('active');

            // Add event listener to close button if not already added
            const closeButton = variationCard.querySelector('.close-variation-card');
            if (closeButton && !closeButton._hasClickListener) {
                closeButton.addEventListener('click', function() {
                    // Add a closing class for animation
                    variationCard.classList.add('closing');

                    // Remove the active class after animation completes
                    setTimeout(() => {
                        variationCard.classList.remove('active');
                        variationCard.classList.remove('closing');
                    }, 700); // Match the animation duration (slightly longer to ensure completion)
                });
                closeButton._hasClickListener = true;
            }

            // Add event listeners to quantity buttons if not already added
            const quantityMinus = variationCard.querySelector('.quantity-minus');
            const quantityPlus = variationCard.querySelector('.quantity-plus');
            const quantityInput = variationCard.querySelector('.quantity-input');

            if (quantityMinus && !quantityMinus._hasClickListener) {
                quantityMinus.addEventListener('click', function() {
                    const currentValue = parseInt(quantityInput.value);
                    if (currentValue > 1) {
                        quantityInput.value = currentValue - 1;
                    }
                });
                quantityMinus._hasClickListener = true;
            }

            if (quantityPlus && !quantityPlus._hasClickListener) {
                quantityPlus.addEventListener('click', function() {
                    const currentValue = parseInt(quantityInput.value);
                    quantityInput.value = currentValue + 1;
                });
                quantityPlus._hasClickListener = true;
            }
        });
    });

    // Add to cart functionality for variable products
    document.querySelectorAll('.add-variable-to-cart-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const productCard = this.closest('.regalia-product-card');
            const variationCard = this.closest('.regalia-variation-card');
            
            if (!productCard || !variationCard) {
                console.error('Product card or variation card not found');
                return;
            }
            
            const productId = this.getAttribute('data-product-id');
            const variationId = variationCard.querySelector('input[name="variation_id"]:checked')?.value;
            const quantity = variationCard.querySelector('.quantity-input')?.value || 1;
            
            if (!productId || !variationId) {
                alert('Please select a variation');
                return;
            }
            
            // Add loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            this.disabled = true;
            
            // Add to cart via AJAX
            const formData = new FormData();
            formData.append('action', 'lci-dashboard-add-to-cart');
            formData.append('product_id', productId);
            formData.append('variation_id', variationId);
            formData.append('quantity', quantity);
            formData.append('security', lci_ajax_object.add_to_cart_nonce);
            
            fetch(lci_ajax_object.ajax_url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Reset button
                this.innerHTML = originalText;
                this.disabled = false;
                
                if (data.error) {
                    // Show error
                    alert(data.message || 'Error adding product to cart');
                } else if (data.success) {
                    // Hide the variation card
                    const variationCard = this.closest('.regalia-variation-card');
                    if (variationCard) {
                        variationCard.classList.remove('active');
                    }
                    
                    // Get product image and name for confirmation modal
                    const productImage = productCard.querySelector('.regalia-product-image img');
                    const productTitle = productCard.querySelector('.regalia-product-title');
                    
                    // Set product details in confirmation modal
                    document.getElementById('added-product-image').src = productImage ? productImage.src : '';
                    document.getElementById('added-product-name').textContent = productTitle ? productTitle.textContent : 'Product';
                    
                    // Remove all backdrops first
                    document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());
                    
                    // Show confirmation modal
                    addToCartConfirmModal.show();
                    
                    // Auto-close functionality with progress bar
                    const progressBar = document.querySelector('#autoCloseProgress .progress-bar');
                    progressBar.style.width = '0%';
                    
                    // Reset and start the progress bar
                    setTimeout(() => {
                        progressBar.style.width = '100%';
                    }, 100);
                    
                    // Auto-close after 3 seconds
                    setTimeout(() => {
                        addToCartConfirmModal.hide();
                    }, 3100);
                    
                    // Clean up again after showing
                    setTimeout(cleanupModalBackdrops, 50);
                    
                    // Update mini cart in background
                    loadMiniCartItems();
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                this.innerHTML = originalText;
                this.disabled = false;
                alert('Error adding product to cart. Please try again.');
            });
        });
    });

    // Function to clean up modal backdrops
    function cleanupModalBackdrops() {
        const backdrops = document.querySelectorAll('.modal-backdrop');
        if (backdrops.length > 1) {
            for (let i = 1; i < backdrops.length; i++) {
                backdrops[i].remove();
            }
        }
    }

    // Load mini cart items on page load if cart has items
    const cartCountBadge = document.querySelector('#mini-cart-button .badge');
    if (cartCountBadge && parseInt(cartCountBadge.textContent) > 0) {
        // Preload cart items in background
        setTimeout(loadMiniCartItems, 1000);
    }
});
