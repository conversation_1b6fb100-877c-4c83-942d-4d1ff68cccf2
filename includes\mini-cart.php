<?php
/**
 * LCI 2025 Dashboard Mini Cart
 *
 * Adds the mini cart to the regalia shop page and checkout page with regalia items only
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

/**
 * Check if current page is the regalia shop page
 *
 * @return bool True if current page is the regalia shop page
 */
function lci_is_regalia_shop_page() {
    global $post;

    // Debug info
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Mini Cart: Checking if current page is regalia shop page');
        error_log('LCI Mini Cart: Current URL: ' . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'unknown'));
        error_log('LCI Mini Cart: Tab parameter: ' . (isset($_GET['tab']) ? $_GET['tab'] : 'not set'));

        if (is_a($post, 'WP_Post')) {
            error_log('LCI Mini Cart: Post ID: ' . $post->ID);
            error_log('LCI Mini Cart: Post content has dashboard shortcode: ' . (has_shortcode($post->post_content, 'lci2025_dashboard') ? 'yes' : 'no'));
        } else {
            error_log('LCI Mini Cart: No post object available');
        }
    }

    // Check if we're on a page with the dashboard shortcode AND the regalia shop tab
    if (is_a($post, 'WP_Post') &&
        has_shortcode($post->post_content, 'lci2025_dashboard') &&
        isset($_GET['tab']) && $_GET['tab'] === 'regalia-shop') {

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Mini Cart: This IS a regalia shop page (dashboard with regalia tab)');
        }
        return true;
    }

    // Check if we're on the WooCommerce checkout page and have regalia items in cart
    if (function_exists('is_checkout') && is_checkout() && function_exists('WC')) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Mini Cart: This is a checkout page, checking for regalia items');
        }

        // Check if cart has items from category 22
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product_id = $cart_item['product_id'];
            if (has_term(22, 'product_cat', $product_id)) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('LCI Mini Cart: Found regalia item in cart (product ID: ' . $product_id . ')');
                }
                return true;
            }
        }
    }

    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Mini Cart: This is NOT a regalia shop page');
    }
    return false;
}

/**
 * Get mini cart HTML
 *
 * @return string Mini cart HTML
 */
function lci_get_mini_cart_html() {
    // Debug info
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Mini Cart: lci_get_mini_cart_html called');
    }

    // Get cart info
    $cart_count = 0;
    $cart_total = '0.00 €';

    if (function_exists('WC')) {
        $cart_count = WC()->cart->get_cart_contents_count();
        $cart_total = WC()->cart->get_cart_total();
    }

    // Start output buffering
    ob_start();

    // Add x-cloak style to hide Alpine.js elements before initialization
    echo '<style>[x-cloak] { display: none !important; }</style>';

    // Use the regalia-specific mini-cart template for the regalia shop page
    if (isset($_GET['tab']) && $_GET['tab'] === 'regalia-shop') {
        include_once LCI2025_PATH . 'templates/components/regalia-mini-cart.php';
    } else {
        // Use the standard mini-cart template for other pages
        include_once LCI2025_PATH . 'templates/components/mini-cart.php';
    }

    // Get the buffered content
    $mini_cart_html = ob_get_clean();

    return $mini_cart_html;
}

/**
 * Get add to cart confirmation modal HTML
 *
 * @return string Add to cart confirmation modal HTML
 */
function lci_get_add_to_cart_confirm_html() {
    // Debug info
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Mini Cart: lci_get_add_to_cart_confirm_html called');
    }

    // Start output buffering
    ob_start();

    // Add x-cloak style to hide Alpine.js elements before initialization
    echo '<style>[x-cloak] { display: none !important; }</style>';

    // Include add to cart confirmation template
    include_once LCI2025_PATH . 'templates/components/add-to-cart-confirm.php';

    // Get the buffered content
    $confirm_html = ob_get_clean();

    return $confirm_html;
}

/**
 * Add mini cart to footer - DEPRECATED
 * This function is kept for backward compatibility but is no longer used
 */
function lci_add_mini_cart_to_footer() {
    // Debug info
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Mini Cart: lci_add_mini_cart_to_footer called - DEPRECATED');
    }

    // This function is no longer used
    return;
}

// Remove the action hook since we no longer want to add the mini cart to the footer
remove_action('wp_footer', 'lci_add_mini_cart_to_footer');

/**
 * Add mini cart to dashboard pages
 */
function lci_add_mini_cart_to_dashboard() {
    // Skip for tours and regalia shop pages as they already have their own implementation
    if (isset($_GET['tab']) && ($_GET['tab'] === 'tours' || $_GET['tab'] === 'regalia-shop')) {
        return;
    }

    // Output the mini cart HTML
    echo '<div class="dashboard-mini-cart-container mb-4">
        <div class="d-flex justify-content-end">
            ' . lci_get_mini_cart_html() . '
        </div>
    </div>';

    // Add a script to ensure the mini-cart is properly initialized
    echo '<script>
        document.addEventListener("DOMContentLoaded", function() {
            // Make sure lci_ajax_object is defined
            if (typeof lci_ajax_object === "undefined") {
                window.lci_ajax_object = {
                    ajax_url: "' . admin_url('admin-ajax.php') . '",
                    nonce: "' . wp_create_nonce('lci_mini_cart_nonce') . '"
                };
            }
        });
    </script>';
}
add_action('lci_dashboard_before_content', 'lci_add_mini_cart_to_dashboard');

/**
 * Enqueue mini cart scripts and styles
 */
function lci_enqueue_mini_cart_scripts() {
    // Debug info
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Mini Cart: lci_enqueue_mini_cart_scripts called');
    }

    // Check if we're on a dashboard page
    if (!is_page('lci-dashboard')) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Mini Cart: Not enqueuing mini cart scripts - not on dashboard page');
        }
        return;
    }

    // Skip for tours and regalia shop pages as they already have their own implementation
    if (isset($_GET['tab']) && ($_GET['tab'] === 'tours' || $_GET['tab'] === 'regalia-shop')) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('LCI Mini Cart: Not enqueuing mini cart scripts - already handled by tours or regalia shop');
        }
        return;
    }

    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('LCI Mini Cart: Enqueuing mini cart scripts');
    }

    // Enqueue Alpine.js if not already enqueued
    if (!wp_script_is('alpine-js', 'enqueued')) {
        wp_enqueue_script('alpine-js', 'https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js', [], '3.13.3', true);
    }

    // Enqueue Alpine.js components CSS
    wp_enqueue_style('lci-alpine-components', LCI2025_URL . 'assets/css/alpine-components.css', [], LCI2025_VERSION);

    // Enqueue mini cart script
    wp_enqueue_script('lci-mini-cart', LCI2025_URL . 'assets/js/mini-cart.js', ['alpine-js'], LCI2025_VERSION, true);

    // Enqueue fundraising script
    wp_enqueue_script('lci-fundraising', LCI2025_URL . 'assets/js/fundraising.js', ['jquery'], LCI2025_VERSION, true);

    // Add AJAX URL and nonce
    wp_localize_script('lci-mini-cart', 'lci_ajax_object', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('lci_mini_cart_nonce')
    ]);
}
add_action('wp_enqueue_scripts', 'lci_enqueue_mini_cart_scripts');

/**
 * Ensure mini cart AJAX object is available on all dashboard pages
 */
function lci_ensure_mini_cart_ajax_object() {
    // Skip if not on dashboard page
    if (!is_page('lci-dashboard')) {
        return;
    }

    // If the mini-cart script is not enqueued (e.g., on tours or regalia shop pages),
    // we still need to make sure the lci_ajax_object is available
    if (!wp_script_is('lci-mini-cart', 'enqueued')) {
        // Enqueue a small script to define the lci_ajax_object
        wp_enqueue_script('lci-mini-cart-ajax-object', LCI2025_URL . 'assets/js/mini-cart-ajax-object.js', [], LCI2025_VERSION, true);

        // Localize the script
        wp_localize_script('lci-mini-cart-ajax-object', 'lci_ajax_object', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lci_mini_cart_nonce')
        ]);
    }
}
add_action('wp_enqueue_scripts', 'lci_ensure_mini_cart_ajax_object', 999); // Run after other scripts
