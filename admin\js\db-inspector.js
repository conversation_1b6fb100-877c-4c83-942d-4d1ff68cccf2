/**
 * LCI 2025 Dashboard Database Inspector JavaScript
 */

// Define Alpine.js components
document.addEventListener('alpine:init', function() {
    // Database Inspector component
    Alpine.data('dbInspector', function() {
        return {
            isLoading: true,
            isLoadingData: false,
            tables: [],
            selectedTable: '',
            tableStructure: [],
            tableData: [],
            searchQuery: '',
            currentPage: 1,
            perPage: 20,
            totalPages: 1,
            totalRecords: 0,

            init() {
                this.fetchTables();
            },

            fetchTables() {
                this.isLoading = true;

                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_get_tables_list',
                        nonce: lciAdmin.nonce
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.tables = data.data.tables;
                        } else {
                            console.error('Error fetching tables:', data.data.message);
                            this.showToast('Error fetching tables: ' + data.data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching tables:', error);
                        this.showToast('Error fetching tables', 'error');
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },

            changeTable() {
                if (!this.selectedTable) {
                    this.tableStructure = [];
                    this.tableData = [];
                    return;
                }

                this.fetchTableStructure();
                this.fetchTableData();
            },

            fetchTableStructure() {
                this.isLoading = true;

                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_get_table_structure',
                        nonce: lciAdmin.nonce,
                        table: this.selectedTable
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.tableStructure = data.data.columns;
                        } else {
                            console.error('Error fetching table structure:', data.data.message);
                            this.showToast('Error fetching table structure: ' + data.data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching table structure:', error);
                        this.showToast('Error fetching table structure', 'error');
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },

            fetchTableData() {
                this.isLoadingData = true;

                fetch(lciAdmin.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_get_table_data',
                        nonce: lciAdmin.nonce,
                        table: this.selectedTable,
                        search: this.searchQuery,
                        page: this.currentPage,
                        per_page: this.perPage
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.tableData = data.data.data;
                            this.totalPages = data.data.total_pages;
                            this.totalRecords = data.data.total;
                            this.currentPage = data.data.page;
                        } else {
                            console.error('Error fetching table data:', data.data.message);
                            this.showToast('Error fetching table data: ' + data.data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching table data:', error);
                        this.showToast('Error fetching table data', 'error');
                    })
                    .finally(() => {
                        this.isLoadingData = false;
                    });
            },

            search() {
                this.currentPage = 1;
                this.fetchTableData();
            },

            refreshData() {
                this.fetchTableStructure();
                this.fetchTableData();
            },

            prevPage() {
                if (this.currentPage > 1) {
                    this.currentPage--;
                    this.fetchTableData();
                }
            },

            nextPage() {
                if (this.currentPage < this.totalPages) {
                    this.currentPage++;
                    this.fetchTableData();
                }
            },

            showToast(message, type = 'info') {
                // Use the global showToast function
                window.showToast(message, type);
            }
        };
    });
});
