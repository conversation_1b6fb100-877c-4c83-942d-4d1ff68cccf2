/**
 * Fix for Alpine.js errors in mini-cart and other components
 */
(function() {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        // Function to patch Alpine.js Object.values error
        function patchAlpineObjectValues() {
            // Check if Alpine.js is loaded
            if (typeof Alpine !== 'undefined') {
                console.log('Patching Alpine.js to prevent Object.values errors');
                
                // Patch Alpine.js internal functions that might cause errors
                const originalValues = Object.values;
                Object.values = function(obj) {
                    // Add null/undefined check before calling original function
                    if (obj === null || obj === undefined) {
                        console.warn('Alpine.js: Attempted to call Object.values on null or undefined');
                        return [];
                    }
                    return originalValues(obj);
                };
                
                // Also patch Object.entries for similar issues
                const originalEntries = Object.entries;
                Object.entries = function(obj) {
                    // Add null/undefined check before calling original function
                    if (obj === null || obj === undefined) {
                        console.warn('Alpine.js: Attempted to call Object.entries on null or undefined');
                        return [];
                    }
                    return originalEntries(obj);
                };
                
                // Also patch Object.keys for similar issues
                const originalKeys = Object.keys;
                Object.keys = function(obj) {
                    // Add null/undefined check before calling original function
                    if (obj === null || obj === undefined) {
                        console.warn('Alpine.js: Attempted to call Object.keys on null or undefined');
                        return [];
                    }
                    return originalKeys(obj);
                };
            }
        }
        
        // Function to fix Alpine.js initialization issues
        function fixAlpineInitialization() {
            // Check if mini-cart container exists
            const miniCartContainer = document.getElementById('mini-cart-container');
            if (miniCartContainer && typeof Alpine !== 'undefined') {
                // Check if Alpine.js is not initialized on the mini-cart
                if (!miniCartContainer.__x) {
                    try {
                        // Try to initialize Alpine.js on the mini-cart
                        Alpine.initTree(miniCartContainer);
                        console.log('Mini-cart Alpine.js component initialized by fix-alpine.js');
                    } catch (error) {
                        console.error('Error initializing mini-cart Alpine.js component:', error);
                    }
                }
            }
            
            // Check for other Alpine.js components that might need initialization
            document.querySelectorAll('[x-data]:not([data-alpine-initialized])').forEach(function(component) {
                if (!component.__x && typeof Alpine !== 'undefined') {
                    try {
                        // Try to initialize Alpine.js on the component
                        Alpine.initTree(component);
                        // Mark as initialized
                        component.setAttribute('data-alpine-initialized', 'true');
                        console.log('Alpine.js component initialized by fix-alpine.js:', component);
                    } catch (error) {
                        console.error('Error initializing Alpine.js component:', error);
                    }
                }
            });
        }
        
        // Apply patches
        patchAlpineObjectValues();
        
        // Apply fixes after a short delay to ensure Alpine.js is loaded
        setTimeout(fixAlpineInitialization, 500);
        
        // Also apply fixes when Alpine.js is initialized
        document.addEventListener('alpine:initialized', function() {
            patchAlpineObjectValues();
            setTimeout(fixAlpineInitialization, 100);
        });
        
        // Apply fixes periodically to catch any late initializations
        setInterval(fixAlpineInitialization, 2000);
    });
})();
