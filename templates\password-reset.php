<?php
/**
 * Password Reset Template
 *
 * This template is used to display the password reset form.
 */

// Get URL parameters
$key = isset($_GET['key']) ? sanitize_text_field($_GET['key']) : '';
$login = isset($_GET['login']) ? sanitize_text_field($_GET['login']) : '';
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
$show_reset = isset($_GET['show-reset-form']) ? sanitize_text_field($_GET['show-reset-form']) : '';

// Check if this is a password reset request
$is_reset_request = ($key && $login && ($action === 'reset_password' || $show_reset === 'true'));

// Log the request
$debug_file = __DIR__ . '/../includes/debug-password-reset.txt';
$log = "==== PASSWORD RESET TEMPLATE LOADED ====\n";
$log .= "GET data: " . print_r($_GET, true) . "\n";
$log .= "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
$log .= "Is reset request: " . ($is_reset_request ? 'Yes' : 'No') . "\n";
file_put_contents($debug_file, $log, FILE_APPEND);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password - LCI 2025</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .reset-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 30px;
            max-width: 500px;
            width: 100%;
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 150px;
            height: auto;
        }
        h2 {
            color: #36b1dc;
            text-align: center;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            font-weight: bold;
            color: #4a5568;
        }
        .form-control {
            border-radius: 5px;
            padding: 12px;
            border: 1px solid #e2e8f0;
        }
        .btn-primary {
            background-color: #36b1dc;
            border: none;
            border-radius: 5px;
            padding: 12px;
            font-weight: bold;
            width: 100%;
            margin-top: 10px;
        }
        .btn-primary:hover {
            background-color: #2d93b7;
        }
        .alert {
            margin-bottom: 20px;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #36b1dc;
            text-decoration: none;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="logo">
            <img src="<?php echo esc_url(LCI2025_URL . 'assets/img/logo.png'); ?>" alt="LCI 2025 Logo">
        </div>

        <h2>Reset Your Password</h2>

        <div class="alert alert-info text-center">
            Please create a new password for your account.
        </div>

        <form id="reset-password-form">
            <input type="hidden" id="reset-login" value="<?php echo esc_attr($login); ?>">
            <input type="hidden" id="reset-key" value="<?php echo esc_attr($key); ?>">

            <div class="form-group">
                <label for="new-password" class="form-label">New Password</label>
                <input type="password" id="new-password" class="form-control" required>
                <small class="text-muted">Password must be at least 8 characters long.</small>
            </div>

            <div class="form-group">
                <label for="confirm-password" class="form-label">Confirm Password</label>
                <input type="password" id="confirm-password" class="form-control" required>
            </div>

            <button type="submit" class="btn btn-primary">
                <i class="fas fa-key me-2"></i> Reset Password
            </button>
        </form>

        <div id="message-container"></div>

        <div class="back-link">
            <a href="<?php echo esc_url(home_url('/lci-dashboard/')); ?>">
                <i class="fas fa-arrow-left me-1"></i> Back to Login
            </a>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetForm = document.getElementById('reset-password-form');
            const messageContainer = document.getElementById('message-container');

            // Log the login parameter for debugging
            const login = document.getElementById('reset-login').value;
            console.log('Login parameter:', login);

            resetForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const login = document.getElementById('reset-login').value;
                const key = document.getElementById('reset-key').value;
                const newPassword = document.getElementById('new-password').value;
                const confirmPassword = document.getElementById('confirm-password').value;

                // Validate passwords
                if (newPassword.length < 8) {
                    showMessage('Error', 'Password must be at least 8 characters long.', 'error');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    showMessage('Error', 'Passwords do not match.', 'error');
                    return;
                }

                // Submit the form via AJAX
                fetch('<?php echo esc_url(admin_url('admin-ajax.php')); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        action: 'lci_ajax_change_password',
                        login: login,
                        password: newPassword,
                        key: key
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('Success', data.data.message, 'success');
                        // Redirect to login after 3 seconds
                        setTimeout(function() {
                            window.location.href = '<?php echo esc_url(home_url('/lci-dashboard/')); ?>';
                        }, 3000);
                    } else {
                        showMessage('Error', data.data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('Error', 'An unexpected error occurred. Please try again.', 'error');
                });
            });

            function showMessage(title, message, type) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';

                messageContainer.innerHTML = `
                    <div class="alert ${alertClass} mt-3">
                        <i class="fas fa-${icon} me-2"></i>
                        <strong>${title}:</strong> ${message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
