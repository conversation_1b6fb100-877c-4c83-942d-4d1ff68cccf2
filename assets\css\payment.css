/**
 * LCI 2025 Dashboard - Payment Page Styles
 */

/* Payment container */
.lci-payment-container {
    width: 100%;
    margin: 0 auto 2rem;
    padding: 2rem;
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    animation: fadeInUp 0.8s ease-out forwards;
    transform-origin: center bottom;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.lci-section-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color, #36b1dc);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #f0f0f0;
    text-align: center;
}

/* Product summary */
.lci-product-summary,
.lci-cart-summary,
.lci-customer-info {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f9f9fa;
    border-radius: 0.5rem;
    border: 1px solid #eaeaea;
}

.lci-product-summary h3,
.lci-cart-summary h3,
.lci-customer-info h3,
.lci-payment-methods h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    color: #333;
}

/* Product details */
.lci-product-details {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.lci-product-image {
    flex: 0 0 100px;
}

.lci-product-image img {
    width: 100%;
    height: auto;
    border-radius: 0.25rem;
    border: 1px solid #eaeaea;
}

.lci-product-info {
    flex: 1;
}

.lci-product-info h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: #333;
}

.lci-product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color, #36b1dc);
    margin-bottom: 0.5rem;
}

.lci-product-description {
    font-size: 0.9rem;
    color: #666;
}

/* Cart items */
.lci-cart-items {
    border: 1px solid #eaeaea;
    border-radius: 0.25rem;
    overflow: hidden;
    background: #fff;
}

.lci-cart-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eaeaea;
}

.lci-cart-item:last-child {
    border-bottom: none;
}

.lci-cart-item-image {
    flex: 0 0 60px;
    margin-right: 1rem;
}

.lci-cart-item-image img {
    width: 100%;
    height: auto;
    border-radius: 0.25rem;
    border: 1px solid #eaeaea;
}

.lci-cart-item-details {
    flex: 1;
}

.lci-cart-item-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.lci-cart-item-quantity {
    font-size: 0.9rem;
    color: #666;
}

.lci-cart-item-price {
    font-weight: 600;
    color: var(--primary-color, #36b1dc);
    margin-left: 1rem;
}

.lci-cart-total {
    padding: 1rem;
    text-align: right;
    font-size: 1.1rem;
    border-top: 2px solid #eaeaea;
    background: #f9f9fa;
}

/* Customer information */
.lci-customer-info {
    position: relative;
    overflow: hidden;
    border: none;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9f7fc 100%);
    box-shadow: 0 10px 30px rgba(54, 177, 220, 0.1);
    transition: all 0.5s ease;
    animation: pulseGlow 3s infinite alternate;
}

.lci-customer-info:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(54, 177, 220, 0.2);
}

.lci-customer-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color, #36b1dc), #4fc3f7);
}

.lci-customer-info h3 {
    display: inline-block;
    position: relative;
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    color: #333;
}

.lci-customer-info h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--primary-color, #36b1dc);
    transition: width 0.3s ease;
}

.lci-customer-info:hover h3::after {
    width: 100%;
}

.lci-customer-details {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    animation: fadeIn 0.5s ease-out forwards;
}

.lci-customer-name,
.lci-customer-email,
.lci-customer-phone {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    border-radius: 0.5rem;
    border: 1px solid rgba(234, 234, 234, 0.5);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.lci-customer-name:hover,
.lci-customer-email:hover,
.lci-customer-phone:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.95);
}

@keyframes pulseGlow {
    0% {
        box-shadow: 0 10px 30px rgba(54, 177, 220, 0.1);
    }
    100% {
        box-shadow: 0 10px 30px rgba(54, 177, 220, 0.3);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Payment methods */
.lci-payment-methods {
    margin-bottom: 2rem;
    animation: slideInRight 0.8s ease-out forwards;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.lci-payment-methods h3 {
    display: inline-block;
    position: relative;
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    color: #333;
}

.lci-payment-methods h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--primary-color, #36b1dc);
    transition: width 0.3s ease;
}

.lci-payment-methods:hover h3::after {
    width: 100%;
}

.lci-payment-method {
    margin-bottom: 1rem;
    padding: 1.25rem;
    border: 1px solid #eaeaea;
    border-radius: 0.75rem;
    background: #fff;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.lci-payment-method::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color, #36b1dc), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lci-payment-method:hover {
    border-color: var(--primary-color, #36b1dc);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transform: translateY(-3px);
}

.lci-payment-method:hover::before {
    opacity: 1;
}

.lci-payment-method-header {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 0.5rem;
}

.lci-payment-method input[type="radio"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
    accent-color: var(--primary-color, #36b1dc);
    vertical-align: middle;
    flex-shrink: 0;
}

.lci-payment-method-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
    margin-bottom: 0;
}

.lci-payment-method-title {
    font-weight: 600;
    flex: 1;
    font-size: 1.1rem;
    display: inline-block;
    vertical-align: middle;
    margin-right: 0.5rem;
}

.lci-payment-method-icon {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.lci-payment-method:hover .lci-payment-method-icon {
    transform: scale(1.05);
}

.lci-payment-method-description {
    width: 100%;
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    padding-left: 2.5rem; /* Align with the text, not the radio button */
    border-top: 1px solid #f0f0f0;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.6;
    animation: fadeIn 0.5s ease-out forwards;
}

.lci-payment-method-fields {
    margin-top: 1rem;
    padding-top: 1rem;
    padding-left: 2.5rem; /* Align with the text, not the radio button */
    border-top: 1px solid #f0f0f0;
    animation: fadeIn 0.5s ease-out forwards;
}

/* Payment actions */
.lci-payment-actions {
    margin-top: 2rem;
    text-align: center;
    animation: fadeInUp 0.8s ease-out 0.3s forwards;
    opacity: 0;
}

.lci-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    text-decoration: none;
    border: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.lci-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
    transition: all 0.6s ease;
    z-index: -1;
}

.lci-btn:hover::before {
    left: 100%;
}

.lci-btn-primary {
    background-color: var(--primary-color, #36b1dc);
    color: white;
    box-shadow: 0 4px 15px rgba(54, 177, 220, 0.3);
}

.lci-btn-primary:hover {
    background-color: #2a8fb3;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(54, 177, 220, 0.4);
}

.lci-btn-primary:active {
    transform: translateY(1px);
    box-shadow: 0 2px 10px rgba(54, 177, 220, 0.3);
}

.lci-btn-secondary {
    background-color: #f0f0f0;
    color: #333;
}

.lci-btn-secondary:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.lci-payment-button {
    min-width: 250px;
    padding: 1rem 2.5rem;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.lci-payment-button i {
    margin-right: 10px;
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.lci-payment-button:hover i {
    transform: rotate(10deg);
}

/* Button pulse effect */
.lci-payment-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.lci-payment-button:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.3;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

/* Notices */
.lci-notice {
    padding: 1rem 1.25rem;
    border-radius: 0.25rem;
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
}

.lci-notice p:last-child {
    margin-bottom: 0;
}

.lci-notice-error {
    background-color: #ffebee;
    color: #c62828;
    border-left: 4px solid #c62828;
}

.lci-notice-warning {
    background-color: #fff8e1;
    color: #ff8f00;
    border-left: 4px solid #ff8f00;
}

.lci-notice-success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 4px solid #2e7d32;
}

/* Responsive styles */
@media (max-width: 768px) {
    .lci-payment-container {
        padding: 1.5rem;
    }

    .lci-product-details {
        flex-direction: column;
    }

    .lci-product-image {
        margin-bottom: 1rem;
    }

    .lci-customer-details {
        grid-template-columns: 1fr;
    }
}

/* WooCommerce payment fields overrides */
.woocommerce-checkout #payment div.payment_box {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-top: 0.5rem;
}

.woocommerce-checkout #payment div.payment_box::before {
    display: none;
}

.woocommerce-checkout #payment ul.payment_methods {
    border: none;
    padding: 0;
}

.woocommerce-checkout #payment ul.payment_methods li {
    line-height: 1.5;
}

/* Form fields */
.lci-payment-method-fields input[type="text"],
.lci-payment-method-fields select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.lci-payment-method-fields label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

/* Credit card fields */
.wc-credit-card-form {
    border: none !important;
    padding: 0 !important;
}

.wc-credit-card-form-card-number,
.wc-credit-card-form-card-expiry,
.wc-credit-card-form-card-cvc {
    font-size: 1rem !important;
    padding: 0.75rem !important;
}

/* Loading spinner */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
