<?php
/**
 * LCI 2025 Dashboard Accommodations Handler
 *
 * Manages accommodation-related operations for the LCI 2025 Dashboard plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class LCI_Accommodations {
    // Accommodation category IDs
    const MAIN_EVENT_CATEGORY_ID = 1;
    const COUNCILOR_PACK_CATEGORY_ID = 20;
    const ADDITIONAL_NIGHT_AFTER_PRETOUR_CATEGORY_ID = 30;
    const ADDITIONAL_NIGHT_BUCHAREST_CATEGORY_ID = 37;

    // All accommodation category IDs
    const ACCOMMODATION_CATEGORY_IDS = [
        self::MAIN_EVENT_CATEGORY_ID,
        self::COUNCILOR_PACK_CATEGORY_ID,
        self::ADDITIONAL_NIGHT_AFTER_PRETOUR_CATEGORY_ID,
        self::ADDITIONAL_NIGHT_BUCHAREST_CATEGORY_ID
    ];

    // Product IDs for each category
    const PRODUCT_IDS = [
        // Main Event Accommodation products
        137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160,
        // Councilor Pack Accommodation products
        161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180,
        // Additional Night After Pretour products
        181, 182, 183, 184, 185, 186, 187, 188, 189, 190,
        // Additional Night Bucharest products
        191, 192, 193, 194, 195, 196, 197, 198, 199, 200
    ];

    // Map product IDs to categories (defined as a static property since PHP constants can't be arrays with string keys)
    public static $PRODUCT_CATEGORY_MAP = array(
        // Main Event Accommodation
        '137' => self::MAIN_EVENT_CATEGORY_ID,
        '138' => self::MAIN_EVENT_CATEGORY_ID,
        // Add more mappings as needed
    );

    /**
     * Initialize the accommodations handler
     */
    public static function init() {
        // Add AJAX handlers
        add_action('wp_ajax_lci_get_accommodation_categories', [__CLASS__, 'ajax_get_accommodation_categories']);
        add_action('wp_ajax_lci_get_accommodation_data', [__CLASS__, 'ajax_get_accommodation_data']);
        add_action('wp_ajax_lci_sync_accommodation_data', [__CLASS__, 'ajax_sync_accommodation_data']);
        add_action('wp_ajax_lci_update_hotel_inventory', [__CLASS__, 'ajax_update_hotel_inventory']);
        add_action('wp_ajax_lci_get_hotel_inventory', [__CLASS__, 'ajax_get_hotel_inventory']);

        // Backward compatibility for old AJAX action
        add_action('wp_ajax_lci_get_participant_accommodations', function() {
            error_log('Using deprecated AJAX action: lci_get_participant_accommodations');
            self::ajax_get_accommodation_data();
        });
    }

    /**
     * Get accommodation categories with names
     *
     * @return array Array of accommodation categories
     */
    public static function get_accommodation_categories() {
        $categories = [];

        $category_names = [
            self::MAIN_EVENT_CATEGORY_ID => 'Main Event Accommodation',
            self::COUNCILOR_PACK_CATEGORY_ID => 'Councilor Pack Accommodation',
            self::ADDITIONAL_NIGHT_AFTER_PRETOUR_CATEGORY_ID => 'Additional Night After Pretour',
            self::ADDITIONAL_NIGHT_BUCHAREST_CATEGORY_ID => 'Additional Night Bucharest'
        ];

        error_log('Getting accommodation categories. Category IDs: ' . implode(', ', self::ACCOMMODATION_CATEGORY_IDS));

        foreach (self::ACCOMMODATION_CATEGORY_IDS as $category_id) {
            error_log('Processing category ID: ' . $category_id);
            $category = get_term($category_id, 'product_cat');

            if ($category && !is_wp_error($category)) {
                error_log('Category found: ' . $category->name);
                $categories[] = [
                    'id' => $category->term_id,
                    'name' => $category_names[$category_id] ?? $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'count' => $category->count
                ];
            } else {
                error_log('Category not found or error: ' . $category_id);
                // Add the category anyway with default values
                $categories[] = [
                    'id' => $category_id,
                    'name' => $category_names[$category_id] ?? 'Category ' . $category_id,
                    'slug' => 'category-' . $category_id,
                    'description' => '',
                    'count' => 0
                ];
            }
        }

        error_log('Returning ' . count($categories) . ' categories');
        return $categories;
    }

    /**
     * Get accommodation data for a specific category
     *
     * @param int $category_id Category ID
     * @param bool $from_woocommerce Whether to get data from WooCommerce instead of database
     * @return array Accommodation data
     */
    public static function get_accommodation_data($category_id = null, $from_woocommerce = false) {
        global $wpdb;

        // Debug information
        error_log('Getting accommodation data for category: ' . ($category_id ? $category_id : 'all') . ', from_woocommerce: ' . ($from_woocommerce ? 'true' : 'false'));

        // If getting from WooCommerce, bypass database
        if ($from_woocommerce) {
            return self::get_accommodation_data_from_woocommerce($category_id);
        }

        $table_name = $wpdb->prefix . 'lci2025_accommodations';

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            error_log('Accommodations table does not exist');
            // If table doesn't exist, get data from WooCommerce instead
            return self::get_accommodation_data_from_woocommerce($category_id);
        }

        // Build query
        $query = "SELECT * FROM $table_name";
        $where_clauses = [];
        $query_args = [];

        // Filter by category if provided
        if ($category_id) {
            $where_clauses[] = "category_id = %d";
            $query_args[] = $category_id;
        }

        // Add WHERE clause if needed
        if (!empty($where_clauses)) {
            $query .= " WHERE " . implode(" AND ", $where_clauses);
        }

        // Add ORDER BY
        $query .= " ORDER BY hotel_name ASC, room_type_name ASC, order_date DESC";

        // Prepare and execute query
        if (!empty($query_args)) {
            $query = $wpdb->prepare($query, $query_args);
        }

        error_log('Executing query: ' . $query);
        $accommodations = $wpdb->get_results($query);
        error_log('Found ' . count($accommodations) . ' accommodations in database');

        // If no accommodations found in database, get from WooCommerce
        if (empty($accommodations)) {
            error_log('No accommodations found in database, getting from WooCommerce');
            return self::get_accommodation_data_from_woocommerce($category_id);
        }

        // Extract unique hotels and room types
        $hotels = [];
        $room_types = [];

        foreach ($accommodations as $accommodation) {
            // Add hotel if not already in the list
            if (!isset($hotels[$accommodation->hotel_id])) {
                $hotels[$accommodation->hotel_id] = [
                    'id' => $accommodation->hotel_id,
                    'name' => $accommodation->hotel_name,
                    'category_id' => $accommodation->category_id
                ];
            }

            // Add room type if not already in the list
            $room_type_key = $accommodation->hotel_id . '_' . $accommodation->room_type_id;
            if (!isset($room_types[$room_type_key]) && $accommodation->room_type_id) {
                $room_types[$room_type_key] = [
                    'id' => $accommodation->room_type_id,
                    'name' => $accommodation->room_type_name,
                    'hotel_id' => $accommodation->hotel_id
                ];
            }
        }

        return [
            'accommodations' => $accommodations,
            'hotels' => array_values($hotels),
            'room_types' => array_values($room_types),
            'source' => 'database',
            'table_exists' => true
        ];
    }

    /**
     * Get accommodation data from WooCommerce
     *
     * @param int $category_id Optional category ID to filter by
     * @return array Accommodation data from WooCommerce
     */
    public static function get_accommodation_data_from_woocommerce($category_id = null) {
        global $wpdb;
        $participants_table = $wpdb->prefix . 'lci2025_participants';

        // Debug information
        error_log('Getting accommodation data from WooCommerce for category: ' . ($category_id ? $category_id : 'all'));

        $accommodations = [];
        $hotels = [];
        $room_types = [];

        // Get all orders with accommodation products
        $args = [
            'limit' => -1,
            'status' => ['processing', 'completed', 'on-hold'],
            'return' => 'ids',
            'orderby' => 'date',
            'order' => 'DESC',
        ];

        // If we have a category ID, filter by products in that category
        if ($category_id) {
            // Get all products in this category
            $args['category'] = [$category_id];
        }

        $orders = wc_get_orders($args);
        error_log('Found ' . count($orders) . ' orders');

        foreach ($orders as $order_id) {
            $order = wc_get_order($order_id);
            $email = $order->get_billing_email();

            // Check if this participant exists in the main participants table
            $participant_data = null;
            if ($email) {
                $participant_data = $wpdb->get_row($wpdb->prepare(
                    "SELECT id, unique_reg_id FROM $participants_table WHERE email = %s LIMIT 1",
                    $email
                ));
            }

            // Process order items
            foreach ($order->get_items() as $item) {
                $product_id = $item->get_product_id();
                $variation_id = $item->get_variation_id();
                $product = wc_get_product($product_id);

                if (!$product) {
                    continue;
                }

                // Check if this is an accommodation product
                $product_categories = wc_get_product_term_ids($product_id, 'product_cat');
                $is_accommodation = false;
                $category_id_found = 0;

                // Check if product is in one of our accommodation categories
                foreach ($product_categories as $cat_id) {
                    if (in_array($cat_id, self::ACCOMMODATION_CATEGORY_IDS)) {
                        $is_accommodation = true;
                        $category_id_found = $cat_id;
                        break;
                    }
                }

                // Skip if not an accommodation product or not in the requested category
                if (!$is_accommodation || ($category_id && $category_id_found != $category_id)) {
                    continue;
                }

                // Get category name
                $category_name = '';
                switch ($category_id_found) {
                    case self::MAIN_EVENT_CATEGORY_ID:
                        $category_name = 'Main Event Accommodation';
                        break;
                    case self::COUNCILOR_PACK_CATEGORY_ID:
                        $category_name = 'Councilor Pack Accommodation';
                        break;
                    case self::ADDITIONAL_NIGHT_AFTER_PRETOUR_CATEGORY_ID:
                        $category_name = 'Additional Night After Pretour';
                        break;
                    case self::ADDITIONAL_NIGHT_BUCHAREST_CATEGORY_ID:
                        $category_name = 'Additional Night Bucharest';
                        break;
                    default:
                        $category = get_term($category_id_found, 'product_cat');
                        $category_name = $category ? $category->name : 'Unknown Category';
                }

                // Get hotel (product) name
                $hotel_name = $product->get_name();

                // Get room type (variation) name
                $room_type_name = '';
                if ($variation_id) {
                    $variation = wc_get_product($variation_id);
                    if ($variation) {
                        $attributes = $variation->get_variation_attributes();
                        $room_type_name = implode(', ', $attributes);

                        // Clean up the room type name
                        $room_type_name = str_replace('attribute_', '', $room_type_name);
                        $room_type_name = str_replace('pa_', '', $room_type_name);
                        $room_type_name = str_replace('-', ' ', $room_type_name);
                        $room_type_name = ucwords($room_type_name);
                    }
                }

                // Add hotel to the list if not already there
                if (!isset($hotels[$product_id])) {
                    $hotels[$product_id] = [
                        'id' => $product_id,
                        'name' => $hotel_name,
                        'category_id' => $category_id_found
                    ];
                }

                // Add room type to the list if not already there
                $room_type_key = $product_id . '_' . $variation_id;
                if (!isset($room_types[$room_type_key]) && $variation_id) {
                    $room_types[$room_type_key] = [
                        'id' => $variation_id,
                        'name' => $room_type_name,
                        'hotel_id' => $product_id
                    ];
                }

                // Create accommodation record
                $accommodation = [
                    'id' => 0, // Temporary ID
                    'participant_id' => $participant_data ? $participant_data->id : 0,
                    'unique_reg_id' => $participant_data ? $participant_data->unique_reg_id : '',
                    'category_id' => $category_id_found,
                    'category_name' => $category_name,
                    'hotel_id' => $product_id,
                    'hotel_name' => $hotel_name,
                    'room_type_id' => $variation_id,
                    'room_type_name' => $room_type_name,
                    'order_id' => $order_id,
                    'order_date' => $order->get_date_created() ? $order->get_date_created()->date('Y-m-d H:i:s') : '',
                    'payment_status' => $order->get_status(),
                    'created_at' => '',
                    'updated_at' => '',
                    'from_woocommerce' => true,
                    // Add participant details for display
                    'first_name' => $order->get_billing_first_name(),
                    'last_name' => $order->get_billing_last_name(),
                    'email' => $email
                ];

                $accommodations[] = (object) $accommodation;
            }
        }

        error_log('Found ' . count($accommodations) . ' accommodations');

        return [
            'accommodations' => $accommodations,
            'hotels' => array_values($hotels),
            'room_types' => array_values($room_types),
            'source' => 'woocommerce',
            'table_exists' => true
        ];
    }

    /**
     * Sync accommodation data from WooCommerce to database
     *
     * @param int $category_id Optional category ID to sync
     * @return array Result with success_count, error_count, and total_count
     */
    public static function sync_accommodation_data($category_id = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'lci2025_accommodations';

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            // Create tables
            self::create_tables();
        }

        // Get accommodation data from WooCommerce
        $data = self::get_accommodation_data_from_woocommerce($category_id);
        $accommodations = $data['accommodations'];

        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'total_count' => count($accommodations),
            'errors' => []
        ];

        // Clear the table if syncing all categories
        if ($category_id === null) {
            $wpdb->query("TRUNCATE TABLE $table_name");
        } else {
            // Delete only records for this category
            $wpdb->delete($table_name, ['category_id' => $category_id]);
        }

        foreach ($accommodations as $accommodation) {
            // Insert accommodation into table
            $insert_data = [
                'participant_id' => $accommodation->participant_id,
                'unique_reg_id' => $accommodation->unique_reg_id,
                'category_id' => $accommodation->category_id,
                'category_name' => $accommodation->category_name,
                'hotel_id' => $accommodation->hotel_id,
                'hotel_name' => $accommodation->hotel_name,
                'room_type_id' => $accommodation->room_type_id,
                'room_type_name' => $accommodation->room_type_name,
                'order_id' => $accommodation->order_id,
                'order_date' => $accommodation->order_date,
                'payment_status' => $accommodation->payment_status,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ];

            $insert_result = $wpdb->insert($table_name, $insert_data);

            if ($insert_result) {
                $result['success_count']++;
            } else {
                $result['error_count']++;
                $result['errors'][] = "Failed to insert accommodation for order {$accommodation->order_id}: {$wpdb->last_error}";
            }
        }

        // Update hotel inventory counts
        self::update_hotel_inventory_counts();

        return $result;
    }

    /**
     * Get hotel inventory
     *
     * @param int $category_id Optional category ID to filter by
     * @return array Hotel inventory data
     */
    public static function get_hotel_inventory($category_id = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'lci2025_hotel_inventory';

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            return [];
        }

        // Build query
        $query = "SELECT * FROM $table_name";
        $where_clauses = [];
        $query_args = [];

        // Filter by category if provided
        if ($category_id) {
            $where_clauses[] = "category_id = %d";
            $query_args[] = $category_id;
        }

        // Add WHERE clause if needed
        if (!empty($where_clauses)) {
            $query .= " WHERE " . implode(" AND ", $where_clauses);
        }

        // Add ORDER BY
        $query .= " ORDER BY hotel_name ASC, room_type_name ASC";

        // Prepare and execute query
        if (!empty($query_args)) {
            $query = $wpdb->prepare($query, $query_args);
        }

        return $wpdb->get_results($query);
    }

    /**
     * Update hotel inventory
     *
     * @param array $inventory Inventory data to update
     * @return array Result with success_count and error_count
     */
    public static function update_hotel_inventory($inventory) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'lci2025_hotel_inventory';

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
            // Create tables
            self::create_tables();
        }

        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'total_count' => count($inventory),
            'errors' => []
        ];

        foreach ($inventory as $item) {
            $data = [
                'hotel_id' => $item['hotel_id'],
                'hotel_name' => $item['hotel_name'],
                'room_type_id' => $item['room_type_id'],
                'room_type_name' => $item['room_type_name'],
                'category_id' => $item['category_id'],
                'total_rooms' => $item['total_rooms'],
                'updated_at' => current_time('mysql')
            ];

            // Check if record exists
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE hotel_id = %d AND room_type_id = %d",
                $item['hotel_id'],
                $item['room_type_id']
            ));

            if ($exists) {
                // Update existing record
                $update_result = $wpdb->update(
                    $table_name,
                    $data,
                    [
                        'hotel_id' => $item['hotel_id'],
                        'room_type_id' => $item['room_type_id']
                    ]
                );

                if ($update_result !== false) {
                    $result['success_count']++;
                } else {
                    $result['error_count']++;
                    $result['errors'][] = "Failed to update inventory for hotel {$item['hotel_name']}, room type {$item['room_type_name']}: {$wpdb->last_error}";
                }
            } else {
                // Insert new record
                $data['created_at'] = current_time('mysql');
                $data['allocated_rooms'] = 0; // Initialize allocated rooms to 0

                $insert_result = $wpdb->insert($table_name, $data);

                if ($insert_result) {
                    $result['success_count']++;
                } else {
                    $result['error_count']++;
                    $result['errors'][] = "Failed to insert inventory for hotel {$item['hotel_name']}, room type {$item['room_type_name']}: {$wpdb->last_error}";
                }
            }
        }

        return $result;
    }

    /**
     * Update hotel inventory counts based on accommodation data
     *
     * @return array Result with success_count and error_count
     */
    public static function update_hotel_inventory_counts() {
        global $wpdb;

        $accommodations_table = $wpdb->prefix . 'lci2025_accommodations';
        $inventory_table = $wpdb->prefix . 'lci2025_hotel_inventory';

        // Check if tables exist
        if ($wpdb->get_var("SHOW TABLES LIKE '$accommodations_table'") !== $accommodations_table ||
            $wpdb->get_var("SHOW TABLES LIKE '$inventory_table'") !== $inventory_table) {
            return [
                'success_count' => 0,
                'error_count' => 0,
                'message' => 'Tables do not exist'
            ];
        }

        // Reset all allocated_rooms to 0
        $wpdb->query("UPDATE $inventory_table SET allocated_rooms = 0");

        // Get counts from accommodations table
        $counts = $wpdb->get_results("
            SELECT hotel_id, room_type_id, COUNT(*) as count
            FROM $accommodations_table
            WHERE room_type_id IS NOT NULL
            GROUP BY hotel_id, room_type_id
        ");

        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'total_count' => count($counts),
            'errors' => []
        ];

        foreach ($counts as $count) {
            // Update inventory count
            $update_result = $wpdb->update(
                $inventory_table,
                ['allocated_rooms' => $count->count],
                [
                    'hotel_id' => $count->hotel_id,
                    'room_type_id' => $count->room_type_id
                ]
            );

            if ($update_result !== false) {
                $result['success_count']++;
            } else {
                $result['error_count']++;
                $result['errors'][] = "Failed to update allocated rooms for hotel ID {$count->hotel_id}, room type ID {$count->room_type_id}: {$wpdb->last_error}";
            }
        }

        return $result;
    }

    /**
     * Get accommodation statistics
     *
     * @return array Statistics about accommodations
     */
    public static function get_accommodation_statistics() {
        global $wpdb;

        $accommodations_table = $wpdb->prefix . 'lci2025_accommodations';
        $inventory_table = $wpdb->prefix . 'lci2025_hotel_inventory';

        // Check if tables exist
        if ($wpdb->get_var("SHOW TABLES LIKE '$accommodations_table'") !== $accommodations_table) {
            return [
                'total' => 0,
                'by_category' => [],
                'by_hotel' => [],
                'by_room_type' => [],
                'inventory' => []
            ];
        }

        // Get total count
        $total = $wpdb->get_var("SELECT COUNT(*) FROM $accommodations_table");

        // Get counts by category
        $by_category = $wpdb->get_results("
            SELECT category_id, category_name, COUNT(*) as count
            FROM $accommodations_table
            GROUP BY category_id
            ORDER BY count DESC
        ");

        // Get counts by hotel
        $by_hotel = $wpdb->get_results("
            SELECT hotel_id, hotel_name, category_id, COUNT(*) as count
            FROM $accommodations_table
            GROUP BY hotel_id
            ORDER BY count DESC
        ");

        // Get counts by room type
        $by_room_type = $wpdb->get_results("
            SELECT hotel_id, hotel_name, room_type_id, room_type_name, COUNT(*) as count
            FROM $accommodations_table
            WHERE room_type_id IS NOT NULL
            GROUP BY hotel_id, room_type_id
            ORDER BY count DESC
        ");

        // Get inventory data if available
        $inventory = [];
        if ($wpdb->get_var("SHOW TABLES LIKE '$inventory_table'") === $inventory_table) {
            $inventory = $wpdb->get_results("
                SELECT *
                FROM $inventory_table
                ORDER BY hotel_name ASC, room_type_name ASC
            ");
        }

        return [
            'total' => $total,
            'by_category' => $by_category,
            'by_hotel' => $by_hotel,
            'by_room_type' => $by_room_type,
            'inventory' => $inventory
        ];
    }

    /**
     * AJAX: Get accommodation categories
     */
    public static function ajax_get_accommodation_categories() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get categories
        $categories = self::get_accommodation_categories();

        wp_send_json_success([
            'categories' => $categories
        ]);
    }

    /**
     * AJAX: Get accommodation data
     */
    public static function ajax_get_accommodation_data() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get category ID if provided
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : null;

        // Check if we should force WooCommerce data
        $from_woocommerce = isset($_GET['from_woocommerce']) && $_GET['from_woocommerce'] === 'true';

        error_log('AJAX: Getting accommodation data for category: ' . ($category_id ? $category_id : 'all') . ', from_woocommerce: ' . ($from_woocommerce ? 'true' : 'false'));

        // Get accommodation data
        $data = self::get_accommodation_data($category_id, $from_woocommerce);

        // Get statistics
        $statistics = self::get_accommodation_statistics();

        error_log('AJAX: Returning ' . count($data['accommodations']) . ' accommodations');

        // Add debug information
        $debug_info = [
            'request' => $_GET,
            'category_id' => $category_id,
            'from_woocommerce' => $from_woocommerce,
            'accommodation_count' => count($data['accommodations']),
            'hotel_count' => count($data['hotels']),
            'room_type_count' => count($data['room_types']),
            'source' => $data['source'],
            'table_exists' => $data['table_exists']
        ];

        wp_send_json_success([
            'accommodations' => $data['accommodations'],
            'hotels' => $data['hotels'],
            'room_types' => $data['room_types'],
            'source' => $data['source'],
            'table_exists' => $data['table_exists'],
            'statistics' => $statistics,
            'debug' => $debug_info
        ]);
    }

    /**
     * AJAX: Sync accommodation data
     */
    public static function ajax_sync_accommodation_data() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get category ID if provided
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : null;

        // Sync accommodation data
        $result = self::sync_accommodation_data($category_id);

        if ($result['error_count'] > 0) {
            $message = sprintf(
                'Sync completed with some errors. %d accommodations synced successfully, %d errors.',
                $result['success_count'],
                $result['error_count']
            );
        } else {
            $message = sprintf(
                'Sync completed successfully. %d accommodations synced.',
                $result['success_count']
            );
        }

        wp_send_json_success([
            'message' => $message,
            'result' => $result
        ]);
    }

    /**
     * AJAX: Update hotel inventory
     */
    public static function ajax_update_hotel_inventory() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get inventory data
        $inventory = isset($_POST['inventory']) ? json_decode(stripslashes($_POST['inventory']), true) : [];

        if (empty($inventory)) {
            wp_send_json_error(['message' => 'No inventory data provided.']);
        }

        // Update inventory
        $result = self::update_hotel_inventory($inventory);

        if ($result['error_count'] > 0) {
            $message = sprintf(
                'Inventory update completed with some errors. %d items updated successfully, %d errors.',
                $result['success_count'],
                $result['error_count']
            );
        } else {
            $message = sprintf(
                'Inventory update completed successfully. %d items updated.',
                $result['success_count']
            );
        }

        wp_send_json_success([
            'message' => $message,
            'result' => $result
        ]);
    }

    /**
     * AJAX: Get hotel inventory
     */
    public static function ajax_get_hotel_inventory() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'lci_admin_nonce')) {
            wp_send_json_error(['message' => 'Invalid security token.']);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
        }

        // Get category ID if provided
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : null;

        // Get inventory data
        $inventory = self::get_hotel_inventory($category_id);

        wp_send_json_success([
            'inventory' => $inventory
        ]);
    }
}
