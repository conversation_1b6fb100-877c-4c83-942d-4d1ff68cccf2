/**
 * Revolut Live Payment Handler
 */
(function($) {
    'use strict';
    
    // Payment popup window reference
    let paymentPopup = null;
    
    // Payment check interval
    let checkInterval = null;
    
    // Initialize Revolut live payment
    function initRevolutLive() {
        // Handle payment button click
        $('.lci-revolut-live-button').on('click', function(e) {
            e.preventDefault();
            
            const button = $(this);
            const messageContainer = $('.lci-payment-messages');
            
            // Disable button and show loading message
            button.prop('disabled', true).text(lciRevolutLive.i18n.processingPayment);
            messageContainer.html('<div class="lci-notice lci-notice-info"><p>' + lciRevolutLive.i18n.processingPayment + '</p></div>');
            
            // Create Revolut session
            $.ajax({
                url: lciRevolutLive.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lci_create_revolut_session',
                    security: lciRevolutLive.nonce,
                    amount: button.data('amount') || 0,
                    description: button.data('description') || 'Payment',
                    product_id: button.data('product-id') || '',
                    quantity: button.data('quantity') || 1
                },
                success: function(response) {
                    if (response.success && response.data.token) {
                        console.log('Revolut session created successfully', response.data);
                        
                        // Open Revolut popup
                        openRevolutPopup(response.data.token, response.data.order_id);
                    } else {
                        // Show error message
                        console.error('Error creating Revolut session', response.data);
                        messageContainer.html('<div class="lci-notice lci-notice-error"><p>' + (response.data.message || lciRevolutLive.i18n.paymentFailed) + '</p></div>');
                        button.prop('disabled', false).text('Pay Now');
                    }
                },
                error: function(xhr, status, error) {
                    // Show error message
                    console.error('AJAX error', error);
                    messageContainer.html('<div class="lci-notice lci-notice-error"><p>' + lciRevolutLive.i18n.paymentFailed + '</p></div>');
                    button.prop('disabled', false).text('Pay Now');
                }
            });
        });
    }
    
    // Open Revolut popup
    function openRevolutPopup(token, orderId) {
        // Build popup URL - using production environment
        const popupUrl = 'https://merchant.revolut.com/card-popup.html?token=' + token;
        
        // Log for debugging
        console.log('Opening Revolut popup with URL:', popupUrl);
        
        // Open popup with larger dimensions for better user experience
        paymentPopup = window.open(popupUrl, 'revolut_payment', 'width=700,height=700,location=yes,resizable=yes,scrollbars=yes,top=100,left=100');
        
        // Check if popup was blocked
        if (!paymentPopup || paymentPopup.closed || typeof paymentPopup.closed === 'undefined') {
            $('.lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p>' + lciRevolutLive.i18n.popupBlocked + '</p></div>');
            $('.lci-revolut-live-button').prop('disabled', false).text('Pay Now');
            return;
        }
        
        // Start checking if popup is closed
        checkInterval = setInterval(function() {
            if (paymentPopup.closed) {
                clearInterval(checkInterval);
                
                // Show processing message
                $('.lci-payment-messages').html('<div class="lci-notice lci-notice-info"><p>' + lciRevolutLive.i18n.paymentCompleted + '</p></div>');
                
                // Check payment status
                checkPaymentStatus(orderId);
            }
        }, 500);
    }
    
    // Check payment status
    function checkPaymentStatus(orderId, attemptCount = 0) {
        // Maximum number of attempts
        const maxAttempts = 5;
        
        // Delay between attempts (in milliseconds)
        const retryDelay = 2000;
        
        $.ajax({
            url: lciRevolutLive.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lci_check_revolut_payment_status',
                security: lciRevolutLive.nonce,
                order_id: orderId
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.status === 'completed') {
                        // Payment successful
                        $('.lci-payment-messages').html('<div class="lci-notice lci-notice-success"><p>' + lciRevolutLive.i18n.paymentSuccess + '</p></div>');
                        
                        // Redirect to success page if provided
                        if (response.data.redirect_url) {
                            setTimeout(function() {
                                window.location.href = response.data.redirect_url;
                            }, 1500);
                        } else {
                            // Reload the page
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        }
                    } else if (response.data.status === 'failed') {
                        // Payment failed
                        $('.lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p>' + lciRevolutLive.i18n.paymentFailed + '</p></div>');
                        $('.lci-revolut-live-button').prop('disabled', false).text('Pay Now');
                    } else {
                        // Payment pending or processing
                        
                        // If we haven't reached the maximum number of attempts, try again
                        if (attemptCount < maxAttempts) {
                            console.log('Payment still pending, retrying... (Attempt ' + (attemptCount + 1) + ' of ' + maxAttempts + ')');
                            
                            // Update message to show retry count
                            $('.lci-payment-messages').html('<div class="lci-notice lci-notice-info"><p>Processing payment, please wait... (Check ' + (attemptCount + 1) + ' of ' + maxAttempts + ')</p></div>');
                            
                            // Try again after delay
                            setTimeout(function() {
                                checkPaymentStatus(orderId, attemptCount + 1);
                            }, retryDelay);
                        } else {
                            // We've reached the maximum number of attempts
                            console.log('Maximum payment check attempts reached');
                            $('.lci-payment-messages').html('<div class="lci-notice lci-notice-info"><p>Your payment is being processed. Please check your order status in a few moments.</p></div>');
                            $('.lci-revolut-live-button').prop('disabled', false).text('Pay Now');
                        }
                    }
                } else {
                    // Error checking payment status
                    $('.lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p>' + (response.data.message || lciRevolutLive.i18n.paymentFailed) + '</p></div>');
                    $('.lci-revolut-live-button').prop('disabled', false).text('Pay Now');
                }
            },
            error: function(xhr, status, error) {
                // Error checking payment status
                if (attemptCount < maxAttempts) {
                    // Try again after delay
                    console.log('Error checking payment status, retrying... (Attempt ' + (attemptCount + 1) + ' of ' + maxAttempts + ')');
                    setTimeout(function() {
                        checkPaymentStatus(orderId, attemptCount + 1);
                    }, retryDelay);
                } else {
                    // We've reached the maximum number of attempts
                    $('.lci-payment-messages').html('<div class="lci-notice lci-notice-error"><p>' + lciRevolutLive.i18n.paymentFailed + '</p></div>');
                    $('.lci-revolut-live-button').prop('disabled', false).text('Pay Now');
                }
            }
        });
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        initRevolutLive();
    });
})(jQuery);