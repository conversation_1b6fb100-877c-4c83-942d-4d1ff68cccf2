/**
 * Accommodation View Styles
 */

/* Container */
.accommodation-container {
    padding: 0;
}

/* Modern Section Headings - 2025 UX/UI Trends */
.accommodation-section-heading {
    position: relative;
    text-align: center;
    padding: 1.5rem 0;
    margin: 2rem 0;
}

.accommodation-section-heading h3 {
    font-size: 1.8rem;
    font-weight: 800;
    letter-spacing: 1.5px;
    color: #333;
    margin: 0;
    position: relative;
    display: inline-block;
    padding: 0 1rem;
    background: linear-gradient(to right, transparent, #fff, transparent);
}

.accommodation-section-heading .heading-underline {
    position: relative;
    height: 4px;
    width: 80px;
    background: linear-gradient(90deg, var(--primary-color), #fab33a);
    margin: 0.8rem auto 0;
    border-radius: 2px;
    transition: width 0.6s ease;
}

/* Wow Effect Animation */
.wow-heading {
    opacity: 0;
    animation: fadeInUp 0.8s ease forwards;
    animation-delay: 0.2s;
}

.wow-heading:hover .heading-underline {
    width: 120px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* CTA Section */
.accommodation-cta-container {
    background-color: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.accommodation-cta-content {
    padding: 2rem;
    width: 100%;
    text-align: center;
}

.accommodation-cta-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #36b1dc;
    text-align: center;
}

.accommodation-cta-text {
    font-size: 1rem;
    line-height: 1.6;
    color: #555;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

/* Products Grid */
.accommodation-products-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (min-width: 768px) {
    .accommodation-products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .accommodation-products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Product Card */
.accommodation-product-card {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 0.75rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    cursor: pointer;
}

.accommodation-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #36b1dc, #2d93b7);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.accommodation-product-card:hover {
    transform: scale(1.03);
    box-shadow: 0 12px 24px rgba(54, 177, 220, 0.2);
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.accommodation-product-card:hover::before {
    opacity: 1;
}

/* Product Image */
.accommodation-product-image {
    position: relative;
    padding-bottom: 60%; /* 3:5 Aspect Ratio */
    overflow: hidden;
}

.accommodation-product-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    padding: 0;
    transition: transform 0.3s ease;
}

.accommodation-product-card:hover .accommodation-product-image img {
    transform: scale(1.05);
}

/* Badges */
.accommodation-badges-left {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    z-index: 10;
}

.accommodation-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.85rem;
    border-radius: 30px;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    color: white;
    margin-bottom: 6px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    letter-spacing: 0.3px;
    text-transform: uppercase;
}

.accommodation-badge i {
    margin-right: 5px;
    font-size: 0.8rem;
}

.accommodation-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.accommodation-badge-sale {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.accommodation-badge-featured {
    background: linear-gradient(135deg, #36b1dc 0%, #2a8fb3 100%);
}

/* Product Content */
.accommodation-product-content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    position: relative;
}

.accommodation-product-title {
    font-weight: 600;
    color: #1a202c;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    line-height: 1.5;
    position: relative;
    padding-bottom: 0.75rem;
}

.accommodation-product-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background-color: #36b1dc;
    transition: width 0.3s ease;
}

.accommodation-product-card:hover .accommodation-product-title::after {
    width: 80px;
}

.accommodation-product-description {
    color: #666;
    margin-bottom: 1rem;
    flex-grow: 1;
    font-size: 0.95rem;
    line-height: 1.5;
    text-align: justify;
}

.accommodation-product-variants {
    font-size: 0.95rem;
    color: #36b1dc;
    text-align: center;
    margin-top: 0.5rem;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

/* Product Actions */
.accommodation-product-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    gap: 10px;
}

.accommodation-product-price {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    width: 30%;
    text-align: left;
}

.accommodation-price-regular {
    color: #36b1dc;
    font-weight: 700;
    font-size: 1.1rem;
    line-height: 2rem;
}

.accommodation-price-sale {
    display: flex;
    flex-direction: column;
}

.accommodation-price-new {
    color: #ff6b6b;
}

.accommodation-price-old {
    text-decoration: line-through;
    font-size: 0.85rem;
    color: #999;
}

/* Buttons */
.accommodation-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

/* Added to cart message */
.accommodation-added-to-cart {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0.75rem;
    background-color: rgba(54, 177, 220, 0.1);
    border-radius: 6px;
    color: #36b1dc;
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
    animation: fadeIn 0.3s ease-in-out;
}

.accommodation-added-to-cart i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Variation Card */
.accommodation-variation-card,
.accommodation-quantity-card {
    position: absolute;
    top: 10%;
    left: 0;
    right: 0;
    width: 100%; /* Match the product card width */
    height: 80%; /* Set height to 80% of the product card */
    background-color: white;
    z-index: 1000; /* Increased z-index */
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px) scale(0.95);
    transition: opacity 0.4s ease, visibility 0.4s ease, transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    border-radius: 0.75rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08), 0 0 0 1000px rgba(0, 0, 0, 0.5);
    overflow-y: auto; /* Added to ensure content is scrollable */
    max-height: none; /* Remove max-height constraint */
    min-height: 400px; /* Ensure minimum height */
}

.accommodation-variation-card.active,
.accommodation-quantity-card.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* Add overlay when variation card is active */
.accommodation-product-card {
    position: relative;
}

.accommodation-product-card::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 500;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: none;
}

.accommodation-product-card:has(.accommodation-variation-card.active)::after,
.accommodation-product-card:has(.accommodation-quantity-card.active)::after {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

.accommodation-variation-card.closing,
.accommodation-quantity-card.closing {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.accommodation-variation-header,
.accommodation-quantity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(54, 177, 220, 0.1);
    position: relative;
    background-color: #f9f9f9;
    margin: -1.5rem -1.5rem 1.5rem -1.5rem;
    padding: 1rem 1.5rem;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
}

.accommodation-variation-header::after,
.accommodation-quantity-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color, #36b1dc), #2a8fb3);
}

.accommodation-variation-header h4,
.accommodation-quantity-header h4 {
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0;
    color: #333;
    letter-spacing: 0.5px;
}

.close-variation-card,
.close-quantity-card {
    background: none;
    border: none;
    font-size: 1.1rem;
    color: #999;
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-variation-card:hover,
.close-quantity-card:hover {
    color: #36b1dc;
    transform: scale(1.1);
}

.accommodation-variation-options,
.accommodation-quantity-options {
    flex-grow: 1;
    margin-bottom: 1.5rem;
    overflow-y: visible;
    padding-right: 0.5rem;
    position: relative;
    z-index: 20;
    max-height: none; /* Remove max-height constraint */
    display: block; /* Ensure it's displayed as a block */
}

.accommodation-variation-options::-webkit-scrollbar {
    width: 6px;
}

.accommodation-variation-options::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.accommodation-variation-options::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px;
}

.accommodation-variation-options::-webkit-scrollbar-thumb:hover {
    background: #36b1dc;
}

.accommodation-variation-option {
    margin-bottom: 0.75rem;
    animation: fadeInUp 0.4s ease forwards;
    animation-delay: calc(var(--option-index, 0) * 0.1s);
    opacity: 1; /* Changed from 0 to 1 to ensure visibility */
    position: relative;
    z-index: 5;
}

.variation-group-title {
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #333;
    position: relative;
    display: inline-block;
    padding-bottom: 0.25rem;
}

.variation-group-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color, #36b1dc), #2a8fb3);
    transition: width 0.3s ease;
}

/* Variation Cards */
.accommodation-variation-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    position: relative;
    z-index: 10;
    width: 100%; /* Ensure full width */
}

.accommodation-variation-card-option {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem 0.85rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    text-align: left;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    height: 100%;
    min-height: 50px; /* Further reduced height */
    z-index: 15;
    max-width: 100%;
}

.accommodation-variation-card-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color, #36b1dc), #2a8fb3);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.accommodation-variation-card-option::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, rgba(54, 177, 220, 0.05), transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.accommodation-variation-card-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(54, 177, 220, 0.15);
    border-color: #e0e0e0;
}

.accommodation-variation-card-option:hover::before {
    opacity: 1;
}

.accommodation-variation-card-option:hover::after {
    opacity: 1;
}

.accommodation-variation-card-option.selected {
    border-color: var(--primary-color, #36b1dc);
    background-color: rgba(54, 177, 220, 0.05);
    box-shadow: 0 8px 20px rgba(54, 177, 220, 0.2);
    transform: translateY(-5px);
}

.accommodation-variation-card-option.selected::before {
    opacity: 1;
}

.accommodation-variation-card-option.selected::after {
    opacity: 1;
    background: radial-gradient(circle at top right, rgba(54, 177, 220, 0.1), transparent 70%);
}

/* Checkmark icon for selected variation */
.accommodation-variation-card-option.selected .checkmark {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 5px;
    right: 5px;
    color: white;
    font-size: 0.6rem;
    background-color: var(--primary-color, #36b1dc);
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(54, 177, 220, 0.3);
    animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    z-index: 2;
}

@keyframes scaleIn {
    from { transform: scale(0); }
    to { transform: scale(1); }
}

.accommodation-variation-card-option .variation-name {
    font-weight: 600;
    color: #333;
    font-size: 0.85rem;
    line-height: 1.2;
    letter-spacing: 0;
    position: relative;
    margin: 0;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 0.5rem;
}

.accommodation-variation-card-option .variation-price {
    font-size: 0.8rem;
    color: #36b1dc;
    font-weight: 600;
    margin-left: 0.5rem;
    white-space: nowrap;
}

.accommodation-variation-option input.error + .accommodation-variation-cards,
.accommodation-variation-cards.error-highlight {
    border: 1px solid #ff6b6b;
    border-radius: 12px;
    background-color: rgba(255, 107, 107, 0.05);
    padding: 0.75rem;
    animation: pulse 1.5s ease infinite;
    position: relative;
}

.accommodation-variation-option input.error + .accommodation-variation-cards::before,
.accommodation-variation-cards.error-highlight::before {
    content: 'Please select an option';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #ff6b6b;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    animation: fadeInDown 0.5s ease-out;
}

/* No variations message */
.no-variations-message {
    text-align: center;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 1rem 0;
    border: 1px dashed #ddd;
}

.no-variations-message p {
    margin-bottom: 0.5rem;
    color: #666;
}

.no-variations-message p:first-child {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

@keyframes fadeInDown {
    from { opacity: 0; transform: translate(-50%, -10px); }
    to { opacity: 1; transform: translate(-50%, 0); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
}

.accommodation-quantity {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
    background-color: #f9f9f9;
}

.accommodation-quantity button {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    font-size: 1rem;
    color: #555;
    cursor: pointer;
    transition: all 0.2s ease;
}

.accommodation-quantity button:hover {
    background-color: #f1f1f1;
    color: #36b1dc;
}

.accommodation-quantity input {
    width: 60px;
    height: 40px;
    border: none;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    text-align: center;
    font-size: 1rem;
    font-weight: 500;
    color: #333;
    background-color: white;
}

.accommodation-quantity input:focus {
    outline: none;
}

.accommodation-variation-actions,
.accommodation-quantity-actions {
    margin-top: auto;
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(54, 177, 220, 0.1);
    position: relative;
    background-color: #f9f9f9;
    margin: 1.5rem -1.5rem -1.5rem -1.5rem;
    border-bottom-left-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
}

.accommodation-variation-actions::before,
.accommodation-quantity-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #2a8fb3, var(--primary-color, #36b1dc));
}

.accommodation-variation-actions .accommodation-btn,
.accommodation-quantity-actions .accommodation-btn {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(54, 177, 220, 0.2);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    width: 100%;
}

.accommodation-variation-actions .accommodation-btn:hover,
.accommodation-quantity-actions .accommodation-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(54, 177, 220, 0.3);
}

.accommodation-variation-actions .accommodation-btn:active,
.accommodation-quantity-actions .accommodation-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(54, 177, 220, 0.2);
}

.accommodation-btn-primary {
    background-color: #36b1dc;
    color: white;
    width: 70%;
}

.accommodation-btn-primary i,
.accommodation-btn-primary span {
    color: #fff !important;
}

.accommodation-btn-primary:hover {
    background-color: #2a8fb3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.accommodation-btn-disabled {
    background-color: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
    width: 70%;
}

/* My Bookings Section */
.accommodation-bookings-list {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.5rem;
}

.accommodation-booking-card {
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
    animation: fadeInUp 0.5s ease-out forwards;
    animation-delay: calc(var(--card-index, 0) * 0.1s);
    opacity: 0;
}

.accommodation-booking-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.accommodation-booking-content {
    display: flex;
    padding: 2rem;
    gap: 2.5rem;
    position: relative;
    z-index: 2;
    align-items: center;
    justify-content: center;
}

.accommodation-booking-image {
    flex: 0 0 180px;
    height: 180px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.accommodation-booking-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.accommodation-booking-card:hover .accommodation-booking-image img {
    transform: scale(1.1);
}

.accommodation-booking-no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e6f7ff 0%, #cceeff 100%);
    color: var(--primary-color, #36b1dc);
    font-size: 3rem;
}

.accommodation-booking-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 1rem;
}

.accommodation-booking-header {
    margin-bottom: 1.5rem;
    text-align: center;
    width: 100%;
}

.accommodation-booking-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin: 0 0 1rem 0;
    line-height: 1.3;
    position: relative;
    display: inline-block;
    text-align: center;
    letter-spacing: 0.5px;
}

.accommodation-booking-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color, #36b1dc), #2a8fb3);
    transition: width 0.3s ease;
}

.accommodation-booking-card:hover .accommodation-booking-title::after {
    width: 100%;
}

.accommodation-booking-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
    width: 100%;
}

.accommodation-booking-date {
    font-size: 0.95rem;
    color: #666;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.accommodation-booking-date i {
    color: var(--primary-color, #36b1dc);
    font-size: 1rem;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 992px) {
    .accommodation-booking-content {
        flex-direction: column;
    }

    .accommodation-booking-image {
        width: 100%;
        height: 220px;
        flex: none;
    }

    .accommodation-booking-title {
        text-align: center;
        display: block;
        width: 100%;
    }

    .accommodation-booking-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
}

/* Accommodation Wizard Styles */
.accommodation-wizard-button-container {
    margin: 2rem 0;
}

/* Pretour Notice Styles */
.accommodation-pretour-notice {
    max-width: 800px;
    margin: 0 auto 1.5rem;
}

.accommodation-pretour-notice .alert {
    border-radius: 10px;
    padding: 1.25rem;
    font-size: 1rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.accommodation-pretour-notice .alert-info {
    background-color: #e6f7ff;
    border-color: #b8e2f2;
    color: #0c5460;
}

.accommodation-btn-primary {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(54, 177, 220, 0.2);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    background-color: #36b1dc;
    color: white !important;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.accommodation-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(54, 177, 220, 0.3);
    background-color: #2a8fb3;
    color: white !important;
    text-decoration: none;
}

/* Wizard Steps Indicator */
.wizard-steps-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
}

.wizard-step-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f0f0f0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    position: relative;
    z-index: 2;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
}

.wizard-step-indicator.active {
    background-color: #36b1dc;
    color: white;
    border-color: #36b1dc;
}

.wizard-step-indicator.completed {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

.wizard-step-line {
    flex: 1;
    height: 3px;
    background-color: #e0e0e0;
    margin: 0 10px;
    transition: all 0.3s ease;
    max-width: 100px;
}

.wizard-step-line.active {
    background-color: #28a745;
}

/* Nights Selector Styles */
.accommodation-wizard-nights {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.nights-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.nights-selector-container {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.nights-btn {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nights-btn-minus {
    background-color: #f1f1f1;
    color: #666;
}

.nights-btn-plus {
    background-color: #36b1dc;
    color: white;
}

.nights-btn:hover:not(:disabled) {
    transform: scale(1.05);
}

.nights-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nights-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 1.5rem;
    min-width: 100px;
}

.nights-display span:first-child {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.nights-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: -5px;
}

/* Alpine.js Modal Animations */
[x-cloak] {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.slide-out {
    animation: slideOut 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(20px); opacity: 0; }
}

.accommodation-wizard-steps {
    position: relative;
}

.accommodation-wizard-step {
    display: none;
}

.accommodation-wizard-step.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

.accommodation-wizard-options {
    margin: 2rem 0;
}

.accommodation-wizard-option {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    border: 2px solid transparent;
}

.accommodation-wizard-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
}

.accommodation-wizard-option.selected {
    border-color: #36b1dc;
    box-shadow: 0 0 0 2px rgba(54, 177, 220, 0.3), 0 12px 24px rgba(0, 0, 0, 0.12);
}

.accommodation-wizard-option-image {
    height: 180px;
    overflow: hidden;
}

.accommodation-wizard-option-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.accommodation-wizard-option:hover .accommodation-wizard-option-image img {
    transform: scale(1.1);
}

.accommodation-wizard-option-content {
    padding: 1.5rem;
    text-align: center;
}

.accommodation-wizard-option-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.accommodation-wizard-option-content p {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0;
}

/* Modal Open Body Style */
body.modal-open {
    overflow: hidden;
}

/* Fallback Modal Styles */
#accommodation-wizard-modal.show-modal .lci-modal-backdrop {
    display: flex !important;
}

.lci-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-y: auto;
    padding: 20px 0;
}

/* Responsive Styles */
@media (max-width: 576px) {
    .accommodation-section-heading h3 {
        font-size: 1.4rem;
        letter-spacing: 1px;
    }

    .accommodation-section-heading {
        padding: 1rem 0;
        margin: 1.5rem 0;
    }

    .accommodation-product-actions {
        flex-direction: column;
    }

    .accommodation-product-price {
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .accommodation-btn-primary,
    .accommodation-btn-disabled {
        width: 100%;
    }

    /* Mini cart fixes for mobile */
    .mini-cart-wrapper {
        display: block !important;
        width: 100% !important;
        margin-bottom: 15px;
    }

    #mini-cart-button {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        width: 100% !important;
        justify-content: center !important;
    }

    /* Ensure the header layout works on mobile */
    .d-flex.justify-content-between.align-items-center.mb-4 {
        flex-direction: column-reverse;
        text-align: center;
    }

    .d-flex.justify-content-between.align-items-center.mb-4 > div {
        margin: 5px 0;
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .d-flex.justify-content-between.align-items-center.mb-4 h2 {
        text-align: center;
        width: 100%;
    }
}
